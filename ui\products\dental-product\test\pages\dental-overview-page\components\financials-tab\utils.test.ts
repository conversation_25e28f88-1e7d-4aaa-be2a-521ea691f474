/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */
import {filterPaymentData} from '../../../../../src/pages/dental-overview-page/components/financials-tab/utils'
import {PaymentStatus} from '../../../../../src/utils/types/enums'

describe('filterPaymentData', () => {
    it('should filter out paymentSchedules that exist in payments', () => {
        const mockPayments = [
            {
                paymentDetails: {
                    paymentDate: new Date('2024-01-01'),
                    paymentAllocations: [
                        {
                            allocationPayableItem: {
                                procedureID: 'PROC001',
                                orthoMonth: 1
                            }
                        }
                    ]
                },
                state: 'Issued'
            }
        ] as any

        const mockPaymentSchedules = [
            {
                _key: {rootId: 'schedule1'},
                payments: [
                    {
                        paymentDetails: {
                            paymentDate: new Date('2024-01-01'),
                            paymentAllocations: [
                                {
                                    allocationPayableItem: {
                                        procedureID: 'PROC001',
                                        orthoMonth: 1
                                    }
                                }
                            ]
                        }
                    },
                    {
                        paymentDetails: {
                            paymentDate: new Date('2024-01-02'),
                            paymentAllocations: [
                                {
                                    allocationPayableItem: {
                                        procedureID: 'PROC002',
                                        orthoMonth: 2
                                    }
                                }
                            ]
                        }
                    }
                ]
            }
        ] as any

        const result = filterPaymentData(mockPayments, mockPaymentSchedules)

        expect(result).toContain(mockPayments[0])

        const filteredSchedule = result.find(item => item._key?.rootId === 'schedule1')
        expect(filteredSchedule).toBeDefined()
        expect(filteredSchedule?.payments).toHaveLength(1)
        expect(
            filteredSchedule?.payments[0].paymentDetails.paymentAllocations[0].allocationPayableItem.procedureID
        ).toBe('PROC002')
    })

    it('should filter out canceled payments', () => {
        const mockPayments = [
            {
                paymentDetails: {
                    paymentDate: new Date('2024-01-01'),
                    paymentAllocations: []
                },
                state: PaymentStatus.Canceled
            },
            {
                paymentDetails: {
                    paymentDate: new Date('2024-01-02'),
                    paymentAllocations: []
                },
                state: 'Issued'
            }
        ] as any

        const result = filterPaymentData(mockPayments, [])

        expect(result).toHaveLength(1)
        expect(result[0].state).toBe('Issued')
    })

    it('should handle empty arrays', () => {
        const result = filterPaymentData([], [])
        expect(result).toEqual([])
    })

    // it('should remove schedules with no remaining payments after filtering', () => {
    //     const mockPayments = [
    //         {
    //             paymentDetails: {
    //                 paymentDate: new Date('2024-01-01'),
    //                 paymentAllocations: [
    //                     {
    //                         allocationPayableItem: {
    //                             procedureID: 'PROC001',
    //                             orthoMonth: 1
    //                         }
    //                     }
    //                 ]
    //             },
    //             state: 'Issued'
    //         }
    //     ] as any

    //     const mockPaymentSchedules = [
    //         {
    //             _key: {rootId: 'schedule1'},
    //             payments: [
    //                 {
    //                     paymentDetails: {
    //                         paymentDate: new Date('2024-01-01'),
    //                         paymentAllocations: [
    //                             {
    //                                 allocationPayableItem: {
    //                                     procedureID: 'PROC001',
    //                                     orthoMonth: 1
    //                                 }
    //                             }
    //                         ]
    //                     }
    //                 }
    //             ]
    //         }
    //     ] as any

    //     const result = filterPaymentData(mockPayments, mockPaymentSchedules)

    //     expect(result).toHaveLength(1)
    //     expect(result[0]).toBe(mockPayments[0])
    // })
})
