/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalUpdatePaymentScheduleInputValidator.CapDentalUpdatePaymentScheduleInputValidatorErrorDefinition.PAYEE_URI_INCORRECT_FOR_PAYMENT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPaymentScheduleUpdateInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalScheduledPaymentEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentPayeeDetailsEntity;
import com.eisgroup.genesis.factory.modeling.types.CapScheduledPayment;

/**
 * Class for validating {@link CapDentalPaymentScheduleUpdateInput}.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalUpdatePaymentScheduleInputValidator extends CapInputValidator<CapDentalPaymentScheduleUpdateInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalUpdatePaymentScheduleInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPaymentScheduleUpdateInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPaymentScheduleUpdateInput input) {
        return getDentalPaymentsStream(input.getPayments())
                .map(payment -> validatePayee(payment))
                .reduce(Streamable::concat)
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePayee(CapDentalScheduledPaymentEntity payment) {
        return Optional.ofNullable(payment)
                .map(CapDentalScheduledPaymentEntity::getPaymentDetails)
                .map(CapDentalPaymentDetailsEntity::getPayeeDetails)
                .map(CapDentalPaymentPayeeDetailsEntity::getPayee)
                .map(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_URI_INCORRECT_FOR_PAYMENT.builder().params(payee.getURIString()).build()))
                .orElse(Streamable.empty());
    }

    private Stream<CapDentalScheduledPaymentEntity> getDentalPaymentsStream(Collection<CapScheduledPayment> scheduledPayments) {
        return Optional.ofNullable(scheduledPayments)
                .orElse(new ArrayList<>())
                .stream()
                .filter(CapDentalScheduledPaymentEntity.class::isInstance)
                .map(CapDentalScheduledPaymentEntity.class::cast);
    }

    public static class CapDentalUpdatePaymentScheduleInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalUpdatePaymentScheduleInputValidatorErrorDefinition PAYEE_URI_INCORRECT_FOR_PAYMENT = new CapDentalUpdatePaymentScheduleInputValidatorErrorDefinition(
                "dpsu-001", "payeeDetails.payee {0} URI is not valid.");

        protected CapDentalUpdatePaymentScheduleInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
