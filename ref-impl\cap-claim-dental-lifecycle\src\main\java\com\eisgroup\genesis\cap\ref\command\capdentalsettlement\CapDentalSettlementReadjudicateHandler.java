/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.adjudication.command.input.CapSettlementCommands;
import com.eisgroup.genesis.cap.adjudication.command.validation.CapSettlementInputValidator;
import com.eisgroup.genesis.cap.adjudication.repository.ClaimSettlementRepository;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementReadjudicateInput;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementValidator;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.errors.ProductCommandHandlerErrorDefinition;
import com.eisgroup.genesis.commands.validator.errors.ValidationErrorException;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementDetailEntity;
import com.eisgroup.genesis.factory.model.capdentalsettlementindex.CapDentalSettlementIdx;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapPolicyHolder;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import com.eisgroup.genesis.factory.modeling.types.SinglePolicyHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.factory.services.AccessTrackInfoService;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.repository.ReadContext;
import com.eisgroup.genesis.versioning.CreatesVersion;

import io.reactivex.Single;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collection;
import java.util.Comparator;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementReadjudicateHandler.CapDentalSettlementReadjudicateErrorDefinition.LOSS_IDENTIFICATION_INCORRECT;
import static com.eisgroup.genesis.repository.ReadContext.EMBED_ALL;

/**
 * Internal command handler for CapDentalSettlement readjudicate.
 * Used in CapSettlement readjudicate
 */
@Modifying
public class CapDentalSettlementReadjudicateHandler implements ProductCommandHandler<CapDentalSettlementReadjudicateInput, CapSettlement> {

    @Autowired
    private CapDentalSettlementValidator capDentalSettlementValidator;

    @Autowired
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Autowired
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Autowired
    private CapSettlementInputValidator capSettlementInputValidator;

    @Autowired
    private AccessTrackInfoService accessTrackInfoService;

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private ClaimSettlementRepository claimSettlementRepository;

    @Autowired
    private CapDentalLinkValidator capDentalLinkValidator;

    @Nonnull
    @Override
    public CapSettlement load(@Nonnull CapDentalSettlementReadjudicateInput input) {
        // Validation is done here as validateAsync would require loaded entity for validation, however load cannot
        // be performed if loss identification is not valid.

        return (CapSettlement)validateRequest(input).get();

    }

    public Lazy validateRequest(@Nonnull CapDentalSettlementReadjudicateInput input) {
        return Lazy.from(() -> validateClaimLossIdentification(input.getClaimLossIdentification())
            .collect(Collectors.toList())).flatMap(errorHolders -> {
            if (!errorHolders.isEmpty()) {
                return Lazy.error(() -> new ValidationErrorException(wrapErrors(errorHolders)));
            }
            return loadSettlementByOriginSource(input.getClaimLossIdentification());
        });
    }

    private Lazy<Object> loadSettlementByOriginSource(EntityLink<RootEntity> lossLink) {
        return capDentalSettlementIndexResolver.resolveSettlementIndex(lossLink)
            .sorted(Comparator.comparing(CapDentalSettlementIdx::getCreatedOn,Comparator.reverseOrder()))
            .findFirst()
            .flatMap(this::resolveSettlementFromIndex);
    }

    private ErrorHolder wrapErrors(Collection<ErrorHolder> errorHolders) {
        return ProductCommandHandlerErrorDefinition.BUSINESS_CONSTRAINT_VIOLATED.builder().addChildren(errorHolders).build();
    }

    private Streamable<ErrorHolder> validateClaimLossIdentification(EntityLink<RootEntity> lossIdentification) {
        return Optional.ofNullable(lossIdentification)
                .map(loss -> capDentalLinkValidator.validateLink(loss, "CapLoss", LOSS_IDENTIFICATION_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    @Nonnull
    @Override
    public CapSettlement execute(@Nonnull CapDentalSettlementReadjudicateInput input, @Nonnull CapSettlement entity) {
        if(entity.getAccessTrackInfo() != null){
            return entity;
        }
        populateClaimLossIdentification(entity, input.getClaimLossIdentification());
        populateSettlementDetail(entity, input.getEntity());
        return entity;
    }

    @Nonnull
    @Override
    public CapSettlement save(@Nonnull CapDentalSettlementReadjudicateInput input, @Nonnull CapSettlement entity) {
        return (CapSettlement) claimSettlementRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return CapSettlementCommands.READJUDICATE_SETTLEMENT;
    }

    @Override
    public Streamable<ErrorHolder> validateAsync(CapDentalSettlementReadjudicateInput input, CapSettlement loadedEntity) {
        return Streamable.concat(capSettlementInputValidator.validateLossEntity(input.getClaimLossIdentification()),
            capDentalSettlementValidator.validateLossNotClosed(input.getClaimLossIdentification()));
    }

    protected CapSettlement populateClaimLossIdentification(CapSettlement entity, EntityLink<RootEntity> loss) {
        entity.setClaimLossIdentification(loss);
        return entity;
    }

    protected CapSettlement populateSettlementDetail(CapSettlement entity, CapDentalSettlementDetailEntity detail) {
        if (detail != null) {
            //do not create new ref for each update
            if (entity.getSettlementDetail() != null) {
                detail.toJson().add(BaseKey.ATTRIBUTE_NAME, entity.getSettlementDetail().getKey().toJson());
            }
            entity.setSettlementDetail(detail);
        }
        return entity;
    }

    protected Lazy<CapSettlement> removeInfo(CapSettlement entity){
        if (entity instanceof CapPolicyHolder) {
            ((CapPolicyHolder) entity).setPolicies(null);
        } else if (entity instanceof SinglePolicyHolder) {
            ((SinglePolicyHolder) entity).setPolicy(null);
        }
        entity.setSettlementResult(null);
        entity.setSettlementLossInfo(null);
        DomainModel model = modelResolver.resolveModel(DomainModel.class);
        KeyTraversalUtil.traverseRoot(entity.toJson(), model);
        return Lazy.of(entity);
    }

    private Lazy<CapSettlement> resolveSettlementFromIndex(CapDentalSettlementIdx settlementIndex) {
        EntityLink<RootEntity> settllementId = new EntityLink<>(RootEntity.class, settlementIndex.getSettlementId());;
        EntityLinkResolver linkResolver = entityLinkResolverRegistry.getByURIScheme(settllementId.getSchema());
        return linkResolver.resolve(settllementId, new ReadContext.Builder().embed(EMBED_ALL)
                .build());
    }

    public static class CapDentalSettlementReadjudicateErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalSettlementReadjudicateErrorDefinition LOSS_IDENTIFICATION_INCORRECT = new CapDentalSettlementReadjudicateErrorDefinition(
                "dsed-002", "claimLossIdentification URI is not valid.");

        private CapDentalSettlementReadjudicateErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
