/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.accumulator.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionSerialization;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.capdental.entity.accumulator.facade.ICapAccumulator;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.impl.CapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorLoadModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

import java.util.List;

public class CapAccumulator implements ICapAccumulator {
    private final PostModelAction<ICapAccumulatorLoadModel, List<ICapAccumulatorContainerModel>> loadAction;
    public CapAccumulator(RestActionConfiguration configuration) {
        loadAction = new PostModelAction<>(configuration);
    }

    @RestActionGetter("loadAction")
    @RestActionContext(target = "/api/capaccumulatorcontainer/CapAccumulatorContainer/v1/accumulator/loadAccumulators")
    @RestActionSerialization(serializationClazz = CapAccumulatorContainerModel.class)
    public PostModelAction<ICapAccumulatorLoadModel, List<ICapAccumulatorContainerModel>> load() {
        return loadAction;
    }
}
