{"TestData": {"entity": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 199.99, "currency": "USD"}, "dateOfService": "$<today-2M:yyyy-MM-dd>", "toothCodes": ["6"], "toothArea": "LA", "quantity": 1, "_type": "CapDentalProcedureEntity"}], "claimData": {"payeeType": "PrimaryInsured", "dateOfBirth": "$<today-20y:yyyy-MM-dd>", "source": "NONEDI", "transactionType": "ActualServices", "receivedDate": "$<today-1M:yyyy-MM-dd>", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//{{Dental_policyId}}", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel"}, "TestData_Full": {"entity": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 199.99, "currency": "USD"}, "dateOfService": "$<today-1d:yyyy-MM-dd>", "surfaces": ["B"], "toothArea": "00", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity"}, "_type": "CapDentalProcedureEntity"}], "claimData": {"patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//83a95ee4-4f12-3ebe-aa2f-673229742c03"}, "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//83a95ee4-4f12-3ebe-aa2f-673229742c03"}, "providerRole": {"_type": "CapProviderRole", "roleCd": ["IndividualProvider"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//83a95ee4-4f12-3ebe-aa2f-673229742c03", "providerLink": "geroot://Provider/IndividualProvider//83a95ee4-4f12-3ebe-aa2f-673229742c03"}, "payeeType": "PrimaryInsured", "dateOfBirth": "$<today-20y:yyyy-MM-dd>", "source": "NONEDI", "transactionType": "ActualServices", "receivedDate": "$<today:yyyy-MM-dd>", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//{{Dental_policyId}}", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel"}, "Ortho": {"downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "$<today-1d:yyyy-MM-dd>", "orthoMonthQuantity": 5, "orthoFrequencyCd": "OneTime", "_type": "CapDentalOrthodonticEntity", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalOrthodonticModel"}}