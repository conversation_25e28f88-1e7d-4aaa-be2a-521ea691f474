/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.validator.CapDentalPaymentTemplateUpdateInputValidator.CapDentalPaymentTemplateUpdateInputValidatorErrorDefinition.PAYEE_INCORRECT;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input.CapDentalPaymentTemplateUpdateInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentAllocationTemplateEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentAllocationTemplatePayeeDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentDetailsTemplateEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetailsTemplate;

/**
 * Class for {@link CapDentalPaymentTemplateUpdateInput} related validations.
 *
 * <AUTHOR>
 * @since 22.12
 */
public class CapDentalPaymentTemplateUpdateInputValidator extends CapInputValidator<CapDentalPaymentTemplateUpdateInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalPaymentTemplateUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPaymentTemplateUpdateInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPaymentTemplateUpdateInput input) {
        return validatePayee(input.getEntity());
    }

    private Streamable<ErrorHolder> validatePayee(CapPaymentDetailsTemplate paymentDetailsTemplate) {
        return Streamable.from(Optional.ofNullable(paymentDetailsTemplate)
                        .filter(CapDentalPaymentDetailsTemplateEntity.class::isInstance)
                        .map(CapDentalPaymentDetailsTemplateEntity.class::cast)
                        .map(CapDentalPaymentDetailsTemplateEntity::getPaymentAllocationTemplates)
                        .orElse(new ArrayList<>())
                        .stream()
                        .map(CapDentalPaymentAllocationTemplateEntity::getAllocationPayeeDetails)
                        .filter(Objects::nonNull)
                        .map(CapDentalPaymentAllocationTemplatePayeeDetailsEntity::getPayee)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
            )
            .flatMap(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_INCORRECT.builder().params(payee.getURIString()).build()));
    }

    public static class CapDentalPaymentTemplateUpdateInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalPaymentTemplateUpdateInputValidatorErrorDefinition PAYEE_INCORRECT = new CapDentalPaymentTemplateUpdateInputValidatorErrorDefinition(
                "cptu-001", "allocationPayeeDetails.payee {0} URI is not valid.");

        protected CapDentalPaymentTemplateUpdateInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
