// tslint:disable
/**
 * CapDentalUnverifiedPolicy model API facade
 * API for CapDentalUnverifiedPolicy
 *
 * OpenAPI spec version: 1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


import * as url from "url";
import * as portableFetch from "portable-fetch";
import { Configuration } from "./configuration";

const BASE_PATH = "https://localhost".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface FetchAPI
 */
export interface FetchAPI {
    (url: string, init?: any): Promise<Response>;
}

/**
 *  
 * @export
 * @interface FetchArgs
 */
export interface FetchArgs {
    url: string;
    options: any;
}

/**
 * 
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected fetch: FetchAPI = portableFetch) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath || this.basePath;
        }
    }
};

/**
 * 
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    name: "RequiredError"
    constructor(public field: string, msg?: string) {
        super(msg);
    }
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDeductibleDetailEntity
 */
export interface CapDentalUnverifiedPolicyCapDeductibleDetailEntity {
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDeductibleDetailEntity
     */
    individualPreventiveINNAnnualDeductible?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDeductibleDetailEntity
     */
    individualMajorINNAnnualDeductible?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDeductibleDetailEntity
     */
    individualBasicINNAnnualDeductible?: Money;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDeductibleDetailEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDeductibleDetailEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalMaximumEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalMaximumEntity {
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    individualBasicINNAnnualMaximum?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    implantsINNAnnualMaximum?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    orthoINNAnnualMaximum?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    individualPreventiveINNAnnualMaximum?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    individualMajorINNAnnualMaximum?: Money;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalMaximumEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity {
    /**
     * Coinsurance percentage outside network.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceOONPct?: number;
    /**
     * Coinsurance Service Type.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceServiceType?: string;
    /**
     * Coinsurance percentage in network.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceINPct?: number;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity
     */
    _key?: EntityKey;
}

/**
 * An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity {
    /**
     * Is patient full time student?
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    isFullTimeStudent?: boolean;
    /**
     * Insured's role.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    insuredRoleNameCd?: string;
    /**
     * Patient's relationship to primary insured.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    relationshipToPrimaryInsuredCd?: string;
    /**
     * Indicates if a party is a primary insured.
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    isMain?: boolean;
    /**
     * The unique registry ID that identifies the subject of the claim.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    registryTypeId?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity {
    /**
     * Late entrant preventive Waiting Period.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    preventWaitingPeriod?: number;
    /**
     * Late entrant basic Waiting Period.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    basicWaitingPeriod?: number;
    /**
     * Late entrant major Waiting Period.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    majorWaitingPeriod?: number;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalTermEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalTermEntity {
    /**
     * 
     * @type {Date}
     * @memberof CapDentalUnverifiedPolicyCapDentalTermEntity
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalUnverifiedPolicyCapDentalTermEntity
     */
    expirationDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalTermEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalTermEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity
 */
export interface CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity {
    /**
     * Claim Without Policy Employer Name.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity
     */
    employerName?: string;
    /**
     * Claim Without Policy Group Number.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity
     */
    groupNumber?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
 */
export interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy {
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    majorOONCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    preventiveOONCoinsurancePercent?: number;
    /**
     * Indicates if policy is verified.
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    isVerified?: boolean;
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalTermEntity}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    patientTerm?: CapDentalUnverifiedPolicyCapDentalTermEntity;
    /**
     * Indicates policy number.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    policyNumber?: string;
    /**
     * Basic waiting period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    basicWaitingPeriod?: string;
    /**
     * Plan Name.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    planName?: string;
    /**
     * Date which the Policy is paid up to.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    policyPaidToDate?: string;
    /**
     * PPO/DHMO product.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    planCategory?: string;
    /**
     * Policy version status
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    policyStatus?: string;
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalTermEntity}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    pcdTerm?: CapDentalUnverifiedPolicyCapDentalTermEntity;
    /**
     * 
     * @type {Array<CapDentalUnverifiedPolicyCapDentalMaximumEntity>}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    dentalMaximums?: Array<CapDentalUnverifiedPolicyCapDentalMaximumEntity>;
    /**
     * 
     * @type {Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity>}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    coinsurances?: Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoCoinsuranceEntity>;
    /**
     * Primary Care Dentist ID.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    pcdId?: string;
    /**
     * Implants Maximum Applies toward Plan Maximum
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    isImplantsMaximumAppliedTowardPlanMaximum?: boolean;
    /**
     * Is late entrant benefit period waiting applied.
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    applyLateEntrantBenefitWaitingPeriods?: boolean;
    /**
     * Date which the Policy is paid up to with Grace period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    policyPaidToDateWithGracePeriod?: string;
    /**
     * 
     * @type {Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity>}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    lateEntrantWaitingPeriodsDetails?: Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity>;
    /**
     * 
     * @type {CapDentalUnverifiedPolicyTerm}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    term?: CapDentalUnverifiedPolicyTerm;
    /**
     * Major waiting period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    majorWaitingPeriod?: string;
    /**
     * The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    txEffectiveDate?: string;
    /**
     * Plan type for the policy.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    plan?: string;
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    unverifiedInfo?: CapDentalUnverifiedPolicyCapDentalUnverifiedInfoEntity;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    basicINNCoinsurancePercent?: number;
    /**
     * Ortho waiting period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    orthoWaitingPeriod?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    isWaitingPeriodWaived?: boolean;
    /**
     * Child Max Age limit.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    childMaxAgeCd?: string;
    /**
     * 
     * @type {Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity>}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    insureds?: Array<CapDentalUnverifiedPolicyCapDentalPolicyInfoInsuredDetailsEntity>;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    nonStandardChildAgeLimit?: number;
    /**
     * Ortho In Network Coinsurance Percentage.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    orthoINNCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    preventiveINNCoinsurancePercent?: number;
    /**
     * Full time student Age limit.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    fullTimeStudentAgeCd?: string;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    waiveINNInd?: boolean;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    majorINNCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    basicOONCoinsurancePercent?: number;
    /**
     * Situs state
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    riskStateCd?: string;
    /**
     * Preventetive waiting period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    preventWaitingPeriod?: string;
    /**
     * Ortho Out of Network Coinsurance Percentage.
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    orthoOONCoinsurancePercent?: number;
    /**
     * Identification number of the policy in CAP subsystem.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    capPolicyId?: string;
    /**
     * Indicates whether policy type is master or certificate (individual).
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    policyType?: string;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    standardChildAgeLimit?: number;
    /**
     * Implants waiting period.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    implantsWaitingPeriod?: string;
    /**
     * 
     * @type {Array<CapDentalUnverifiedPolicyCapDeductibleDetailEntity>}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    deductibleDetails?: Array<CapDentalUnverifiedPolicyCapDeductibleDetailEntity>;
    /**
     * Defines policy version stored on CAP side.
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    capPolicyVersionId?: string;
    /**
     * The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    productCd?: string;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    waiveOONInd?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _timestamp?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _archived?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _type: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy
     */
    _key?: RootEntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess
 */
export interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess
     */
    success?: CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody
 */
export interface CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody {
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody
     */
    body?: CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccess;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalUnverifiedPolicyTerm
 */
export interface CapDentalUnverifiedPolicyTerm {
    /**
     * 
     * @type {Date}
     * @memberof CapDentalUnverifiedPolicyTerm
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalUnverifiedPolicyTerm
     */
    expirationDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnverifiedPolicyTerm
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalUnverifiedPolicyTerm
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapUnverifiedPolicyWriteInput
 */
export interface CapUnverifiedPolicyWriteInput {
    /**
     * 
     * @type {CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy}
     * @memberof CapUnverifiedPolicyWriteInput
     */
    entity: CapDentalUnverifiedPolicyCapDentalUnverifiedPolicy;
}

/**
 * 
 * @export
 * @interface CapUnverifiedPolicyWriteInputBody
 */
export interface CapUnverifiedPolicyWriteInputBody {
    /**
     * 
     * @type {CapUnverifiedPolicyWriteInput}
     * @memberof CapUnverifiedPolicyWriteInputBody
     */
    body?: CapUnverifiedPolicyWriteInput;
    /**
     * 
     * @type {string}
     * @memberof CapUnverifiedPolicyWriteInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapUnverifiedPolicyWriteInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface EndpointFailure
 */
export interface EndpointFailure {
    /**
     * 
     * @type {ErrorHolder}
     * @memberof EndpointFailure
     */
    data?: ErrorHolder;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailure
     */
    failure?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EndpointFailure
     */
    httpCode?: number;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailure
 */
export interface EndpointFailureFailure {
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailure
     */
    response?: string;
    /**
     * 
     * @type {EndpointFailure}
     * @memberof EndpointFailureFailure
     */
    failure?: EndpointFailure;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailureBody
 */
export interface EndpointFailureFailureBody {
    /**
     * 
     * @type {EndpointFailureFailure}
     * @memberof EndpointFailureFailureBody
     */
    body?: EndpointFailureFailure;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailureBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailureFailureBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface EntityKey
 */
export interface EntityKey {
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    rootId?: string;
    /**
     * 
     * @type {number}
     * @memberof EntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    parentId?: string;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    id?: string;
}

/**
 * 
 * @export
 * @interface EntityLink
 */
export interface EntityLink {
    /**
     * 
     * @type {string}
     * @memberof EntityLink
     */
    _uri?: string;
}

/**
 * 
 * @export
 * @interface EntityLinkRequest
 */
export interface EntityLinkRequest {
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    offset?: number;
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    limit?: number;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof EntityLinkRequest
     */
    links?: Array<EntityLink>;
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface EntityLinkRequestBody
 */
export interface EntityLinkRequestBody {
    /**
     * 
     * @type {EntityLinkRequest}
     * @memberof EntityLinkRequestBody
     */
    body?: EntityLinkRequest;
    /**
     * 
     * @type {string}
     * @memberof EntityLinkRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof EntityLinkRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface ErrorHolder
 */
export interface ErrorHolder {
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    code?: string;
    /**
     * 
     * @type {any}
     * @memberof ErrorHolder
     */
    details?: any;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    errorCode?: string;
    /**
     * 
     * @type {Array<ErrorHolder>}
     * @memberof ErrorHolder
     */
    errors?: Array<ErrorHolder>;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    field?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    logReference?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    message?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    path?: string;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequest
 */
export interface LoadEntityByBusinessKeyRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequestBody
 */
export interface LoadEntityByBusinessKeyRequestBody {
    /**
     * 
     * @type {LoadEntityByBusinessKeyRequest}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    body?: LoadEntityByBusinessKeyRequest;
    /**
     * 
     * @type {string}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequest
 */
export interface LoadSingleEntityRootRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequestBody
 */
export interface LoadSingleEntityRootRequestBody {
    /**
     * 
     * @type {LoadSingleEntityRootRequest}
     * @memberof LoadSingleEntityRootRequestBody
     */
    body?: LoadSingleEntityRootRequest;
    /**
     * 
     * @type {string}
     * @memberof LoadSingleEntityRootRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof LoadSingleEntityRootRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface Money
 */
export interface Money {
    /**
     * 
     * @type {number}
     * @memberof Money
     */
    amount: number;
    /**
     * 
     * @type {string}
     * @memberof Money
     */
    currency: string;
}

/**
 * 
 * @export
 * @interface ObjectSuccess
 */
export interface ObjectSuccess {
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccess
     */
    response?: string;
    /**
     * 
     * @type {any}
     * @memberof ObjectSuccess
     */
    success?: any;
}

/**
 * 
 * @export
 * @interface ObjectSuccessBody
 */
export interface ObjectSuccessBody {
    /**
     * 
     * @type {ObjectSuccess}
     * @memberof ObjectSuccessBody
     */
    body?: ObjectSuccess;
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccessBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ObjectSuccessBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface RootEntityKey
 */
export interface RootEntityKey {
    /**
     * 
     * @type {string}
     * @memberof RootEntityKey
     */
    rootId?: string;
    /**
     * 
     * @type {number}
     * @memberof RootEntityKey
     */
    revisionNo?: number;
}


/**
 * DefaultApi - fetch parameter creator
 * @export
 */
export const DefaultApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * The command that write new or updates existing claim policy
         * @param {CapUnverifiedPolicyWriteInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params: { body?: CapUnverifiedPolicyWriteInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/command/write`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapUnverifiedPolicyWriteInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.businessKey' is not null or undefined
            if (params.businessKey === null || params.businessKey === undefined) {
                throw new RequiredError('params.businessKey','Required parameter params.businessKey was null or undefined when calling apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet.');
            }
            const localVarPath = `/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/entities/{businessKey}/{revisionNo}`
                .replace(`{${"businessKey"}}`, encodeURIComponent(String(params.businessKey)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet.');
            }
            const localVarPath = `/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/entities/{rootId}/{revisionNo}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/link/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"EntityLinkRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params: { modelType?: string,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/unverifiedpolicy/CapDentalUnverifiedPolicy/v1/model/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.modelType !== undefined) {
                localVarQueryParameter['modelType'] = params.modelType;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * The command that write new or updates existing claim policy
         * @param {CapUnverifiedPolicyWriteInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params: { body?: CapUnverifiedPolicyWriteInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalUnverifiedPolicyCapDentalUnverifiedPolicySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params: { modelType?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * The command that write new or updates existing claim policy
         * @param {CapUnverifiedPolicyWriteInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params: { body?: CapUnverifiedPolicyWriteInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params, options)(fetch, basePath);
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params: { modelType?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params, options)(fetch, basePath);
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * The command that write new or updates existing claim policy
     * @param {CapUnverifiedPolicyWriteInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params: { body?: CapUnverifiedPolicyWriteInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1CommandWritePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} businessKey 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesBusinessKeyRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1EntitiesRootIdRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory model root entity records for given links.
     * @param {EntityLinkRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1LinkPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
     * @param {string} [modelType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params: { modelType?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiUnverifiedpolicyCapDentalUnverifiedPolicyV1ModelGet(params, options)(this.fetch, this.basePath);
    }

}

