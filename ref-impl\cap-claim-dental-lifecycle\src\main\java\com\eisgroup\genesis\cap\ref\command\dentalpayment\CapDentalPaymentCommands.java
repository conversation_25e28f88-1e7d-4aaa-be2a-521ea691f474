/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

/**
 * Holds command names of Dental Payment.
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapDentalPaymentCommands {

    public static final String GENERATE_PAYMENTS = "generatePayments";
    public static final String REQUEST_ISSUE_FAIL_PAYMENT = "requestIssueFailPayment";
    public static final String PROCESS_PAYMENT_LIFECYCLE = "processPaymentLifecycle";

    /**
     * Class is for constants only, no constructor to be exposed.
     */
    private CapDentalPaymentCommands() {
    }
}
