@Persistable
@CommandListener("Save","initPayment,updatePayment,approvePayment,cancelPayment,clearPayment,issuePayment,requestStopPayment,stopPayment,voidPayment,requestIssuePayment,requestIssueFailPayment,declinePayment")
Transformation CapDentalPaymentToIndex {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        CapDentalPaymentIndex.CapDentalPaymentIdxEntity
    }

    Attr originSource is payment.originSource._uri
    Attr paymentDate is payment.paymentDetails.paymentDate
    Attr paymentId is ToExtLink(payment)._uri
    Attr state is payment.state
}
