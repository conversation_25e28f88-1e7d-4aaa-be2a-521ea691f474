Secure using privilege ("LossIntake: Initialize Loss") {
    Model CapDentalLoss {
        Command initLoss
    }
}

Secure using privilege ("LossIntake: Update Loss") {
    Model CapDentalLoss {
        Command updateLoss
    }
}

Secure using privilege ("LossIntake: Update Loss Draft") {
    Model CapDentalLoss {
        Command updateLossDraft
    }
}

Secure using privilege ("LossIntake: Submit Loss") {
    Model CapDentalLoss {
        Command submitLoss
    }
}

Secure using privilege ("LossIntake: Open Loss") {
    Model CapDentalLoss {
        Command openLoss
    }
}

Secure using privilege ("LossIntake: Close Loss") {
    Model CapDentalLoss {
        Command closeLoss
    }
}

Secure using privilege ("LossIntake: Set Loss Sub-Status") {
    Model CapDentalLoss {
        Command setLossSubStatus
    }
}