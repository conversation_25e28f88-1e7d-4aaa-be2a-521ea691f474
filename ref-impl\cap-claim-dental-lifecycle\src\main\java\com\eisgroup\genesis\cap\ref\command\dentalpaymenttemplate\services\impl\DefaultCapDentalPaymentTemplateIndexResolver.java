/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.CapDentalPaymentTemplateIndexResolver;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplateindex.CapDentalPaymentTemplateIdxEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.parameter.TransformationInput;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import com.google.gson.JsonObject;

import java.util.List;

/**
 * Default {@link DefaultCapDentalPaymentTemplateIndexResolver} implementation.
 * Uses {@link ModeledTransformationService} to resolve {@link CapDentalPaymentTemplateIdxEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class DefaultCapDentalPaymentTemplateIndexResolver implements CapDentalPaymentTemplateIndexResolver {

    private final String indexTransformationName;
    private final ModeledTransformationService modeledTransformationService;
    private final ModelRepository<TransformationModel> transformationRepository;

    public DefaultCapDentalPaymentTemplateIndexResolver(String indexTransformationName,
                                                        ModeledTransformationService modeledTransformationService,
                                                        ModelRepository<TransformationModel> transformationRepository) {
        this.indexTransformationName = indexTransformationName;
        this.modeledTransformationService = modeledTransformationService;
        this.transformationRepository = transformationRepository;
    }


    @Override
    public Streamable<CapDentalPaymentTemplateIdxEntity> resolvePaymentTemplateIndex(
            EntityLink<RootEntity> originSource, List<String> applicableStates) {
        TransformationModel indexModel = transformationRepository.getActiveModel(indexTransformationName);
        return modeledTransformationService.transformAll(indexModel, createTransformationInput(indexModel, originSource))
                .flatMap(transformationOutput -> Streamable.from(transformationOutput.asCollection())
                        .cast(CapDentalPaymentTemplateIdxEntity.class))
                .filter(index -> applicableStates.contains(index.getState()));
    }

    private TransformationInput createTransformationInput(TransformationModel indexModel, EntityLink<RootEntity> originSource) {
        JsonObject originSourceKeyFilter = new JsonObject();
        originSourceKeyFilter.addProperty(CapDentalPaymentTemplateIdxEntity.ORIGIN_SOURCE_ATTR, originSource.getURIString());
        return TransformationInput.builder(indexModel).generic(originSourceKeyFilter).build();
    }
}
