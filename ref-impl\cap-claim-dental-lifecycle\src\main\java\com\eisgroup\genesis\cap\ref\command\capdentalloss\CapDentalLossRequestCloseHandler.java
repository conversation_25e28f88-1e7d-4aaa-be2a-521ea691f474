/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import static com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossCommands.REQUEST_CLOSE_LOSS;

/**
 * Command handler for Dental Loss close to extend core functionality
 *
 * <AUTHOR>
 * @since 25.11
 */
public class CapDentalLossRequestCloseHandler extends CapDentalLossCloseHandler {
    @Override
    public String getName() {
        return REQUEST_CLOSE_LOSS;
    }
}
