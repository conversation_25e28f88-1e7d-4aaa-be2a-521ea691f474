AssertClaimOverideAllowed=MT+Claim override isAllowed cannot be used and the same time with isDenied override.
AssertClaimOverideDenied=MT+Claim isDenied cannot be used at the same time with isAllowed override.
AssertServiceOverideAllowed=MT+Service override isAllowed cannot be used and the same time with isDenied override.
AssertServiceOverideDenied=MT+Service override isDenied cannot be used at the same time with isAllowed override.
ClaimOverrideBasicWaitingPeriodHigherThanZero=MT+overrideBasicWaitingPeriod has to be greater than 0.
ClaimOverrideGracePeriodEqualOrHigherThanZero=MT+Claim overrideGracePeriod cannot be negative.
ClaimOverrideLateEntrantWaitingPeriodHigherThanZero=MT+Claim overrideLateEntrantWaitingPeriod has to be greater than 0.
ClaimOverrideMajorWaitingPeriodHigherThanZero=MT+overrideMajorWaitingPeriod has to be greater than 0.
ClaimOverrideOrthoWaitingPeriodHigherThanZero=MT+overrideOrthoWaitingPeriod has to be greater than 0.
ClaimOverridePaymentInterestAmountEqualOrHigherThanZero=MT+Claim overridePaymentInterestAmount cannot be negative.
ClaimOverridePaymentInterestDaysEqualOrHigherThanZero=MT+Claim overridePaymentInterestDays cannot be negative.
ClaimOverridePreventiveWaitingPeriodHigherThanZero=MT+overridePreventiveWaitingPeriod has to be greater than 0.
MandatoryClaimURI=MT+A claimLossIdentification link to Claim is mandatory.
ServiceOverrideCoinsurancePctEqualOrHigherThanZero=MT+overrideCoinsurancePct cannot be negative.
ServiceOverrideConsideredAmountEqualOrHigherThanZero=MT+overrideConsideredAmount cannot be negative.
ServiceOverrideCopayAmountEqualOrHigherThanZero=MT+overrideCopayAmount cannot be negative.
ServiceOverrideCoveredAmountEqualOrHigherThanZero=MT+overrideCoveredAmount cannot be negative.
ServiceOverrideDeductibleEqualOrHigherThanZero=MT+overrideDeductible cannot be negative.
ServiceOverrideGracePeriodEqualOrHigherThanZero=MT+Service overrideGracePeriod cannot be negative.
ServiceOverrideLateEntrantWaitingPeriodHigherThanZero=MT+Service overrideLateEntrantWaitingPeriod has to be greater than 0.
ServiceOverrideMaximumAmountEqualOrHigherThanZero=MT+overrideMaximumAmount cannot be negative.
ServiceOverridePatientResponsibilityEqualOrHigherThanZero=MT+overridePatientResponsibility cannot be negative.
ServiceOverridePaymentInterestAmountEqualOrHigherThanZero=MT+Service overridePaymentInterestAmount cannot be negative.
ServiceOverridePaymentInterestDaysEqualOrHigherThanZero=MT+Service overridePaymentInterestDays cannot be negative.
ServiceOverrideReplacementLimitHigherThanZero=MT+overrideReplacementLimit has to be greater than 0.
ServiceOverrideServiceFrequencyLimitHigherThanZero=MT+overrideServiceFrequencyLimit has to be greater than 0.
ServiceOverrideServiceWaitingPeriodHigherThanZero=MT+overrideServiceWaitingPeriod has be to greater than 0.