@Description("Payment transaction information. The Root Entity of CAP Payment Domain.")
@StateMachine
Entity CapDentalPaymentEntity is CapPayment {

    //EISDEVTS-45186
    @Description("Dental claim for which the payment is created.")
    @Searchable
    ExtLink originSource: RootEntity

    //EISDEVTS-45186
    @Description("Dental payment details.")
    @KrakenChildContext
    @KrakenField
    Ref paymentDetails: CapDentalPaymentDetailsEntity

    //GENESIS-182571
    @Description("Unique payment number.")
    @Searchable
    Attr paymentNumber: String

    //EISDEVTS-40550
    @Description("Link to the schedule the payment was created from.")
    ExtLink paymentSchedule: RootEntity

    //GENESIS-182571
    @Description("Payment state in the lifecycle.")
    @Searchable
    Attr state: String

    @Description("Date when the payment was canceled or issue failed.")
    Attr cancelationDate: Datetime

    @Description("Entity that holds error and EOB messages details.")
    @ReadOnly
    Attr messages: *CapDentalPaymentMessageEntity
}

//GENESIS-365783
@Description("Used to process Payment Hub lifecycle event in automated proccess.")
Entity CapDentalProcessPaymentLifecycleOutput {

    //EISDEVTS-57177
    Attr paymentEvent: String

    //EISDEVTS-57177
    Attr paymentMethodType: String

    //EISDEVTS-57177
    Attr paymentUri: String
}

@Description("Holds information of message type.")
Entity CapDentalPaymentMessageEntity is MessageType {}
