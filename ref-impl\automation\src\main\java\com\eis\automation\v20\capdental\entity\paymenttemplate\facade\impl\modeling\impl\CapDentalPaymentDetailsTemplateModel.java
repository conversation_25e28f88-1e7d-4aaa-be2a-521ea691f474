/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.modeling.impl.CapBasePaymentDetailsTemplateModel;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.modeling.interf.ICapBasePaymentAllocationTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentDetailsTemplateModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentDetailsTemplateModel extends CapBasePaymentDetailsTemplateModel implements ICapDentalPaymentDetailsTemplateModel {

    @JsonSerialize(as = List.class, contentAs = CapDentalPaymentAllocationTemplateModel.class)
    public List<ICapBasePaymentAllocationTemplateModel> getPaymentAllocationTemplates() {
        return super.getPaymentAllocationTemplates();
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalPaymentAllocationTemplateModel.class)
    public void setPaymentAllocationTemplates(List<ICapBasePaymentAllocationTemplateModel> paymentAllocationTemplates) {
        super.setPaymentAllocationTemplates(paymentAllocationTemplates);
    }
}
