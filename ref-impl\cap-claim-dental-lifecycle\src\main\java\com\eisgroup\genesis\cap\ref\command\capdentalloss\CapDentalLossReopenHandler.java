/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.loss.command.ClaimLossReopenHandler;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossReopenInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Command handler for Dental Loss reopen to extend core functionality
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossReopenHandler extends ClaimLossReopenHandler<ClaimLossReopenInput, CapLoss> {

    @Autowired
    private CapValidatorRegistry validators;

    @Override
    public Streamable<ErrorHolder> validateAsync(ClaimLossReopenInput input, CapLoss loadedEntity) {
        return Streamable.concat(validators.validateRequest(input),
                super.validateAsync(input, loadedEntity));
    }


}
