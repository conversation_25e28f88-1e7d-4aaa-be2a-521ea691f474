/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalDetailModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalDetailModel extends TypeModel implements ICapDentalDetailModel {

    private String lossDesc;
    private ICapDentalClaimDataModel claimData;
    private List<ICapDentalProcedureModel> submittedProcedures;
    @JsonProperty(value = "_modelName")
    private String modelName;
    @JsonProperty(value = "_modelVersion")
    private String modelVersion;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    @JsonProperty(value = "_timestamp")
    private LocalDateTime timestamp;

    public String getLossDesc() {
        return lossDesc;
    }

    public void setLossDesc(String lossDesc) {
        this.lossDesc = lossDesc;
    }

    @JsonSerialize(as = CapDentalClaimDataModel.class)
    public ICapDentalClaimDataModel getClaimData() {
        return claimData;
    }

    @JsonDeserialize(as = CapDentalClaimDataModel.class)
    public void setClaimData(ICapDentalClaimDataModel claimData) {
        this.claimData = claimData;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalProcedureModel.class)
    public List<ICapDentalProcedureModel> getSubmittedProcedures() {
        return submittedProcedures;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalProcedureModel.class)
    public void setSubmittedProcedures(List<ICapDentalProcedureModel> submittedProcedures) {
        this.submittedProcedures = submittedProcedures;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
