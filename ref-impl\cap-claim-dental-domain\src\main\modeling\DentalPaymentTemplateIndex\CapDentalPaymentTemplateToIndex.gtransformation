@Persistable
@CommandListener("Save","initPaymentTemplate,updatePaymentTemplate,activatePaymentTemplate,completePaymentTemplate,cancelPaymentTemplate,suspendPaymentTemplate,unsuspendPaymentTemplate")
Transformation CapDentalPaymentTemplateToIndex {
    Input {
        CapDentalPaymentTemplate.CapDentalPaymentTemplateEntity as paymentTemplate
    }
    Output {
        CapDentalPaymentTemplateIndex.CapDentalPaymentTemplateIdxEntity
    }

    Attr originSource is paymentTemplate.originSource._uri
    Attr paymentTemplate is ToExtLink(paymentTemplate)._uri
    Attr state is paymentTemplate.state

}