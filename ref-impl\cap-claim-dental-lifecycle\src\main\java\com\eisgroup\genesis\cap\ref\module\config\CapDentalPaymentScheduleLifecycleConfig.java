/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapPaymentScheduleService;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.services.impl.DefaultCapDentalPaymentScheduleService;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalInitPaymentScheduleInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalUpdatePaymentScheduleInputValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.generator.impl.SimpleNumberGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.context.annotation.Bean;

/**
 * Claim Dental Payment Schedule lifecycle services configuration.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleLifecycleConfig {

    @Bean
    public CapValidatorRegistry capValidatorRegistry() {
        return new CapValidatorRegistry();
    }


    @Bean
    public CapPaymentScheduleService<CapDentalPaymentScheduleEntity> capDentalPaymentScheduleService(ModeledTransformationService modeledTransformationService,
                                                                                                     CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator) {
        return new DefaultCapDentalPaymentScheduleService(modeledTransformationService,
                ModelRepositoryFactory.getRepositoryFor(TransformationModel.class), capDentalPaymentScheduleValidator, "CalculatePaymentSchedule");
    }

    @Bean
    public EntityNumberGenerator paymentScheduleNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("cap_payment_schedule_number", "SCH%d", sequenceGenerator);
    }


    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator(CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository,
                                                                               ModeledTransformationService modeledTransformationService) {
        return new CapDentalPaymentScheduleValidator(capDentalPaymentScheduleIndexRepository,
            modeledTransformationService,
            ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }

    @Bean
    public CapDentalBuildPaymentScheduleInputValidator capDentalBuildPaymentScheduleInputValidator(EntityLinkResolverRegistry entityLinkResolverRegistry,
                                                                                                   CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalBuildPaymentScheduleInputValidator(entityLinkResolverRegistry, capDentalLinkValidator);
    }

    @Bean
    public CapDentalPreviewPaymentScheduleInputValidator capDentalPreviewPaymentScheduleInputValidator(EntityLinkResolverRegistry entityLinkResolverRegistry,
                                                                                                       CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPreviewPaymentScheduleInputValidator(entityLinkResolverRegistry, capDentalLinkValidator);
    }

    @Bean
    public CapDentalInitPaymentScheduleInputValidator capDentalInitPaymentScheduleInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalInitPaymentScheduleInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalUpdatePaymentScheduleInputValidator capDentalUpdatePaymentScheduleInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalUpdatePaymentScheduleInputValidator(capDentalLinkValidator);
    }
}
