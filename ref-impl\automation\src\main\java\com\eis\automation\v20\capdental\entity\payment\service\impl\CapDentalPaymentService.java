/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.payment.facade.ICapDentalPayment;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentAllocationModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.payment.service.ICapDentalPaymentService;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.service.ICapDentalPaymentTemplateService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Lazy
@Component("capDentalPayment")
public class CapDentalPaymentService implements ICapDentalPaymentService {

    @Autowired
    private ICapDentalPaymentTemplateService capDentalPaymentTemplate;
    @Autowired
    private ICapDentalPayment capDentalPayment;
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdCapDentalPaymentLocation;
    private TestData tdSpecificCapDentalPaymentLocation;

    @Override
    public ICapDentalPayment getFacade() {
        return capDentalPayment;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdCapDentalPaymentLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificCapDentalPaymentLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdCapDentalPaymentLocation = testDataProvider.getJSONTestData("/capdental/payment");
        tdSpecificCapDentalPaymentLocation = testDataProvider.getJSONTestData("/capdental/payment/specific");
    }

    public ICapDentalPaymentModel initDentalPayment(ICapDentalPaymentModel capDentalPaymentModel) {
        return (ICapDentalPaymentModel) getFacade().init().perform(b -> b.setModel(capDentalPaymentModel));
    }

    public ICapDentalPaymentModel initDentalPayment(ICustomerModel payee, ICapDentalLossModel dentalClaimModel,
                                                    ICapDentalSettlementModel dentalSettlementModel) {
        return (ICapDentalPaymentModel) getFacade().init().perform(b -> b.setModel(
                createDentalPaymentModel(payee, dentalClaimModel, dentalSettlementModel)));
    }

    public ICapDentalPaymentModel createDentalPaymentModel(ICustomerModel customerModel, ICapDentalLossModel dentalClaimModel,
                                                           ICapDentalSettlementModel dentalSettlementModel) {
        // Initiate Dental Payment Template
        ICapDentalPaymentTemplateModel dentalPaymentTemplateModel = capDentalPaymentTemplate.initDentalPaymentTemplate(
                capDentalPaymentTemplate.createDentalPaymentTemplateModel(customerModel, dentalClaimModel, dentalSettlementModel));
        // Create Dental Payment model
        ICapDentalPaymentModel capDentalPaymentModel = modelUtils.create(getTestData("Write", "TestData"));
        capDentalPaymentModel.setOriginSource(dentalClaimModel.getGentityUri().getUriModel());
        capDentalPaymentModel.getEntity().getPayeeDetails().setPayee(customerModel.getGentityUri().getUriModel());
        CapDentalPaymentAllocationModel allocationModel = (CapDentalPaymentAllocationModel) capDentalPaymentModel.getEntity().getPaymentAllocations().get(0);
        allocationModel.setAllocationSource(dentalSettlementModel.getGentityUri().getUriModel());
        allocationModel.getAllocationPayableItem().setClaimSource(dentalClaimModel.getGentityUri().getUriModel());
        return capDentalPaymentModel;
    }

    public ICapDentalPaymentModel loadDentalPayment(ICapDentalPaymentModel capDentalPaymentModel) {
        return (ICapDentalPaymentModel) getFacade().entities().perform(b -> b
                .setRootId(capDentalPaymentModel.rootId())
                .setRevisionNumber(capDentalPaymentModel.revisionNumber()));
    }

    public ICapDentalPaymentModel requestIssueDentalPayment(ICapDentalPaymentModel capDentalPaymentModel) {
        return (ICapDentalPaymentModel) getFacade().requestIssue().perform(b -> b.setModel(capDentalPaymentModel.getKey()));
    }

    public ICapDentalPaymentModel issueDentalPayment(ICapDentalPaymentModel capDentalPaymentModel) {
        return (ICapDentalPaymentModel) getFacade().issue().perform(b -> b.setModel(capDentalPaymentModel.getKey()));
    }

    public ICapDentalPaymentModel clearDentalPayment(ICapDentalPaymentModel capDentalPaymentModel) {
        return (ICapDentalPaymentModel) getFacade().clear().perform(b -> b.setModel(capDentalPaymentModel.getKey()));
    }
}
