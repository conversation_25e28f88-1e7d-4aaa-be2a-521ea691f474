#!/usr/bin/env bash
JAVA_EXEC="${JAVA_HOME:-java}${JAVA_HOME:+/bin/java} ${JAVA_OPTS}"

# Set NM_HOST
export NM_HOST=${EXTERNAL_IP:-$( hostname -I | cut -d ' ' -f 1)}

# Set APP_NAME
APP_NAME=${GENESIS_APP_NAME:-`echo $NM_HOST`}

# Change to /usr/lib/genesis/
cd /usr/lib/genesis/

# Execute
${JAVA_EXEC} \
    -Dgenesis.infra.facade.url="${GENESIS_INFRA_APP_URL:-$GENESIS_INFRA_FACADE_URL}" \
    -Dgenesis.deployer.goals="${deployer_goal:-deploy}" \
    -Dgenesis.app.name="$APP_NAME" \
        org.springframework.boot.loader.launch.PropertiesLauncher ${1}
