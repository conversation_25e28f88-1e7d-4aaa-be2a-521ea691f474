// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"CapDentalPaymentIndex",
  "name":"CapDentalPaymentIndex",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalPaymentIdxEntity"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapDentalPaymentIdxEntity":{
      "attributes":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"originSource",
          "type":{
            "type":"STRING"
          }
        },
        "paymentDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"paymentDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "paymentId":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      {
                        "@class":"java.lang.Long",
                        "value":"0"
                      }
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"paymentId",
          "type":{
            "type":"STRING"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.cap.factory.features.Persistable":{
          "keyspace":"CapDentalPaymentDefinition"
        },
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.Persistable":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "CapDentalPaymentDefinition"
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentIdxEntity",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalPaymentIndex {
    export type Variations = never


    export class CapDentalPaymentIdxEntity extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentIdxEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentIndex'
        readonly _modelType: string = 'CapDentalPaymentIndex'
        readonly _modelVersion?: string = '1'
        readonly originSource?: string
        readonly paymentDate?: Date
        readonly paymentId?: string
        readonly state?: string
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalPaymentIdxEntity, ()=> new CapDentalPaymentIdxEntity)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}