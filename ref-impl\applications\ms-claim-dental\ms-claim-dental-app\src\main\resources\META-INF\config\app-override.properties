genesis.ms.name=Dental
genesis.microservice.schema.prefix=cap_dental
genesis.lifecycle.state.validation.strict=false
external.models.store.url=http://infra_facade:8484

entity.validator.derived.schema.strict=true

genesis.security.service.account.mode=basic
genesis.security.service.account.basic.credentials=Y2FwLXNlcnZpY2U6Y2FwLXNlcnZpY2U=

genesis.domain.deserialize.external.types=true
genesis.link.remote.schemas=workflow,CapAccumulator

cap.search.reindex.job.name=CapDentalReindexJob

# disable non-metadata (deprecated) indexing features
cap.default.reindex.listener.enabled=false
cap.default.reindex.listener.v2.enabled=false
cap.transformation.indexing.listener.enabled=false