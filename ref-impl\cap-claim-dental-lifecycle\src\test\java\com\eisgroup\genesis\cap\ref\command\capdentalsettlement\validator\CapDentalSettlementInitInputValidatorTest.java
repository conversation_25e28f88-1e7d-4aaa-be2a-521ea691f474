/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementInitInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.entity.Stateful;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalsettlementindex.CapDentalSettlementIdx;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator.CapDentalSettlementInitInputErrorDefinition.LOSS_STATE_INCOMPLETE;
import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator.CapDentalSettlementInitInputErrorDefinition.SETTLEMENT_EXISTS;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.withSettings;

public class CapDentalSettlementInitInputValidatorTest {

    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private EntityLinkResolver<RootEntity> entityLinkResolver;

    @InjectMocks
    private CapDentalSettlementInitInputValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testErrorWhenSettlementExist() {
        //given
        CapDentalSettlementIdx settlementIndex = mock(CapDentalSettlementIdx.class);
        when(capDentalSettlementIndexResolver.resolveSettlementIndex(any(EntityLink.class)))
                .thenReturn(Streamable.of(settlementIndex));
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        RootEntity rootEntity = mock(RootEntity.class);
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.of(rootEntity));

        CapDentalSettlementInitInput request = new CapDentalSettlementInitInput(JsonUtils.load("requestSamples/settlementInitInput.json"));

        //when
        TestStreamable.create(validator.validate(request))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(SETTLEMENT_EXISTS.getCode()));
    }

    @Test
    public void testValidationWhenSettlementNotExist() {
        //given
        when(capDentalSettlementIndexResolver.resolveSettlementIndex(any(EntityLink.class)))
                .thenReturn(Streamable.empty());
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        RootEntity rootEntity = mock(RootEntity.class);
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.of(rootEntity));

        CapDentalSettlementInitInput request = new CapDentalSettlementInitInput(JsonUtils.load("requestSamples/settlementInitInput.json"));

        //when
        TestStreamable.create(validator.validate(request))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testErrorWhenLossIncomplete() {
        // given
        when(capDentalSettlementIndexResolver.resolveSettlementIndex(any(EntityLink.class)))
                .thenReturn(Streamable.empty());
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        RootEntity rootEntity = mock(RootEntity.class, withSettings().extraInterfaces(Stateful.class));
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.of(rootEntity));
        when(((Stateful) rootEntity).getState()).thenReturn("Incomplete");

        CapDentalSettlementInitInput request = new CapDentalSettlementInitInput(JsonUtils.load("requestSamples/settlementInitInput.json"));

        // when
        TestStreamable.create(validator.validate(request))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(LOSS_STATE_INCOMPLETE.getCode()));
    }

    @Test
    public void testValidationWhenLossIsNotIncomplete() {
        // given
        when(capDentalSettlementIndexResolver.resolveSettlementIndex(any(EntityLink.class)))
                .thenReturn(Streamable.empty());
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        RootEntity rootEntity = mock(RootEntity.class, withSettings().extraInterfaces(Stateful.class));
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.of(rootEntity));
        when(((Stateful) rootEntity).getState()).thenReturn("Open");

        CapDentalSettlementInitInput request = new CapDentalSettlementInitInput(JsonUtils.load("requestSamples/settlementInitInput.json"));

        // when
        TestStreamable.create(validator.validate(request))
            .assertNoErrors()
            .assertNoValues();
    }
}