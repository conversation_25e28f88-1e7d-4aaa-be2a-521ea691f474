/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

import java.util.Collections;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalBuildPaymentScheduleInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.TargetEntityNotFoundException;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;

public class CapDentalBuildPaymentScheduleInputValidatorTest {

    private static final String SETTLEMENT_LINK = "gentity://CapSettlement/CapDentalSettlement//861a158d-1a86-4b00-9331-8427677e05d2/1";
    private static final String SETTLEMENT_LINK_INCORRECT_TYPE = "gentity://IncorrectType/CapDentalSettlement//861a158d-1a86-4b00-9331-8427677e05d2/1";
    private static final String ORIGIN_SOURCE = "gentity://CapLoss/CapDentalLoss//06e52a9b-e8ba-4afc-845b-6cd060d37a05/1";
    private static final String CAP_LOSS_MODEL_TYPE = "CapLoss";

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private EntityLinkResolver<RootEntity> entityLinkResolver;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;

    @InjectMocks
    private CapDentalBuildPaymentScheduleInputValidator inputValidator;

    @Before
    public void setUp() {
        initMocks(this);
    }

    @Test
    public void testValidationPasses() {
        // given
        CapDentalBuildPaymentScheduleInput input = mock(CapDentalBuildPaymentScheduleInput.class);
        EntityLink<RootEntity> settlementLink = new EntityLink<>(RootEntity.class, SETTLEMENT_LINK);
        when(input.getSettlements()).thenReturn(Collections.singletonList(settlementLink));
        when(input.getOriginSource()).thenReturn(new EntityLink<>(RootEntity.class, ORIGIN_SOURCE));

        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        CapDentalSettlementEntity settlement = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalBuildPaymentScheduleInputValidator/settlement.json"));
        when(entityLinkResolver.resolve(eq(settlementLink), any())).thenReturn(Lazy.of(settlement));

        // when
        TestStreamable.create(inputValidator.validate(input))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testIncorrectOriginSourceLink() {
        // given
        CapDentalBuildPaymentScheduleInput input = mock(CapDentalBuildPaymentScheduleInput.class);
        EntityLink<RootEntity> settlementLink = new EntityLink<>(RootEntity.class, SETTLEMENT_LINK);
        when(input.getSettlements()).thenReturn(Collections.singletonList(settlementLink));
        EntityLink<RootEntity> originSource = new EntityLink<>(RootEntity.class, ORIGIN_SOURCE);
        when(input.getOriginSource()).thenReturn(originSource);

        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        CapDentalSettlementEntity settlement = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalBuildPaymentScheduleInputValidator/settlement.json"));
        when(entityLinkResolver.resolve(eq(settlementLink), any())).thenReturn(Lazy.of(settlement));

        ErrorHolder errorHolder = mock(ErrorHolder.class);
        when(capDentalLinkValidator.validateLink(eq(originSource), eq(CAP_LOSS_MODEL_TYPE), any()))
                .thenReturn(Streamable.of(errorHolder));

        // when
        TestStreamable.create(inputValidator.validate(input))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(errorHolder);
    }

    @Test
    public void testIncorrectLinkType() {
        // given
        CapDentalBuildPaymentScheduleInput input = mock(CapDentalBuildPaymentScheduleInput.class);
        EntityLink<RootEntity> settlementLink = new EntityLink<>(RootEntity.class, SETTLEMENT_LINK_INCORRECT_TYPE);
        when(input.getSettlements()).thenReturn(Collections.singletonList(settlementLink));

        when(input.getOriginSource()).thenReturn(new EntityLink<>(RootEntity.class, ORIGIN_SOURCE));

        // when
        TestStreamable.create(inputValidator.validate(input))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(CapDentalBuildPaymentScheduleInputValidator
                .CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_URI_INCORRECT.getCode()));
    }

    @Test
    public void testNonExistingSettlement() {
        // given
        CapDentalBuildPaymentScheduleInput input = mock(CapDentalBuildPaymentScheduleInput.class);
        EntityLink<RootEntity> settlementLink = new EntityLink<>(RootEntity.class, SETTLEMENT_LINK);
        when(input.getSettlements()).thenReturn(Collections.singletonList(settlementLink));

        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        when(entityLinkResolver.resolve(eq(settlementLink), any())).thenReturn(Lazy.error(TargetEntityNotFoundException::new));

        when(input.getOriginSource()).thenReturn(new EntityLink<>(RootEntity.class, ORIGIN_SOURCE));

        // when
        TestStreamable.create(inputValidator.validate(input).map(ErrorHolder::getCode))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(CapDentalBuildPaymentScheduleInputValidator
                .CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_URI_INCORRECT.getCode());
    }

    @Test
    public void testSettlementDataValidations() {
        // given
        CapDentalBuildPaymentScheduleInput input = mock(CapDentalBuildPaymentScheduleInput.class);
        EntityLink<RootEntity> settlementLink = new EntityLink<>(RootEntity.class, SETTLEMENT_LINK);
        when(input.getSettlements()).thenReturn(Collections.singletonList(settlementLink));
        when(input.getOriginSource()).thenReturn(new EntityLink<>(RootEntity.class, ORIGIN_SOURCE));

        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        CapDentalSettlementEntity settlement = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalBuildPaymentScheduleInputValidator/settlementNotValid.json"));
        when(entityLinkResolver.resolve(eq(settlementLink), any())).thenReturn(Lazy.of(settlement));

        // when
        TestStreamable.create(inputValidator.validate(input).map(ErrorHolder::getCode))
                // then
                .assertNoErrors()
                .assertValueCount(3)
                .assertValues(
                        CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.OTHER_SETTLEMENT_ORIGIN_SOURCE.getCode(),
                        CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_NOT_APPROVED.getCode(),
                        CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.PROPOSAL_NOT_PAY.getCode());

    }
}
