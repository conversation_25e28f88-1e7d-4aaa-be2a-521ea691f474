/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf;

import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;

import java.time.LocalDate;
import java.util.List;

public interface ICapDentalClaimInfoModel extends ITypeModel {

    String getSource();

    LocalDate getReceivedDate();

    ICapDentalClaimDataModel getClaimData();

    List<ICapDentalSettlementInfoProcedureModel> getSubmittedProcedures();

    ICapDentalPatientModel getPatient();
}
