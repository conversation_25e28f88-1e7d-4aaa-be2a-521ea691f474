{"swagger": "2.0", "x-dxp-spec": {"imports": {"dental.search": {"schema": "integration.dental.search.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster - Dental - Search", "version": "1", "title": "CAP Adjuster - Dental - Search"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-dental-search", "description": "CAP Adjuster - Dental - Search"}], "paths": {"/losses-dental-search/loss/{rootId}/settlement": {"get": {"x-dxp-path": "/api/common/search/v3/CapDentalSettlement", "x-dxp-method": "post", "tags": ["/cap-adjuster/v1/losses-dental-search"], "parameters": [{"name": "rootId", "in": "path", "x-dxp-mapping": "@body/body.query.claimLossIdentification.notEqual"}]}}, "/losses-dental-search/settlement": {"post": {"x-dxp-path": "/api/common/search/v3/CapDentalSettlement", "tags": ["/cap-adjuster/v1/losses-dental-search"]}}, "/losses-dental-search/loss": {"post": {"summary": "Search Loss", "x-dxp-path": "/api/common/search/v3/CapDentalLoss", "tags": ["/cap-adjuster/v1/losses-dental-search"]}}}}