/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.v20.cap.entity.common.modeling.impl.PeriodModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.IPeriodModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalPreauthorizationModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

public class CapDentalPreauthorizationModel extends TypeModel implements ICapDentalPreauthorizationModel {

    private String authorizedBy;
    private IPeriodModel authorizationPeriod;
    private Boolean isProcedureAuthorized;

    public String getAuthorizedBy() {
        return authorizedBy;
    }

    public void setAuthorizedBy(String authorizedBy) {
        this.authorizedBy = authorizedBy;
    }

    @JsonSerialize(as = PeriodModel.class)
    public IPeriodModel getAuthorizationPeriod() {
        return authorizationPeriod;
    }

    @JsonDeserialize(as = PeriodModel.class)
    public void setAuthorizationPeriod(IPeriodModel authorizationPeriod) {
        this.authorizationPeriod = authorizationPeriod;
    }

    public Boolean getIsProcedureAuthorized() {
        return isProcedureAuthorized;
    }

    public void setIsProcedureAuthorized(Boolean isProcedureAuthorized) {
        this.isProcedureAuthorized = isProcedureAuthorized;
    }
}
