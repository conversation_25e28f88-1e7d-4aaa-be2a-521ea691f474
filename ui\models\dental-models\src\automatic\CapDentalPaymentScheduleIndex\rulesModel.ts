// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALPAYMENTSCHEDULEINDEX } from "./kraken_model_tree_CapDentalPaymentScheduleIndex"

let name = "CapDentalPaymentScheduleIndex"

let namespace = "CapDentalPaymentScheduleIndex"

let currencyCd = "USD"

export type CapDentalPaymentScheduleIndexEntryPointName = never

let entryPointNames = [
] as CapDentalPaymentScheduleIndexEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALPAYMENTSCHEDULEINDEX as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalPaymentScheduleIndex = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
