/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input;

import com.eisgroup.genesis.cap.financial.command.paymentschedule.input.CapPaymentScheduleInitInput;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.modeling.types.CapScheduledPayment;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Collection;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

/**
 * Command input for initiating Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleInitInput extends CapPaymentScheduleInitInput {

    public CapDentalPaymentScheduleInitInput(JsonObject original) {
        super(original);
    }

    public CapDentalPaymentScheduleInitInput(CapDentalPaymentScheduleEntity entity) {
        super(new JsonObject());
        setChildObject(PAYMENT_TEMPLATE, entity.getPaymentTemplate());
        setChildObject(ORIGIN_SOURCE, entity.getOriginSource());
        setChildObject(PAYMENTS, entity.getPayments());
    }

    @Override
    public Collection<CapScheduledPayment> getPayments() {
        return Optional.ofNullable(getRawChild(PAYMENTS))
                .filter(JsonElement::isJsonArray)
                .map(JsonElement::getAsJsonArray)
                .map(jsonElements -> StreamSupport.stream(jsonElements.spliterator(), false)
                        .filter(JsonElement::isJsonObject)
                        .map(JsonElement::getAsJsonObject)
                        .map(payment -> ModelInstanceFactory.createInstance("CapDentalPaymentSchedule",
                                "1", payment))
                        .map(CapScheduledPayment.class::cast)
                        .collect(Collectors.toList()))
                .orElse(null);
    }
}
