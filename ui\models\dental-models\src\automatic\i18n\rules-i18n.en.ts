// tslint:disable
import { Localization } from '@eisgroup/i18n'

export const enUS: Localization.ResourceBundle = {
    locale : { country : 'US' , language : 'en' } ,
    version : 1 ,
    ns : 'rules',
    resources : {
        'MandatoryPatientRoleCd': 'Patient roleCd is required.',
        'AssertServiceOverideAllowed': 'Service override isAllowed cannot be used and the same time with isDenied override.',
        'number-set-max-error': 'Value must be {{0}} or smaller',
        'ReceivedDateCannotBeInFuture': 'Received Date cannot be in the future.',
        'ProcedureQuantityMax': 'Quantity must not be greater than 99.',
        'MandatoryClaimDiscountName': 'discountName is mandatory.',
        'MandatoryClaimPayeeType': 'Payee Type is mandatory.',
        'ConsideredCannotBeNegative': 'cob.considered cannot be negative.',
        'AssertClaimOverideAllowed': 'Claim override isAllowed cannot be used and the same time with isDenied override.',
        'CleanClaimDateCannotBeBeforeFutureDate': 'cleanClaimDate cannot be in the future.',
        'rule-mandatory-empty-error': 'Field must be empty',
        'MandatoryPatientRegistryId': 'Patient is required.',
        'MandatoryClaimPatient': 'Patient is mandatory.',
        'MandatoryHistoryClaimPatient': 'patient is mandatory.',
        'ClaimOverridePreventiveWaitingPeriodHigherThanZero': 'overridePreventiveWaitingPeriod has to be greater than 0.',
        'SuspendLossOnlyAvailableForOrtho': 'suspendLoss command cannot be performed if transactionType is not OrthodonticServices.',
        'DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank': 'Date of Service cannot be after the Received Date.',
        'ServiceOverrideCoveredAmountEqualOrHigherThanZero': 'overrideCoveredAmount cannot be negative.',
        'ClaimOverrideOrthoWaitingPeriodHigherThanZero': 'overrideOrthoWaitingPeriod has to be greater than 0.',
        'ProcedureQuantityMin': 'Quantity has to be equal or more than 1.',
        'ServiceOverridePaymentInterestDaysEqualOrHigherThanZero': 'Service overridePaymentInterestDays cannot be negative.',
        'MandatoryHistoryDOSDateIfActualServices': 'DOSDate is mandatory when isPredet is not true.',
        'MandatoryClaimPolicyHolder': 'Policy Holder is mandatory.',
        'ServiceOverrideReplacementLimitHigherThanZero': 'overrideReplacementLimit has to be greater than 0.',
        'MandatoryClaimReceivedDate': 'Received Date is mandatory.',
        'ServiceOverrideGracePeriodEqualOrHigherThanZero': 'Service overrideGracePeriod cannot be negative.',
        'MandatoryClaimProviderFeeType': 'providerFee.type is mandatory.',
        'CapDentalPaymentDetailsEntity-001': 'payeeDetails are required.',
        'ServiceOverrideMaximumAmountEqualOrHigherThanZero': 'overrideMaximumAmount cannot be negative.',
        'ServiceOverrideServiceWaitingPeriodHigherThanZero': 'overrideServiceWaitingPeriod has be to greater than 0.',
        'rule-regexp-error': 'Field must match regular expression pattern: {{0}}',
        'rule-length-error': 'Text must not be longer than {{0}}',
        'ClaimOverridePaymentInterestDaysEqualOrHigherThanZero': 'Claim overridePaymentInterestDays cannot be negative.',
        'MandatoryClaimURI': 'A claimLossIdentification link to Claim is mandatory.',
        'MandatoryClaimDiscountPercentage': 'discountPercentage is mandatory.',
        'ClaimOverridePaymentInterestAmountEqualOrHigherThanZero': 'Claim overridePaymentInterestAmount cannot be negative.',
        'MandatoryClaimProvider': 'Provider is mandatory when the isUnknownOrIntProvider flag is not true.',
        'MandatoryAlternatePayeeRegistryId': 'AlternatePayee registryId is required.',
        'AssertClaimOverideDenied': 'Claim isDenied cannot be used at the same time with isAllowed override.',
        'MandatoryClaimProviderFee': 'providerFee.fee is mandatory.',
        'DiscountAmountCannotBeNegative': 'discountAmount cannot be negative.',
        'MandatoryAuthorizationBy': 'authorizationBy is mandatory.',
        'MandatorySubmittedFee': 'Charges is required.',
        'MandatoryPolicyholderRegistryId': 'Policyholder registryId is required.',
        'AssertServiceOverideDenied': 'Service override isDenied cannot be used at the same time with isAllowed override.',
        'MandatoryClaimSource': 'Source for the claim is mandatory.',
        'MandatoryOrthodonticFrequency': 'Payment Frequency is required.',
        'MandatoryProcedureCode': 'Procedure Code is required.',
        'MandatoryDiagnosisQualifier': 'Diagnosis List Qualifier is required.',
        'SubmittedFeeCannotBeNegative': 'Charges cannot be a negative value.',
        'ServiceOverrideCopayAmountEqualOrHigherThanZero': 'overrideCopayAmount cannot be negative.',
        'MandatoryAlternatePayee': 'alternatePayee is mandatory.',
        'rule-assertion-error': 'Assertion failed',
        'number-set-min-max-step-error': 'Value must be in interval between {{0}} and {{1}} inclusively with increment {{2}}',
        'MandatoryPolicyholderRoleCd': 'Policyholder roleCd is required.',
        'ClaimOverrideMajorWaitingPeriodHigherThanZero': 'overrideMajorWaitingPeriod has to be greater than 0.',
        'PaidCannotBeNegative': 'cob.paid cannot be negative.',
        'number-set-max-step-error': 'Value must be {{0}} or smaller with decrement {{1}}',
        'DownPaymentCannotBeNegative': 'Down Payment cannot be a negative value.',
        'MandatoryClaimDiscountType': 'discountType is mandatory.',
        'ServiceOverrideCoinsurancePctEqualOrHigherThanZero': 'overrideCoinsurancePct cannot be negative.',
        'ServiceOverrideConsideredAmountEqualOrHigherThanZero': 'overrideConsideredAmount cannot be negative.',
        'ServiceOverrideServiceFrequencyLimitHigherThanZero': 'overrideServiceFrequencyLimit has to be greater than 0.',
        'AuthorizationPeriodValidation': 'authorizationPeriod.endDate must be later than authorizationPeriod.startDate.',
        'MandatoryClaimDiscountAmount': 'discountAmount is mandatory.',
        'MandatoryProcedureDateOfService': 'Date of Service is required.',
        'AllowedCannotBeNegative': 'cob.allowed cannot be negative.',
        'MandatoryActualOrPredeterminationActualServicesForClaim': 'Submitted Procedures Details are required.',
        'CapDentalPaymentPayeeDetailsEntity-001': 'payee is required.',
        'CleanClaimDateCannotBeBeforeReceivedDate': 'cleanClaimDate cannot be before the receivedDate.',
        'number-set-min-step-error': 'Value must be {{0}} or larger with increment {{1}}',
        'ServiceOverridePatientResponsibilityEqualOrHigherThanZero': 'overridePatientResponsibility cannot be negative.',
        'MandatoryClaimTransactionType': 'Claim Type is mandatory.',
        'value-list-error': 'Value must be one of: {{0}}',
        'rule-size-error': 'Invalid collection size',
        'MandatoryOneOrthoServiceForClaim': 'ortho details are required and only one Ortho service can be provided.',
        'rule-mandatory-error': 'Field is mandatory',
        'rule-size-range-error': 'Invalid collection size',
        'MandatoryHistoryCdtCoveredCd': 'cdtCoveredCd is mandatory.',
        'number-set-min-max-error': 'Value must be in interval between {{0}} and {{1}} inclusively',
        'ClaimOverrideLateEntrantWaitingPeriodHigherThanZero': 'Claim overrideLateEntrantWaitingPeriod has to be greater than 0.',
        'DateOfServiceCannotBeInFuture': 'Date of Service cannot be future date.',
        'ServiceOverridePaymentInterestAmountEqualOrHigherThanZero': 'Service overridePaymentInterestAmount cannot be negative.',
        'MandatoryProviderRegistryId': 'Provider registryId is required.',
        'number-set-min-error': 'Value must be {{0}} or larger',
        'MandatoryClaimPartyRoleCd': 'Roles is required.',
        'ServiceOverrideLateEntrantWaitingPeriodHigherThanZero': 'Service overrideLateEntrantWaitingPeriod has to be greater than 0.',
        'DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet': 'dateOfService cannot be after the cleanClaimDate.',
        'MandatoryProviderRoleCd': 'Provider roleCd is required.',
        'MandatoryAlternatePayeeRoleCd': 'AlternatePayee roleCd is required.',
        'MandatoryAuthorizationPeriod': 'authorizationPeriod is mandatory.',
        'MandatoryProcedureQuantity': 'Quantity is required.',
        'ServiceOverrideDeductibleEqualOrHigherThanZero': 'overrideDeductible cannot be negative.',
        'ClaimOverrideBasicWaitingPeriodHigherThanZero': 'overrideBasicWaitingPeriod has to be greater than 0.',
        'MandatoryClaimPolicyId': 'Policy is mandatory.',
        'CapDentalPaymentEntity-001': 'originSource is required.',
        'CapDentalPaymentEntity-002': 'paymentNetAmount is required.',
        'ClaimOverrideGracePeriodEqualOrHigherThanZero': 'Claim overrideGracePeriod cannot be negative.'
   }
}