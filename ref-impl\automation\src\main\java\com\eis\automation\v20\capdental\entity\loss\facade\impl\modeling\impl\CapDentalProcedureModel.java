/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.databinding.serializer.annotation.JsonMonetarySerializeFormat;
import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.*;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalProcedureModel extends TypeModel implements ICapDentalProcedureModel {

    private String toothSystem;
    private String procedureType;
    private Integer quantity;
    private String procedureCode;
    private String description;
    private String preauthorizationNumber;
    private String toothArea;
    private List<String> surfaces;
    private List<String> toothCodes;
    private Money submittedFee;
    private Boolean predetInd;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate dateOfService;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate priorProsthesisPlacementDate;
    private ICapDentalPreauthorizationModel preauthorization;
    private ICapDentalOrthodonticModel ortho;
    private ICapDentalTreatmentReasonModel treatmentReason;
    private List<ICapDentalDiagnosisCodeModel> diagnosisCodes;
    private ICapDentalProcedureCoordinationOfBenefitsModel cob;

    public String getToothSystem() {
        return toothSystem;
    }

    public void setToothSystem(String toothSystem) {
        this.toothSystem = toothSystem;
    }

    public String getProcedureType() {
        return procedureType;
    }

    public void setProcedureType(String procedureType) {
        this.procedureType = procedureType;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getProcedureCode() {
        return procedureCode;
    }

    public void setProcedureCode(String procedureCode) {
        this.procedureCode = procedureCode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPreauthorizationNumber() {
        return preauthorizationNumber;
    }

    public void setPreauthorizationNumber(String preauthorizationNumber) {
        this.preauthorizationNumber = preauthorizationNumber;
    }

    public String getToothArea() {
        return toothArea;
    }

    public void setToothArea(String toothArea) {
        this.toothArea = toothArea;
    }

    public List<String> getSurfaces() {
        return surfaces;
    }

    public void setSurfaces(List<String> surfaces) {
        this.surfaces = surfaces;
    }

    public List<String> getToothCodes() {
        return toothCodes;
    }

    public void setToothCodes(List<String> toothCodes) {
        this.toothCodes = toothCodes;
    }

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getSubmittedFee() {
        return submittedFee;
    }

    public void setSubmittedFee(Money submittedFee) {
        this.submittedFee = submittedFee;
    }

    public Boolean getPredetInd() {
        return predetInd;
    }

    public void setPredetInd(Boolean predetInd) {
        this.predetInd = predetInd;
    }

    public LocalDate getDateOfService() {
        return dateOfService;
    }

    public void setDateOfService(LocalDate dateOfService) {
        this.dateOfService = dateOfService;
    }

    public LocalDate getPriorProsthesisPlacementDate() {
        return priorProsthesisPlacementDate;
    }

    public void setPriorProsthesisPlacementDate(LocalDate priorProsthesisPlacementDate) {
        this.priorProsthesisPlacementDate = priorProsthesisPlacementDate;
    }

    @JsonSerialize(as = CapDentalPreauthorizationModel.class)
    public ICapDentalPreauthorizationModel getPreauthorization() {
        return preauthorization;
    }

    @JsonDeserialize(as = CapDentalPreauthorizationModel.class)
    public void setPreauthorization(ICapDentalPreauthorizationModel preauthorization) {
        this.preauthorization = preauthorization;
    }

    @JsonSerialize(as = CapDentalOrthodonticModel.class)
    public ICapDentalOrthodonticModel getOrtho() {
        return ortho;
    }

    @JsonDeserialize(as = CapDentalOrthodonticModel.class)
    public void setOrtho(ICapDentalOrthodonticModel ortho) {
        this.ortho = ortho;
    }

    @JsonSerialize(as = CapDentalTreatmentReasonModel.class)
    public ICapDentalTreatmentReasonModel getTreatmentReason() {
        return treatmentReason;
    }

    @JsonDeserialize(as = CapDentalTreatmentReasonModel.class)
    public void setTreatmentReason(ICapDentalTreatmentReasonModel treatmentReason) {
        this.treatmentReason = treatmentReason;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalDiagnosisCodeModel.class)
    public List<ICapDentalDiagnosisCodeModel> getDiagnosisCodes() {
        return diagnosisCodes;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalDiagnosisCodeModel.class)
    public void setDiagnosisCodes(List<ICapDentalDiagnosisCodeModel> diagnosisCodes) {
        this.diagnosisCodes = diagnosisCodes;
    }

    @JsonSerialize(as = CapDentalProcedureCoordinationOfBenefitsModel.class)
    public ICapDentalProcedureCoordinationOfBenefitsModel getCob() {
        return cob;
    }

    @JsonDeserialize(as = CapDentalProcedureCoordinationOfBenefitsModel.class)
    public void setCob(ICapDentalProcedureCoordinationOfBenefitsModel cob) {
        this.cob = cob;
    }
}
