/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input;

import com.eisgroup.genesis.cap.financial.command.template.input.CapPaymentTemplateInitInput;
import com.eisgroup.genesis.common.Required;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetailsTemplate;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Optional;

/**
 * {@link com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity} init request input
 *
 * <AUTHOR>
 * @since 22.2
 */
public class CapDentalPaymentTemplateInitInput extends CapPaymentTemplateInitInput {

    private static final String ORIGIN_SOURCE = "originSource";

    public CapDentalPaymentTemplateInitInput(JsonObject original) {
        super(original);
    }

    public CapDentalPaymentTemplateInitInput(CapPaymentDetailsTemplate details, EntityLink<RootEntity> originSource) {
        super(details);
        setChildObject(ORIGIN_SOURCE, originSource);
    }

    @Required
    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(originSource -> new EntityLink<>(RootEntity.class, originSource))
                .orElse(null);
    }
}
