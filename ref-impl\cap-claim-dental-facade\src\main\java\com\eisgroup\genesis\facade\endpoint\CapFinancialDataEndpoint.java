/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade.endpoint;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.commands.validator.errors.ValidationErrorException;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.facade.module.EndpointPackage;
import com.eisgroup.genesis.facade.payload.Endpoint;
import com.eisgroup.genesis.facade.request.HttpOperationType;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity;
import com.eisgroup.genesis.factory.model.dentalinternal.CapDentalFinancialDataInput;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Get financial data endpoint
 *
 * <AUTHOR>
 * @since 22.2
 */
public class CapFinancialDataEndpoint implements EndpointPackage {

    private static final ModelRepository<TransformationModel> MODEL_REPOSITORY = ModelRepositoryFactory.getRepositoryFor(TransformationModel.class);

    @Autowired
    private ModeledTransformationService transformationService;

    @Endpoint(path = "/", operationType = HttpOperationType.POST)
    public Lazy<CapDentalPaymentTemplateEntity> getFinancialData(CapFinancialDataRequest request) {
        return validateRequest(request)
                .flatMap(r -> transformationService.transform(MODEL_REPOSITORY.getActiveModel("CapTemplateFinancialData"), constructInput(r)));
    }

    @Override
    public String getName() {
        return "financialData";
    }

    private CapDentalFinancialDataInput constructInput(CapFinancialDataRequest request) {
        CapDentalFinancialDataInput result = (CapDentalFinancialDataInput) ModelInstanceFactory.createInstance("DentalInternal",
                "1", "CapDentalFinancialDataInput");
        result.setOriginSource(request.getOriginSource());
        result.setSettlements(request.getSettlements());
        return result;
    }

    private Lazy<CapFinancialDataRequest> validateRequest(CapFinancialDataRequest request) {
        if (request.getOriginSource() == null) {
            return Lazy.error(() -> new ValidationErrorException(FinancialDataErrorDefinition.ORIGIN_SOURCE_REQUIRED
                    .builder().build()));
        }

        if (request.getSettlements() == null || request.getSettlements().isEmpty()) {
            return Lazy.error(() -> new ValidationErrorException(FinancialDataErrorDefinition.SETTLEMENTS_REQUIRED
                    .builder().build()));
        }
        return Lazy.of(request);
    }

    private static class FinancialDataErrorDefinition extends BaseErrorDefinition {

        public static final FinancialDataErrorDefinition ORIGIN_SOURCE_REQUIRED = new FinancialDataErrorDefinition("finData001",
                "originSource is required");
        public static final FinancialDataErrorDefinition SETTLEMENTS_REQUIRED = new FinancialDataErrorDefinition("finData002",
                "settlements are required");

        protected FinancialDataErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
