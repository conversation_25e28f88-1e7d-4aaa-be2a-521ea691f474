/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.loss.facade.ICapDentalLoss;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.impl.CustomerModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IProviderModel;
import com.exigen.istf.data.TestData;

public interface ICapDentalLossService extends ITestDataService, IFacadeService<ICapDentalLoss> {

    ICapDentalLossModel initDentalLoss(String policyId);

    ICapDentalLossModel initDentalLoss(ICapDentalLossModel dentalLossModel);

    ICapDentalLossModel createDentalLossModel(String policyId);

    ICapDentalLossModel createDentalLossModel(String policyId, CustomerModel customerModel, IProviderModel providerModel);

    ICapDentalLossModel createDentalLossModel(TestData td, String policyId, CustomerModel customerModel, IProviderModel providerModel);

    ICapDentalLossModel submitDentalLoss(ICapDentalLossModel initDentalLossModel);

    ICapDentalLossModel loadDentalLoss(ICapDentalLossModel dentalLossModel);

    ICapDentalLossModel loadDentalLoss(ICapDentalLossModel dentalLossModel, String state);

    ICapDentalLossModel createUpdateDentalLossModel(ICapDentalLossModel dentalLossModel);
}
