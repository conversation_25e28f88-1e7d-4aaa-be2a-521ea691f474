$ cross-env NODE_OPTIONS=--max-old-space-size=10240 APP_BASE_URL=/dental infra-scripts build-fast
Start building...
Compiled with warnings.
[7m    at HarmonyImportSpecifierDependency.getLinkingErrors (D:\ProgramData\ms-claim-benefits-dental\ui\node_modules\webpack\lib\dependencies\HarmonyImportDependency.js:231:7)[27m
ModuleDependencyWarning: Should not import the named export 'version' (imported as 'version') from default-exporting module (only default export is available soon)
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(24891:3) from "autoprefixer" plugin: end value has mixed support, consider using flex-end instead

Code:
  justify-content: end
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(24899:3) from "autoprefixer" plugin: end value has mixed support, consider using flex-end instead

Code:
  justify-content: end
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(34122:13805) from "autoprefixer" plugin: start value has mixed support, consider using flex-start instead

Code:
  align-content:start
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(34122:39082) from "autoprefixer" plugin: start value has mixed support, consider using flex-start instead

Code:
  align-content:start
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(34122:168753) from "autoprefixer" plugin: start value has mixed support, consider using flex-start instead

Code:
  align-content:start
[7mModuleWarning: Module Warning (from ../../node_modules/postcss-loader/dist/cjs.js):[27m
(34122:248615) from "autoprefixer" plugin: start value has mixed support, consider using flex-start instead

Code:
  align-content:start
[7mAssetsOverSizeLimitWarning: asset size limit: The following asset(s) exceed the recommended size limit (244 KiB).[27m
This can impact web performance.
Assets: 
  fontawesome-webfontc1e38fd9e0e74ba58f7a..svg (434 KiB)
  main.css (1.6 MiB)
  main.js (5.2 MiB)
  622.js (7.87 MiB)
  929.js (22.4 MiB)
  339.js (3.3 MiB)
  526.js (4.56 MiB)
[7mEntrypointsOverSizeLimitWarning: entrypoint size limit: The following entrypoint(s) combined asset size exceeds the recommended limit (244 KiB). This can impact web performance.[27m
Entrypoints:
  main (6.8 MiB)
      main.css
      main.js
