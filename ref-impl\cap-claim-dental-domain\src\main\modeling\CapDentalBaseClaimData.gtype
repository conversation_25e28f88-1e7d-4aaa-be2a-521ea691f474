BaseType CapDentalBaseClaimData {

    @Description("Clean Claim Date.")
    Attr cleanClaimDate: Date

    @CapLossDate
    @Description("Initial Date Of Service.")
    Attr initialDateOfService: Datetime

    Attr cob: *CapDentalClaimCoordinationOfBenefitsEntity

    @Description("Deprected this should come from customer.")
    Attr dateOfBirth: Date

    @Description("Digital Image Numbers.")
    Attr digitalImageNumbers: *String

    @Description("Flag to identify if the provider is unknown or international.")
    Attr isUnknownOrIntProvider: Bo<PERSON>an

    @Description("Missing Tooth.")
    Attr missingTooths: *String

    @Description("Payee Type.")
    @Lookup("CapDNPayeeType")
    Attr payeeType: String

    @Description("Place of Treatment.")
    @Lookup("CapDNPlaceOfTreatment")
    Attr placeOfTreatment: String


    @KrakenChildContext
    @KrakenField
    Attr providerDiscount: CapDentalProviderDiscountEntity

    @KrakenChildContext
    @KrakenField
    Attr providerFees: *CapDentalProviderFeesEntity

    @Description("Received Date.")
    Attr receivedDate: Date

    @Description("Comments and Remarks.")
    Attr remark: String

    @Description("Source EDI NONEDI.")
    @Lookup("CapDNClaimSource")
    Attr source: String

    @Description("Type of Claim.")
    @Lookup("CapDNTransactionType")
    Attr transactionType: String

    Attr treatmentReason: CapDentalTreatmentReasonEntity

    @Description("Contains Alternate Payee role information")
    @KrakenChildContext
    @KrakenField
    Attr alternatePayeeRole: CapAlternatePayeeRole

    @Description("Contains patient role information")
    @CapSubject("${registryId}")
    @KrakenChildContext
    @KrakenField
    Attr patientRole: CapPatientRole

    @Description("Contains Policy Holder role information")
    @KrakenChildContext
    @KrakenField
    Attr policyholderRole: CapPolicyholderRole

    @Description("Contains provider role information")
    @KrakenChildContext
    @KrakenField
    Attr providerRole: CapProviderRole
}
