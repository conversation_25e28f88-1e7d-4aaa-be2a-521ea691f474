/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.common.modeling.impl.TermModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ITermModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimCoordinationOfBenefitsModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDate;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalClaimCoordinationOfBenefitsModel extends TypeModel implements ICapDentalClaimCoordinationOfBenefitsModel {

    private ITermModel period;
    private String address;
    private String otherPolicyType;
    private String PolicyNumber;
    private String otherInsuranceCompany;
    private String policyholderFirstName;
    private String typeOfCob;
    private String otherCoverageType;
    private String policyholderRelationshipToPatient;
    private String policyholderGender;
    private String policyholderLastName;
    private String plan;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate policyholderDateOfBirth;

    @JsonSerialize(as = TermModel.class)
    public ITermModel getPeriod() {
        return period;
    }

    @JsonDeserialize(as = TermModel.class)
    public void setPeriod(ITermModel period) {
        this.period = period;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getOtherPolicyType() {
        return otherPolicyType;
    }

    public void setOtherPolicyType(String otherPolicyType) {
        this.otherPolicyType = otherPolicyType;
    }

    public String getPolicyNumber() {
        return PolicyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        PolicyNumber = policyNumber;
    }

    public String getOtherInsuranceCompany() {
        return otherInsuranceCompany;
    }

    public void setOtherInsuranceCompany(String otherInsuranceCompany) {
        this.otherInsuranceCompany = otherInsuranceCompany;
    }

    public String getPolicyholderFirstName() {
        return policyholderFirstName;
    }

    public void setPolicyholderFirstName(String policyholderFirstName) {
        this.policyholderFirstName = policyholderFirstName;
    }

    public String getTypeOfCob() {
        return typeOfCob;
    }

    public void setTypeOfCob(String typeOfCob) {
        this.typeOfCob = typeOfCob;
    }

    public String getOtherCoverageType() {
        return otherCoverageType;
    }

    public void setOtherCoverageType(String otherCoverageType) {
        this.otherCoverageType = otherCoverageType;
    }

    public String getPolicyholderRelationshipToPatient() {
        return policyholderRelationshipToPatient;
    }

    public void setPolicyholderRelationshipToPatient(String policyholderRelationshipToPatient) {
        this.policyholderRelationshipToPatient = policyholderRelationshipToPatient;
    }

    public String getPolicyholderGender() {
        return policyholderGender;
    }

    public void setPolicyholderGender(String policyholderGender) {
        this.policyholderGender = policyholderGender;
    }

    public String getPolicyholderLastName() {
        return policyholderLastName;
    }

    public void setPolicyholderLastName(String policyholderLastName) {
        this.policyholderLastName = policyholderLastName;
    }

    public String getPlan() {
        return plan;
    }

    public void setPlan(String plan) {
        this.plan = plan;
    }

    public LocalDate getPolicyholderDateOfBirth() {
        return policyholderDateOfBirth;
    }

    public void setPolicyholderDateOfBirth(LocalDate policyholderDateOfBirth) {
        this.policyholderDateOfBirth = policyholderDateOfBirth;
    }
}
