<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ms-claim-dental-applications-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-deployer-app</artifactId>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/banner.txt</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <descriptors>
                        <descriptor>src/assembly/workflow-resources.xml</descriptor>
                    </descriptors>
                    <outputDirectory>${project.build.directory}/classes/workflow</outputDirectory>
                    <finalName>cap-claim-dental</finalName>
                </configuration>
                <executions>
                    <execution>
                        <id>zip-workflow-resources</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- Fat-jar assembly -->
        <dependency>
            <groupId>com.eisgroup.genesis.apps</groupId>
            <artifactId>spring-app-bundle</artifactId>
            <classifier>app</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.apps</groupId>
            <artifactId>deployer-app-config</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-repository</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.confidential-data</artifactId>
            <type>tile</type>
            <classifier>deployer</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.security</groupId>
            <artifactId>security-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-lookups</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.lookups</groupId>
            <artifactId>lookups-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.search</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.unique-fields</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.query-fields</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.jobs</groupId>
            <artifactId>jobs-bundle</artifactId>
            <type>tile</type>
            <classifier>deployer</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.model</groupId>
            <artifactId>external-models-bundle</artifactId>
            <type>tile</type>
            <classifier>deployer</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.entity-lock</artifactId>
            <type>tile</type>
        </dependency>

        <!-- Libraries -->
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>column-store-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>search-index-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <classifier>producer</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <type>tile</type>
            <classifier>consumer</classifier>
        </dependency>

        <!-- Transformations -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-transformation-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <!-- versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-versioning-ms-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.loss</groupId>
            <artifactId>cap-loss-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.adjudication</groupId>
            <artifactId>cap-adjudication-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.versioning</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>
    </dependencies>
</project>
