Model CapDentalPaymentDefinition

Common {
    Number is output.paymentNumber
    Attribute paymentNumber is output.paymentNumber
}

//Variation payment
Command initPayment {
    Activity payment_initPayment with {
        Identified using ["CapDentalOverview", output._key.rootId]
    }

    Activity payment_initPayment
}

Command requestIssuePayment {
    Activity payment_requestIssuePayment with {
        Identified using ["CapDentalOverview", output._key.rootId]
    }

    Activity payment_requestIssuePayment
}

Command issuePayment {
    Activity payment_issuePayment with {
        Identified using ["CapDentalOverview", output._key.rootId]
    }

    Activity payment_issuePayment
}

Command cancelPayment {
    Activity payment_cancelPayment with {
        Identified using ["CapDentalOverview", output._key.rootId]
    }

    Activity payment_cancelPayment
}
