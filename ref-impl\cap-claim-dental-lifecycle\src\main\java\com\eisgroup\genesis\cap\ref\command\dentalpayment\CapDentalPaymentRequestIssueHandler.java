/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.common.repository.api.CapCommonRepository;
import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentRequestIssueHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for Request Issue for a {@link CapDentalPaymentEntity}.
 *
 * <AUTHOR>
 * @since 22.12
 */
public class CapDentalPaymentRequestIssueHandler extends CapPaymentRequestIssueHandler<IdentifierRequest, CapDentalPaymentEntity> {

    @Autowired
    private CapCommonRepository<CapDentalPaymentEntity> claimPaymentRepository;
    @Autowired
    private ModelResolver modelResolver;

    public CapDentalPaymentRequestIssueHandler() {
    }

    @Nonnull
    public CapDentalPaymentEntity load(@Nonnull IdentifierRequest input) {
        return this.claimPaymentRepository.load(input.getKey(), this.modelResolver.getModelName(), this.getVariation());
    }
}
