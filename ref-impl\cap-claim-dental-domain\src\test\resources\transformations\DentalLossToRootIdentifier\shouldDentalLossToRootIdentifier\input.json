{"_key": {"rootId": "067c0c29-f9ca-465f-8303-02653330336d", "revisionNo": 1}, "_modelName": "CapDentalLoss", "_modelType": "CapLoss", "_modelVersion": "1", "_timestamp": "2021-11-24T17:32:36.496Z", "_type": "CapDentalLossEntity", "lossNumber": "L1009", "policyId": "capMock://CapDentalUnverifiedPolicy/cf133357-061c-4913-9d0b-34a665c20137/1", "state": "Incomplete", "lossDetail": {"_key": {"rootId": "067c0c29-f9ca-465f-8303-02653330336d", "revisionNo": 1, "parentId": "067c0c29-f9ca-465f-8303-02653330336d", "id": "561f6457-1eed-3308-a914-7cd1613d108f"}, "_modelName": "CapDentalLoss", "_modelType": "CapLoss", "_modelVersion": "1", "_timestamp": "2021-11-24T17:32:36.497Z", "_type": "CapDentalDetailEntity", "claimData": {"payeeType": "vendor", "dateOfBirth": "2000-01-01", "source": "non-EDI", "transactionType": "service", "receivedDate": "2021-11-03", "patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"}, "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"}, "_type": "CapDentalClaimDataEntity", "_key": {"id": "03efe073-a36f-323b-9f99-789fc7734117", "rootId": "067c0c29-f9ca-465f-8303-02653330336d", "revisionNo": 1, "parentId": "561f6457-1eed-3308-a914-7cd1613d108f"}}, "lossDesc": "For notes", "submittedProcedures": [{"predetInd": false, "procedureCode": "D0330", "submittedFee": {"amount": 199.99, "currency": "USD"}, "dateOfService": "2021-07-12", "surfaces": ["B"], "toothArea": "A", "quantity": 1, "_type": "CapDentalProcedureEntity", "_key": {"id": "ae6ffe41-4b97-3b88-a9fc-91e7af7d80cc", "rootId": "067c0c29-f9ca-465f-8303-02653330336d", "revisionNo": 1, "parentId": "561f6457-1eed-3308-a914-7cd1613d108f"}}]}}