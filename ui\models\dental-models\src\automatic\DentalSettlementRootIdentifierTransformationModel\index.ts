// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"DentalSettlementRootIdentifierTransformationModel",
  "name":"DentalSettlementRootIdentifierTransformationModel",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapRootIdentifierEntity"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapRootIdentifierEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "modelTypes":[
                  ]
                }
              },
              "parents":[
              ],
              "typeName":"RootEntity"
            }
          ],
          "typeName":"CapRootIdentifier"
        }
      ],
      "baseTypes":[
        "CapRootIdentifier"
      ],
      "extLinks":{
        "links":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"links",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapRootIdentifierEntity",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace DentalSettlementRootIdentifierTransformationModel {
    export type Variations = never


    export class CapRootIdentifierEntity extends MAPI.BusinessEntity implements BusinessTypes.CapRootIdentifier, MAPI.RootBusinessType {
    constructor() { super(CapRootIdentifierEntity.name) }
        readonly _modelName: string = 'DentalSettlementRootIdentifierTransformationModel'
        readonly _modelType: string = 'DentalSettlementRootIdentifierTransformationModel'
        readonly _modelVersion?: string = '1'
        readonly links: MAPI.ExternalLink[] = []
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapRootIdentifierEntity, ()=> new CapRootIdentifierEntity)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}