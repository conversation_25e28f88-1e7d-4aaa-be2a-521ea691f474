/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalDHMOFrequencyModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalDHMOFrequencyModel extends TypeModel implements ICapDentalDHMOFrequencyModel {

    private String isAppliedTowardsMOOP;
    private String frequencyRange;
    private String frequencyPeriodType;
    private Integer frequencyPeriod;
    private Boolean isCovered;
    private String frequency;

    public String getIsAppliedTowardsMOOP() {
        return isAppliedTowardsMOOP;
    }

    public void setIsAppliedTowardsMOOP(String isAppliedTowardsMOOP) {
        this.isAppliedTowardsMOOP = isAppliedTowardsMOOP;
    }

    public String getFrequencyRange() {
        return frequencyRange;
    }

    public void setFrequencyRange(String frequencyRange) {
        this.frequencyRange = frequencyRange;
    }

    public String getFrequencyPeriodType() {
        return frequencyPeriodType;
    }

    public void setFrequencyPeriodType(String frequencyPeriodType) {
        this.frequencyPeriodType = frequencyPeriodType;
    }

    public Integer getFrequencyPeriod() {
        return frequencyPeriod;
    }

    public void setFrequencyPeriod(Integer frequencyPeriod) {
        this.frequencyPeriod = frequencyPeriod;
    }

    public Boolean getIsCovered() {
        return isCovered;
    }

    public void setIsCovered(Boolean isCovered) {
        this.isCovered = isCovered;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }
}
