/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.cap.loss.repository.ClaimLossRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.model.ModelResolver;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Command handler for Dental Loss suspend action
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossSuspendHandler implements ProductCommandHandler<IdentifierRequest, CapDentalLossEntity> {

    @Autowired
    private ClaimLossRepository<CapDentalLossEntity> claimLossRepository;

    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapDentalLossEntity load(@Nonnull IdentifierRequest identifierRequest) {
        return claimLossRepository.load(identifierRequest.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public CapDentalLossEntity execute(@Nonnull IdentifierRequest identifierRequest, @Nonnull CapDentalLossEntity entity) {
        return entity;
    }

    @Nonnull
    @Override
    public CapDentalLossEntity save(@Nonnull IdentifierRequest identifierRequest, @Nonnull CapDentalLossEntity entity) {
        return claimLossRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return CapDentalLossCommands.SUSPEND_LOSS;
    }
}
