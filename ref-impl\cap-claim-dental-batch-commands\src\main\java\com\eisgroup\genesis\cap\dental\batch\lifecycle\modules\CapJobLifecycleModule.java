/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.lifecycle.modules;

import com.eisgroup.genesis.cap.dental.batch.jobs.CapDentalPaymentGenerationJob;
import com.eisgroup.genesis.cap.dental.batch.jobs.CapDentalPaymentIssueJob;
import com.eisgroup.genesis.cap.jobs.reindex.CapReindexJob;
import com.eisgroup.genesis.cap.jobs.reindex.step.CapReindexJobStep;
import com.eisgroup.genesis.jobs.lifecycle.api.module.AbstractJobLifecycleModule;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.google.common.collect.ImmutableMap;

import java.util.Map;

import static com.eisgroup.genesis.cap.dental.batch.commands.CapJobCommands.PAYMENT_GENERATION_JOB;
import static com.eisgroup.genesis.cap.dental.batch.commands.CapJobCommands.PAYMENT_ISSUE_JOB;

/**
 * CAP job lifecycle module configuration
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapJobLifecycleModule extends AbstractJobLifecycleModule {

    private static final String NAME = "capDentalFinancialJobs";

    @Override
    public Map<String, CommandHandler<?, ?>> getBatchCommandHandlers() {
        return ImmutableMap.<String, CommandHandler<?, ?>>builder()
                .put(PAYMENT_GENERATION_JOB, new CapDentalPaymentGenerationJob())
                .put(PAYMENT_ISSUE_JOB, new CapDentalPaymentIssueJob())
                .put(CapReindexJob.NAME, new CapReindexJob())
                .put(CapReindexJobStep.NAME, new CapReindexJobStep())
                .build();
    }

    @Override
    public String getModelName() {
        return NAME;
    }
}
