<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="unsuspendLossPaymentSchedules" name="Call unsuspendLossPaymentSchedules" isExecutable="true">
    <startEvent id="startUnsuspendLossPaymentTemplates" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="unsuspendLossSchedules" name="unsuspendLossPaymentSchedules" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="unsuspendLossPaymentSchedules" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'originSource':{'_uri': '${_uri}'}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="unsuspendedTemplates"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-262FEB3F-C730-44BE-90F0-B61AA2225024" sourceRef="startUnsuspendLossPaymentTemplates" targetRef="unsuspendLossSchedules"></sequenceFlow>
    <endEvent id="end"></endEvent>
    <sequenceFlow id="sid-5A6B7451-B608-476B-8D0C-6F504E761AB0" sourceRef="unsuspendLossSchedules" targetRef="end"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_unsuspendLossPaymentSchedules">
    <bpmndi:BPMNPlane bpmnElement="unsuspendLossPaymentSchedules" id="BPMNPlane_unsuspendLossPaymentSchedules">
      <bpmndi:BPMNShape bpmnElement="startUnsuspendLossPaymentTemplates" id="BPMNShape_startUnsuspendLossPaymentTemplates">
        <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="unsuspendLossSchedules" id="BPMNShape_unsuspendLossSchedules">
        <omgdc:Bounds height="80.0" width="100.0" x="375.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="705.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-5A6B7451-B608-476B-8D0C-6F504E761AB0" id="BPMNEdge_sid-5A6B7451-B608-476B-8D0C-6F504E761AB0" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="474.95000000000005" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="705.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-262FEB3F-C730-44BE-90F0-B61AA2225024" id="BPMNEdge_sid-262FEB3F-C730-44BE-90F0-B61AA2225024" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="129.94999980975598" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="374.9999999999317" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>