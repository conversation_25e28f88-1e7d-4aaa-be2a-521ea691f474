/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalClaimInfoModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalPatientModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementInfoProcedureModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalClaimInfoModel extends TypeModel implements ICapDentalClaimInfoModel {

    private String source;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate receivedDate;
    private ICapDentalClaimDataModel claimData;
    private List<ICapDentalSettlementInfoProcedureModel> submittedProcedures;
    private ICapDentalPatientModel patient;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public LocalDate getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDate receivedDate) {
        this.receivedDate = receivedDate;
    }

    @JsonSerialize(as = CapDentalClaimDataModel.class)
    public ICapDentalClaimDataModel getClaimData() {
        return claimData;
    }

    @JsonDeserialize(as = CapDentalClaimDataModel.class)
    public void setClaimData(ICapDentalClaimDataModel claimData) {
        this.claimData = claimData;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalSettlementInfoProcedureModel.class)
    public List<ICapDentalSettlementInfoProcedureModel> getSubmittedProcedures() {
        return submittedProcedures;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalSettlementInfoProcedureModel.class)
    public void setSubmittedProcedures(List<ICapDentalSettlementInfoProcedureModel> submittedProcedures) {
        this.submittedProcedures = submittedProcedures;
    }

    @JsonSerialize(as = CapDentalPatientModel.class)
    public ICapDentalPatientModel getPatient() {
        return patient;
    }

    @JsonDeserialize(as = CapDentalPatientModel.class)
    public void setPatient(ICapDentalPatientModel patient) {
        this.patient = patient;
    }
}
