/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalrecovery;

import javax.annotation.Nonnull;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.recovery.CapRecoveryUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.input.CapDentalRecoveryUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;

/**
 * Command handler for recovery update.
 *
 * <AUTHOR>
 * @since 22.14
 */
@Modifying
public class CapDentalRecoveryUpdateHandler extends CapRecoveryUpdateHandler<CapDentalRecoveryUpdateInput, CapDentalPaymentEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalRecoveryUpdateInput input, @Nonnull CapDentalPaymentEntity entity) {
        return capValidatorRegistry.validateRequest(input);
    }

//    @Override
//    protected CapPayment populateClaimPaymentAttributes(@Nonnull ClaimPaymentUpdateInput input, @Nonnull CapPayment entity) {
//        CapDentalPaymentEntity payment = (CapDentalPaymentEntity) super.populateClaimPaymentAttributes(input, entity);
//        payment.setPaymentNetAmount(((CapDentalRecoveryUpdateInput) input).getPaymentNetAmount());
//        return payment;
//    }
}
