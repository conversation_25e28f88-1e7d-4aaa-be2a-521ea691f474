{"entity": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 100, "currency": "USD"}, "dateOfService": "2025-04-01", "surfaces": ["B"], "toothArea": "01", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity"}, "_type": "CapDentalProcedureEntity"}, {"procedureCode": "D0120", "submittedFee": {"amount": 50, "currency": "USD"}, "dateOfService": "2025-03-01", "surfaces": ["B"], "toothArea": "01", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity"}, "_type": "CapDentalProcedureEntity"}], "claimData": {"patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//{{indvCustomerId}}"}, "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//{{indvCustomerId}}"}, "providerRole": {"_type": "CapProviderRole", "roleCd": ["IndividualProvider"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//{{indvCustomerId}}", "providerLink": "geroot://Provider/IndividualProvider//96dab7d2-12d0-3467-aa74-571a90f245e1"}, "alternatePayeeRole": {"_type": "CapAlternatePayeeRole", "roleCd": ["<PERSON><PERSON><PERSON><PERSON>"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//{{indvCustomerId}}"}, "payeeType": "PrimaryInsured", "dateOfBirth": "2005-04-02", "source": "NONEDI", "transactionType": "ActualServices", "receivedDate": "2025-04-02", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "{{dentalUnverifyPolicyVersionId}}"}