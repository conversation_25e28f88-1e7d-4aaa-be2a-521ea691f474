/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf;

import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;

import java.time.LocalDate;
import java.util.List;

public interface ICapDentalClaimDataModel extends ITypeModel {

    List<ICapDentalProviderFeesModel> getProviderFees();

    void setProviderFees(List<ICapDentalProviderFeesModel> providerFees);

    List<String> getDigitalImageNumbers();

    String getPayeeType();

    UriModel getPayee();

    void setPayeeType(String payeeType);

    UriModel  getAlternatePayee();

    void setAlternatePayee(UriModel alternatePayee);

    String getRemark();

    String getSource();

    void setSource(String source);

    LocalDate getCleanClaimDate();

    LocalDate getDateOfBirth();

    LocalDate getReceivedDate();

    String getTransactionType();

    void setTransactionType(String transactionType);

    void setReceivedDate(LocalDate receivedDate);

    String getPlaceOfTreatment();

    List<String> getMissingTooths();

    ICapDentalPartyRoleModel getPatientRole();

    void setPolicyholderRole(ICapDentalPartyRoleModel policyholderRole);

    ICapDentalPartyRoleModel getProviderRole();

    void setProviderRole(ICapDentalPartyRoleModel providerRole);

    ICapDentalPartyRoleModel getPolicyholderRole();

    void setPatientRole(ICapDentalPartyRoleModel patientRole);

    ICapDentalProviderDiscountModel getProviderDiscount();

    List<ICapDentalClaimCoordinationOfBenefitsModel> getCob();
}
