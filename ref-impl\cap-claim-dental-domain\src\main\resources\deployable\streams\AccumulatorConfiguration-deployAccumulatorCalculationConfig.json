[{"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Annual_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_IndividualDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Annual_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_IndividualDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Annual_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_IndividualDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Annual_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_IndividualDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Annual_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_IndividualDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Lifetime_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_IndividualDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Lifetime_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_IndividualDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Lifetime_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_IndividualDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Lifetime_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_IndividualDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualDeductible_Lifetime_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_IndividualDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Annual_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_FamilyDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Annual_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_FamilyDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Annual_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_FamilyDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Annual_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_FamilyDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Annual_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_FamilyDeductible_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Lifetime_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_FamilyDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Lifetime_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_FamilyDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Lifetime_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_FamilyDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Lifetime_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_FamilyDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "FamilyDeductible_Lifetime_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_FamilyDeductible_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Annual_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_IndividualMaximum_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Annual_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_IndividualMaximum_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Annual_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_IndividualMaximum_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Annual_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_IndividualMaximum_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Annual_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_IndividualMaximum_Annual_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Lifetime_Dental", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_IndividualMaximum_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Lifetime_Dental", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_IndividualMaximum_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Lifetime_Dental", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_IndividualMaximum_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Lifetime_Dental", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_IndividualMaximum_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "IndividualMaximum_Lifetime_Dental", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_IndividualMaximum_Lifetime_Dental"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Annual_Implants", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_ImplantsMaximum_Annual_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Annual_Implants", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_ImplantsMaximum_Annual_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Annual_Implants", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_ImplantsMaximum_Annual_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Annual_Implants", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_ImplantsMaximum_Annual_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Annual_Implants", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_ImplantsMaximum_Annual_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Lifetime_Implants", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_ImplantsMaximum_Lifetime_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Lifetime_Implants", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_ImplantsMaximum_Lifetime_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Lifetime_Implants", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_ImplantsMaximum_Lifetime_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Lifetime_Implants", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_ImplantsMaximum_Lifetime_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "ImplantsMaximum_Lifetime_Implants", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_ImplantsMaximum_Lifetime_Implants"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Annual_Orthodontics", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_OrthoDeductible_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Annual_Orthodontics", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_OrthoDeductible_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Annual_Orthodontics", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_OrthoDeductible_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Annual_Orthodontics", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_OrthoDeductible_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Annual_Orthodontics", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_OrthoDeductible_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Lifetime_Orthodontics", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_OrthoDeductible_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Lifetime_Orthodontics", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_OrthoDeductible_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Lifetime_Orthodontics", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_OrthoDeductible_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Lifetime_Orthodontics", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_OrthoDeductible_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoDeductible_Lifetime_Orthodontics", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_OrthoDeductible_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Annual_Orthodontics", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_OrthoMaximum_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Annual_Orthodontics", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_OrthoMaximum_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Annual_Orthodontics", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_OrthoMaximum_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Annual_Orthodontics", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_OrthoMaximum_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Annual_Orthodontics", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_OrthoMaximum_Annual_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Lifetime_Orthodontics", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Lifetime_Orthodontics", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_OrthoMaximum_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Lifetime_Orthodontics", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_OrthoMaximum_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Lifetime_Orthodontics", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_OrthoMaximum_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "OrthoMaximum_Lifetime_Orthodontics", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_OrthoMaximum_Lifetime_Orthodontics"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Annual_TMJ", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_TMJDeductible_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Annual_TMJ", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_TMJDeductible_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Annual_TMJ", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_TMJDeductible_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Annual_TMJ", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_TMJDeductible_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Annual_TMJ", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_TMJDeductible_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Lifetime_TMJ", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_TMJDeductible_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Lifetime_TMJ", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_TMJDeductible_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Lifetime_TMJ", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_TMJDeductible_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Lifetime_TMJ", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_TMJDeductible_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJDeductible_Lifetime_TMJ", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_TMJDeductible_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Annual_TMJ", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_TMJMaximum_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Annual_TMJ", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_TMJMaximum_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Annual_TMJ", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_TMJMaximum_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Annual_TMJ", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_TMJMaximum_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Annual_TMJ", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_TMJMaximum_Annual_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Lifetime_TMJ", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_TMJMaximum_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Lifetime_TMJ", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_TMJMaximum_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Lifetime_TMJ", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_TMJMaximum_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Lifetime_TMJ", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_TMJMaximum_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "TMJMaximum_Lifetime_TMJ", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_TMJMaximum_Lifetime_TMJ"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Annual_CosmeticServices", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_CosmeticDeductible_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Annual_CosmeticServices", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_CosmeticDeductible_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Annual_CosmeticServices", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_CosmeticDeductible_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Annual_CosmeticServices", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_CosmeticDeductible_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Annual_CosmeticServices", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_CosmeticDeductible_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Lifetime_CosmeticServices", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_CosmeticDeductible_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Lifetime_CosmeticServices", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_CosmeticDeductible_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Lifetime_CosmeticServices", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_CosmeticDeductible_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Lifetime_CosmeticServices", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_CosmeticDeductible_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticDeductible_Lifetime_CosmeticServices", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_CosmeticDeductible_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Annual_CosmeticServices", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_CosmeticMaximum_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Annual_CosmeticServices", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_CosmeticMaximum_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Annual_CosmeticServices", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_CosmeticMaximum_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Annual_CosmeticServices", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_CosmeticMaximum_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Annual_CosmeticServices", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_CosmeticMaximum_Annual_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Lifetime_CosmeticServices", "accumulatorAction": "create"}]}, "key": {"transactionType": "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Lifetime_CosmeticServices", "accumulatorAction": "reserve"}]}, "key": {"transactionType": "DentalSettlement_CosmeticMaximum_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Lifetime_CosmeticServices", "accumulatorAction": "use"}]}, "key": {"transactionType": "DentalPayment_CosmeticMaximum_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Lifetime_CosmeticServices", "accumulatorAction": "recover"}]}, "key": {"transactionType": "DentalPaymentRecover_CosmeticMaximum_Lifetime_CosmeticServices"}}, {"configuration": {"actions": [{"accumulatorType": "CosmeticMaximum_Lifetime_CosmeticServices", "accumulatorAction": "adjust"}]}, "key": {"transactionType": "ManualAmountAdjustment_CosmeticMaximum_Lifetime_CosmeticServices"}}]