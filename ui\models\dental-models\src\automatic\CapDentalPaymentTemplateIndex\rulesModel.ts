// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALPAYMENTTEMPLATEINDEX } from "./kraken_model_tree_CapDentalPaymentTemplateIndex"

let name = "CapDentalPaymentTemplateIndex"

let namespace = "CapDentalPaymentTemplateIndex"

let currencyCd = "USD"

export type CapDentalPaymentTemplateIndexEntryPointName = never

let entryPointNames = [
] as CapDentalPaymentTemplateIndexEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALPAYMENTTEMPLATEINDEX as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalPaymentTemplateIndex = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
