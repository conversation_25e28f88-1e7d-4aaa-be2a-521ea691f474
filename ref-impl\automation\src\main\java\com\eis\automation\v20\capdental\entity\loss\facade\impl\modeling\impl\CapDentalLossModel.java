/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.impl.CapPolicyInfoModel;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.interf.ICapPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalDetailModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalLossModel extends TypeModel implements ICapDentalLossModel {

    private String policyId;
    private ICapDentalDetailModel entity;
    private ICapDentalDetailModel lossDetail;
    private String state;
    private String lossNumber;
    private String lossSubStatusCd;
    private String reasonCd;
    private String reasonDescription;
    private ICapPolicyInfoModel policy;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    @JsonProperty(value = "_timestamp")
    private LocalDateTime timestamp;

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    @JsonSerialize(as = CapDentalDetailModel.class)
    public ICapDentalDetailModel getEntity() {
        return entity;
    }

    @JsonDeserialize(as = CapDentalDetailModel.class)
    public void setEntity(ICapDentalDetailModel entity) {
        this.entity = entity;
    }

    @JsonSerialize(as = CapDentalDetailModel.class)
    public ICapDentalDetailModel getLossDetail() {
        return lossDetail;
    }

    @JsonDeserialize(as = CapDentalDetailModel.class)
    public void setLossDetail(ICapDentalDetailModel lossDetail) {
        this.lossDetail = lossDetail;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getLossNumber() {
        return lossNumber;
    }

    public void setLossNumber(String lossNumber) {
        this.lossNumber = lossNumber;
    }

    public String getLossSubStatusCd() {
        return lossSubStatusCd;
    }

    public void setLossSubStatusCd(String lossSubStatusCd) {
        this.lossSubStatusCd = lossSubStatusCd;
    }

    public String getReasonCd() {
        return reasonCd;
    }

    public void setReasonCd(String reasonCd) {
        this.reasonCd = reasonCd;
    }

    public String getReasonDescription() {
        return reasonDescription;
    }

    public void setReasonDescription(String reasonDescription) {
        this.reasonDescription = reasonDescription;
    }

    @JsonSerialize(as = CapPolicyInfoModel.class)
    public ICapPolicyInfoModel getPolicy() {
        return policy;
    }

    @JsonDeserialize(as = CapPolicyInfoModel.class)
    public void setPolicy(ICapPolicyInfoModel policy) {
        this.policy = policy;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public String entityName() {
        return "CapDentalLoss";
    }

    @Override
    public String endpointName() {
        return "CapLoss";
    }
}
