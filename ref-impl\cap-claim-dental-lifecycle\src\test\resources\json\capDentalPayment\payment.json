{"_key": {"rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1}, "_type": "CapDentalPaymentEntity", "_modelName": "CapDentalPaymentDefinition", "_modelVersion": "1", "_modelType": "CapPayment", "_timestamp": "2025-07-15T05:38:37.877Z", "_variation": "payment", "state": "IssueRequested", "paymentNumber": "P2", "paymentNetAmount": {"currency": "USD", "amount": 50}, "creationDate": "2025-07-15T05:37:39.692Z", "direction": "outgoing", "originSource": {"_uri": "gentity://CapLoss/CapDentalLoss//f19fd4fb-a071-465f-afe9-4792d386bf7c/1"}, "paymentSchedule": {"_uri": "gentity://CapPaymentSchedule/CapDentalPaymentSchedule//7ad3e80c-1967-4d78-9e1a-9798023739e6/1"}, "paymentDetails": {"_key": {"rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "id": "b78bbdbe-de10-354b-9d2e-15d54ecdeabc"}, "_type": "CapDentalPaymentDetailsEntity", "_modelName": "CapDentalPaymentDefinition", "_modelVersion": "1", "_modelType": "CapPayment", "_timestamp": "2025-07-15T05:38:37.871Z", "payeeDetails": {"payee": {"_uri": "geroot://Provider/IndividualProvider//6dedf093-b731-352d-8b5f-ac37c851ca7c"}, "_type": "CapDentalPaymentPayeeDetailsEntity", "_key": {"id": "9c114cbe-f809-3bf0-9a99-953073c49fc0", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "b78bbdbe-de10-354b-9d2e-15d54ecdeabc"}}, "paymentAllocations": [{"allocationLossInfo": {"lossSource": {"_uri": "gentity://CapLoss/CapDentalLoss//f19fd4fb-a071-465f-afe9-4792d386bf7c/1"}, "_type": "CapDentalPaymentAllocationLossInfoEntity", "_key": {"id": "f062cfe1-e431-38b1-a113-3119f84c2768", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "70a13e4e-c701-363a-bc26-f24570c8aa17"}}, "allocationPayableItem": {"procedureID": "9ae87e23-3bf3-4d76-a014-665e643564ad", "claimSource": {"_uri": "gentity://CapLoss/CapDentalLoss//f19fd4fb-a071-465f-afe9-4792d386bf7c/1"}, "_type": "CapDentalPaymentAllocationPayableItemEntity", "_key": {"id": "c591c079-2559-3883-be69-64a55c1750d6", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "70a13e4e-c701-363a-bc26-f24570c8aa17"}}, "allocationDentalDetails": {"accumulatorDetails": [{"accumulatorType": "IndividualMaximum", "renewalType": "Annual", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 50}, "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "5a51f723-7ed0-3629-bcba-e6f17d5e364b", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "9f58204e-8ade-32c4-a2da-d43e50357260"}}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "9f58204e-8ade-32c4-a2da-d43e50357260", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "66526459-84de-3c98-ae54-c8c3a286f670"}}, {"accumulatorType": "IndividualDeductible", "renewalType": "Annual", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 50}, "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "7464ae08-36d9-3a10-a347-6e4d6574f1c2", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "550de428-1e1b-3c89-ab6c-fe41eb1c3690"}}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "550de428-1e1b-3c89-ab6c-fe41eb1c3690", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "66526459-84de-3c98-ae54-c8c3a286f670"}}, {"accumulatorType": "FamilyDeductible", "renewalType": "Annual", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 50}, "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "3b966f07-a72b-337e-aee7-d1bb9b7d9ec9", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "4da2b511-a028-3339-bd9f-5095ab447b17"}}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "4da2b511-a028-3339-bd9f-5095ab447b17", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "66526459-84de-3c98-ae54-c8c3a286f670"}}], "transactionTypeCd": "ActualServices", "patient": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//75632e17-5b4d-40ba-9780-62f15b3faeae"}, "_type": "CapDentalPaymentAllocationDentalDetailsEntity", "_key": {"id": "66526459-84de-3c98-ae54-c8c3a286f670", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "70a13e4e-c701-363a-bc26-f24570c8aa17"}}, "allocationGrossAmount": {"currency": "USD", "amount": 50}, "allocationNetAmount": {"currency": "USD", "amount": 50}, "allocationSource": {"_uri": "gentity://CapSettlement/CapDentalSettlement//ce832105-608c-4f64-b625-e0afa56ca1b4/1"}, "_type": "CapDentalPaymentAllocationEntity", "_key": {"id": "70a13e4e-c701-363a-bc26-f24570c8aa17", "rootId": "c49c2676-dff5-4718-b26e-9e91c3b973b9", "revisionNo": 1, "parentId": "b78bbdbe-de10-354b-9d2e-15d54ecdeabc"}}], "paymentDate": "2025-07-15T05:36:39.778Z"}}