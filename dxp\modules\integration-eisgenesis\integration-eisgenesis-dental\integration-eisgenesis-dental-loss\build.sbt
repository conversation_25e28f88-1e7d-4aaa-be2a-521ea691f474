Common.projectSettings

lazy val integrationEisGenesisDentalLoss = project.in(file("."))
  .enablePlugins(PlayMinimalJava, DxpCodegenPlugin)

libraryDependencies += "com.eisgroup.dxp" % "integrationeisgenesiscommon" % Common.dxpCoreVersion
libraryDependencies += "com.eisgroup.genesis.ref.cap.applications" % "ms-claim-dental-swagger" % Common.capDentalVersion

// When building  on some of the nodes it somehow excludes commons-lang 3.7 library, adding a bumped up version explicitly.
libraryDependencies += "org.apache.commons" % "commons-lang3" % "3.11"