// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALLOSS } from "./kraken_model_tree_CapDentalLoss"

let name = "CapDentalLoss"

let namespace = "CapDentalLoss"

let currencyCd = "USD"

export type CapDentalLossEntryPointName = "CapDentalLoss:ClaimReopenAction" | "CapDentalLoss:ClaimSubmitAction" | "CapDentalLoss:ClaimSuspendAction" | "CapDentalLoss:ClaimUpdateValidation" | "CapDentalLoss:UIClaimInfoValidation" | "CapDentalLoss:UIPolicyAndPatientValidation"

let entryPointNames = [
    "CapDentalLoss:ClaimReopenAction",
    "CapDentalLoss:ClaimSubmitAction",
    "CapDentalLoss:ClaimSuspendAction",
    "CapDentalLoss:ClaimUpdateValidation",
    "CapDentalLoss:UIClaimInfoValidation",
    "CapDentalLoss:UIPolicyAndPatientValidation"
] as CapDentalLossEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALLOSS as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalLoss = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
