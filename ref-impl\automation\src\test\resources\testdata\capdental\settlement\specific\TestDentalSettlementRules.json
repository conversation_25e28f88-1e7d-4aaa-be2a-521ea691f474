{"TestData_Readjudicate1": {"_updateStrategy": "MERGE", "claimLossIdentification": {"_uri": "gentity://CapLoss/CapDentalLoss//{{dentalClaimId}}/1"}, "entity": {"claimOverride": {"overrideClaimValue": {"overridePaymentInterestAmount": {"amount": 50, "currency": "USD"}, "overrideBasicWaitingPeriod": 4, "overridePreventiveWaitingPeriod": 3, "overrideOrthoWaitingPeriod": 6, "overrideGracePeriod": 8, "overridePaymentInterestDays": 9, "overrideMajorWaitingPeriod": 5, "overrideLateEntrantWaitingPeriod": 7, "_type": "CapDentalOverrideClaimValueEntity"}, "isAllowed": true, "isDenied": false, "_type": "CapDentalClaimOverrideEntity"}, "serviceOverrides": [{"isAllowed": false, "isDenied": true, "overrideServiceValue": {"overridePaymentInterestAmount": {"amount": 20, "currency": "USD"}, "overrideDeductible": {"amount": 30, "currency": "USD"}, "overrideMaximumAmount": {"amount": 3000, "currency": "USD"}, "overrideGracePeriod": 4, "overridePaymentInterestDays": 5, "overrideServiceWaitingPeriod": 2, "overrideLateEntrantWaitingPeriod": 3, "overrideServiceFrequencyLimit": 4, "overridePatientResponsibility": {"amount": 40, "currency": "USD"}, "overrideCopayAmount": {"amount": 80, "currency": "USD"}, "overrideConsideredAmount": {"amount": 60, "currency": "USD"}, "overrideReplacementLimit": 3, "overrideCoveredAmount": {"amount": 70, "currency": "USD"}, "overrideCoinsurancePct": 15, "_type": "CapDentalOverrideServiceValueEntity"}, "serviceSource": "{{serviceSource}}", "_type": "CapDentalServiceOverrideEntity"}], "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "$<today:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "CapDentalSettlementDetailEntity"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl.CapDentalSettlementModel"}, "TestData_Readjudicate2": {"_updateStrategy": "MERGE", "claimLossIdentification": {"_uri": "gentity://CapLoss/CapDentalLoss//{{dentalClaimId}}/1"}, "entity": {"claimOverride": {"overrideClaimValue": {"overridePaymentInterestAmount": {"amount": -10, "currency": "USD"}, "overrideBasicWaitingPeriod": 0, "overridePreventiveWaitingPeriod": 0, "overrideOrthoWaitingPeriod": 0, "overrideGracePeriod": 0, "overridePaymentInterestDays": -9, "overrideMajorWaitingPeriod": 0, "overrideLateEntrantWaitingPeriod": 0, "_type": "CapDentalOverrideClaimValueEntity"}, "isAllowed": false, "isDenied": true, "_type": "CapDentalClaimOverrideEntity"}, "serviceOverrides": [{"isAllowed": true, "isDenied": false, "overrideServiceValue": {"overridePaymentInterestAmount": {"amount": -20, "currency": "USD"}, "overrideDeductible": {"amount": -30, "currency": "USD"}, "overrideMaximumAmount": {"amount": -300, "currency": "USD"}, "overrideGracePeriod": -4, "overridePaymentInterestDays": 0, "overrideServiceWaitingPeriod": 0, "overrideLateEntrantWaitingPeriod": 0, "overrideServiceFrequencyLimit": 0, "overridePatientResponsibility": {"amount": -40, "currency": "USD"}, "overrideCopayAmount": {"amount": -80, "currency": "USD"}, "overrideConsideredAmount": {"amount": -60, "currency": "USD"}, "overrideReplacementLimit": 0, "overrideCoveredAmount": {"amount": -70, "currency": "USD"}, "overrideCoinsurancePct": -15, "_type": "CapDentalOverrideServiceValueEntity"}, "_type": "CapDentalServiceOverrideEntity"}], "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "$<today:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "CapDentalSettlementDetailEntity"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl.CapDentalSettlementModel"}}