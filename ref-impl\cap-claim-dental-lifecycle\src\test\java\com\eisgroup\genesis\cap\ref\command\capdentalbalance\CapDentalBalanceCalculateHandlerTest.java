/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.input.CapDentalBalanceCalculateInput;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.CapDentalBalanceCalculator;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceCalculationRulesOutput;
import com.eisgroup.genesis.model.ModelResolver;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalBalanceCalculateHandlerTest {

    @InjectMocks
    private CapDentalBalanceCalculateHandler handler;

    @Mock
    private ModelResolver modelResolver;

    @Mock
    private CapDentalBalanceCalculator capDentalBalanceCalculator;

    @Before
    public void setUp() {
        when(modelResolver.getModelName()).thenReturn("CapDentalBalance");
    }

    @Test
    public void shouldLoadEntity() {
        //given
        CapDentalBalanceCalculateInput input = new CapDentalBalanceCalculateInput(new JsonObject());

        //when
        CapDentalBalanceCalculationRulesOutput result = handler.load(input);

        //then
        assertThat(result.getModelFactory().getModelName(), equalTo("CapDentalBalance"));
    }

    @Test
    public void testHandlerExecute() {
        //given
        CapDentalBalanceCalculateInput input = new CapDentalBalanceCalculateInput(new JsonObject());
        CapDentalBalanceCalculationRulesOutput output = (CapDentalBalanceCalculationRulesOutput) ModelInstanceFactory.createInstance("CapDentalBalance", "1", CapDentalBalanceCalculationRulesOutput.class.getSimpleName());
        when(capDentalBalanceCalculator.calculateLossBalance(any())).thenReturn(Lazy.of(output));

        //when
        CapDentalBalanceCalculationRulesOutput result = handler.execute(input, output);

        //then
        assertThat(result.getKey(), notNullValue());
    }

    @Test
    public void testHandlerSave() {
        //given
        CapDentalBalanceCalculateInput input = new CapDentalBalanceCalculateInput(new JsonObject());
        CapDentalBalanceCalculationRulesOutput output = (CapDentalBalanceCalculationRulesOutput) ModelInstanceFactory.createInstance("CapDentalBalance", "1", CapDentalBalanceCalculationRulesOutput.class.getSimpleName());

        //when
        CapDentalBalanceCalculationRulesOutput result = handler.save(input, output);

        //then
        assertThat(result, is(output));
    }

    @Test
    public void testReturnHandlerName() {
        //when
        String result = handler.getName();

        //then
        assertThat(result, equalTo("calculateLossBalance"));
    }
}
