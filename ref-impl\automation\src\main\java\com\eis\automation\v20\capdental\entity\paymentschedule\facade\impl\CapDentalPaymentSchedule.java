/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionSerialization;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.ICapDentalPaymentSchedule;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl.CapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewBuildModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public class CapDentalPaymentSchedule implements ICapDentalPaymentSchedule {
    private final PostModelAction<ICapDentalPaymentSchedulePreviewBuildModel, ICapDentalPaymentSchedulePreviewModel> previewAction;

    public CapDentalPaymentSchedule(RestActionConfiguration configuration) {
        previewAction = new PostModelAction<>(configuration);
    }

    @RestActionGetter("previewAction")
    @RestActionContext(target = "/api/cappaymentschedule/{product}/{version}/command/previewPaymentSchedule")
    @RestActionSerialization(serializationClazz = CapDentalPaymentSchedulePreviewModel.class)
    public PostModelAction<ICapDentalPaymentSchedulePreviewBuildModel, ICapDentalPaymentSchedulePreviewModel> preview() {
        return previewAction;
    }
}
