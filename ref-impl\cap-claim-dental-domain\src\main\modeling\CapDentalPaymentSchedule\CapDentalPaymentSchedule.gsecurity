Secure using privilege ("Financial: Initiate Payment Schedule") {
    Model CapDentalPaymentSchedule {
        Command initPaymentSchedule
    }
}

Secure using privilege ("Financial: Complete Payment Schedule") {
    Model CapDentalPaymentSchedule {
        Command completePaymentSchedule
    }
}

Secure using privilege ("Financial: Activate Payment Schedule") {
    Model CapDentalPaymentSchedule {
        Command activatePaymentSchedule
    }
}

Secure using privilege ("Financial: Update Payment Schedule") {
    Model CapDentalPaymentSchedule {
        Command updatePaymentSchedule
    }
}