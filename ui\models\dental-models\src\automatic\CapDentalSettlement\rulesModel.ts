// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALSETTLEMENT } from "./kraken_model_tree_CapDentalSettlement"

let name = "CapDentalSettlement"

let namespace = "CapDentalSettlement"

let currencyCd = "USD"

export type CapDentalSettlementEntryPointName = "CapDentalSettlement:AdjudicateSettlement" | "CapDentalSettlement:InitSettlement"

let entryPointNames = [
    "CapDentalSettlement:AdjudicateSettlement",
    "CapDentalSettlement:InitSettlement"
] as CapDentalSettlementEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALSETTLEMENT as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalSettlement = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
