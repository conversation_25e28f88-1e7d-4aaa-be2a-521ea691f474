{"_key": {"rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1}, "_type": "CapDentalSettlementEntity", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "2025-07-10T05:51:45.806Z", "settlementResult": {"proposal": "PAY", "entries": [{"calculationResult": {"procedureType": "Preventive", "charge": {"currency": "USD", "amount": 50}, "coveredFee": {"currency": "USD", "amount": 50}, "payableDeductible": {"currency": "USD", "amount": 50}, "procedureID": "4f43b30d-0026-3786-a2a6-259062101b34", "submittedCode": "D0140", "coinsuranceAmt": {"currency": "USD", "amount": 0}, "coveredCode": "D0140", "patientResponsibility": {"currency": "USD", "amount": 50}, "consideredFee": {"currency": "USD", "amount": 50}, "netBenefitAmount": {"currency": "USD", "amount": 0}, "coinsurancePercentage": 80, "_type": "CapDentalCalculationResultEntity", "_key": {"id": "a5276956-ce36-36fb-8d53-e49f686c3fde", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368"}}, "serviceSource": "4f43b30d-0026-3786-a2a6-259062101b34", "reservedAccumulators": [{"accumulatorType": "IndividualMaximum", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "networkType": "INN", "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0a867da4-d8ff-385f-8b55-a17355ce7049", "id": "077e390e-f2d3-36af-af22-b65b602afd0a"}}, "_type": "CapDentalAccumulatorEntity", "_key": {"id": "0a867da4-d8ff-385f-8b55-a17355ce7049", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368"}}, {"accumulatorType": "IndividualDeductible", "reservedAmount": {"currency": "USD", "amount": 50}, "renewalType": "Annual", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "networkType": "INN", "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "696559ee-342c-39dd-800a-dbec6f12db01", "id": "5f53ab88-a7fb-30d8-a22e-9876848e0bc6"}}, "_type": "CapDentalAccumulatorEntity", "_key": {"id": "696559ee-342c-39dd-800a-dbec6f12db01", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368"}}, {"accumulatorType": "FamilyDeductible", "reservedAmount": {"currency": "USD", "amount": 50}, "renewalType": "Annual", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "networkType": "INN", "term": {"effectiveDate": "2024-08-01T00:00:00.000Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0cc26385-ecfa-3a95-88df-7f2aedeed4fc", "id": "25f9f055-004f-3c91-afce-c6062f374d90"}}, "_type": "CapDentalAccumulatorEntity", "_key": {"id": "0cc26385-ecfa-3a95-88df-7f2aedeed4fc", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368"}}], "status": {"flag": "Allowed", "fee": {"currency": "USD", "amount": 50}, "procedureID": "4f43b30d-0026-3786-a2a6-259062101b34", "submittedCode": "D0140", "coveredCode": "D0140", "_type": "CapDentalCalculationStatusEntity", "_key": {"id": "35e17b8f-0bc0-34fd-9ef1-4026f51dd3e2", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368"}}, "_type": "CapDentalResultEntryEntity", "_key": {"id": "078923ac-0cdb-341c-a4b6-aa4e2a4e3368", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "210ff3d7-d59a-3afb-8dfe-fb623ff5a837"}}], "payeeRef": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "paymentAmount": {"currency": "USD", "amount": 0}, "_type": "CapDentalDecisionResultEntity", "_key": {"id": "210ff3d7-d59a-3afb-8dfe-fb623ff5a837", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0538d778-04c0-46dd-9de0-24203e005992"}}, "settlementLossInfo": {"submittedProcedures": [{"masterPolicyInfo": [{"orthoWaitingPeriod": "0", "childMaxAgeCd": "26", "frequencyLimitations": {"basicLimitations": {"basicPeriodontalSurgery": "1in36Months", "basicStainlessSteelCrownsAgeLimit": "16", "basicStainlessSteelCrowns": "1in36Months", "_type": "CapPolicyBasicLimitationsEntity", "_key": {"id": "c1fcb039-ccfa-3678-859d-9f22104e83ae", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "11ce18f6-0d19-360d-9d25-5a3dce24424f"}}, "preventiveLimitations": {"preventiveFluorideTreatment": "1in6Months", "preventiveFluorideTreatmentAgeLimit": "19", "preventiveOralEvaluations": "1in6Months", "preventiveBitewingRadiographs": "1in12Months", "_type": "CapPolicyPreventiveLimitationsEntity", "_key": {"id": "9f5ab498-bab5-3f58-abbb-8389d9461172", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "11ce18f6-0d19-360d-9d25-5a3dce24424f"}}, "majorLimitations": {"majorCrowns": "1in120Months", "majorDentureAdjustments": "1in12Months", "majorImplants": "1in120Months", "_type": "CapPolicyMajorLimitationsEntity", "_key": {"id": "b8944502-1fd0-362e-a611-a79af4877636", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "11ce18f6-0d19-360d-9d25-5a3dce24424f"}}, "_type": "CapPolicyFrequencyLimitationsEntity", "_key": {"id": "11ce18f6-0d19-360d-9d25-5a3dce24424f", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "6fd9b159-3276-3bf7-b78d-263e1ee41c75"}}, "orthoINNCoinsurancePercent": 50, "basicWaitingPeriod": "0", "planName": "Low", "fullTimeStudentAgeCd": "26", "planCategory": "PPO", "serviceCategory": {"scFillings": "Basic", "scAllOtherRadiographs": "Preventive", "scImplantServices": "Major", "scSurgicalPeriodontics": "Basic", "scCrowns": "Major", "scFullMouthRadiographs": "Preventive", "scFluorides": "Preventive", "scBitewingRadiographs": "Preventive", "scStainlessSteelCrowns": "Basic", "scOralEvaluations": "Preventive", "scRootCanals": "Basic", "_type": "CapPolicyServiceCategoryEntity", "_key": {"id": "dc367bc0-e4e5-3a7d-9b8b-b56c548c5de2", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "6fd9b159-3276-3bf7-b78d-263e1ee41c75"}}, "orthoChildAgeLimit": 19, "riskStateCd": "NY", "orthoOONCoinsurancePercent": 50, "preventWaitingPeriod": "0", "applyLateEntrantBenefitWaitingPeriods": true, "lateEntrantWaitingPeriodsDetails": [{"preventWaitingPeriod": "0", "basicWaitingPeriod": "0", "majorWaitingPeriod": "0", "_type": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity", "_key": {"id": "166403e3-a9b8-35bb-ba0a-017bd36a8489", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "6fd9b159-3276-3bf7-b78d-263e1ee41c75"}}], "term": {"effectiveDate": "2024-08-01T00:00:00Z", "_type": "Term", "_key": {"id": "23c67030-f050-3161-b129-dae17dc1bccb", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "6fd9b159-3276-3bf7-b78d-263e1ee41c75"}}, "majorWaitingPeriod": "0", "plan": "Low", "orthoAvailability": "<PERSON><PERSON><PERSON><PERSON>", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_type": "CapDentalPolicyInfoEntity", "_key": {"id": "6fd9b159-3276-3bf7-b78d-263e1ee41c75", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4f43b30d-0026-3786-a2a6-259062101b34"}}], "dentist": {"dentistID": "1111111111", "_type": "CapDentalDentistEntity", "_key": {"id": "cc143552-0144-3f3a-b0aa-f7ce98f77d83", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4f43b30d-0026-3786-a2a6-259062101b34"}}, "certPolicyInfo": {"isOrthoCoverageIncluded": true, "basicWaitingPeriod": "0", "planName": "Low", "serviceCategory": {"scFillings": "Basic", "scAllOtherRadiographs": "Preventive", "scImplantServices": "Major", "scSurgicalPeriodontics": "Basic", "scCrowns": "Major", "scFullMouthRadiographs": "Preventive", "scFluorides": "Preventive", "scBitewingRadiographs": "Preventive", "scStainlessSteelCrowns": "Basic", "scOralEvaluations": "Preventive", "scRootCanals": "Basic", "_type": "CapPolicyServiceCategoryEntity", "_key": {"id": "fa04f160-35ea-3edf-b38d-25b7d159960d", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, "coinsurances": [{"coinsuranceOONPct": 80, "coinsuranceServiceType": "Preventive", "coinsuranceINPct": 80, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "32ae142d-2ee1-340f-9f3b-6f2512e62868", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, {"coinsuranceOONPct": 50, "coinsuranceServiceType": "Basic", "coinsuranceINPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "a44d1995-895c-3b9b-bd15-11b9ef7e2d9d", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, {"coinsuranceOONPct": 50, "coinsuranceServiceType": "Major", "coinsuranceINPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "89bb4fff-2ab5-3eb9-a987-e98e23996a9f", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}], "applyLateEntrantBenefitWaitingPeriods": true, "isImplantsMaximumAppliedTowardPlanMaximum": true, "lateEntrantWaitingPeriodsDetails": [{"preventWaitingPeriod": "0", "basicWaitingPeriod": "0", "majorWaitingPeriod": "0", "_type": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity", "_key": {"id": "33ebbca4-a4b4-3e73-a6c8-86a9148616df", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}], "term": {"effectiveDate": "2024-08-01T00:00:00Z", "_type": "Term", "_key": {"id": "1f272f38-9a0a-34bf-aad8-b1724f0de2ca", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, "majorWaitingPeriod": "0", "cosmeticWaitingPeriod": "0", "plan": "Low", "orthoWaitingPeriod": "0", "childMaxAgeCd": "26", "orthoDeductibleType": "None", "frequencyLimitations": {"basicLimitations": {"basicPeriodontalSurgery": "1in36Months", "basicStainlessSteelCrownsAgeLimit": "16", "basicStainlessSteelCrowns": "1in36Months", "_type": "CapPolicyBasicLimitationsEntity", "_key": {"id": "efa2bc74-f215-301c-8960-b620a1738309", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "504ab163-5fca-35d4-9b2d-24fa46d39b14"}}, "preventiveLimitations": {"preventiveFluorideTreatment": "1in6Months", "preventiveFluorideTreatmentAgeLimit": "19", "preventiveOralEvaluations": "1in6Months", "preventiveBitewingRadiographs": "1in12Months", "_type": "CapPolicyPreventiveLimitationsEntity", "_key": {"id": "27ef2f31-abe3-3997-8f37-7263236d7d1f", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "504ab163-5fca-35d4-9b2d-24fa46d39b14"}}, "majorLimitations": {"majorCrowns": "1in120Months", "majorDentureAdjustments": "1in12Months", "majorImplants": "1in120Months", "_type": "CapPolicyMajorLimitationsEntity", "_key": {"id": "ebaf917a-bb64-3040-8d70-6c6ed14ee5e0", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "504ab163-5fca-35d4-9b2d-24fa46d39b14"}}, "_type": "CapPolicyFrequencyLimitationsEntity", "_key": {"id": "504ab163-5fca-35d4-9b2d-24fa46d39b14", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, "insureds": [{"hireDate": "2023-07-31", "isMain": true, "relationshipToPrimaryInsuredCd": "Self", "insuredRoleNameCd": "PrimaryInsured", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "_type": "CapDentalPolicyInfoInsuredDetailsEntity", "_key": {"id": "b94b69f4-1259-3d32-b4d7-7cc8b5b7b3b8", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}], "orthoLateEntrantWaitingPeriod": "0", "orthoINNCoinsurancePercent": 50, "isImplantCoverageIncluded": true, "fullTimeStudentAgeCd": "26", "orthoChildAgeLimit": 19, "cosmeticDeductibleType": "None", "riskStateCd": "NY", "orthoOONCoinsurancePercent": 50, "preventWaitingPeriod": "0", "capPolicyId": "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "isTmjCoverageIncluded": true, "waitingPeriodApplyTo": "NewAndExistingEmployees", "capPolicyVersionId": "capPolicy://PAS/gentity://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc/2", "tmjDeductibleType": "None", "enrollmentTypeCd": "AnnualEnrollment", "coverageEligibility": {"waitingPeriodAmount": 30, "waitingPeriodModeCd": "Days", "eligibilityTypeCd": "ExistingEmployees", "waitingPeriodDefCd": "1stMonthFollowingAmountAndMode", "_type": "CapPolicyEligibilityEntity", "_key": {"id": "16757680-62bb-3552-a1b6-582cf3b10adb", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a"}}, "isCosmeticServicesIncluded": true, "orthoAvailability": "<PERSON><PERSON><PERSON><PERSON>", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_type": "CapDentalPolicyInfoEntity", "_key": {"id": "be0f5b66-340e-325f-a7c7-e3b7d7c47d4a", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4f43b30d-0026-3786-a2a6-259062101b34"}}, "procedureCode": "D0140", "submittedFee": {"amount": 50, "currency": "USD"}, "dateOfService": "2025-06-03", "surfaces": ["B"], "toothArea": "01", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity", "_key": {"id": "d16579f9-7b49-3892-86fe-7a8e5900d5ed", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4f43b30d-0026-3786-a2a6-259062101b34"}}, "ortho": {"_type": "CapDentalOrthodonticEntity", "orthoFrequencyCd": "Quarterly", "orthoMonthQuantity": 11, "_key": {"id": "537c4cad-15ef-31e9-8c45-179627232a54", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4f43b30d-0026-3786-a2a6-259062101b34"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "4f43b30d-0026-3786-a2a6-259062101b34", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "526533c7-8f30-3a26-b7ae-c8b1cee1e1c4"}}], "patient": {"patientID": "PA00001", "historyProcedures": [], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "birthDate": "1985-06-09", "accumulators": [{"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "appliesToProcedureCategories": ["CosmeticServices"], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "d4741dfe-a5e8-3086-be7a-f6595ea2c0ae", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "appliesToProcedureCategories": ["CosmeticServices"], "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "dd15c24d-7622-3f03-8125-e125cd051f66", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 50, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualDeductible", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "5f53ab88-a7fb-30d8-a22e-9876848e0bc6", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "a119dffd-6c91-3abe-9cd3-14a5ea9bb0b0"}}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "a119dffd-6c91-3abe-9cd3-14a5ea9bb0b0", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 50, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "6d0d6581-706f-336c-87c4-1c4d625e349a", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "b9cb87e8-0216-35c5-8cfb-1a1f3ab4dd5e"}}, "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "b9cb87e8-0216-35c5-8cfb-1a1f3ab4dd5e", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 150, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "25f9f055-004f-3c91-afce-c6062f374d90", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "e22b7a32-3464-3c2c-ad85-b6cd44b87206"}}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "e22b7a32-3464-3c2c-ad85-b6cd44b87206", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 150, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "5cc948e3-1a80-3f3a-9e33-abbbcfec6b04", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "4fc2cc20-ea1a-3a74-94ad-6e6fda50c3be"}}, "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "4fc2cc20-ea1a-3a74-94ad-6e6fda50c3be", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 750, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Implants"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "2ef77caa-3ee5-3c64-8b6c-34cc7be42050", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "1964ce61-4965-3341-9c0c-b5ed20c2c6fa"}}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "1964ce61-4965-3341-9c0c-b5ed20c2c6fa", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 750, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Implants"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "a5c23c18-88fd-3393-99b2-31e84993308a", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9623da69-1c91-309e-823e-11107a823211"}}, "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "9623da69-1c91-309e-823e-11107a823211", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "TMJMaximum", "appliesToProcedureCategories": ["TMJ"], "renewalType": "Lifetime", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "c28f87f6-7b39-3f59-9f2c-0acd489d1386", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "TMJMaximum", "appliesToProcedureCategories": ["TMJ"], "renewalType": "Lifetime", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "9e6fe4e5-e0a6-3ae4-8e75-24a3e69a82eb", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualMaximum", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "077e390e-f2d3-36af-af22-b65b602afd0a", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "6fd43dbb-5f4c-32a7-a5ee-b61ba7a2cc7f"}}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "6fd43dbb-5f4c-32a7-a5ee-b61ba7a2cc7f", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualMaximum", "appliesToProcedureCategories": ["Basic", "Major", "Preventive"], "renewalType": "Annual", "term": {"effectiveDate": "2024-08-01T00:00:00Z", "expirationDate": "2025-07-31T23:59:59.999Z", "_type": "Term", "_key": {"id": "add22d0a-ec78-3915-8f6f-2e58e3381ec8", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "add9c07d-bdac-3c5a-8bce-a8bdea82cb8c"}}, "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "add9c07d-bdac-3c5a-8bce-a8bdea82cb8c", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "OrthoMaximum", "appliesToProcedureCategories": ["Orthodontics"], "renewalType": "Lifetime", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "a80052e0-0b19-31f7-8475-fccd03fb8cca", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "OrthoMaximum", "appliesToProcedureCategories": ["Orthodontics"], "renewalType": "Lifetime", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "c0d30202-6b55-3c1a-8e64-f5ca44d0f0b0", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "9abc6449-81c2-3772-8f03-66c1c18e3f4f"}}], "_type": "CapDentalPatientEntity", "_key": {"id": "9abc6449-81c2-3772-8f03-66c1c18e3f4f", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "526533c7-8f30-3a26-b7ae-c8b1cee1e1c4"}}, "claimData": {"initialDateOfService": "2025-06-03T00:00:00Z", "patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "_key": {"id": "838a9764-54fb-306a-9ae9-b17dfa1ba99d", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "d7dc913d-798e-337f-8ce5-84dabb561874"}}, "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "_key": {"id": "1400098f-9e05-3585-9441-f5915947158a", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "d7dc913d-798e-337f-8ce5-84dabb561874"}}, "providerRole": {"_type": "CapProviderRole", "roleCd": ["IndividualProvider"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "providerLink": "geroot://Provider/IndividualProvider//cc4eb7ae-09a5-300a-9bf2-13583b1d8cc0", "_key": {"id": "50dacf17-b760-3cd4-acf9-e59e36f3e91d", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "d7dc913d-798e-337f-8ce5-84dabb561874"}}, "alternatePayeeRole": {"_type": "CapAlternatePayeeRole", "roleCd": ["<PERSON><PERSON><PERSON><PERSON>"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//afd0ecad-dca5-3573-b86d-29309b05c2de", "_key": {"id": "6d2d3bd8-b020-32c0-a7e1-20a101526b26", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "d7dc913d-798e-337f-8ce5-84dabb561874"}}, "payeeType": "PrimaryInsured", "dateOfBirth": "2005-04-02", "source": "NONEDI", "transactionType": "OrthodonticServices", "receivedDate": "2025-06-03", "_type": "CapDentalClaimDataEntity", "_key": {"id": "d7dc913d-798e-337f-8ce5-84dabb561874", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "526533c7-8f30-3a26-b7ae-c8b1cee1e1c4"}}, "_type": "CapDentalClaimInfoEntity", "_key": {"id": "526533c7-8f30-3a26-b7ae-c8b1cee1e1c4", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0538d778-04c0-46dd-9de0-24203e005992"}}, "accessTrackInfo": {"_key": {"id": "50c0ebef-5f72-3a18-9ed8-b1aba9216ab9", "rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0538d778-04c0-46dd-9de0-24203e005992"}, "_type": "AccessTrackInfo", "createdBy": "workflow-service", "createdOn": "2025-07-10T05:51:44.366Z", "updatedBy": "workflow-service", "updatedOn": "2025-07-10T05:51:44.366Z"}, "state": "Approved", "settlementNumber": "S73", "claimLossIdentification": {"_uri": "capMock://CapDentalLoss//681e982a-1d24-4050-b480-82ec03ff2131/1"}, "settlementDetail": {"_key": {"rootId": "0538d778-04c0-46dd-9de0-24203e005992", "revisionNo": 1, "parentId": "0538d778-04c0-46dd-9de0-24203e005992", "id": "3643082d-a598-3161-a35c-5cdcd44c6023"}, "_type": "CapDentalSettlementDetailEntity", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "2025-07-10T05:51:45.801Z"}}