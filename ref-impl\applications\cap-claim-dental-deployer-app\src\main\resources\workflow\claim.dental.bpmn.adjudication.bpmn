<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.0">
  <process id="claim.dental.bpmn.adjudication" name="Claim Dental Process - Adjudication" isExecutable="true">
    <serviceTask id="sid-334E7269-7D7C-4480-9654-F0AFE7979451" name="Initialize Adjudication" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalSettlement" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="initSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'claimLossIdentification': {'_uri': '${_uri}'},'entity': {'_modelName': 'CapDentalSettlement','_modelVersion': '1','_modelType': 'CapSettlement','_type': 'CapDentalSettlementDetailEntity'}}" target="payload"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" target="settlementCreationResult"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-86414BE7-FCA5-49C6-9A5D-D37D0BC293BE" sourceRef="dental_Adjudication_start" targetRef="sid-334E7269-7D7C-4480-9654-F0AFE7979451"></sequenceFlow>
    <callActivity id="call.adjudication.subprocess" name="Adjudication sub-process" calledElement="claim.dental.bpmn.adjudication.subprocess" flowable:calledElementType="key" flowable:fallbackToDefaultTenant="false">
      <extensionElements>
        <flowable:in sourceExpression="${settlementCreationResult._key}" target="_key"></flowable:in>
        <flowable:in sourceExpression="${settlementCreationResult._modelVersion}" target="_modelVersion"></flowable:in>
        <flowable:in sourceExpression="${settlementCreationResult._modelName}" target="_modelName"></flowable:in>
        <flowable:out sourceExpression="${settlementAdjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
        <flowable:out sourceExpression="gentity://CapSettlement/CapDentalSettlement//${settlementAdjudicationResult._key.rootId}/1" target="settlementURI"></flowable:out>
        <flowable:out sourceExpression="${settlementAdjudicationResult._key}" target="settlementKey"></flowable:out>
        <flowable:out sourceExpression="${settlementAdjudicationResult._modelName}" target="settlementModelName"></flowable:out>
        <flowable:out sourceExpression="${settlementAdjudicationResult._modelVersion}" target="settlementVersion"></flowable:out>
      </extensionElements>
    </callActivity>
    <endEvent id="dental_Adjudication_end"></endEvent>
    <sequenceFlow id="sid-551BF901-BF5E-4FB9-BE1C-BFC669D9EC70" sourceRef="sid-334E7269-7D7C-4480-9654-F0AFE7979451" targetRef="call.adjudication.subprocess"></sequenceFlow>
    <sequenceFlow id="sid-9D0EFCDB-1F20-4EBD-8BE8-55BEEFC81025" sourceRef="call.adjudication.subprocess" targetRef="dental_Adjudication_end"></sequenceFlow>
    <startEvent id="dental_Adjudication_start" flowable:initiator="initiator" flowable:formFieldValidation="true"></startEvent>
    <textAnnotation id="sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE">
      <text>Input params:
Claim
*  _key
* _modelName
* _modelVersion
* _uri</text>
    </textAnnotation>
    <textAnnotation id="sid-067861BD-C96C-4B28-86CF-DB517CD7F841">
      <text>(api: initSettlement)
Create Settlement entity
</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_claim.dental.bpmn.adjudication">
    <bpmndi:BPMNPlane bpmnElement="claim.dental.bpmn.adjudication" id="BPMNPlane_claim.dental.bpmn.adjudication">
      <bpmndi:BPMNShape bpmnElement="sid-334E7269-7D7C-4480-9654-F0AFE7979451" id="BPMNShape_sid-334E7269-7D7C-4480-9654-F0AFE7979451">
        <omgdc:Bounds height="80.0" width="100.0" x="495.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="call.adjudication.subprocess" id="BPMNShape_call.adjudication.subprocess">
        <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dental_Adjudication_end" id="BPMNShape_dental_Adjudication_end">
        <omgdc:Bounds height="28.0" width="28.0" x="960.0" y="161.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE" id="BPMNShape_sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE">
        <omgdc:Bounds height="125.0" width="118.99999999999997" x="75.0" y="150.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-067861BD-C96C-4B28-86CF-DB517CD7F841" id="BPMNShape_sid-067861BD-C96C-4B28-86CF-DB517CD7F841">
        <omgdc:Bounds height="99.99999999999997" width="132.99999999999994" x="480.00000000000006" y="240.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dental_Adjudication_start" id="BPMNShape_dental_Adjudication_start">
        <omgdc:Bounds height="30.0" width="30.0" x="225.25" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-86414BE7-FCA5-49C6-9A5D-D37D0BC293BE" id="BPMNEdge_sid-86414BE7-FCA5-49C6-9A5D-D37D0BC293BE" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="255.19999980314591" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="494.99999999986244" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-9D0EFCDB-1F20-4EBD-8BE8-55BEEFC81025" id="BPMNEdge_sid-9D0EFCDB-1F20-4EBD-8BE8-55BEEFC81025" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="819.9499999999294" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="960.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-551BF901-BF5E-4FB9-BE1C-BFC669D9EC70" id="BPMNEdge_sid-551BF901-BF5E-4FB9-BE1C-BFC669D9EC70" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="594.9499999998377" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="720.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>