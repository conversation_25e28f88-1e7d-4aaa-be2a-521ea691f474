{"_key": {"rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1}, "_type": "CapDentalLossEntity", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "lossDetail": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 100, "currency": "USD"}, "dateOfService": "2025-04-01", "surfaces": ["B"], "toothArea": "01", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity", "_key": {"id": "da98385f-5b99-3b38-96d6-b85bbce8583f", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "21e05432-e5d5-317b-976a-cfe713522c09"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "21e05432-e5d5-317b-976a-cfe713522c09", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "f52aab1a-d265-376d-aaf0-309fef50ac46"}}, {"procedureCode": "D0120", "submittedFee": {"amount": 50, "currency": "USD"}, "dateOfService": "2025-03-01", "surfaces": ["B"], "toothArea": "01", "quantity": 1, "predetInd": false, "preauthorization": {"isProcedureAuthorized": false, "_type": "CapDentalPreauthorizationEntity", "_key": {"id": "7344da65-6ae3-3337-b04c-d15a7143a84b", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "82005327-af15-340e-9aeb-73a66f2da71c"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "82005327-af15-340e-9aeb-73a66f2da71c", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "f52aab1a-d265-376d-aaf0-309fef50ac46"}}], "claimData": {"patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b62a291a-0cf4-3bc6-9b8f-940df79e3fc5", "_key": {"id": "fe7fe7c4-60f5-3b7b-9240-0241102e92e9", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "ea31f5c4-1994-3931-a9ee-ae82c83b82d6"}}, "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b62a291a-0cf4-3bc6-9b8f-940df79e3fc5", "_key": {"id": "aca77540-bae8-3e95-a43b-22feae2598ca", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "ea31f5c4-1994-3931-a9ee-ae82c83b82d6"}}, "providerRole": {"_type": "CapProviderRole", "roleCd": ["IndividualProvider"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b62a291a-0cf4-3bc6-9b8f-940df79e3fc5", "providerLink": "geroot://Provider/IndividualProvider//96dab7d2-12d0-3467-aa74-571a90f245e1", "_key": {"id": "7c1261f2-23c2-3114-9981-f4677eefa9ca", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "ea31f5c4-1994-3931-a9ee-ae82c83b82d6"}}, "alternatePayeeRole": {"_type": "CapAlternatePayeeRole", "roleCd": ["<PERSON><PERSON><PERSON><PERSON>"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b62a291a-0cf4-3bc6-9b8f-940df79e3fc5", "_key": {"id": "9ef4b3ad-32a2-3086-b676-a54dd64b405c", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "ea31f5c4-1994-3931-a9ee-ae82c83b82d6"}}, "payeeType": "PrimaryInsured", "dateOfBirth": "2005-04-02", "source": "NONEDI", "transactionType": "ActualServices", "receivedDate": "2025-04-02", "_type": "CapDentalClaimDataEntity", "_key": {"id": "ea31f5c4-1994-3931-a9ee-ae82c83b82d6", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "f52aab1a-d265-376d-aaf0-309fef50ac46"}}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity", "_key": {"id": "f52aab1a-d265-376d-aaf0-309fef50ac46", "rootId": "4e87d9ef-4486-4332-97f6-8c8611386e4e", "revisionNo": 1, "parentId": "4e87d9ef-4486-4332-97f6-8c8611386e4e"}, "_timestamp": "2025-05-28T07:07:55.777Z", "_version": "bd9711d4-86bb-465b-8e57-715a957a631a"}, "policyId": "capPolicy://CapUP/gentity://UnverifiedPolicy/CapDentalUnverifiedPolicy//e37810a4-e63a-4f53-b1cc-97efa31bf1b9/1", "lossNumber": "DN158", "claimType": "dental", "state": "Incomplete", "_timestamp": "2025-05-28T07:07:55.78Z", "_version": "9aff69d0-50b0-4f1d-9dee-584942a7b381"}