package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryCancelHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPatientHistoryLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalPatientHistoryLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalPatientHistory";

    private static final String MODEL_TYPE = "CapDentalPatientHistoryModelType";

    private CapDentalPatientHistoryLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalPatientHistoryLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalPatientHistoryInitHandler.class,
                CapDentalPatientHistoryUpdateHandler.class,
                CapDentalPatientHistoryCancelHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalPatientHistoryLifecycleConfig.class), equalTo(true));
    }
}
