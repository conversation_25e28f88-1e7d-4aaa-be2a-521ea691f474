/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentClearHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;

/**
 * Command handler for clearing a {@link CapDentalPaymentEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentClearHandler extends CapPaymentClearHandler<IdentifierRequest, CapDentalPaymentEntity> {
}
