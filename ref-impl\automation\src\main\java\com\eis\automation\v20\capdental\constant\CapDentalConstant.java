/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.constant;

public class CapDentalConstant {

    public static class CapEnvironementConfigConstants {
        public static final String CLAIM_DENTAL = "CLAIM_DENTAL";

        public CapEnvironementConfigConstants() {
        }
    }

    public static class Components {
        public static final String CAP_DENTAL_UP = "CAP.Dental Unverified Policy";
        public static final String CAP_DENTAL_LOSS = "CAP.Dental Loss";
        public static final String CAP_DENTAL_SETTLEMENT = "CAP.Dental Settlement";
        public static final String CAP_DENTAL_PAYMENT = "CAP.Dental Payment";
        public static final String CAP_DENTAL_PAYMENT_TEMPLATE = "CAP.Dental Payment Template";
        public static final String CAP_DENTAL_PATIENT_HISTORY = "CAP.Dental Patient History";
        public static final String CAP_DENTAL_AUTO_ADJUDICATION = "CAP.Dental Auto Adjudication";
    }

    public static class BamMessage {
        public static final String CAP_DENTAL_LOSS_REOPEN = "CapDentalLoss_reopenLoss";
        public static final String CAP_DENTAL_SETTLEMENT_CLOSE =  "CapDentalSettlement_closeSettlement";
    }

    public static class CapModelNames {
        public static final String CAP_DENTAL_SETTLEMENT = "CapDentalSettlement";
        public static final String CAP_DENTAL_PAYMENT_TEMPLATE = "CapDentalPaymentTemplate";
        public static final String CAP_DENTAL_PAYMENT_SCHEDULE = "CapDentalPaymentSchedule";
    }

    public static class DentalPlanCategory {
        public static final String PPO = "PPO";
    }

    public static class DentalClaimTransactionType {
        public static final String ORTHODONTIC_SERVICES = "OrthodonticServices";
        public static final String PREDETERMINATION_ACTUAL_SERVICES = "PredeterminationActualServices";
        public static final String PREDETERMINATION_ORTHODONTICS = "PredeterminationOrthodontics";
        public static final String ACTUAL_SERVICES = "ActualServices";
    }

    public static class DentalClaimSource {
        public static final String NONEDI = "NONEDI";
        public static final String EDI = "EDI";
    }

    public static class DentalClaimReasonCd {
        public static final String PAID = "Paid";
        public static final String PREDETERMINED = "Predetermined";
        public static final String DENIED = "Denied";
        public static final String CANCELED = "Canceled";
    }

    public static class DentalClaimProviderDiscountType {
        public static final String PERCENTAGE = "Percentage";
        public static final String AMOUNT = "Amount";
    }

    public static class DentalAllocationLobCd {
        public static final String DENTAL = "Dental";
    }

    public static class DentalSettlementProposal {
        public static final String PAY = "PAY";
        public static final String DENY = "DENY";
        public static final String PEND = "PEND";
        public static final String PREDET_DENY = "PREDET - DENY";
    }

    public static class DentalSubmittedProcedureCode {
        public static final String D2542 = "D2542";
        public static final String D0330 = "D0330";
        public static final String D0110 = "D0110";
        public static final String D8692 = "D8692";
        public static final String D8680 = "D8680";
        public static final String D2160 = "D2160";
        public static final String D0210 = "D0210";
    }

    public static class  DentalToothCodes {
        public static final String TOOTH_CODE_5 = "5";
        public static final String TOOTH_CODE_6 = "6";
    }

    public static class DentalProcedureStatus {
        public static final String ALLOWED = "Allowed";
        public static final String REVIEW_REQUIRED = "Review Required";
    }

    public static class DentalPayeeType {
        public static final String ALTERNATE = "AlternatePayee";
        public static final String SERVICE_PROVIDER = "Provider";
        public static final String PRIMARY_INSURED = "PrimaryInsured";
    }

    public static class DentalOrthoFrequencyCd {
        public static final String ONE_TIME = "OneTime";
        public static final String MONTHLY = "Monthly";
    }

    public static class DentalPaymentScheduleState {
        public static final String ACTIVE = "Active";
    }

    public static class DentalAccumulatorType {
        public static final String INDIVIDUAL_DEDUCTIBLE = "IndividualDeductible";
        public static final String INDIVIDUAL_MAXIMUM = "IndividualMaximum";
    }
    public static class DentalAccumulatorProcedureCategory {
        public static final String BASIC = "Basic";
        public static final String MAJOR = "Major";
    }

    public static class DentalAccumulatorRenewalType {
        public static final String ANNUAL = "Annual";
    }

    public static class DentalAccumulatorNetworkType {
        public static final String INN = "INN";
    }

    public static class DentalSettlementAccumulatorType {
        public static final String INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_BASIC = "IndividualDeductible_INN_Annual_Basic";
        public static final String INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_MAJOR = "IndividualDeductible_INN_Annual_Major";
        public static final String INDIVIDUAL_MAXIMUM_INN_ANNUAL_BASIC = "IndividualMaximum_INN_Annual_Basic";
        public static final String INDIVIDUAL_MAXIMUM_INN_ANNUAL_MAJOR = "IndividualMaximum_INN_Annual_Major";
    }
}