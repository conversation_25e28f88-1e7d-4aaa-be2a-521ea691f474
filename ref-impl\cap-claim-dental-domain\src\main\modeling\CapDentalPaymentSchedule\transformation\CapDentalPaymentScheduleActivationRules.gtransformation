@CapEndpoint("verifyPaymentScheduleActivation")
@CapRawRequest
@Passthrough("schedule")
@KeyStrategy("PASSTHROUGH")
Transformation CapDentalPaymentScheduleActivationRules {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
        DentalInternal.CapUserCarryingEntity as userRequest
    }
    Output {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity
    }

    Var resolvedUserId is Ternary(Equals(userRequest.userId, Null()), CurrentUser().userId, LoadUserIdByUUID(userRequest.userId))
    Var user is SafeInvoke(resolvedUserId, ExtLink(AsExtLink("geroot://User/User//" + resolvedUserId)))
    Var authorityLevel is FlatMap(user.userProfiles[defaultUserProfile].authorityLevels[activeAuthority]<levelDesc>)
    Var openlRequest is createRequest()
    Var response is ExecuteRules("claim-dental-financial", "_api_paymentSchedule_activation", openlRequest)

    Attr paymentScheduleActivationResult is mapResults(schedule.paymentScheduleActivationResult, response)

    @Passthrough("paymentScheduleActivationResult")
    Producer mapResults(paymentScheduleActivationResult, response) {
        Attr activationStatus is response.activationStatus
        Attr activationMessages is response.activationMessages
    }

    Producer createRequest() {
        Attr userAuthority is FlatMap(toClaimAuthority(authorityLevel))
        Attr paymentSchedule is Root().schedule
    }

    Filter defaultUserProfile {
        defaultInd == true
    }

    Filter activeAuthority {
        PeriodOverlaps(nowPeriod(), Super())
    }

    Producer nowPeriod() {
        Attr startDate is Now()
        Attr endDate is IncreaseByDuration(Now(),"PT1S")
    }

    Producer toClaimAuthority(authority) {
        Attr authorityLevel is authority.level
        Attr typeCd is authority.typeCd
        Attr subTypeCd is authority.subTypeCd
    }

    Sort levelDesc {
        "level" -> "DESC"
    }

}