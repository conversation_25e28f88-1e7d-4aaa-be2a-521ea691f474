/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.adjudication.command.CapRequestSettlementAdjudicationHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementInitInput;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import com.eisgroup.genesis.factory.modeling.types.builder.CapSettlementBuilder;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.versioning.CreatesVersion;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Internal command handler for CapDentalSettlement create.
 * Used in CapSettlement init automated process
 */
public class CapDentalSettlementInitHandler extends CapRequestSettlementAdjudicationHandler<CapDentalSettlementInitInput, CapSettlement> {

    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapSettlement load(@Nonnull CapDentalSettlementInitInput request) {
        return Lazy.from(() -> CapSettlementBuilder.createRoot(modelResolver.getModelName(), modelResolver.getModelVersion())
                .build()).get();
    }

    @Nonnull
    @Override
    public CapSettlement save(@Nonnull CapDentalSettlementInitInput input, @Nonnull CapSettlement entity) {
        return super.save(input, entity);
    }
}
