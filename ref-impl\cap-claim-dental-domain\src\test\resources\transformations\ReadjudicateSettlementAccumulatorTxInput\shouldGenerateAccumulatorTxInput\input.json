{"_key": {"rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1}, "_type": "CapDentalSettlementEntity", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "2025-06-25T08:16:02.442Z", "settlementResult": {"proposal": "PAY", "entries": [{"calculationResult": {"procedureType": "Orthodontics", "charge": {"currency": "USD", "amount": 500}, "coveredFee": {"currency": "USD", "amount": 80}, "payableDeductible": {"currency": "USD", "amount": 0}, "procedureID": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5", "submittedCode": "D8010", "coinsuranceAmt": {"currency": "USD", "amount": 40}, "coveredCode": "D8010", "patientResponsibility": {"currency": "USD", "amount": 40}, "consideredFee": {"currency": "USD", "amount": 80}, "netBenefitAmount": {"currency": "USD", "amount": 40}, "coinsurancePercentage": 50, "_type": "CapDentalCalculationResultEntity", "_key": {"id": "050d2532-926e-3737-9855-90f1ea9025b5", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, "serviceSource": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5", "reservedAccumulators": [{"reservedAmount": {"currency": "USD", "amount": 40}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "c972f3be-5dcc-3546-b07b-0a2118e254c9", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, {"reservedAmount": {"currency": "USD", "amount": 40}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "bd07701e-8977-3ff0-b289-e7da732cdc78", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, {"accumulatorType": "OrthoMaximum", "reservedAmount": {"currency": "USD", "amount": 40}, "renewalType": "Lifetime", "appliesToProcedureCategories": ["Orthodontics"], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "d20a259e-6a8f-3a35-9a24-82625171e7c8", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, {"reservedAmount": {"currency": "USD", "amount": 40}, "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "34faa7f9-02f7-377c-bb4e-494e8c35cfb0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, {"accumulatorType": "IndividualDeductible", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "04f88a05-287f-3d50-b533-7a1b019ae1e0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, {"accumulatorType": "FamilyDeductible", "reservedAmount": {"currency": "USD", "amount": 0}, "renewalType": "Annual", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "0dce4346-a172-35ed-a082-f31f4ddfc8ea", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}], "status": {"flag": "Allowed", "fee": {"currency": "USD", "amount": 500}, "procedureID": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5", "submittedCode": "D8010", "coveredCode": "D8010", "_type": "CapDentalCalculationStatusEntity", "_key": {"id": "ed65998b-687c-3d02-91ba-0cea7fbcf74d", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd"}}, "_type": "CapDentalResultEntryEntity", "_key": {"id": "7c7d727b-3126-3ffa-bb1f-dbef9c4de6cd", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}], "payeeRef": "geroot://Provider/IndividualProvider//617b9520-2428-34df-b11b-060c230bd4ff", "messages": [{"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "91b3a46f-6e07-33c9-85ec-520a3d9dc81f", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "4493ecae-4049-3f90-ac9e-ebf49daf1805", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "bfcaff3d-dea5-320f-a7ab-9cf16e49a4ff", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "db522fb6-3775-3faf-82e1-c6aa9bc699af", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "1ded0648-1328-3500-a99b-45544be183e4", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "e17c966b-f7de-37e5-b231-279577ba42ef", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "c38c3d7e-e635-3e3c-96ce-2727c372b4e5", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "fbccc710-a90d-33fd-8dac-582b8f976000", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "b03298d9-8a4e-34a6-96d0-9244939b52eb", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "87b632d4-10fa-3aed-b88b-b9d8d8acbf11", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "e976da98-306e-3b48-9469-1cb67dc40cfe", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "8ce53e54-e82a-36cc-bb99-7ccf2d2efd8a", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "ea6822ea-ea21-3d1e-8387-38519a991645", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "476ad72c-80a0-3bb3-9b20-d647035b0258", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}, {"severity": "Warning", "code": "ATLEAST_ONE_FIELD", "message": "Should be at least one toothArea or toothCodes filled in", "_type": "MessageType", "_key": {"id": "dd42d3d8-c826-3b27-8f37-41a00f6aee08", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf"}}], "paymentAmount": {"currency": "USD", "amount": 40}, "_type": "CapDentalDecisionResultEntity", "_key": {"id": "cc0ff407-9c18-3f8d-bf88-9e3e9cc477bf", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484"}}, "settlementLossInfo": {"submittedProcedures": [{"masterPolicyInfo": [{"orthoWaitingPeriod": "6", "childMaxAgeCd": "26", "frequencyLimitations": {"basicLimitations": {"basicPeriodontalSurgery": "1in36Months", "basicStainlessSteelCrownsAgeLimit": "16", "basicStainlessSteelCrowns": "1in36Months", "_type": "CapPolicyBasicLimitationsEntity", "_key": {"id": "5fde01ff-9ea5-3604-927b-d72c73536472", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "12a2f4f5-3f8a-3ef1-a023-4689d01f6f52"}}, "preventiveLimitations": {"preventiveFluorideTreatment": "1in6Months", "preventiveFluorideTreatmentAgeLimit": "14", "preventiveOralEvaluations": "1in6Months", "preventiveBitewingRadiographs": "1in12Months", "_type": "CapPolicyPreventiveLimitationsEntity", "_key": {"id": "c419f3a5-ea54-38f7-b873-822ae3ba3405", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "12a2f4f5-3f8a-3ef1-a023-4689d01f6f52"}}, "majorLimitations": {"majorCrowns": "1in120Months", "majorDentureAdjustments": "1in12Months", "majorImplants": "1in120Months", "_type": "CapPolicyMajorLimitationsEntity", "_key": {"id": "974bde00-d7fc-3d51-ad2d-b2d14994fbc1", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "12a2f4f5-3f8a-3ef1-a023-4689d01f6f52"}}, "_type": "CapPolicyFrequencyLimitationsEntity", "_key": {"id": "12a2f4f5-3f8a-3ef1-a023-4689d01f6f52", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "a00994ac-be89-32f4-bdf3-2ca3db0d3a89"}}, "orthoINNCoinsurancePercent": 50, "basicWaitingPeriod": "6", "planName": "Low", "fullTimeStudentAgeCd": "26", "planCategory": "PPO", "serviceCategory": {"scFillings": "Basic", "scAllOtherRadiographs": "Preventive", "scImplantServices": "Major", "scSurgicalPeriodontics": "Basic", "scCrowns": "Major", "scFullMouthRadiographs": "Preventive", "scFluorides": "Preventive", "scBitewingRadiographs": "Preventive", "scStainlessSteelCrowns": "Basic", "scOralEvaluations": "Preventive", "scRootCanals": "Basic", "_type": "CapPolicyServiceCategoryEntity", "_key": {"id": "ff6a0cf3-151e-30bc-b8f4-2cc491a8fa26", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "a00994ac-be89-32f4-bdf3-2ca3db0d3a89"}}, "riskStateCd": "NY", "orthoOONCoinsurancePercent": 50, "preventWaitingPeriod": "0", "applyLateEntrantBenefitWaitingPeriods": true, "lateEntrantWaitingPeriodsDetails": [{"preventWaitingPeriod": "6", "basicWaitingPeriod": "6", "majorWaitingPeriod": "6", "_type": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity", "_key": {"id": "79225434-e5be-3d16-8438-e282a092f3af", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "a00994ac-be89-32f4-bdf3-2ca3db0d3a89"}}], "term": {"effectiveDate": "2024-07-01T00:00:00Z", "_type": "Term", "_key": {"id": "2d92d074-00a8-3e15-aa8a-0cbb4b5f179e", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "a00994ac-be89-32f4-bdf3-2ca3db0d3a89"}}, "majorWaitingPeriod": "12", "plan": "Low", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_type": "CapDentalPolicyInfoEntity", "_key": {"id": "a00994ac-be89-32f4-bdf3-2ca3db0d3a89", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5"}}], "dentist": {"dentistID": "**********", "providerTIN": "111111111", "_type": "CapDentalDentistEntity", "_key": {"id": "5240bb77-457a-34f3-a255-67019f39b96b", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5"}}, "certPolicyInfo": {"isOrthoCoverageIncluded": true, "basicWaitingPeriod": "6", "planName": "Low", "serviceCategory": {"scFillings": "Basic", "scAllOtherRadiographs": "Preventive", "scImplantServices": "Major", "scSurgicalPeriodontics": "Basic", "scCrowns": "Major", "scFullMouthRadiographs": "Preventive", "scFluorides": "Preventive", "scBitewingRadiographs": "Preventive", "scStainlessSteelCrowns": "Basic", "scOralEvaluations": "Preventive", "scRootCanals": "Basic", "_type": "CapPolicyServiceCategoryEntity", "_key": {"id": "239b5209-31a7-30ab-acd3-cf1611e37c11", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, "coinsurances": [{"coinsuranceOONPct": 80, "coinsuranceServiceType": "Preventive", "coinsuranceINPct": 80, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "762ef200-f0b8-396d-b539-063f1ba89aed", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, {"coinsuranceOONPct": 50, "coinsuranceServiceType": "Basic", "coinsuranceINPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "7a980987-2ef8-32cd-bd17-d0b49cc09151", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, {"coinsuranceOONPct": 50, "coinsuranceServiceType": "Major", "coinsuranceINPct": 50, "_type": "CapDentalPolicyInfoCoinsuranceEntity", "_key": {"id": "44a141f5-8ad6-3349-bcb8-6e49ec3c50b5", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}], "applyLateEntrantBenefitWaitingPeriods": true, "isImplantsMaximumAppliedTowardPlanMaximum": true, "lateEntrantWaitingPeriodsDetails": [{"preventWaitingPeriod": "6", "basicWaitingPeriod": "6", "majorWaitingPeriod": "6", "_type": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity", "_key": {"id": "1a84379b-a4f7-3c86-8d65-61cd3f6aba0b", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}], "term": {"effectiveDate": "2024-07-01T00:00:00Z", "_type": "Term", "_key": {"id": "224b7489-687c-3e9a-8f3b-47fcd56bda32", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, "majorWaitingPeriod": "12", "cosmeticWaitingPeriod": "6", "plan": "Low", "orthoWaitingPeriod": "6", "childMaxAgeCd": "26", "orthoDeductibleType": "None", "frequencyLimitations": {"basicLimitations": {"basicPeriodontalSurgery": "1in36Months", "basicStainlessSteelCrownsAgeLimit": "16", "basicStainlessSteelCrowns": "1in36Months", "_type": "CapPolicyBasicLimitationsEntity", "_key": {"id": "dfd2b67e-ddb7-32d8-86f7-f0e23dcde6f9", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "3d9f4602-b30e-35dc-be2b-4a4d8df23ed2"}}, "preventiveLimitations": {"preventiveFluorideTreatment": "1in6Months", "preventiveFluorideTreatmentAgeLimit": "14", "preventiveOralEvaluations": "1in6Months", "preventiveBitewingRadiographs": "1in12Months", "_type": "CapPolicyPreventiveLimitationsEntity", "_key": {"id": "ad6150da-5246-3079-9475-e60bce51b302", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "3d9f4602-b30e-35dc-be2b-4a4d8df23ed2"}}, "majorLimitations": {"majorCrowns": "1in120Months", "majorDentureAdjustments": "1in12Months", "majorImplants": "1in120Months", "_type": "CapPolicyMajorLimitationsEntity", "_key": {"id": "42a15552-ac84-3ec1-a29d-7b916d162461", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "3d9f4602-b30e-35dc-be2b-4a4d8df23ed2"}}, "_type": "CapPolicyFrequencyLimitationsEntity", "_key": {"id": "3d9f4602-b30e-35dc-be2b-4a4d8df23ed2", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, "insureds": [{"hireDate": "2000-01-01", "isMain": true, "relationshipToPrimaryInsuredCd": "Self", "insuredRoleNameCd": "PrimaryInsured", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "_type": "CapDentalPolicyInfoInsuredDetailsEntity", "_key": {"id": "b284d29f-81ef-3f4f-8669-164d4cc0ef56", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}], "orthoLateEntrantWaitingPeriod": "6", "orthoINNCoinsurancePercent": 50, "isImplantCoverageIncluded": true, "fullTimeStudentAgeCd": "26", "cosmeticDeductibleType": "None", "riskStateCd": "NY", "orthoOONCoinsurancePercent": 50, "preventWaitingPeriod": "0", "capPolicyId": "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/0b5f6922-a8bf-336c-b5b0-02a3231bd8f7", "isTmjCoverageIncluded": false, "waitingPeriodApplyTo": "NewAndExistingEmployees", "capPolicyVersionId": "capPolicy://PAS/gentity://PolicySummary/DNIndividual/policy/0b5f6922-a8bf-336c-b5b0-02a3231bd8f7/1", "tmjDeductibleType": "None", "enrollmentTypeCd": "AnnualEnrollment", "coverageEligibility": {"waitingPeriodAmount": 30, "waitingPeriodModeCd": "Days", "eligibilityTypeCd": "ExistingEmployees", "waitingPeriodDefCd": "1stMonthFollowingAmountAndMode", "_type": "CapPolicyEligibilityEntity", "_key": {"id": "93d35031-47a2-33ac-b86d-39c268727f10", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d"}}, "isCosmeticServicesIncluded": true, "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_type": "CapDentalPolicyInfoEntity", "_key": {"id": "bd6e6f15-783f-3d22-ac63-dbe216b0e45d", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5"}}, "_type": "CapDentalProcedureEntity", "diagnosisCodes": [], "surfaces": [], "toothCodes": [], "_key": {"id": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cb7dd9da-0989-370a-8f6a-b4dd60606651"}, "ortho": {"_type": "CapDentalOrthodonticEntity", "_key": {"id": "81b8cae2-6f12-41ed-80ce-526dce3a44f0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "985e5ab7-e3b3-422a-a0ab-bd3d0a3114e5"}, "orthoFrequencyCd": "Monthly", "orthoMonthQuantity": 11}, "predetInd": false, "quantity": 1, "dateOfService": "2024-08-01", "submittedFee": {"currency": "USD", "amount": 500}, "procedureCode": "D8010"}], "patient": {"patientID": "PA00001", "historyProcedures": [{"procedureStatus": "Allowed", "quantity": 1, "procedureCode": "D0120", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "9d6e4909-da29-315d-82d9-d529cc8b54d4", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1d54dd40-594e-3e16-95d8-562b1dd83aa2"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "0fad81ec-4016-3c5a-b4d7-6ad468f92a0e", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1d54dd40-594e-3e16-95d8-562b1dd83aa2"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "1d54dd40-594e-3e16-95d8-562b1dd83aa2", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D0120", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "da7ebd98-1a08-35db-8394-4d227642151e", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7969d5c6-4dd1-37cd-a113-b6cde9a8ac2b"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "c6e7573b-dbf3-36a9-b532-b936d4e860a0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7969d5c6-4dd1-37cd-a113-b6cde9a8ac2b"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "7969d5c6-4dd1-37cd-a113-b6cde9a8ac2b", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Allowed", "quantity": 1, "procedureCode": "D0140", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "3f930a6a-7aa3-37ae-a8d4-b56f747afda3", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "8383e35b-194a-33e8-ba2d-8e92a7f9c6ac"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "bea2137a-b64c-32db-a542-ac4ac6c488a3", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "8383e35b-194a-33e8-ba2d-8e92a7f9c6ac"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "8383e35b-194a-33e8-ba2d-8e92a7f9c6ac", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D0140", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "326560c2-2d8e-3253-9e99-f5142bd93367", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "ee0d00b3-345f-3453-8501-4e61a57791d7"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "e9ad687e-f289-3445-b329-99312d025a31", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "ee0d00b3-345f-3453-8501-4e61a57791d7"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "ee0d00b3-345f-3453-8501-4e61a57791d7", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D1206", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "3ef87afc-e345-3a80-9521-45f4b176e438", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "52bd6b84-ec7c-3e76-a4e8-185129705de2"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "7e7d83df-f6d3-3789-a82f-96980cacd817", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "52bd6b84-ec7c-3e76-a4e8-185129705de2"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "52bd6b84-ec7c-3e76-a4e8-185129705de2", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D1206", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "e2416b17-8ef6-3e33-89cf-330c915bb9c4", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "0ffdbb26-c073-349e-a6c1-8c9f25339fd8"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "45e8443e-99f5-3b2b-91a3-8c134d4e3831", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "0ffdbb26-c073-349e-a6c1-8c9f25339fd8"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "0ffdbb26-c073-349e-a6c1-8c9f25339fd8", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D1208", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "f65f92cf-2cfe-3471-8e78-49752515e1f6", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cce8aeea-2c1a-3c9b-ae4a-4200fb2ff9b0"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "15b6fdb5-1272-36fc-80e8-1edd1a67fbb9", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cce8aeea-2c1a-3c9b-ae4a-4200fb2ff9b0"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "cce8aeea-2c1a-3c9b-ae4a-4200fb2ff9b0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Denied", "quantity": 1, "procedureCode": "D1208", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "ebbaf501-53c6-3055-b618-673a4b9a8605", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "6bc726ac-50a1-3908-91f5-09574e697b69"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "8a29a1b0-b0ea-348f-9f18-38ca4eb33ebe", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "6bc726ac-50a1-3908-91f5-09574e697b69"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "6bc726ac-50a1-3908-91f5-09574e697b69", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Allowed", "quantity": 1, "procedureCode": "D0220", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "f58b0ca2-0684-3989-91bb-0ecce6fead0d", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "2d6606e1-2a09-3920-b439-8586285dad08"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "745fa7c5-975e-3b04-adc1-79cbb81e154e", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "2d6606e1-2a09-3920-b439-8586285dad08"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "2d6606e1-2a09-3920-b439-8586285dad08", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Combined - Max Covered", "quantity": 1, "procedureCode": "D0220", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "8a57f87c-aa8f-3dbd-b581-969722ecfede", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "5a3abc6f-874e-3693-ae45-cbdd30bb00e8"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "2a6c9c04-032a-378f-9bb9-9dbcb08b89ca", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "5a3abc6f-874e-3693-ae45-cbdd30bb00e8"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "5a3abc6f-874e-3693-ae45-cbdd30bb00e8", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Combined - Max Covered", "quantity": 1, "procedureCode": "D0230", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "8bd77f36-b43d-326b-a679-dce8b170604a", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "20efbd40-20b7-3e3d-941c-484a1fb31358"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "2c241e5e-77c2-342a-9dad-8895357ae3eb", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "20efbd40-20b7-3e3d-941c-484a1fb31358"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "20efbd40-20b7-3e3d-941c-484a1fb31358", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Combined - Max Covered", "quantity": 1, "procedureCode": "D0230", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "38e2ab26-4261-3d6d-a00f-d7e172d732e7", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7402c79f-8bb7-3a97-aeed-3ba2d3177ef1"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "550eb118-722a-37ea-8cd0-161879aa6b73", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "7402c79f-8bb7-3a97-aeed-3ba2d3177ef1"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "7402c79f-8bb7-3a97-aeed-3ba2d3177ef1", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Combined - Max Covered", "quantity": 1, "procedureCode": "D0270", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "f30d12b7-4818-3151-b0d9-fe6982ed4537", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "79f87235-1161-3de9-9746-f473d4ce94a9"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "d15d485f-77ed-323d-83bc-62785616c09b", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "79f87235-1161-3de9-9746-f473d4ce94a9"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "79f87235-1161-3de9-9746-f473d4ce94a9", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"procedureStatus": "Combined - Max Covered", "quantity": 1, "procedureCode": "D0270", "surfaces": [], "lossNumber": "DN249", "preauthorization": {"_type": "CapDentalPreauthorizationEntity", "_key": {"id": "6f905841-05b9-3683-aae6-4aafbe6cd92c", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "b0f08cce-082b-3d34-97af-600005e2ee5d"}}, "toothCodes": [], "predetInd": false, "dateOfService": "2025-06-01", "dentist": {"dentistID": "**********", "_type": "CapDentalDentistEntity", "_key": {"id": "83206f70-d538-3c2f-b16a-1f2b32973da0", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "b0f08cce-082b-3d34-97af-600005e2ee5d"}}, "_type": "CapDentalProcedureEntity", "_key": {"id": "b0f08cce-082b-3d34-97af-600005e2ee5d", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "birthDate": "1970-01-01", "accumulators": [{"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "CosmeticMaximum", "appliesToProcedureCategories": ["CosmeticServices"], "renewalType": "Lifetime", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "fbd51958-1b1e-3a12-ac50-17ac337f8725", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "CosmeticMaximum", "appliesToProcedureCategories": ["CosmeticServices"], "renewalType": "Lifetime", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "38bc80e5-f26f-3477-83c1-ff7e7f406389", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 50, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "160ab4f8-da1d-3ea7-a0f0-247d74f32ea5", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 50, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "IndividualDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "943083b8-829c-35de-9c9f-162c7ecb24d4", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 150, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "ef175f2d-d3fd-3804-a89a-d378b591763c", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 150, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Major", "Basic", "Preventive"], "renewalType": "Annual", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "12a7e88b-73f6-32a4-a074-2188fc0b640a", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 750, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Implants"], "renewalType": "Annual", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "2172f193-fd12-35c8-87b2-be09e3e7cd92", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 750, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "FamilyDeductible", "appliesToProcedureCategories": ["Implants"], "renewalType": "Annual", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "cdbc638d-bbd2-344c-89b4-6f7e5202e934", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "appliesToProcedureCategories": [], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "b5c22c3d-edee-3682-a3cd-fb32c7230a57", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "TMJMaximum", "appliesToProcedureCategories": ["TMJ"], "renewalType": "Lifetime", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "8be24aa1-3182-31f7-ac01-931742c510ba", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "TMJMaximum", "appliesToProcedureCategories": ["TMJ"], "renewalType": "Lifetime", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "0b06a729-3bdb-3e49-8608-60c3cc230663", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "appliesToProcedureCategories": [], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "ee81fa30-19ba-3056-9360-9426765ee547", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "OrthoMaximum", "appliesToProcedureCategories": ["Orthodontics"], "renewalType": "Lifetime", "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "9d195cef-ecb8-3779-a633-451d37de2d73", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "accumulatorType": "OrthoMaximum", "appliesToProcedureCategories": ["Orthodontics"], "renewalType": "Lifetime", "networkType": "OON", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "8a4e8784-e54c-3bfb-ad94-e24aa226e67d", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}, {"remainingAmount": {"amount": 1000, "currency": "USD"}, "reservedAmount": {"amount": 0, "currency": "USD"}, "appliesToProcedureCategories": [], "networkType": "INN", "_type": "CapDentalAccumulatorEntity", "_key": {"id": "7e24540f-c516-334c-967b-da4b5ec385b8", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "4c776997-45e8-31f6-8ecc-aab9b090edf9"}}], "_type": "CapDentalPatientEntity", "_key": {"id": "4c776997-45e8-31f6-8ecc-aab9b090edf9", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cb7dd9da-0989-370a-8f6a-b4dd60606651"}}, "claimData": {"initialDateOfService": "2024-08-01T00:00:00Z", "_type": "CapDentalClaimDataEntity", "cob": [], "digitalImageNumbers": [], "missingTooths": [], "providerFees": [], "_key": {"id": "1a92da66-02b5-4ef1-969d-f96acaec21f4", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "cb7dd9da-0989-370a-8f6a-b4dd60606651"}, "treatmentReason": {"_type": "CapDentalTreatmentReasonEntity", "_key": {"id": "88c5d115-57d5-4a89-8fcd-c467b80c71ce", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a92da66-02b5-4ef1-969d-f96acaec21f4"}}, "providerRole": {"_type": "CapProviderRole", "roleCd": ["IndividualProvider"], "_key": {"id": "5d4d7924-bcfa-45df-8a8e-a14490eb25b7", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a92da66-02b5-4ef1-969d-f96acaec21f4"}, "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "providerLink": "geroot://Provider/IndividualProvider//617b9520-2428-34df-b11b-060c230bd4ff"}, "source": "NONEDI", "policyholderRole": {"_type": "CapPolicyholderRole", "roleCd": ["Member"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "_key": {"id": "082e62d7-f699-39c0-81a3-56ca0acf9007", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a92da66-02b5-4ef1-969d-f96acaec21f4"}}, "patientRole": {"_type": "CapPatientRole", "roleCd": ["SubjectOfClaims"], "registryId": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2", "_key": {"id": "1b77a358-1b45-334d-9cf1-680ac9371023", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a92da66-02b5-4ef1-969d-f96acaec21f4"}}, "receivedDate": "2025-06-25", "payeeType": "Provider", "transactionType": "OrthodonticServices"}, "_type": "CapDentalClaimInfoEntity", "_key": {"id": "cb7dd9da-0989-370a-8f6a-b4dd60606651", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484"}}, "accessTrackInfo": {"_key": {"id": "f3ca0bbe-1af6-3e51-ac59-a91c296b5ad2", "rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484"}, "_type": "AccessTrackInfo", "createdBy": "service-account-system", "createdOn": "2025-06-25T08:16:02.142Z", "updatedBy": "service-account-system", "updatedOn": "2025-06-25T08:16:02.142Z"}, "state": "Approved", "settlementNumber": "S179", "claimLossIdentification": {"_uri": "capMock://CapDentalLoss/067c0c29-f9ca-465f-8303-02653330336d/1"}, "settlementDetail": {"_key": {"rootId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "revisionNo": 1, "parentId": "1a6634f3-1591-43c2-bc8d-71bcf98ef484", "id": "1c1074f2-fc0d-360a-9233-ff0136da7a74"}, "_type": "CapDentalSettlementDetailEntity", "_modelName": "CapDentalSettlement", "_modelVersion": "1", "_modelType": "CapSettlement", "_timestamp": "2025-06-25T08:16:02.437Z"}}