/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.input;

import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceItemEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.test.utils.JsonUtils;
import org.junit.Test;

import javax.money.MonetaryAmount;

import java.util.Collection;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalBalanceInitInputTest {

    private static final String ORIGIN_SOURCE = "originSourceUri";
    private static final String PAYEE_URI = "payeeUri";

    private CapDentalBalanceInitInput input = new CapDentalBalanceInitInput(JsonUtils.load(
            "requestSamples/balanceInitInput.json"));

    @Test
    public void shouldReturnOriginSource() {
        //when
        EntityLink<RootEntity> result = input.getOriginSource();

        //then
        assertThat(result.getURIString(), equalTo(ORIGIN_SOURCE));
    }

    @Test
    public void shouldReturnPayee() {
        //when
        EntityLink<RootEntity> result = input.getPayee();

        //then
        assertThat(result.getURIString(), equalTo(PAYEE_URI));
    }

    @Test
    public void shouldReturnTotalBalanceAmount() {
        //when
        MonetaryAmount result = input.getTotalBalanceAmount();

        //then
        assertThat(result.getNumber().intValue(), equalTo(100));
        assertThat(result.getCurrency().getCurrencyCode(), equalTo("USD"));
    }

    @Test
    public void shouldReturnBalanceItems() {
        //when
        Collection<CapDentalBalanceItemEntity> result = input.getBalanceItems();

        //then
        assertThat(result.size(), equalTo(1));
    }
}
