/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.databinding.serializer.annotation.JsonMonetarySerializeFormat;
import com.eis.automation.v20.cap.entity.common.modeling.impl.PeriodModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.IPeriodModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalOverrideServiceValueModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

public class CapDentalOverrideServiceValueModel extends TypeModel implements ICapDentalOverrideServiceValueModel {

    private Money overridePaymentInterestAmount;
    private Money overrideDeductible;
    private Money overrideMaximumAmount;
    private Integer overrideGracePeriod;
    private IPeriodModel overrideEligibilityPeriod;
    private Integer overridePaymentInterestDays;
    private String overrideStudentDependentStatus;
    private String overrideFeeSchedule;
    private Integer overrideMajorWaitingPeriod;
    private Integer overrideLateEntrantWaitingPeriod;
    private String overrideServiceCategory;

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getOverridePaymentInterestAmount() {
        return overridePaymentInterestAmount;
    }

    public void setOverridePaymentInterestAmount(Money overridePaymentInterestAmount) {
        this.overridePaymentInterestAmount = overridePaymentInterestAmount;
    }

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getOverrideDeductible() {
        return overrideDeductible;
    }

    public void setOverrideDeductible(Money overrideDeductible) {
        this.overrideDeductible = overrideDeductible;
    }

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getOverrideMaximumAmount() {
        return overrideMaximumAmount;
    }

    public void setOverrideMaximumAmount(Money overrideMaximumAmount) {
        this.overrideMaximumAmount = overrideMaximumAmount;
    }

    public Integer getOverrideGracePeriod() {
        return overrideGracePeriod;
    }

    public void setOverrideGracePeriod(Integer overrideGracePeriod) {
        this.overrideGracePeriod = overrideGracePeriod;
    }

    @JsonSerialize(as = PeriodModel.class)
    public IPeriodModel getOverrideEligibilityPeriod() {
        return overrideEligibilityPeriod;
    }

    @JsonDeserialize(as = PeriodModel.class)
    public void setOverrideEligibilityPeriod(IPeriodModel overrideEligibilityPeriod) {
        this.overrideEligibilityPeriod = overrideEligibilityPeriod;
    }

    public Integer getOverridePaymentInterestDays() {
        return overridePaymentInterestDays;
    }

    public void setOverridePaymentInterestDays(Integer overridePaymentInterestDays) {
        this.overridePaymentInterestDays = overridePaymentInterestDays;
    }

    public String getOverrideStudentDependentStatus() {
        return overrideStudentDependentStatus;
    }

    public void setOverrideStudentDependentStatus(String overrideStudentDependentStatus) {
        this.overrideStudentDependentStatus = overrideStudentDependentStatus;
    }

    public String getOverrideFeeSchedule() {
        return overrideFeeSchedule;
    }

    public void setOverrideFeeSchedule(String overrideFeeSchedule) {
        this.overrideFeeSchedule = overrideFeeSchedule;
    }

    public Integer getOverrideMajorWaitingPeriod() {
        return overrideMajorWaitingPeriod;
    }

    public void setOverrideMajorWaitingPeriod(Integer overrideMajorWaitingPeriod) {
        this.overrideMajorWaitingPeriod = overrideMajorWaitingPeriod;
    }

    public Integer getOverrideLateEntrantWaitingPeriod() {
        return overrideLateEntrantWaitingPeriod;
    }

    public void setOverrideLateEntrantWaitingPeriod(Integer overrideLateEntrantWaitingPeriod) {
        this.overrideLateEntrantWaitingPeriod = overrideLateEntrantWaitingPeriod;
    }

    public String getOverrideServiceCategory() {
        return overrideServiceCategory;
    }

    public void setOverrideServiceCategory(String overrideServiceCategory) {
        this.overrideServiceCategory = overrideServiceCategory;
    }
}
