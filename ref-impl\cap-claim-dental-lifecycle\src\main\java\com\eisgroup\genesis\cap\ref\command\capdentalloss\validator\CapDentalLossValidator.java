/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.ALTERNATE_PAYEE_REGISTRY_ID_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PATIENT_REGISTRY_ID_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PROVIDER_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PROVIDER_REGISTRY_ID_LINK_INCORRECT;

import java.util.Optional;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalClaimDataEntity;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalDetailEntity;
import com.eisgroup.genesis.factory.modeling.types.LossDetail;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Class for dental specific loss validations.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalLossValidator {

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalLossValidator(CapDentalLinkValidator capDentalLinkValidator) {
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    public Streamable<ErrorHolder> validateLinks(LossDetail lossDetail) {
        return Optional.ofNullable(lossDetail)
                .filter(CapDentalDetailEntity.class::isInstance)
                .map(CapDentalDetailEntity.class::cast)
                .map(CapDentalDetailEntity::getClaimData)
                .map(claimData -> Streamable.concat(validateProvider(claimData),
                    validateProviderLink(claimData),
                    validatePatient(claimData),
                    validatePolicyHolder(claimData),
                    validateAlternatePayee(claimData)))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validateProvider(CapDentalClaimDataEntity claimData) {
        return Optional.ofNullable(claimData)
            .map(CapDentalClaimDataEntity::getProviderRole)
            .map(providerRole -> new EntityLink<>(RootEntity.class, providerRole.getRegistryId()))
            .map(provider -> capDentalLinkValidator.validateLink(provider, "Customer", PROVIDER_REGISTRY_ID_LINK_INCORRECT.builder().build()))
            .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validateProviderLink(CapDentalClaimDataEntity claimData) {
        return Optional.ofNullable(claimData)
                .map(CapDentalClaimDataEntity::getProviderRole)
                .map(providerRole -> new EntityLink<>(RootEntity.class, providerRole.getProviderLink()))
                .map(provider -> capDentalLinkValidator.validateLink(provider, "Provider", PROVIDER_LINK_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePolicyHolder(CapDentalClaimDataEntity claimData) {
        return Optional.ofNullable(claimData)
                .map(CapDentalClaimDataEntity::getPolicyholderRole)
                .map(policyholderRole -> new EntityLink<>(RootEntity.class, policyholderRole.getRegistryId()))
                .map(policyholder -> capDentalLinkValidator.validateLink(policyholder, "Customer", POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePatient(CapDentalClaimDataEntity claimData) {
        return Optional.ofNullable(claimData)
                .map(CapDentalClaimDataEntity::getPatientRole)
                .map(patientRole -> new EntityLink<>(RootEntity.class, patientRole.getRegistryId()))
                .map(patient -> capDentalLinkValidator.validateLink(patient, "Customer", PATIENT_REGISTRY_ID_LINK_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validateAlternatePayee(CapDentalClaimDataEntity claimData) {
        return Optional.ofNullable(claimData)
                .map(CapDentalClaimDataEntity::getAlternatePayeeRole)
                .map(alternatePayeeRole -> new EntityLink<>(RootEntity.class, alternatePayeeRole.getRegistryId()))
                .map(alternatePayee -> capDentalLinkValidator.validateLink(alternatePayee, "Customer", ALTERNATE_PAYEE_REGISTRY_ID_LINK_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    public static class CapDentalLossValidationErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalLossValidationErrorDefinition PROVIDER_REGISTRY_ID_LINK_INCORRECT = new CapDentalLossValidationErrorDefinition(
            "cdlv001", "provider registryId URL is not valid.");
        public static final CapDentalLossValidationErrorDefinition PROVIDER_LINK_INCORRECT = new CapDentalLossValidationErrorDefinition(
                "cdlv002", "provider registryId URL is not valid.");
        public static final CapDentalLossValidationErrorDefinition POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT = new CapDentalLossValidationErrorDefinition(
                "cdlv003", "policyHolder registryId URL is not valid.");
        public static final CapDentalLossValidationErrorDefinition PATIENT_REGISTRY_ID_LINK_INCORRECT = new CapDentalLossValidationErrorDefinition(
                "cdlv004", "patient registryId URL is not valid.");
        public static final CapDentalLossValidationErrorDefinition ALTERNATE_PAYEE_REGISTRY_ID_LINK_INCORRECT = new CapDentalLossValidationErrorDefinition(
                "cdlv005", "alternatePayee registryId URL is not valid.");

        public CapDentalLossValidationErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
