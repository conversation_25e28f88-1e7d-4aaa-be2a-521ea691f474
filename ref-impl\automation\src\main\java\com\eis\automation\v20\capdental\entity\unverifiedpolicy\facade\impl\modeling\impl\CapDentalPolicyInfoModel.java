/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.impl.CapPolicyInfoModel;
import com.eis.automation.v20.cap.entity.common.modeling.impl.TermModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ITermModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDeductibleDetailModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalMaximumModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapPolicyInfoCoinsuranceModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapPolicyInfoLateEntrantWaitingPeriodModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPolicyInfoModel extends CapPolicyInfoModel implements ICapDentalPolicyInfoModel {

    @JsonProperty("isImplantsMaximumAppliedTowardPlanMaximum")
    private Boolean isImplantsMaximumAppliedTowardPlanMaximum;
    private Integer majorOONCoinsurancePercent;
    private Integer preventiveOONCoinsurancePercent;
    private ITermModel patientTerm;
    private ITermModel pcdTerm;
    private String basicWaitingPeriod;
    private String planName;
    private String planCategory;
    private String pcdId;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate policyPaidToDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate policyPaidToDateWithGracePeriod;
    private Boolean applyLateEntrantBenefitWaitingPeriods;
    private String majorWaitingPeriod;
    private String plan;
    private Integer basicINNCoinsurancePercent;
    private String orthoWaitingPeriod;
    private String childMaxAgeCd;
    @JsonProperty("isWaitingPeriodWaived")
    private Boolean isWaitingPeriodWaived;
    private Integer nonStandardChildAgeLimit;
    private Integer orthoINNCoinsurancePercent;
    private Integer preventiveINNCoinsurancePercent;
    private Integer majorINNCoinsurancePercent;
    private Integer basicOONCoinsurancePercent;
    private Integer orthoOONCoinsurancePercent;
    private Integer standardChildAgeLimit;
    private String fullTimeStudentAgeCd;
    private String preventWaitingPeriod;
    private String implantsWaitingPeriod;
    private Boolean waiveOONInd;
    private List<ICapPolicyInfoLateEntrantWaitingPeriodModel> lateEntrantWaitingPeriodsDetails;
    private List<ICapPolicyInfoCoinsuranceModel> coinsurances;
    private List<ICapDentalMaximumModel> dentalMaximums;
    private List<ICapDeductibleDetailModel> deductibleDetails;

    public Integer getMajorOONCoinsurancePercent() {
        return majorOONCoinsurancePercent;
    }

    public void setMajorOONCoinsurancePercent(Integer majorOONCoinsurancePercent) {
        this.majorOONCoinsurancePercent = majorOONCoinsurancePercent;
    }

    public Integer getPreventiveOONCoinsurancePercent() {
        return preventiveOONCoinsurancePercent;
    }

    public void setPreventiveOONCoinsurancePercent(Integer preventiveOONCoinsurancePercent) {
        this.preventiveOONCoinsurancePercent = preventiveOONCoinsurancePercent;
    }

    @JsonSerialize(as = TermModel.class)
    public ITermModel getPatientTerm() {
        return patientTerm;
    }

    @JsonDeserialize(as = TermModel.class)
    public void setPatientTerm(ITermModel patientTerm) {
        this.patientTerm = patientTerm;
    }

    @JsonSerialize(as = TermModel.class)
    public ITermModel getPcdTerm() {
        return pcdTerm;
    }

    @JsonDeserialize(as = TermModel.class)
    public void setPcdTerm(ITermModel pcdTerm) {
        this.pcdTerm = pcdTerm;
    }

    public String getBasicWaitingPeriod() {
        return basicWaitingPeriod;
    }

    public void setBasicWaitingPeriod(String basicWaitingPeriod) {
        this.basicWaitingPeriod = basicWaitingPeriod;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public String getPlanCategory() {
        return planCategory;
    }

    public void setPlanCategory(String planCategory) {
        this.planCategory = planCategory;
    }

    public String getPcdId() {
        return pcdId;
    }

    public void setPcdId(String pcdId) {
        this.pcdId = pcdId;
    }

    public LocalDate getPolicyPaidToDate() {
        return policyPaidToDate;
    }

    public void setPolicyPaidToDate(LocalDate policyPaidToDate) {
        this.policyPaidToDate = policyPaidToDate;
    }

    public LocalDate getPolicyPaidToDateWithGracePeriod() {
        return policyPaidToDateWithGracePeriod;
    }

    public void setPolicyPaidToDateWithGracePeriod(LocalDate policyPaidToDateWithGracePeriod) {
        this.policyPaidToDateWithGracePeriod = policyPaidToDateWithGracePeriod;
    }

    public Boolean getApplyLateEntrantBenefitWaitingPeriods() {
        return applyLateEntrantBenefitWaitingPeriods;
    }

    public void setApplyLateEntrantBenefitWaitingPeriods(Boolean applyLateEntrantBenefitWaitingPeriods) {
        this.applyLateEntrantBenefitWaitingPeriods = applyLateEntrantBenefitWaitingPeriods;
    }

    public String getMajorWaitingPeriod() {
        return majorWaitingPeriod;
    }

    public void setMajorWaitingPeriod(String majorWaitingPeriod) {
        this.majorWaitingPeriod = majorWaitingPeriod;
    }

    public String getPlan() {
        return plan;
    }

    public void setPlan(String plan) {
        this.plan = plan;
    }

    public Integer getBasicINNCoinsurancePercent() {
        return basicINNCoinsurancePercent;
    }

    public void setBasicINNCoinsurancePercent(Integer basicINNCoinsurancePercent) {
        this.basicINNCoinsurancePercent = basicINNCoinsurancePercent;
    }

    public String getOrthoWaitingPeriod() {
        return orthoWaitingPeriod;
    }

    public void setOrthoWaitingPeriod(String orthoWaitingPeriod) {
        this.orthoWaitingPeriod = orthoWaitingPeriod;
    }

    public String getChildMaxAgeCd() {
        return childMaxAgeCd;
    }

    public void setChildMaxAgeCd(String childMaxAgeCd) {
        this.childMaxAgeCd = childMaxAgeCd;
    }

    @JsonProperty("isWaitingPeriodWaived")
    public Boolean getWaitingPeriodWaived() {
        return isWaitingPeriodWaived;
    }

    @JsonProperty("isWaitingPeriodWaived")
    public void setWaitingPeriodWaived(Boolean waitingPeriodWaived) {
        isWaitingPeriodWaived = waitingPeriodWaived;
    }

    @JsonProperty("isImplantsMaximumAppliedTowardPlanMaximum")
    public Boolean getImplantsMaximumAppliedTowardPlanMaximum() {
        return isImplantsMaximumAppliedTowardPlanMaximum;
    }

    @JsonProperty("isImplantsMaximumAppliedTowardPlanMaximum")
    public void setImplantsMaximumAppliedTowardPlanMaximum(Boolean implantsMaximumAppliedTowardPlanMaximum) {
        isImplantsMaximumAppliedTowardPlanMaximum = implantsMaximumAppliedTowardPlanMaximum;
    }

    public Integer getNonStandardChildAgeLimit() {
        return nonStandardChildAgeLimit;
    }

    public void setNonStandardChildAgeLimit(Integer nonStandardChildAgeLimit) {
        this.nonStandardChildAgeLimit = nonStandardChildAgeLimit;
    }

    public Integer getOrthoINNCoinsurancePercent() {
        return orthoINNCoinsurancePercent;
    }

    public void setOrthoINNCoinsurancePercent(Integer orthoINNCoinsurancePercent) {
        this.orthoINNCoinsurancePercent = orthoINNCoinsurancePercent;
    }

    public Integer getPreventiveINNCoinsurancePercent() {
        return preventiveINNCoinsurancePercent;
    }

    public void setPreventiveINNCoinsurancePercent(Integer preventiveINNCoinsurancePercent) {
        this.preventiveINNCoinsurancePercent = preventiveINNCoinsurancePercent;
    }

    public Integer getMajorINNCoinsurancePercent() {
        return majorINNCoinsurancePercent;
    }

    public void setMajorINNCoinsurancePercent(Integer majorINNCoinsurancePercent) {
        this.majorINNCoinsurancePercent = majorINNCoinsurancePercent;
    }

    public Integer getBasicOONCoinsurancePercent() {
        return basicOONCoinsurancePercent;
    }

    public void setBasicOONCoinsurancePercent(Integer basicOONCoinsurancePercent) {
        this.basicOONCoinsurancePercent = basicOONCoinsurancePercent;
    }

    public Integer getOrthoOONCoinsurancePercent() {
        return orthoOONCoinsurancePercent;
    }

    public void setOrthoOONCoinsurancePercent(Integer orthoOONCoinsurancePercent) {
        this.orthoOONCoinsurancePercent = orthoOONCoinsurancePercent;
    }

    public Integer getStandardChildAgeLimit() {
        return standardChildAgeLimit;
    }

    public void setStandardChildAgeLimit(Integer standardChildAgeLimit) {
        this.standardChildAgeLimit = standardChildAgeLimit;
    }

    public String getFullTimeStudentAgeCd() {
        return fullTimeStudentAgeCd;
    }

    public void setFullTimeStudentAgeCd(String fullTimeStudentAgeCd) {
        this.fullTimeStudentAgeCd = fullTimeStudentAgeCd;
    }

    public String getPreventWaitingPeriod() {
        return preventWaitingPeriod;
    }

    public void setPreventWaitingPeriod(String preventWaitingPeriod) {
        this.preventWaitingPeriod = preventWaitingPeriod;
    }

    public String getImplantsWaitingPeriod() {
        return implantsWaitingPeriod;
    }

    public void setImplantsWaitingPeriod(String implantsWaitingPeriod) {
        this.implantsWaitingPeriod = implantsWaitingPeriod;
    }

    public Boolean getWaiveOONInd() {
        return waiveOONInd;
    }

    public void setWaiveOONInd(Boolean waiveOONInd) {
        this.waiveOONInd = waiveOONInd;
    }

    @JsonSerialize(as = List.class, contentAs = CapPolicyInfoLateEntrantWaitingPeriodModel.class)
    public List<ICapPolicyInfoLateEntrantWaitingPeriodModel> getLateEntrantWaitingPeriodsDetails() {
        return lateEntrantWaitingPeriodsDetails;
    }

    @JsonDeserialize(as = List.class, contentAs = CapPolicyInfoLateEntrantWaitingPeriodModel.class)
    public void setLateEntrantWaitingPeriodsDetails(List<ICapPolicyInfoLateEntrantWaitingPeriodModel> lateEntrantWaitingPeriodsDetails) {
        this.lateEntrantWaitingPeriodsDetails = lateEntrantWaitingPeriodsDetails;
    }

    @JsonSerialize(as = List.class, contentAs = CapPolicyInfoCoinsuranceModel.class)
    public List<ICapPolicyInfoCoinsuranceModel> getCoinsurances() {
        return coinsurances;
    }

    @JsonDeserialize(as = List.class, contentAs = CapPolicyInfoCoinsuranceModel.class)
    public void setCoinsurances(List<ICapPolicyInfoCoinsuranceModel> coinsurances) {
        this.coinsurances = coinsurances;
    }

    @Override
    public String entityName() {
        return "CapDentalUnverifiedPolicy";
    }


    @JsonSerialize(as = List.class, contentAs = CapDentalMaximumModel.class)
    public List<ICapDentalMaximumModel> getDentalMaximums() {
        return dentalMaximums;
    }
    @JsonDeserialize(as = List.class, contentAs = CapDentalMaximumModel.class)
    public void setDentalMaximums(List<ICapDentalMaximumModel> dentalMaximums) {
        this.dentalMaximums = dentalMaximums;
    }

    @JsonSerialize(as = List.class, contentAs = CapDeductibleDetailModel.class)
    public List<ICapDeductibleDetailModel> getDeductibleDetails() {
        return deductibleDetails;
    }
    @JsonDeserialize(as = List.class, contentAs = CapDeductibleDetailModel.class)
    public void setDeductibleDetails(List<ICapDeductibleDetailModel> deductibleDetails) {
        this.deductibleDetails = deductibleDetails;
    }
}
