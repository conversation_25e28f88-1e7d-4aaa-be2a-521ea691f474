@Passthrough
@KeyStrategy("PASSTHROUGH")
@PayloadMutator
@CommandListener("Execute", "adjudicateSettlement")
Transformation CapDentalSettlementEnrichment {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        CapDentalSettlement.CapDentalSettlementEntity
    }
    Var settlementVar is settlement
    Var loss is ExtLink(settlement.claimLossIdentification)
    Attr settlementLossInfo is mapClaimInfo(loss.lossDetail)

    Var accumulatorContainer is New() {
        Var policyHolder is ExtLink(SafeInvoke(loss.lossDetail.claimData.policyholderRole.registryId, AsExtLink(loss.lossDetail.claimData.policyholderRole.registryId)))
        Var customer is ExtLink(SafeInvoke(loss.lossDetail.claimData.policyholderRole.registryId, AsExtLink(loss.lossDetail.claimData.policyholderRole.registryId)))

        Var policy is ExtLink(AsExtLink(loss.policyId))
        Var accumulatorUri is "CapAccumulator://container/" + customer._modelName + "/" + customer._key.rootId + "/" + policy._modelName + "/" + policy._key.rootId
        Var allAccumulators is ExtLink(AsExtLink(accumulatorUri)).accumulators
        Var filterAccumulator is allAccumulators[filterByPartyOrResource]


        Attr accumulators is SafeInvoke(filterAccumulator, mapAccumulator(filterAccumulator))
    }

    Producer mapClaimInfo(detail) {
        Attr claimData is detail.claimData
        Attr patient is mapPatientFromCustomer(detail)
        //GENESIS-366362
        Attr submittedProcedures is mapProcedures(detail.submittedProcedures, detail.claimData.receivedDate, AsExtLink(detail.claimData.providerRole.providerLink))
    }

    Producer mapPatientFromCustomer(detail){
        //GENESIS-366362
        Var patientCustomer is ExtLink(AsExtLink(detail.claimData.patientRole.registryId))
        Attr registryId is detail.claimData.patientRole.registryId
        Attr patientID is "PA00001"
        Attr birthDate is patientCustomer.details.person.birthDate
        Attr disabilities is patientCustomer.details.disabilities
        Attr accumulators is SafeInvoke(accumulatorContainer.accumulators, accumulatorContainer.accumulators)

        //GENESIS-366362
        Var pastSettlements is Query("claim_dental_claim_patient", detail.claimData.patientRole.registryId)
        Var resolvedEntities is resolveExtLinks(pastSettlements).resolvedExtLink
        Var transformedPatientHistories is SafeInvoke(resolvedEntities, FlatMap(transformSettlementToPatientHistory(resolvedEntities[FilterBySettlement]).history))

        //GENESIS-366362
        Var patientHistories is Query("claim_dental_history_patient",AsExtLink(detail.claimData.patientRole.registryId))
        Var resolvedPatientHistories is resolveExtLinks(patientHistories).resolvedExtLink[filterNotCanceled]

        Var allHistories is FlatMap(transformedPatientHistories,resolvedPatientHistories)
        Attr historyProcedures is SafeInvoke(allHistories, transformPatientHistoryToProcedure(allHistories))
    }

    Producer resolveExtLinks(pastSettlement) {
        Attr resolvedExtLink is ExtLink(pastSettlement)
    }

    Filter FilterBySettlement {
        Equals(_type, "CapDentalSettlementEntity")
        Not(_key.rootId == settlementVar._key.rootId)
    }

    Producer transformSettlementToPatientHistory(pastProcedureWithSettlement) {
        Attr history is SafeInvoke(pastProcedureWithSettlement, Transform("CapDentalSettlementToPatientHistory", pastProcedureWithSettlement))
    }

    Producer transformPatientHistoryToProcedure(patientHistory) {
        Attr dentist is New(){
            Attr dentistID is Super().patientHistory.patientHistoryData.providerData.dentistID
        }

        Attr dateOfService is AsDate(patientHistory.patientHistoryData.serviceData.DOSDate)
        Attr procedureCode is patientHistory.patientHistoryData.serviceData.cdtCoveredCd
        Attr toothArea is patientHistory.patientHistoryData.serviceData.toothArea
        Attr toothCodes is patientHistory.patientHistoryData.serviceData.toothCodes
        Attr surfaces is patientHistory.patientHistoryData.serviceData.surfaces
        Attr quantity is patientHistory.patientHistoryData.serviceData.quantity
        Attr procedureStatus is patientHistory.patientHistoryData.serviceData.decision
        Attr predetInd is patientHistory.patientHistoryData.serviceData.isPredet
        Attr lossNumber is patientHistory.patientHistoryData.claimData.claimNumber

        Attr preauthorization is New() {
            Attr isProcedureAuthorized is Super().patientHistory.patientHistoryData.serviceData.isProcedureAuthorized
            Attr authorizationPeriod is Super().patientHistory.patientHistoryData.serviceData.authorizationPeriod
        }
    }


    @Passthrough("inProcedure")
    Producer mapProcedures(inProcedure, receivedDate, providerLink) {
        Var versionOnDate is Ternary(inProcedure.predetInd == true, Elvis(receivedDate, Now()), Elvis(inProcedure.dateOfService, Now()))
        Var policy is SearchPolicyVersion(loss.policyId, versionOnDate)
        Var isUnverifiedPolicy is SafeInvoke(policy, Equals(policy._modelName, "CapDentalUnverifiedPolicy"))
        Var isCertPolicy is SafeInvoke(policy, Equals(policy._modelName, "DNIndividual"))
        Var providerInfo is LoadProvider(providerLink)

        Attr certPolicyInfo is SafeInvoke(policy, Ternary(Or(isUnverifiedPolicy, isCertPolicy),
            transformPolicy(policy, inProcedure).transformed,
            Null()))
        Attr masterPolicyInfo is SafeInvoke(policy, Ternary(isCertPolicy,
            transformMasterPolicy(ExtLink(policy.masterLink.source), inProcedure).transformed,
            Null()))

        Attr dentist is SafeInvoke(providerInfo,
            Ternary(providerInfo.provider._modelName == "OrganizationProvider",
                mapOrganizationDentist(providerInfo),
                Ternary(providerInfo.provider._modelName == "IndividualProvider",
                    mapIndividualDentist(providerInfo),
                    Null())))
    }

    Producer mapIndividualDentist(providerInfo) {
        Attr dentistID is providerInfo.provider.providerNpi
        Attr providerTIN is providerInfo.customer.details.person.taxId
    }

    Producer mapOrganizationDentist(providerInfo) {
        Attr dentistID is providerInfo.provider.providerNpi
        Attr providerTIN is providerInfo.customer.details.legalEntity.legalId
    }

    Producer transformPolicy(policy, inProcedure) {
        Var transformationName is policy._modelName + "To" + settlementVar._modelName
        Attr transformed is SafeInvoke(transformationName, Transform(transformationName, input(policy), createProcedureEntity(inProcedure)))
    }

    Producer transformMasterPolicy(masterPolicy, inProcedure) {
        Var transformationName is masterPolicy._modelName + "To" + settlementVar._modelName
        Attr transformed is SafeInvoke(transformationName, Transform(transformationName, input(masterPolicy), createProcedureEntity(inProcedure)))
    }

    // due to Transform not supporting external model inputs
    @Passthrough
    Producer input(policy) {
         Attr _type is "CapPolicyEntity"
         Attr _modelName is "CapPolicyHolder"
    }

    Producer createAccumulators(accumulatorType, renewalType, appliesToProcedureCategory, networkType, remainingAmount) {
        Attr accumulatorType is accumulatorType
        Attr renewalType is renewalType
        Attr appliesToProcedureCategory is appliesToProcedureCategory
        Attr networkType is networkType
        Attr remainingAmount is createMoney(remainingAmount)
    }

    Producer mapAccumulator(accumulator) {
        Attr remainingAmount is createMoney(accumulator.remainingAmount)
        Attr reservedAmount is createMoney(accumulator.reservedAmount)
        Var fallback is New() {} // Has to be created outside of the Ternary loop or it throws an error.
        Var values is Ternary(accumulator.type == "IndividualDeductible_Annual_Dental", accumulatorValues("IndividualDeductible","Annual"),
                        Ternary(accumulator.type == "IndividualDeductible_Lifetime_Dental", accumulatorValues("IndividualDeductible","Lifetime"),
                            Ternary(accumulator.type == "FamilyDeductible_Annual_Dental", accumulatorValues("FamilyDeductible","Annual"),
                                Ternary(accumulator.type == "FamilyDeductible_Lifetime_Dental", accumulatorValues("FamilyDeductible","Lifetime"),
                                    Ternary(accumulator.type == "ImplantsMaximum_Annual_Implants", accumulatorValues("ImplantsMaximum","Annual"),
                                        Ternary(accumulator.type == "ImplantsMaximum_Lifetime_Implants", accumulatorValues("ImplantsMaximum","Lifetime"),
                                           Ternary(accumulator.type == "OrthoMaximum_Annual_Orthodontics", accumulatorValues("OrthoMaximum","Annual"),
                                               Ternary(accumulator.type == "OrthoMaximum_Lifetime_Orthodontics", accumulatorValues("OrthoMaximum","Lifetime"),
                                                   Ternary(accumulator.type == "OrthoDeductible_Annual_Orthodontics", accumulatorValues("OrthoDeductible","Annual"),
                                                       Ternary(accumulator.type == "OrthoDeductible_Lifetime_Orthodontics", accumulatorValues("OrthoDeductible","Lifetime"),
                                                           Ternary(accumulator.type == "TMJMaximum_Annual_TMJ", accumulatorValues("TMJMaximum","Annual"),
                                                               Ternary(accumulator.type == "TMJMaximum_Lifetime_TMJ", accumulatorValues("TMJMaximum","Lifetime"),
                                                                   Ternary(accumulator.type == "TMJDeductible_Annual_TMJ", accumulatorValues("TMJDeductible","Annual"),
                                                                       Ternary(accumulator.type == "TMJDeductible_Lifetime_TMJ", accumulatorValues("TMJDeductible","Lifetime"),
                                                                           Ternary(accumulator.type == "CosmeticMaximum_Annual_CosmeticServices", accumulatorValues("CosmeticMaximum","Annual"),
                                                                               Ternary(accumulator.type == "CosmeticMaximum_Lifetime_CosmeticServices", accumulatorValues("CosmeticMaximum","Lifetime"),
                                                                                   Ternary(accumulator.type == "CosmeticDeductible_Annual_CosmeticServices", accumulatorValues("CosmeticDeductible","Annual"),
                                                                                       Ternary(accumulator.type == "CosmeticDeductible_Lifetime_CosmeticServices", accumulatorValues("CosmeticDeductible","Lifetime"),
                                                                                           Ternary(accumulator.type == "IndividualMaximum_Annual_Dental", accumulatorValues("IndividualMaximum","Annual"),
                                                                                               Ternary(accumulator.type == "IndividualMaximum_Lifetime_Dental", accumulatorValues("IndividualMaximum","Lifetime"),

                                            fallback
                                           ))))))))))))))))))))
        Attr appliesToProcedureCategories is accumulator.extension.appliedToServices
        Attr accumulatorType is values.accumulatorType2
        Attr renewalType is values.renewalType2
        Attr networkType is accumulator.extension.networkType
        Attr term is accumulator.extension.term
    }

    // Passthrough not possible with primitives, mapping manually
    Producer accumulatorValues(accumulatorType1, renewalType1){
        Attr accumulatorType2 is accumulatorType1
        Attr renewalType2 is renewalType1
    }

    Producer createMoney(remainingOONAmount) {
        Attr amount is remainingOONAmount
        Attr currency is "USD"
    }

    @Passthrough
    Producer createProcedureEntity(inProcedure) {
        Attr _type is inProcedure._type
        Attr _modelName is settlementVar._modelName
    }

    Producer toPolicy(ipolicy) {
        Attr policyNumber is ipolicy.policyNumber
        Attr _modelName is ipolicy._modelName
        Attr _rootId is ipolicy._key.rootId
    }

    Filter filterNotCanceled {
        !Equals(state, "Canceled")
    }

    Filter filterByPartyOrResource {
        party._uri == Root().loss.lossDetail.claimData.patientRole.registryId || (Equals(Null(), party) && resource._uri == Root().loss.policyId)
    }
}
