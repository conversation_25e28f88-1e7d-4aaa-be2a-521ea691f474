package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.common.command.api.Execute;
import com.eisgroup.genesis.cap.loss.command.population.CapDefaultLossPopulator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossInitInput;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.test.utils.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossInitHandlerTest {

    @InjectMocks
    private CapDentalLossInitHandler handler;

    @Mock
    private ModelResolver modelResolver;

    @Mock
    private CapDefaultLossPopulator capDefaultLossPopulator;

    @Mock
    private EntityNumberGenerator generator;

    @Mock
    private Execute executeAction;

    @Before
    public void setUp() {
        ModelRepository<DomainModel> repo = ModelRepositoryFactory.getRepositoryFor(DomainModel.class);
        when(modelResolver.resolveModel(DomainModel.class)).thenAnswer(args -> repo.getActiveModel("CapDentalLoss"));
    }

    @Test
    public void testHandlerExecute() {
        //given
        CapDentalLossInitInput input = new CapDentalLossInitInput(JsonUtils.load(
                "requestSamples/lossInitInput.json"));
        CapDentalLossEntity entity = (CapDentalLossEntity) ModelInstanceFactory.createRootInstance("CapDentalLoss", "1");
        when(capDefaultLossPopulator.populateAttributes(input, entity)).thenReturn(Lazy.of(entity));
        when(generator.generate(anyString())).thenReturn("1");
        when(executeAction.perform(any(), any(), any())).thenReturn(Lazy.of(entity));

        //when
        CapLoss result = handler.execute(input, entity);

        //then
        assertTrue(result instanceof CapDentalLossEntity);
        assertThat(((CapDentalLossEntity) result).getClaimType(), equalTo("dental"));
    }

    @Test
    public void testReturnHandlerName() {
        assertThat(handler.getName(), equalTo("initLoss"));
    }
}
