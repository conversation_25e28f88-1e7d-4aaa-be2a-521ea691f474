/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.input;

import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceItemEntity;
import com.google.gson.JsonObject;

import javax.money.MonetaryAmount;
import java.util.Collection;

/**
 * Dental balance update request.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalBalanceUpdateInput extends IdentifierRequest {

    private static final String TOTAL_BALANCE_AMOUNT = "totalBalanceAmount";
    private static final String BALANCE_ITEMS = "balanceItems";

    public CapDentalBalanceUpdateInput(JsonObject original) {
        super(original);
    }

    public MonetaryAmount getTotalBalanceAmount() {
        return getMonetaryAmount(TOTAL_BALANCE_AMOUNT);
    }

    public Collection<CapDentalBalanceItemEntity> getBalanceItems() {
        return getCollection(CapDentalBalanceItemEntity.class, BALANCE_ITEMS);
    }
}
