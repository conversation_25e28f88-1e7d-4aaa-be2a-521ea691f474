
Transformation CapDentalSettlementToPatientHistory {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        *CapDentalPatientHistory.CapDentalPatientHistoryEntity as history
    }

    Mapping history is settlement.settlementLossInfo.submittedProcedures {
        Var procedure is  New() {
            Attr key is Super()._key
            Attr dateOfService is Super().dateOfService
            Attr toothArea is Super().toothArea
            Attr toothCodes is Super().toothCodes
            Attr surfaces is Super().surfaces
            Attr quantity is Super().quantity
            Attr predetInd is Super().predetInd
            Attr preauthorization is Super().preauthorization
            Attr dentist is Super().dentist
        }
        Attr patientHistoryData is mapPatientHistoryData(procedure, Super().settlement)

        Producer mapPatientHistoryData(procedure,settlement) {
            Attr claimData is mapClaimData(procedure,settlement)
            Attr serviceData is mapServiceData(procedure,settlement)
            Attr providerData is mapProviderData(procedure,settlement)
        }

        Producer mapServiceData(procedure,settlement) {
            Attr DOSDate is AsTime(procedure.dateOfService)
            Attr toothArea is procedure.toothArea
            Attr toothCodes is procedure.toothCodes
            Attr surfaces is procedure.surfaces
            Attr quantity is procedure.quantity
            Attr isPredet is procedure.predetInd

            Attr isProcedureAuthorized is  procedure.preauthorization.isProcedureAuthorized
            Attr authorizationPeriod is procedure.preauthorization.authorizationPeriod
            Filter FilterById {
                 Equals(serviceSource, Super().procedure.key.id)
            }
            Var entry is First(settlement.settlementResult.entries[FilterById])
            Attr cdtSubmittedCd is entry.calculationResult.submittedCode
            Attr cdtCoveredCd is entry.calculationResult.coveredCode
            Attr procedureType is entry.calculationResult.procedureType
            Attr submittedAmount is entry.calculationResult.charge
            Attr deductibleAmount is entry.calculationResult.payableDeductible
            Attr coinsuranceAmount is entry.calculationResult.coinsuranceAmt
            Attr copayAmount is entry.calculationResult.copay
            Attr benefitAmount is entry.calculationResult.netBenefitAmount
            Attr decision is entry.status.flag

            Attr remarkCodes is entry.status.remarkMessages.remarkCode

            Attr consideredAmount is entry.calculationResult.consideredFee
            Attr coveredAmount is entry.calculationResult.coveredFee
        }



        Producer mapProviderData(procedure,settlement) {
            //GENESIS-366362
            Attr provider is AsExtLink(settlement.settlementLossInfo.claimData.providerRole.providerLink)
            Var providerInfo is ExtLink(AsExtLink(settlement.settlementLossInfo.claimData.providerRole.providerLink))
            Attr providerBusinessName is providerInfo.businessName

            Attr providerTIN is	procedure.dentist.providerTIN
            Attr inOutNetwork is procedure.dentist.inOutNetwork
            Attr dentistID is procedure.dentist.dentistID
        }

        Producer mapClaimData(procedure,settlement) {
            Attr claim is settlement.claimLossIdentification
            Var capLoss is ExtLink(settlement.claimLossIdentification)
            Attr claimNumber is	capLoss.lossNumber
            Attr policy	is capLoss.policyId

            //TODO we are supposed to take it from masterPolicyInfo but the mapping from Policy is not fully done, CapDentalPolicyInfoEntity	planCategory
            Attr planCategory is "PPO"
            Attr policyNumber is capLoss.policy.policyNumber

            //GENESIS-366362
            Attr patient is AsExtLink(settlement.settlementLossInfo.claimData.patientRole.registryId)
            Attr patientNumber is ExtLink(AsExtLink(settlement.settlementLossInfo.claimData.patientRole.registerId)).customerNumber
            Attr digitalImageNumbers is settlement.settlementLossInfo.claimData.digitalImageNumbers
            Attr remark	is settlement.settlementLossInfo.claimData.remark
        }

    }

}
