/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.input.CapDentalBalanceUpdateInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepository;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceEntity;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.google.gson.JsonObject;
import org.hamcrest.CoreMatchers;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalBalanceUpdateHandlerTest {

    @InjectMocks
    private CapDentalBalanceUpdateHandler handler;

    @Mock
    private ModelResolver modelResolver;

    @Mock
    private CapDentalBalanceRepository capDentalBalanceRepository;

    @Before
    public void setUp() {
        when(modelResolver.getModelName()).thenReturn("CapDentalBalance");
    }

    @Test
    public void shouldLoadEntity() {
        //given
        CapDentalBalanceUpdateInput input = new CapDentalBalanceUpdateInput(new JsonObject());
        CapDentalBalanceEntity entity = (CapDentalBalanceEntity) ModelInstanceFactory.createRootInstance("CapDentalBalance", "1");
        when(capDentalBalanceRepository.load(any(), any())).thenReturn(Lazy.of(entity));

        //when
        CapDentalBalanceEntity result = handler.load(input);

        //then
        assertThat(result.getModelFactory().getModelName(), equalTo("CapDentalBalance"));
    }

    @Test
    public void testHandlerExecute() {
        //given
        CapDentalBalanceUpdateInput input = new CapDentalBalanceUpdateInput(JsonUtils.load(
                "requestSamples/balanceUpdateInput.json"));
        CapDentalBalanceEntity entity = (CapDentalBalanceEntity) ModelInstanceFactory.createRootInstance("CapDentalBalance", "1");

        //when
        CapDentalBalanceEntity result = handler.execute(input, entity);

        //then
        assertThat(result.getTotalBalanceAmount().getNumber().intValue(), CoreMatchers.equalTo(100));
        assertThat(result.getTotalBalanceAmount().getCurrency().getCurrencyCode(), CoreMatchers.equalTo("USD"));
        assertThat(result.getBalanceItems().size(), CoreMatchers.equalTo(1));
    }

    @Test
    public void shouldSaveEntity() {
        //given
        CapDentalBalanceUpdateInput input = new CapDentalBalanceUpdateInput(new JsonObject());
        CapDentalBalanceEntity entity = (CapDentalBalanceEntity) ModelInstanceFactory.createRootInstance("CapDentalBalance", "1");
        when(capDentalBalanceRepository.save(entity)).thenReturn(Lazy.of(entity));

        //when
        handler.save(input, entity);

        //then
        verify(capDentalBalanceRepository).save(entity);
    }

    @Test
    public void testReturnHandlerName() {
        //when
        String result = handler.getName();

        //then
        assertThat(result, equalTo("updateBalance"));
    }
}
