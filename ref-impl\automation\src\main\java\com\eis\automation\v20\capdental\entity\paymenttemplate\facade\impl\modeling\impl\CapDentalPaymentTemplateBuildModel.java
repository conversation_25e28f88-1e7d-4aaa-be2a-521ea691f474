/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl;

import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;

import java.util.List;

public class CapDentalPaymentTemplateBuildModel extends TypeModel implements ICapDentalPaymentTemplateBuildModel {

    private UriModel originSource;
    private List<UriModel> settlements;

    public UriModel getOriginSource() {
        return originSource;
    }

    public void setOriginSource(UriModel originSource) {
        this.originSource = originSource;
    }

    public List<UriModel> getSettlements() {
        return settlements;
    }

    public void setSettlements(List<UriModel> settlements) {
        this.settlements = settlements;
    }
}
