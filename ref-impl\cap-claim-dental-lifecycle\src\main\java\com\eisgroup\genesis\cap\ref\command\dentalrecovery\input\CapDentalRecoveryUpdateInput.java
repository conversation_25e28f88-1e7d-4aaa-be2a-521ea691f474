/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalrecovery.input;

import javax.money.MonetaryAmount;

import com.eisgroup.genesis.cap.financial.command.payment.input.CapPaymentUpdateInput;
import com.google.gson.JsonObject;

/**
 * Input for recovery update.
 *
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalRecoveryUpdateInput extends CapPaymentUpdateInput {

    private static final String PAYMENT_NET_AMOUNT = "paymentNetAmount";

    public CapDentalRecoveryUpdateInput(JsonObject original) {
        super(original);
    }

    public MonetaryAmount getPaymentNetAmount() {
        return getMonetaryAmount(PAYMENT_NET_AMOUNT);
    }
}
