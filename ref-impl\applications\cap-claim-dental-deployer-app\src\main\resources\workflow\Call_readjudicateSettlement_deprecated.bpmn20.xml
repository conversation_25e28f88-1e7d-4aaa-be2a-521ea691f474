<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="readjudicateSettlementCall" name="Call readjudicateSettlement" isExecutable="true">
    <startEvent id="startAdjudicateClaim" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="openClaimPendLoss" name="call readjudicateSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelName}" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="readjudicateSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'claimLossIdentification': {'_uri': '${_uri}'},'entity': {  ${settlementTimestamp} '_modelName': 'CapDentalSettlement','_modelVersion': '1','_modelType': 'CapSettlement','_type': 'CapDentalSettlementDetailEntity'}}" target="payload"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="settlementCreationResult"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <endEvent id="end"></endEvent>
    <sequenceFlow id="sid-A90CC629-0007-4B18-918B-DE11B6F72C95" sourceRef="startAdjudicateClaim" targetRef="openClaimPendLoss"></sequenceFlow>
    <sequenceFlow id="sid-8E79974D-5A1F-4F0E-B9A3-929F08DCE790" sourceRef="openClaimPendLoss" targetRef="end"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_readjudicateSettlementCall">
    <bpmndi:BPMNPlane bpmnElement="readjudicateSettlementCall" id="BPMNPlane_readjudicateSettlementCall">
      <bpmndi:BPMNShape bpmnElement="startAdjudicateClaim" id="BPMNShape_startAdjudicateClaim">
        <omgdc:Bounds height="30.0" width="30.0" x="330.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="openClaimPendLoss" id="BPMNShape_openClaimPendLoss">
        <omgdc:Bounds height="80.0" width="105.5" x="570.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="958.5" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-A90CC629-0007-4B18-918B-DE11B6F72C95" id="BPMNEdge_sid-A90CC629-0007-4B18-918B-DE11B6F72C95" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="52.75" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="359.94999873104683" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="465.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="465.0" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="570.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8E79974D-5A1F-4F0E-B9A3-929F08DCE790" id="BPMNEdge_sid-8E79974D-5A1F-4F0E-B9A3-929F08DCE790" flowable:sourceDockerX="52.75" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="675.4499999999671" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="817.0" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="817.0" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="958.5" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>