/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.service;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.json.VariableEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.wrapper.JsonWrapperFactory;
import com.eisgroup.genesis.json.wrapper.JsonWrapperFactoryProvider;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.executor.CommandExecutionContext;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.model.Variation;
import com.eisgroup.genesis.util.GsonUtil;
import com.eisgroup.genesis.versioning.CreatesVersion;
import com.eisgroup.genesis.versioning.Version;
import com.eisgroup.genesis.versioning.VersionCriteria;
import com.eisgroup.genesis.versioning.VersioningReadRepository;
import com.eisgroup.genesis.versioning.VersioningService;
import com.eisgroup.genesis.versioning.VersioningWriteRepository;
import com.eisgroup.genesis.versioning.predicates.UniqueVersionPredicate;
import org.reflections.ReflectionUtils;

import javax.annotation.Nonnull;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Workaround due to DefaultVersioningService expecting RootEntity, but ProductVersioningCommandExecutorListener handles JsonEntity.
 * In case ProductCommandHandler is implemented for ModelEntity(which is not RootEntity) class cass exception occurs in DefaultVersioningService
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalVersioningService<P extends JsonEntity, V extends Version> implements VersioningService<P, V> {

    public static final String SAVE_METHOD = "save";
    public static final String EXECUTE_METHOD = "execute";

    private final Map<String, Optional<Class<V>>> versionTypeCache = new ConcurrentHashMap<>();
    private final JsonWrapperFactory jsonWrapperFactory = JsonWrapperFactoryProvider.getJsonWrapperFactory();

    private final VersioningReadRepository<V> versioningReadRepository;
    private final VersioningWriteRepository<V> versioningWriteRepository;

    public CapDentalVersioningService(VersioningReadRepository<V> versioningReadRepository,
                                    VersioningWriteRepository<V> versioningWriteRepository) {
        this.versioningReadRepository = versioningReadRepository;
        this.versioningWriteRepository = versioningWriteRepository;
    }

    @Nonnull
    @Override
    public <H extends CommandHandler<?, ?>> Lazy<P> createEntityRevision(@Nonnull P payload, @Nonnull Class<H> handlerClass,
                                                                         Variation variation) {
        Optional<Class<V>> versionTypeClass = getVersionTypeClass(handlerClass);
        if (versionTypeClass.isEmpty()) {
            return Lazy.of(payload);
        }

        RootEntity originalEntity = origin((RootEntity) payload);
        UUID rootId = originalEntity.getKey().getRootId();

        var domainModel = resolveDomainModel((RootEntity) payload);
        var copy = (P) ModelInstanceFactory.createRootInstance(domainModel.getName(), domainModel.getVersion(),
                variation, GsonUtil.copy(payload.toJson()));

        return Optional.of(payload)
                .filter(entity -> new UniqueVersionPredicate(originalEntity).isSatisfied((RootEntity) entity))
                .map(uniqueEntity -> resolveNextRevision(rootId, domainModel, variation, versionTypeClass.get())
                        .flatMap(revisionNumber -> {
                            copy.toJson().add(BaseKey.ATTRIBUTE_NAME, new RootEntityKey(rootId, revisionNumber).toJson());
                            KeyTraversalUtil.traverseRoot(copy.toJson(), domainModel);
                            return Lazy.of(copy);
                        }))
                .orElse(Lazy.of(payload));
    }

    @Nonnull
    @Override
    public <H extends CommandHandler<?, ?>> Lazy<V> writeVersion(@Nonnull P payload, @Nonnull Class<H> handlerClass,
                                                                  Variation variation) {
        if (!(payload instanceof RootEntity)){
            return Lazy.empty();
        }
        Optional<Class<V>> versionTypeClass = getVersionTypeClass(handlerClass);
        if (versionTypeClass.isEmpty()) {
            return Lazy.empty();
        }

        var domainModel = resolveDomainModel((RootEntity) payload);
        String schemaName = resolveSchemeName(domainModel, variation);

        return versioningWriteRepository.writeAsync(schemaName, versionTypeClass.get(), (RootEntity)payload);
    }

    /**
     * Checks if {@link CreatesVersion} annotation is used in current command handler and tries to resolve version type class from it.
     * Each time when it is executed for new command handler it adds result to cache.
     * It can also be used as indicator to check if standard versioning mechanism is enabled for given command handler.
     *
     * @param handlerClass command handler class
     * @param <H>          command handler type
     * @return {@link Optional} holding version type class. Otherwise returns {@link Optional#empty()} (standard versioning is disabled).
     */
    private <H extends CommandHandler<?, ?>> Optional<Class<V>> getVersionTypeClass(Class<H> handlerClass) {
        String handlerClassName = handlerClass.getName();

        return versionTypeCache.computeIfAbsent(handlerClassName, s -> ReflectionUtils.getAllMethods(handlerClass, m -> {
                    if (ProductCommandHandler.class.isAssignableFrom(handlerClass)) {
                        return SAVE_METHOD.equals(m.getName()) && m.isAnnotationPresent(CreatesVersion.class);
                    } else {
                        return EXECUTE_METHOD.equals(m.getName()) && m.isAnnotationPresent(CreatesVersion.class);
                    }
                }).stream()
                .findFirst()
                .map(method -> method.getAnnotation(CreatesVersion.class))
                .map(CreatesVersion::value)
                .map(type -> (Class<V>) type));
    }

    private RootEntity origin(RootEntity payload) {
        final var currentInstance = CommandExecutionContext.getCurrentInstance();
        if (currentInstance == null || currentInstance.getEntitySnapshot(ProductCommandHandler.LOADED_HINT).isEmpty()) {
            throw new IllegalStateException("Could not resolve original entity for creating new revision of payload: " + payload.getKey());
        }
        final var jsonEntity = currentInstance.getEntitySnapshot(ProductCommandHandler.LOADED_HINT).orElseThrow();
        final RootEntity original = (RootEntity) jsonWrapperFactory.wrap(jsonEntity.toJson(), payload.getClass());

        final RootEntityKey originalKey = original.getKey();
        final RootEntityKey key = payload.getKey();

        if (originalKey == null || !originalKey.equals(key) || !getVariation(payload).equals(getVariation(original))) {
            throw new IllegalStateException("Could not resolve original entity for creating new revision of payload: " + payload.getKey());
        }
        return original;
    }

    private Variation getVariation(RootEntity entity) {
        if (entity instanceof VariableEntity) {
            return ((VariableEntity) entity).getVariation();
        } else {
            return Variation.INVARIANT;
        }
    }

    protected Lazy<Integer> resolveNextRevision(UUID rootId, DomainModel domainModel, Variation variation, Class<V> versionTypeClass) {
        String schemaName = ModeledEntitySchemaResolver.getSchemaNameUsing(domainModel, variation);

        return versioningReadRepository.loadAllVersion(schemaName, new VersionCriteria<>(rootId), versionTypeClass)
            .map(version -> version.getKey().getEntityRevision())
            .sorted().findLast().or(() -> Lazy.of(1))
            .map(currentMax -> currentMax + 1);
    }

    private String resolveSchemeName(DomainModel domainModel, Variation variation) {
        return ModeledEntitySchemaResolver.getSchemaNameUsing(domainModel, variation);
    }

    private DomainModel resolveDomainModel(RootEntity entity) {
        var modelResolver = new ModelResolver(entity.getModelType(), entity.getModelName(), entity.getModelVersion());
        return modelResolver.resolveModel(DomainModel.class);
    }

}
