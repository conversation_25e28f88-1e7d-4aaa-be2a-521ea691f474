/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;


import com.eisgroup.genesis.facade.endpoint.command.CommandEndpoint;
import org.junit.Before;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalPaymentDefinitionFacadeTest {

    private static final String MODEL_NAME = "CapDentalPaymentDefinition";

    private static final String MODEL_TYPE = "CapPayment";

    private CapDentalPaymentDefinitionFacade facade;

    @Before
    public void setup() {
        facade = new CapDentalPaymentDefinitionFacade();
    }

    @Test
    public void shouldReturnCorrectEndpoints() {
        // when
        var result = facade.getEndpoints();
        // then
        assertThat(result, notNullValue());
        assertThat(result.stream().anyMatch(endoint -> CommandEndpoint.class.isAssignableFrom(endoint.getClass())), equalTo(true));
    }

    @Test
    public void shouldReturnModelName() {
        assertThat(facade.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(facade.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnVersion() {
        assertThat(facade.getFacadeVersion(), equalTo(1));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(facade.getModelVersion(), equalTo("1"));
    }

}
