/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.modeling.impl.CapBasePaymentTemplateModel;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.modeling.interf.ICapBasePaymentDetailsTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentTemplateModel extends CapBasePaymentTemplateModel implements ICapDentalPaymentTemplateModel {

    @JsonSerialize(as = CapDentalPaymentDetailsTemplateModel.class)
    public ICapBasePaymentDetailsTemplateModel getPaymentDetailsTemplate() {
        return super.getPaymentDetailsTemplate();
    }

    @JsonDeserialize(as = CapDentalPaymentDetailsTemplateModel.class)
    public void setPaymentDetailsTemplate(ICapBasePaymentDetailsTemplateModel paymentDetailsTemplate) {
        super.setPaymentDetailsTemplate(paymentDetailsTemplate);
    }

    @JsonSerialize(as = CapDentalPaymentDetailsTemplateModel.class)
    public ICapBasePaymentDetailsTemplateModel getEntity() {
        return super.getEntity();
    }

    @JsonDeserialize(as = CapDentalPaymentDetailsTemplateModel.class)
    public void setEntity(ICapBasePaymentDetailsTemplateModel entity) {
        super.setEntity(entity);
    }

    @Override
    public String entityName() {
        return "CapDentalPaymentTemplate";
    }

    @Override
    public String endpointName() {
        return "CapPaymentTemplate";
    }
}
