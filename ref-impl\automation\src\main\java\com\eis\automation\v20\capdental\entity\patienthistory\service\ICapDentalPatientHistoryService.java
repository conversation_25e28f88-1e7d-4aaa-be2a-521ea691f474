/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.ICapDentalPatientHistory;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;

public interface ICapDentalPatientHistoryService extends ITestDataService, IFacadeService<ICapDentalPatientHistory>  {

    ICapDentalPatientHistoryModel initPatientHistory(ICapDentalPatientHistoryWrapperModel patientHistoryWrapperModel);

    ICapDentalPatientHistoryWrapperModel createPatientHistoryModel(ICustomerModel customerModel);

    ICapDentalPatientHistoryModel loadPatientHistory(ICapDentalPatientHistoryModel patientHistoryModel);
}
