// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"CapDentalPaymentTemplateIndex",
  "name":"CapDentalPaymentTemplateIndex",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalPaymentTemplateIdxEntity"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapDentalPaymentTemplateIdxEntity":{
      "attributes":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"originSource",
          "type":{
            "type":"STRING"
          }
        },
        "paymentTemplate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      {
                        "@class":"java.lang.Long",
                        "value":"0"
                      }
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"paymentTemplate",
          "type":{
            "type":"STRING"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.cap.factory.features.Persistable":{
          "keyspace":"CapDentalPaymentTemplate"
        },
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.Persistable":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "CapDentalPaymentTemplate"
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentTemplateIdxEntity",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalPaymentTemplateIndex {
    export type Variations = never


    export class CapDentalPaymentTemplateIdxEntity extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentTemplateIdxEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentTemplateIndex'
        readonly _modelType: string = 'CapDentalPaymentTemplateIndex'
        readonly _modelVersion?: string = '1'
        readonly originSource?: string
        readonly paymentTemplate?: string
        readonly state?: string
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalPaymentTemplateIdxEntity, ()=> new CapDentalPaymentTemplateIdxEntity)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}