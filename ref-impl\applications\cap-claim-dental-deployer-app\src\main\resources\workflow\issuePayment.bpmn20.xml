<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" xmlns:design="http://flowable.org/design" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" design:palette="flowable-process-palette">
  <process id="dentalIssuePayment" name="dentalIssuePayment" isExecutable="true">
    <serviceTask id="dispatchOutboundPayment" name="Dispatch outbound payment(Payment Hub)" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter source="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="{&quot;payment&quot;:  ${dispatchRequestPayload.body.success.request}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="OutboundPayment" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="dispatch" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="_dispatchPaymentPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="requestIssuePaymentFailedCommand" name="Request Issue Payment Failed" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter source="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="{&quot;_key&quot; : ${_requestIssuePayload._key}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="requestIssueFailPayment" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="payment" target="_variation" targetType="string"></flowable:eventInParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="issuePaymentCommand" name="Issue payment" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter source="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="{&quot;_key&quot; : ${_requestIssuePayload._key}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="issuePayment" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="payment" target="_variation" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="_issuedPaymentPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="sid-381CEA0A-D258-4856-A393-8848A30FCE44" name="Generate Dispatch Request" flowable:async="true" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceGroup('CapDentalPaymentDefinition').serviceName('payment_generateDispatchOutboundPaymentRequest').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{"body": {"_key": ${_requestIssuePayload._key}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="disallowRedirects">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[dispatchRequestPayload]]></flowable:string>
        </flowable:field>
        <flowable:field name="ignoreException">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveRequestVariables">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParameters">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseParametersTransient">
          <flowable:string><![CDATA[false]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:string><![CDATA[true]]></flowable:string>
        </flowable:field>
        <flowable:executionListener event="end" expression="${execution.setVariable('messages', dispatchRequestPayload.body.success.messages)}"></flowable:executionListener>
        <design:stencilid><![CDATA[HttpTask]]></design:stencilid>
        <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
      </extensionElements>
    </serviceTask>
    <serviceTask id="sid-D70CF9B9-02BF-4EA6-BE43-106035033B0E" name="Cancel Payment" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter source="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="{&quot;_key&quot; : ${_requestIssuePayload._key}, &quot;messages&quot; : ${messages}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="cancelPayment" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="payment" target="_variation" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="_cancelPaymentPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <userTask id="sid-1C3D24B6-E02A-47A6-B0E6-F7F94FC5365C" name="Payments Canceled" flowable:formFieldValidation="false" flowable:priority="1">
      <extensionElements>
        <flowable:taskListener event="create" expression="${task.setVariableLocal('description', StringHelper.stringBuilder().append('Payment ').append(_cancelPaymentPayload.paymentNumber).append(' is canceled.').build())}"></flowable:taskListener>
        <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_management', 'candidate')}"></flowable:taskListener>
        <flowable:taskListener event="complete" expression="${execution.setVariable('isResolutionDone', task.getVariableLocal('resolutionCd') == 'done')}"></flowable:taskListener>
        <flowable:taskListener event="create" expression="${task.setVariableLocal('_uri', _cancelPaymentPayload.originSource._uri)}"></flowable:taskListener>
        <flowable:taskListener event="create" expression="${task.setVariableLocal('_modelName','CapLoss')}"></flowable:taskListener>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108"></exclusiveGateway>
    <exclusiveGateway id="sid-845C034D-01AA-4BE9-998A-DF173B78D82B"></exclusiveGateway>
    <startEvent id="requestIssuePayment" name="requestIssuePayment" isInterrupting="true">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentDefinition_requestIssuePayment]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="payload" sourceType="string" target="_requestIssuePayload"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:executionListener event="end" expression="${flwStringUtils.hasText(_uri) ? processEngine.getRuntimeService().updateBusinessKey(execution.getProcessInstanceId(), _uri) : null}"></flowable:executionListener>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <endEvent id="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495"></endEvent>
    <boundaryEvent id="sid-DDA8DD2E-19B5-45DA-99CD-3273E6CF8FD0" attachedToRef="dispatchOutboundPayment">
      <errorEventDefinition flowable:errorVariableLocalScope="true" flowable:errorVariableTransient="true"></errorEventDefinition>
    </boundaryEvent>
    <sequenceFlow id="sid-EF5FC8E3-91B4-479B-96B9-8BCFEB13B06A" sourceRef="sid-DDA8DD2E-19B5-45DA-99CD-3273E6CF8FD0" targetRef="requestIssuePaymentFailedCommand"></sequenceFlow>
    <sequenceFlow id="sid-6B1B813F-A536-4288-8E8E-B44A3FB580FC" sourceRef="dispatchOutboundPayment" targetRef="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495"></sequenceFlow>
    <sequenceFlow id="sid-8C3468E5-8A2B-45BC-B11D-4476ABAF14BA" sourceRef="requestIssuePaymentFailedCommand" targetRef="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495"></sequenceFlow>
    <sequenceFlow id="sid-3B93167E-C342-4E52-8376-2DBFF80B4450" sourceRef="issuePaymentCommand" targetRef="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495"></sequenceFlow>
    <sequenceFlow id="sid-04444176-F6D4-4251-A62E-C3457A24F88B" sourceRef="sid-381CEA0A-D258-4856-A393-8848A30FCE44" targetRef="sid-845C034D-01AA-4BE9-998A-DF173B78D82B"></sequenceFlow>
    <sequenceFlow id="sid-78E98054-BCCE-4D7C-82C0-F0194FD79E0E" sourceRef="sid-D70CF9B9-02BF-4EA6-BE43-106035033B0E" targetRef="sid-1C3D24B6-E02A-47A6-B0E6-F7F94FC5365C"></sequenceFlow>
    <sequenceFlow id="sid-C160B52C-629D-4DA9-8AF9-C11DC8BE19A9" sourceRef="sid-1C3D24B6-E02A-47A6-B0E6-F7F94FC5365C" targetRef="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495"></sequenceFlow>
    <sequenceFlow id="sid-89F61BFB-BC1E-49BE-B455-D50D0C8A3A3C" sourceRef="requestIssuePayment" targetRef="sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108"></sequenceFlow>
    <sequenceFlow id="sid-E0A47B4C-EE21-42B0-B09E-BCE9379E02B5" name="No" sourceRef="sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108" targetRef="issuePaymentCommand">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${_requestIssuePayload.paymentNetAmount == null || _requestIssuePayload.paymentNetAmount.amount == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-113AF513-484E-4E01-8247-271F095311FF" name="Yes" sourceRef="sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108" targetRef="sid-381CEA0A-D258-4856-A393-8848A30FCE44">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${_requestIssuePayload.paymentNetAmount !=null && _requestIssuePayload.paymentNetAmount.amount > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-A2095CC0-1304-4D38-ADC2-560C1404ECE9" name="Yes" sourceRef="sid-845C034D-01AA-4BE9-998A-DF173B78D82B" targetRef="dispatchOutboundPayment">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${dispatchRequestPayload.body.success.validationResult == "Valid"}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-71E31A37-51F1-42ED-81E5-91AE0B390069" name="No" sourceRef="sid-845C034D-01AA-4BE9-998A-DF173B78D82B" targetRef="sid-D70CF9B9-02BF-4EA6-BE43-106035033B0E">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${dispatchRequestPayload.body.failure != null || dispatchRequestPayload.body.success.validationResult != "Valid"}]]></conditionExpression>
    </sequenceFlow>
    <textAnnotation id="bpmnTextAnnotation_3">
      <extensionElements>
        <design:stencilid><![CDATA[TextAnnotation]]></design:stencilid>
        <design:text><![CDATA[Payment NET amount > 0]]></design:text>
      </extensionElements>
      <text>Payment NET amount &gt; 0</text>
    </textAnnotation>
    <textAnnotation id="bpmnTextAnnotation_16">
      <extensionElements>
        <design:stencilid><![CDATA[TextAnnotation]]></design:stencilid>
        <design:text><![CDATA[Validation status is valid?]]></design:text>
      </extensionElements>
      <text>Validation status is valid?</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_issueDentalPayment">
    <bpmndi:BPMNPlane bpmnElement="issuePayment" id="BPMNPlane_issueDentalPayment">
      <bpmndi:BPMNShape bpmnElement="dispatchOutboundPayment" id="BPMNShape_dispatchOutboundPayment">
        <omgdc:Bounds height="104.0" width="122.0" x="1139.0" y="318.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="requestIssuePaymentFailedCommand" id="BPMNShape_requestIssuePaymentFailedCommand">
        <omgdc:Bounds height="80.0" width="111.0" x="1144.5" y="511.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="issuePaymentCommand" id="BPMNShape_issuePaymentCommand">
        <omgdc:Bounds height="80.0" width="100.0" x="1150.0" y="184.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-381CEA0A-D258-4856-A393-8848A30FCE44" id="BPMNShape_sid-381CEA0A-D258-4856-A393-8848A30FCE44">
        <omgdc:Bounds height="80.0" width="100.0" x="694.0" y="330.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-D70CF9B9-02BF-4EA6-BE43-106035033B0E" id="BPMNShape_sid-D70CF9B9-02BF-4EA6-BE43-106035033B0E">
        <omgdc:Bounds height="80.0" width="100.0" x="915.0" y="511.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1C3D24B6-E02A-47A6-B0E6-F7F94FC5365C" id="BPMNShape_sid-1C3D24B6-E02A-47A6-B0E6-F7F94FC5365C">
        <omgdc:Bounds height="80.0" width="100.0" x="915.0" y="680.148698354997"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bpmnTextAnnotation_3" id="BPMNShape_bpmnTextAnnotation_3">
        <omgdc:Bounds height="50.0" width="120.0" x="537.0" y="263.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="bpmnTextAnnotation_16" id="BPMNShape_bpmnTextAnnotation_16">
        <omgdc:Bounds height="50.0" width="120.0" x="834.2214221563015" y="425.7249579202845"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108" id="BPMNShape_sid-C644AC82-CF55-4DF2-BCCF-46DAC31FB108">
        <omgdc:Bounds height="40.0" width="40.0" x="502.0" y="350.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-845C034D-01AA-4BE9-998A-DF173B78D82B" id="BPMNShape_sid-845C034D-01AA-4BE9-998A-DF173B78D82B">
        <omgdc:Bounds height="40.0" width="40.0" x="945.0" y="350.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="requestIssuePayment" id="BPMNShape_requestIssuePayment">
        <omgdc:Bounds height="30.0" width="30.0" x="324.0" y="355.0"></omgdc:Bounds>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="121.0" x="278.5" y="389.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E3AABD29-93E2-41FB-9270-0154EDC4A495" id="BPMNShape_sid-E3AABD29-93E2-41FB-9270-0154EDC4A495">
        <omgdc:Bounds height="28.0" width="28.0" x="1422.407045714902" y="356.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-DDA8DD2E-19B5-45DA-99CD-3273E6CF8FD0" id="BPMNShape_sid-DDA8DD2E-19B5-45DA-99CD-3273E6CF8FD0">
        <omgdc:Bounds height="30.0" width="30.0" x="1185.107809870018" y="407.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-89F61BFB-BC1E-49BE-B455-D50D0C8A3A3C" id="BPMNEdge_sid-89F61BFB-BC1E-49BE-B455-D50D0C8A3A3C" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="354.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="502.0" y="370.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-04444176-F6D4-4251-A62E-C3457A24F88B" id="BPMNEdge_sid-04444176-F6D4-4251-A62E-C3457A24F88B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="794.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="945.0" y="370.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-113AF513-484E-4E01-8247-271F095311FF" id="BPMNEdge_sid-113AF513-484E-4E01-8247-271F095311FF" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="542.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="694.0" y="370.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="18.0" x="552.0" y="340.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8C3468E5-8A2B-45BC-B11D-4476ABAF14BA" id="BPMNEdge_sid-8C3468E5-8A2B-45BC-B11D-4476ABAF14BA" flowable:sourceDockerX="56.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1255.5" y="551.0"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="551.0"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="384.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A2095CC0-1304-4D38-ADC2-560C1404ECE9" id="BPMNEdge_sid-A2095CC0-1304-4D38-ADC2-560C1404ECE9" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="61.0" flowable:targetDockerY="52.0">
        <omgdi:waypoint x="985.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="1139.0" y="370.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="18.0" x="995.0" y="340.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-C160B52C-629D-4DA9-8AF9-C11DC8BE19A9" id="BPMNEdge_sid-C160B52C-629D-4DA9-8AF9-C11DC8BE19A9" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1015.0" y="720.148698354997"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="720.148698354997"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="384.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-E0A47B4C-EE21-42B0-B09E-BCE9379E02B5" id="BPMNEdge_sid-E0A47B4C-EE21-42B0-B09E-BCE9379E02B5" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="522.0" y="350.0"></omgdi:waypoint>
        <omgdi:waypoint x="522.0" y="224.0"></omgdi:waypoint>
        <omgdi:waypoint x="1150.0" y="224.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="16.0" x="532.0" y="320.8"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EF5FC8E3-91B4-479B-96B9-8BCFEB13B06A" id="BPMNEdge_sid-EF5FC8E3-91B4-479B-96B9-8BCFEB13B06A" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="56.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="1200.107809870018" y="437.0"></omgdi:waypoint>
        <omgdi:waypoint x="1200.107809870018" y="474.0"></omgdi:waypoint>
        <omgdi:waypoint x="1200.0" y="474.0"></omgdi:waypoint>
        <omgdi:waypoint x="1200.0" y="511.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6B1B813F-A536-4288-8E8E-B44A3FB580FC" id="BPMNEdge_sid-6B1B813F-A536-4288-8E8E-B44A3FB580FC" flowable:sourceDockerX="61.0" flowable:sourceDockerY="52.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1261.0" y="370.0"></omgdi:waypoint>
        <omgdi:waypoint x="1422.407045714902" y="370.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-71E31A37-51F1-42ED-81E5-91AE0B390069" id="BPMNEdge_sid-71E31A37-51F1-42ED-81E5-91AE0B390069" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="965.0" y="390.0"></omgdi:waypoint>
        <omgdi:waypoint x="965.0" y="511.0"></omgdi:waypoint>
        <bpmndi:BPMNLabel>
          <omgdc:Bounds height="18.0" width="16.0" x="975.0" y="400.0"></omgdc:Bounds>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3B93167E-C342-4E52-8376-2DBFF80B4450" id="BPMNEdge_sid-3B93167E-C342-4E52-8376-2DBFF80B4450" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="1250.0" y="224.0"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="224.0"></omgdi:waypoint>
        <omgdi:waypoint x="1436.407045714902" y="356.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-78E98054-BCCE-4D7C-82C0-F0194FD79E0E" id="BPMNEdge_sid-78E98054-BCCE-4D7C-82C0-F0194FD79E0E" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="965.0" y="591.0"></omgdi:waypoint>
        <omgdi:waypoint x="965.0" y="680.148698354997"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
