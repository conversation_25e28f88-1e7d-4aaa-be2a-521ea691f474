/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.context;

import com.eis.automation.tzappa.rest.client.auth.IAuthorization;
import com.eis.automation.tzappa.rest.client.context.InlineRequestContext;
import com.eis.automation.tzappa.rest.client.impl.RestClient;
import com.eis.automation.tzappa.rest.modeling.container.ResponseContainer;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.CapAccumulator;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.impl.CapAccumulatorLoadModel;
import com.eis.automation.v20.capdental.entity.claimsearch.facade.impl.ClaimSearch;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.CapDentalLoss;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.CapDentalPatientHistory;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl.CapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.CapDentalPayment;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.CapDentalPaymentSchedule;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl.CapDentalPaymentSchedulePreviewBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.CapDentalPaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl.CapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.CapDentalSettlement;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl.CapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.CapDentalUnverifiedPolicy;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl.CapDentalPolicyInfoModel;
import com.eis.automation.v20.platform.common.container.GenesisResponseContainer;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Response;
import java.util.function.Function;

@Lazy
@Component
public class CapDentalBeanDefinition {

    @Autowired
    private IAuthorization auth;

    @Autowired
    @Qualifier("platform.jsonMapper")
    private ObjectMapper mapper;

    @Autowired
    @Qualifier("cap.capDentalUnverifiedPolicy.restClient")
    private RestClient clientCapDentalUnverifiedPolicy;

    @Autowired
    @Qualifier("cap.capDentalLoss.restClient")
    private RestClient clientCapDentalLoss;

    @Autowired
    @Qualifier("cap.capDentalSettlement.restClient")
    private RestClient clientCapDentalSettlement;

    @Autowired
    @Qualifier("cap.capDentalPayment.restClient")
    private RestClient clientCapDentalPayment;

    @Autowired
    @Qualifier("cap.capDentalPaymentTemplate.restClient")
    private RestClient clientCapDentalPaymentTemplate;

    @Autowired
    @Qualifier("cap.capDentalPatientHistory.restClient")
    private RestClient clientCapDentalPatientHistory;

    @Autowired
    @Qualifier("cap.claimSearch.restClient")
    private RestClient clientClaimSearch;

    @Autowired
    @Qualifier("cap.capAccumulatorLoad.restClient")
    private RestClient clientCapAccumulatorLoad;

    @Autowired
    @Qualifier("cap.capDentalPaymentSchedulePreview.restClient")
    private RestClient clientCapDentalPaymentSchedulePreview;

    @Lazy
    @Bean("cap.capDentalUnverifiedPolicy")
    public CapDentalUnverifiedPolicy capDentalUnverifiedPolicy() {
        Function<Response, ResponseContainer<CapDentalPolicyInfoModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalPolicyInfoModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalUnverifiedPolicy")
                .pathParam("version", "v1").build();

        return new CapDentalUnverifiedPolicy(new RestActionConfiguration(requestContext, clientCapDentalUnverifiedPolicy, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalLoss")
    public CapDentalLoss capDentalLoss() {
        Function<Response, ResponseContainer<CapDentalLossModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalLossModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalLoss")
                .pathParam("version", "v1").build();

        return new CapDentalLoss(new RestActionConfiguration(requestContext, clientCapDentalLoss, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalSettlement")
    public CapDentalSettlement capDentalSettlement() {
        Function<Response, ResponseContainer<CapDentalSettlementModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalSettlementModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalSettlement")
                .pathParam("version", "v1").build();

        return new CapDentalSettlement(new RestActionConfiguration(requestContext, clientCapDentalSettlement, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalPayment")
    public CapDentalPayment capDentalPayment() {
        Function<Response, ResponseContainer<CapDentalPaymentModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalPaymentModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalPaymentDefinition")
                .pathParam("version", "v1").build();

        return new CapDentalPayment(new RestActionConfiguration(requestContext, clientCapDentalPayment, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalPaymentTemplate")
    public CapDentalPaymentTemplate capDentalPaymentTemplate() {
        Function<Response, ResponseContainer<CapDentalPaymentTemplateModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalPaymentTemplateModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalPaymentTemplate")
                .pathParam("version", "v1").build();

        return new CapDentalPaymentTemplate(new RestActionConfiguration(requestContext, clientCapDentalPaymentTemplate, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalPatientHistory")
    public CapDentalPatientHistory capDentalPatientHistory() {
        Function<Response, ResponseContainer<CapDentalPatientHistoryModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalPatientHistoryModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalPatientHistory")
                .pathParam("version", "v1").build();

        return new CapDentalPatientHistory(new RestActionConfiguration(requestContext, clientCapDentalPatientHistory, serFunction));
    }

    @Lazy
    @Bean("cap.claimSearch")
    public ClaimSearch claimSearch() {
        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "search")
                .pathParam("version", "v2").build();

        return new ClaimSearch(new RestActionConfiguration(requestContext, clientClaimSearch, null));
    }

    @Lazy
    @Bean("cap.capAccumulatorLoad")
    public CapAccumulator capAccumulatorLoad() {
        Function<Response, ResponseContainer<CapAccumulatorLoadModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapAccumulatorLoadModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapAccumulatorContainer")
                .pathParam("version", "v1").build();

        return new CapAccumulator(new RestActionConfiguration(requestContext, clientCapAccumulatorLoad, serFunction));
    }

    @Lazy
    @Bean("cap.capDentalPaymentSchedulePreview")
    public CapDentalPaymentSchedule capDentalPaymentSchedulePreview() {
        Function<Response, ResponseContainer<CapDentalPaymentSchedulePreviewBuildModel>> serFunction =
                response -> new GenesisResponseContainer<>(response, CapDentalPaymentSchedulePreviewBuildModel.class);

        InlineRequestContext requestContext = InlineRequestContext
                .builder()
                .auth(auth)
                .pathParam("product", "CapDentalPaymentSchedule")
                .pathParam("version", "v1").build();

        return new CapDentalPaymentSchedule(new RestActionConfiguration(requestContext, clientCapDentalPaymentSchedulePreview, serFunction));
    }
}