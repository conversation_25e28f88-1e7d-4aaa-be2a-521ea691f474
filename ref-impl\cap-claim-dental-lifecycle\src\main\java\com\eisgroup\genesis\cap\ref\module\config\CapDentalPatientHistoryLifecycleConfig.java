/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator.CapDentalPatientHistoryInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator.CapDentalPatientHistoryUpdateInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator.CapDentalPatientHistoryUpdateValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;

import org.springframework.context.annotation.Bean;

/**
 * Claim Dental Patient History lifecycle services configuration.
 *
 * <AUTHOR>
 * @since 22.12
 */
public class CapDentalPatientHistoryLifecycleConfig {

    @Bean
    public CapValidatorRegistry capValidatorRegistry() {
        return new CapValidatorRegistry();
    }

    @Bean
    public CapDentalPatientHistoryUpdateValidator capDentalPatientHistoryUpdateValidator() {
        return new CapDentalPatientHistoryUpdateValidator();
    }

    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalPatientHistoryInitInputValidator capDentalPatientHistoryInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPatientHistoryInitInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalPatientHistoryUpdateInputValidator capDentalPatientHistoryUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPatientHistoryUpdateInputValidator(capDentalLinkValidator);
    }
}
