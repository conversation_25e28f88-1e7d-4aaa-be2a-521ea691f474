{"TestData": {"originSource": {"_uri": "gentity://CapLoss/CapDentalLoss//{{Dental_rootId}}/1"}, "entity": {"paymentAllocationTemplates": [{"allocationSource": {"_uri": "gentity://capsettlement/DentalSettlement//{{dentalSettlementId}}/1"}, "allocationPayeeDetails": {"payee": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}"}, "_type": "CapDentalPaymentAllocationTemplatePayeeDetailsEntity"}, "allocationLossInfo": {"lossSource": {"_uri": "gentity://CapLoss/CapDentalLoss//{{Dental_rootId}}/1"}, "_type": "CapDentalPaymentAllocationTemplateLossInfoEntity"}, "allocationLobCd": "Dental", "_type": "CapDentalPaymentAllocationTemplateEntity"}], "_type": "CapDentalPaymentDetailsTemplateEntity", "_modelName": "CapDentalPaymentTemplate"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl.CapDentalPaymentTemplateModel"}}