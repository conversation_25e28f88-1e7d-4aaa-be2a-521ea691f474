import sbt.Keys._
import sbt._
import sbtbuildinfo.BuildInfoPlugin.autoImport._

object BuildPluginsSettings {

  // Disable generation of javadocs / scale during packaging process
  lazy val javadocSettings = Seq(
    Compile / doc / sources := Seq.empty,
    Compile / packageDoc / publishArtifact := false
  )

  // Build Info Version Generation
  lazy val buildInfoSettings = Seq(
    buildInfoPackage := "configs.dxp",
    buildInfoKeys := Seq[BuildInfoKey](name, version, scalaVersion, sbtVersion)
      ++ Seq[BuildInfoKey](
      "buildNumber" -> sys.env.getOrElse("BUILD_NUMBER", ""),
      "revision" -> sys.env.getOrElse("GIT_COMMIT", ""),
      "dxpCoreVersion" -> Common.dxpCoreVersion,
      BuildInfoKey.action("buildTime") {
        val dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        dateFormat.setTimeZone(java.util.TimeZone.getTimeZone("UTC"))
        dateFormat.format(new java.util.Date())
      }
    )
  )
}