/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.settlement;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.eis.automation.v20.platform.entity.activity.facade.IActivity;
import com.eis.automation.v20.platform.entity.activity.facade.impl.modeling.ActivityModel;
import com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended;
import com.eis.automation.v20.platform.utils.assertion.bam.BamModel;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.testng.annotations.Test;

import java.util.List;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.BamConstant.BamStatus.FINISHED;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.CLOSED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.BamMessage.CAP_DENTAL_SETTLEMENT_CLOSE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_SETTLEMENT;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0110;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_LOSS_IS_IN_CLOSED_STATE;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.PlatformPredicates.bam;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalSettlementLifecycle extends CapDentalBaseTest {

    @Autowired
    private IProviderService provider;
    @Lazy
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IActivity activity;
    @Autowired
    private IClaimSearchService claimSearch;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-167972", component = CAP_DENTAL_SETTLEMENT)
    public void testInitCloseAndReadjudicateSettlement() {
        // Preconditions
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));

        // Dental Claim1
        ICapDentalLossModel dentalClaimModel = createSubmittedDentalClaim(createdDentalPolicy, individualCustomer, individualProvider, D0330);

        // Dental Claim3
        ICapDentalLossModel dentalClaimModel3 = createSubmittedDentalClaim(createdDentalPolicy, individualCustomer, individualProvider, D0110);
        ICapDentalLossModel closedDentalClaim3 = capDentalLoss.loadDentalLoss(dentalClaimModel3, CLOSED);
        ICapDentalSettlementModel initSettlement3 = claimSearch.searchDentalSettlement(dentalClaimModel3, 1).get(0);

        // Step 1.1
        ICapDentalSettlementModel initSettlement = claimSearch.searchDentalSettlement(dentalClaimModel, 1).get(0);
        ICapDentalSettlementModel closeSettlement = (
                ICapDentalSettlementModel) capDentalSettlement.getFacade().close().perform(k -> k.setModel(initSettlement.getKey()));
        assertThat(closeSettlement.getState()).isEqualTo(CLOSED);

        // Step 1.2
        List<ActivityModel> paymentActivities = activity.fetch().performNoSuccessAnalysis(b -> b
                .setModel(closeSettlement), bam(CAP_DENTAL_SETTLEMENT_CLOSE)).safeGetResponseBody();
        CustomAssertionsExtended.assertThat(BamModel.of(paymentActivities)).hasMessage(CAP_DENTAL_SETTLEMENT_CLOSE).withStatus(FINISHED);

        // Step 7
        ICapDentalSettlementModel loadSettlement = capDentalSettlement.loadDentalSettlement(initSettlement3);
        ICapDentalSettlementModel readjudicateModel = modelUtils.create(
                capDentalSettlement.getSpecificTestData("TestDentalSettlementRules", "TestData_Readjudicate1"));
        readjudicateModel.setClaimLossIdentification(loadSettlement.getClaimLossIdentification());
        readjudicateModel.getEntity().setTimestamp(loadSettlement.getTimestamp());
        IFailureResponse failure = getError(() -> capDentalSettlement.getFacade().readjudicate().perform(b -> b.setModel(readjudicateModel)));
        FailureAssertion.assertThat(failure).hasError(THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_LOSS_IS_IN_CLOSED_STATE);

        // Step 8 - Removed cause can not initiate Dental Settlement manually
    }
    public ICapDentalLossModel createSubmittedDentalClaim(ICapDentalPolicyInfoModel createdDentalPolicy, IndividualCustomerModel individualCustomer,
                                                IIndividualProviderModel individualProvider, String procedureCode) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(procedureCode);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setPredetInd(false);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        return capDentalLoss.submitDentalLoss(initDentalClaim);
    }
}
