Entity CapDentalInternalEntity is RootEntity {
}

Entity CapDentalFinancialDataInput {
    ExtLink originSource: RootEntity
    ExtLink settlements: *RootEntity
}

Entity CancelActivePaymentTemplatesOutput {
    ExtLink originSource: RootEntity
    ExtLink cancelledTemplates: *RootEntity
}

Entity SuspendActivePaymentTemplatesOutput {
    ExtLink originSource: RootEntity
    ExtLink suspendedTemplates: *RootEntity
}

Entity UnsuspendPaymentTemplatesOutput {
    ExtLink originSource: RootEntity
    ExtLink unsuspendedTemplates: *RootEntity
}

Entity CapDentalPaymentGenerationOutput {
    ExtLink payments: *RootEntity
}

Entity SuspendActivePaymentSchedulesOutput {
    ExtLink originSource: RootEntity
    ExtLink suspendedSchedules: *RootEntity
}

Entity UnsuspendPaymentSchedulesOutput {
    ExtLink originSource: RootEntity
    ExtLink unsuspendedSchedules: *RootEntity
}

Entity CapDentalFinancialCalculateLossBalanceDataInput {
    ExtLink originSource: RootEntity
}

Entity CapUserCarryingEntity {
    Attr userId: String
}

