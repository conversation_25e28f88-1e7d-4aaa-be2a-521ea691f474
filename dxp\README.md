# DXP API Project

This document describes how to work with DXP API Project

**Table of Content**

- [Requirements](#Requirements)
- [Configure Project Dependencies](#Configure Project Dependencies)
- [Configure Coding Conventions](#Configure Coding Conventions)
- [Compile/Build and Run Project](#Compile/Build and Run Project)
- [Known Issues](#Known Issues)
- [Additional Documentation](#Additional Documentation)
- [Support Channel](#Support Channel)

## Requirements
* IDE with GIT and SBT support (IntelliJ or Eclipse)
* Java 21
* SBT (is used as build tool to compile Scala and Java code in the project)
  `<root_project_directory>/project/build.properties`
* Scala plugin/extension (add to the IDE plugins)

## Configure Project Dependencies

Add file to User home .ivy2 folder (with name ***.credentials.genesis***) contains user domain credentials with access to Nexus repository:
```
realm=Sonatype Nexus Repository Manager
host=sfoeisgennexus01.exigengroup.com
user=<username>
password=<password>
```

## Configure Coding Conventions

Configure code style template to follow specified formatting and standards
Code style settings can be set by using Checkstyle rules config file (IDE Integration) that is available using the following path in project:
`<root_project_directory>/project/codequality`

Add Code Style Settings:
1. Open following settings:
   `File → Settings → Editor → Code Style → Java → ⚙ ️→ Import Scheme → IntelliJ IDEA code style XML`
2. Choose Checkstyle file from following directory (as a config file):
   `<root_project_directory>/project/codequality/Genesis_Intellij.xml`
3. Save settings

## Compile/Build and Run Project

Use SBT shell to run/debug application using following steps:
1. Open root folder of the project
2. Compile project: use following command - `;clean ;compile`
3. Debug project: click `Attach debugger to sbt shell` button on the left sidebar of sbt shell
4. Run application: use following command - `run <port>` (e.g. 9000)
5. Verify that application is running using DXP Swagger UI in browser:
   http://localhost:9000/core/swagger/index.html

## Known Issues

While first compilation/build process you can face with issues for existing packages, e.g.:
```
[error] ..\modules\integration-eisgenesis\integration-eisgenesis-common\app\dataproviders\common\utils\GenesisPathUtils.java:
        package com.google.common.collect does not exist
[error] com.google.common.collect.ImmutableMap
```
***Solution:*** Remove all local artifacts from .m2 and .ivy2 folders in your <user_home> folder, check that you are using Nexus from configuration and recompile project

## Additional Documentation

[DXP API Development - CookBook](https://wiki.eisgroup.com/display/GRC/DXP+API+Development)

## Support Channel

There is an available channel on Slack to help on any questions: `#v20-dxp-api-support`