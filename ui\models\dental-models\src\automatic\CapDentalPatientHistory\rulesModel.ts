// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALPATIENTHISTORY } from "./kraken_model_tree_CapDentalPatientHistory"

let name = "CapDentalPatientHistory"

let namespace = "CapDentalPatientHistory"

export type CapDentalPatientHistoryEntryPointName = "CapDentalPatientHistory:ClaimPatientHistoryCreateAction" | "CapDentalPatientHistory:ClaimPatientHistoryUpdateAction"

let entryPointNames = [
    "CapDentalPatientHistory:ClaimPatientHistoryCreateAction",
    "CapDentalPatientHistory:ClaimPatientHistoryUpdateAction"
] as CapDentalPatientHistoryEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALPATIENTHISTORY as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalPatientHistory = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree}, 'rules-model')));
