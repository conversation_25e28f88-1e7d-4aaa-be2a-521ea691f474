<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-batch-commands</artifactId>

    <build>
        <plugins>
            <plugin>
                <groupId>com.eisgroup.genesis.tools</groupId>
                <artifactId>fintrospector-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <configuration>
                            <introspectors>
                                <introspector>
                                    <groupId>com.eisgroup.genesis.lifecycle</groupId>
                                    <artifactId>lifecycle-fintrospector</artifactId>
                                    <version>${lifecycle.framework.version}</version>
                                </introspector>
                            </introspectors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.jobs</groupId>
            <artifactId>jobs-lifecycle</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-jobs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-repository</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-lifecycle</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!--- Testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.utils</groupId>
            <artifactId>testing-utils</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.model</groupId>
            <artifactId>model-repository-binary-impl</artifactId>
        </dependency>
    </dependencies>

</project>
