//EISDEVTS-40079
@Description("Allocation acumulator details.")
BaseType CapDentalPaymentAllocationAccumulatorDetailsEntity {

    //EISDEVTS-40055
    @Description("Accumulator amount used for the allocation.")
    Attr accumulatorAmount: Money

    //EISDEVTS-40079
    @Description("Accumulator type.")
    Attr accumulatorType: String

    //EISDEVTS-40079
    @Description("Defines to which procedure category accumulator applies to.")
    @Deprecated
    Attr appliesToProcedureCategory: String

    //EISDEVTS-40079
    @Description("Network type for accumulator.")
    Attr networkType: String

    //EISDEVTS-40079
    @Description("Renewal type for accumulator.")
    Attr renewalType: String

    @Description("Defines to which procedure category(s) maximum/deductible applies to.")
    @NonComparable
    Attr appliesToProcedureCategories: *String

    @Description("Accumulator Term.")
    @NonComparable
    Attr term: Term
}