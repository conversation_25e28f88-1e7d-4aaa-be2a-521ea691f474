/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.adjudication.command.config.CapAdjudicationChildConfig;
import com.eisgroup.genesis.cap.adjudication.command.config.CapAdjudicationLifecycleConfig;
import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.common.command.listener.config.CapCommandListenerConfig;
import com.eisgroup.genesis.cap.common.policy.populator.config.CapPolicyPopulatorConfig;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementAdjudicateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementApproveHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementDisapproveHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementReadjudicateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementRefreshHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalSettlementLifecycleConfig;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;
import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Claim Dental Settlement lifecycle module configuration.
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalSettlementLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        Collection<CommandHandler<?, ?>> handlers = new ArrayList<>();

        handlers.add(new CapDentalSettlementAdjudicateHandler());
        handlers.add(new CapDentalSettlementApproveHandler());
        handlers.add(new CapDentalSettlementCloseHandler());
        handlers.add(new CapDentalSettlementDisapproveHandler());
        handlers.add(new CapDentalSettlementReadjudicateHandler());
        handlers.add(new CapDentalSettlementInitHandler());
        handlers.add(new CapDentalSettlementRefreshHandler());

        return handlers;
    }

    public String getModelName() {
        return "CapDentalSettlement";
    }

    public String getModelVersion() {
        return "1";
    }

    public String getModelType() {
        return "CapSettlement";
    }

    public String getStateMachineName() {
        return getModelName();
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[] {
            CapDentalSettlementLifecycleConfig.class,
            CapAdjudicationLifecycleConfig.class,
            CapAdjudicationChildConfig.class,
            CapBaseCommandConfig.class,
            CapTransformationCommandsConfig.class,
            CapPolicyPopulatorConfig.class,
            CapCommandListenerConfig.class};
    }
}
