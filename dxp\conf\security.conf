# Secret key
# ~~~~~~~~~~
# The secret key is used to secure cryptographics functions.
# This must be changed for production, but we recommend not changing it in this file.
# See http://www.playframework.com/documentation/latest/ApplicationSecret for more details.
play.http.secret.key = "ph_yKcB:rsnj]RhuJReAA;C=VcnTv9W2?jdp:e^=ONp^ppcJo`<6gBnBdXFISg;e"

core.authenticators.technicaluser.username=qa
core.authenticators.technicaluser.password=qa

# Default security filters stack configuration (enabled in actions.conf)
core.security.authentication = [
  {
    "guest": {
      routes: [
        "/core/.*"
      ],
      logonFilters: []
    }
  },
  {
    "persona": {
      routes: [
        "/cap-adjuster.*"
      ],
      logonFilters: [
        {
          name: "PassThroughLogonFilter",
          displayName: "GenesisAuthToken",
          mode: "SUFFICIENT",
          authHeaderName: "X-Authorization",
          description: "Forwards 'X-Authorization' header as 'Authorization' header to Genesis"
        },
        {
          name: "BasicAuthPassThroughLogonFilter",
          mode: "SUFFICIENT",
          description: "Genesis Basic credentials"
        },
        {
          name: "PassThroughLogonFilter",
          displayName: "GenesisSSOToken",
          mode: "SUFFICIENT",
          description: "Genesis Authorization header"
        },
        {
          name: "PassThroughLogonErrorFilter",
          mode: "REQUIRED",
          scope: "RESPONSE_ERROR"
        }
      ],
      authenticationProvider: {
        name: "PassThroughAuthenticationProvider"
      }
    }
  }
]
