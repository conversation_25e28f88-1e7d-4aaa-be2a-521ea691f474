/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.CapUnverifiedPolicy;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.ICapDentalUnverifiedPolicy;

public class CapDentalUnverifiedPolicy extends CapUnverifiedPolicy implements ICapDentalUnverifiedPolicy {

    public CapDentalUnverifiedPolicy(RestActionConfiguration configuration) {
        super(configuration);
    }
}
