// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"RootEntity",
  "name":"CapPolicyHolder",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapPolicyEntity"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapPolicyEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Looks like a workaround when passing external model inputs to Transform operation executor in enriching transformations. Transform operation should be fixed in platform, CapTransform operation should be temporarily used in ref-impl and this should be deleted."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Looks like a workaround when passing external model inputs to Transform operation executor in enriching transformations. Transform operation should be fixed in platform, CapTransform operation should be temporarily used in ref-impl and this should be deleted.",
          "messageBundle":"domain-messages/CapPolicyHolder/description"
        },
        "class com.eisgroup.genesis.factory.model.features.Deprecated":{
        }
      },
      "links":{
      },
      "name":"CapPolicyEntity",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapPolicyHolder {
    export type Variations = never


    export class CapPolicyEntity extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapPolicyEntity.name) }
        readonly _modelName: string = 'CapPolicyHolder'
        readonly _modelType: string = 'RootEntity'
        readonly _modelVersion?: string = '1'
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapPolicyEntity, ()=> new CapPolicyEntity)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}