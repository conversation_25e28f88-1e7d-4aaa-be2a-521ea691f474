/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalPaymentCancelInput;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.time.TimeUtils;

import javax.annotation.Nonnull;
import java.util.ArrayList;

/**
 * Command handler for cancelling a {@link CapDentalPaymentEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentCancelHandler extends CapPaymentCancelHandler<CapDentalPaymentCancelInput, CapDentalPaymentEntity> {

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull CapDentalPaymentCancelInput request, @Nonnull CapDentalPaymentEntity entity) {
        return Lazy.of(entity)
                .map(payment -> {
                    if (payment.getMessages() == null) {
                        payment.setMessages(new ArrayList<>());
                    }
                    payment.getMessages().addAll(request.getMessages());
                    payment.setCancelationDate(TimeUtils.currentTime());
                    return payment;
                }).get();
    }
}
