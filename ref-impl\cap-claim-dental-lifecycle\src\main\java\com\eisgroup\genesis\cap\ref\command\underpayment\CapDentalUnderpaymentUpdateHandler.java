/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.underpayment;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.underpayment.CapUnderpaymentUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.input.CapDentalUnderpaymentUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapPayment;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for updating a {@link CapPayment} with provided input data.
 *
 * <AUTHOR>
 * @since 22.15
 */
@Modifying
public class CapDentalUnderpaymentUpdateHandler extends CapUnderpaymentUpdateHandler<CapDentalUnderpaymentUpdateInput, CapDentalPaymentEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Autowired
    private ModelResolver modelResolver;


    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalUnderpaymentUpdateInput request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull CapDentalUnderpaymentUpdateInput request, @Nonnull CapDentalPaymentEntity entity) {
        return populateAttributesForUpdate(request, entity).get();
    }

    private Lazy<CapDentalPaymentEntity> populateAttributesForUpdate(CapDentalUnderpaymentUpdateInput request, CapDentalPaymentEntity entity) {
        return Lazy.deferUsing(Streamable::currentContext, contextView -> {
            CapPaymentDetails paymentDetails = request.getEntity();
            paymentDetails.toJson().add(BaseKey.ATTRIBUTE_NAME, entity.getPaymentDetails().getKey().toJson());
            entity.setPaymentDetails(paymentDetails);
            entity.setPaymentNetAmount(request.getPaymentNetAmount());
            KeyTraversalUtil.traverseRoot(contextView, entity.toJson(), modelResolver.resolveModel(DomainModel.class));
            return Lazy.of(entity);
        });
    }
}
