/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.CapPaymentScheduleUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPaymentScheduleUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;

/**
 * Internal command for updating Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
@Modifying
public class CapDentalPaymentScheduleUpdateHandler extends CapPaymentScheduleUpdateHandler<CapDentalPaymentScheduleUpdateInput, CapDentalPaymentScheduleEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPaymentScheduleUpdateInput request, @Nonnull CapDentalPaymentScheduleEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }
}
