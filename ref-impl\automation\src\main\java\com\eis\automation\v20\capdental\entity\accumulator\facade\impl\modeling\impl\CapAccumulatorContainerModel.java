/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@JsonFilter(AbstractFilter.NAME)
public class CapAccumulatorContainerModel extends TypeModel implements ICapAccumulatorContainerModel {
    private String customerURI;
    private String policyURI;
    @JsonProperty(value = "_modelName")
    private String modelName;
    @JsonProperty(value = "_modelVersion")
    private String modelVersion;
    @JsonProperty(value = "_modelType")
    private String modelType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    @JsonProperty(value = "_timestamp")
    private LocalDateTime timestamp;
    private UriModel lastTransaction;
    private List<ICapAccumulatorModel> accumulators;

    @JsonSerialize(as = List.class, contentAs = CapAccumulatorModel.class)
    public List<ICapAccumulatorModel> getAccumulators() {
        return accumulators;
    }

    @JsonDeserialize(as = List.class, contentAs = CapAccumulatorModel.class)
    public void setAccumulators(List<ICapAccumulatorModel> accumulators) {
        this.accumulators = accumulators;
    }

    public String getCustomerURI() {
        return customerURI;
    }

    public void setCustomerURI(String customerURI) {
        this.customerURI = customerURI;
    }

    public String getPolicyURI() {
        return policyURI;
    }

    public void setPolicyURI(String policyURI) {
        this.policyURI = policyURI;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public UriModel getLastTransaction() {
        return lastTransaction;
    }

    public void setLastTransaction(UriModel lastTransaction) {
        this.lastTransaction = lastTransaction;
    }
}
