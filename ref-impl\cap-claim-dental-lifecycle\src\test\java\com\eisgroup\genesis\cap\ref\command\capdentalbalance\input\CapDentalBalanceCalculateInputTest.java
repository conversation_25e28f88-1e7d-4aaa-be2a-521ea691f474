/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.input;

import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonObject;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalBalanceCalculateInputTest {

    private static final String ORIGIN_SOURCE = "originSource";
    private CapDentalBalanceCalculateInput input = new CapDentalBalanceCalculateInput(createOriginal());

    @Test
    public void shouldReturnOriginSource() {
        //when
        EntityLink<RootEntity> result = input.getOriginSource();
        assertThat(result.getURIString(), equalTo(ORIGIN_SOURCE));
    }

    private JsonObject createOriginal() {
        JsonObject original = new JsonObject();
        EntityLink originSource = new EntityLink(RootEntity.class, ORIGIN_SOURCE);
        original.add(ORIGIN_SOURCE, originSource.toJson());
        return original;
    }
}
