[{"name": "Security: Bypass Dimension Filtering Rules"}, {"name": "Security: Load User"}, {"name": "Security: Load Security Domain"}, {"name": "Security: Manage Domain"}, {"name": "Security: Manage Access Profile"}, {"name": "Security: Manage User"}, {"name": "Security: Manage User Profile"}, {"name": "Security: Manage Operational Assignment"}, {"name": "Security: Manage Configured Access Profile"}, {"name": "Conversion: Generate XSD Schema"}, {"name": "Conversion: Generate Mapping Report"}, {"name": "Conversion: I<PERSON>rt"}, {"name": "Conversion: Export"}, {"name": "Billing: Account Create"}, {"name": "Policy: Quote Create Version"}, {"name": "Policy: Quote Delete Endorsement"}, {"name": "Policy: Quote Decline by Customer"}, {"name": "Policy: Quote Endorsement Issue"}, {"name": "Policy: Cancel Manual Renewal"}, {"name": "Policy: Quote Delete Renewal"}, {"name": "Policy: Quote Suspense"}, {"name": "Policy: Quote Remove suspense"}, {"name": "Policy: Quote Decline by Company"}, {"name": "Policy: Policy Set Do not Renew"}, {"name": "Policy: Policy CTE"}, {"name": "Policy: Inquiry"}, {"name": "Policy: <PERSON><PERSON><PERSON>"}, {"name": "Policy: Quote Propose"}, {"name": "Policy: Policy Reinstate"}, {"name": "Policy: Quote Inquiry"}, {"name": "Policy: Policy renewal"}, {"name": "Policy: Quote Update"}, {"name": "Policy: Quo<PERSON> From Quote"}, {"name": "Policy: Policy Update"}, {"name": "Policy: Policy Roll back"}, {"name": "Policy: Quote Initialize"}, {"name": "Policy: Policy Set Manual Renew"}, {"name": "Policy: Quote Rate"}, {"name": "Policy: Quote Issue Request"}, {"name": "Policy: Quote Issue"}, {"name": "Policy: Policy Remove Do not Renew"}, {"name": "Policy: Policy Remove Cancel Notice"}, {"name": "Policy: Policy OOSE"}, {"name": "Policy: Policy Set Cancel Notice"}, {"name": "Policy: Policy Rewrite"}, {"name": "Policy: Policy Cancel"}, {"name": "Policy: Quote <PERSON><PERSON> from Policy"}, {"name": "Policy: Update Quote Operations"}, {"name": "Policy: Load Operations"}, {"name": "Policy: Single BOR Transfer"}, {"name": "Policy: Policy Split"}, {"name": "Policy: Policy Spin"}, {"name": "Policy: Policy Rescind"}, {"name": "Policy: Policy Premium Override Create"}, {"name": "Policy: Policy Premium Override Update"}, {"name": "Policy: UW Measure Creation"}, {"name": "Policy: UW Measure Cancellation"}, {"name": "Policy: Quote Move"}, {"name": "Policy: <PERSON><PERSON> from Policy"}, {"name": "Decision Table: Write"}, {"name": "Policy: Archive"}, {"name": "Policy: Initiate Purchase"}, {"name": "Policy: Complete Purchase"}, {"name": "Policy: Cancel Purchase"}, {"name": "Policy: Quote <PERSON><PERSON> from Archived Policy"}, {"name": "Applicability Override: Init"}, {"name": "Applicability Override: Write"}, {"name": "Applicability Override: Accept"}, {"name": "Applicability Override: Decline"}, {"name": "Policy: Accept Contract"}, {"name": "Metadata Aggregator: Export model specification"}, {"name": "Metadata Aggregator: Convert model specification"}, {"name": "Party: Create Party"}, {"name": "Party: Update Party"}, {"name": "Party: Merge <PERSON>"}, {"name": "Party: Access Party"}, {"name": "Party: Search Party"}, {"name": "Party: <PERSON><PERSON>"}, {"name": "Party: Access History"}, {"name": "Organization: Manage Organizational Person"}, {"name": "Organization: Search Organization by Role"}, {"name": "Organization: Search Person"}, {"name": "Organization: Load Organization"}, {"name": "Organization: Apply Role"}, {"name": "Organization: Manage Organization"}, {"name": "Organization: Load Organization Roles"}, {"name": "Organization: Load Organization Person"}, {"name": "Configuration Server: Export product specification"}, {"name": "Configuration Server: Pipeline execution"}, {"name": "CEM: Create/Update Customer"}, {"name": "CEM: Load Customer"}, {"name": "CEM: Create Scheduled Update"}, {"name": "CEM: Load Scheduled Update"}, {"name": "CEM: Delete Scheduled Update"}, {"name": "CEM: Create/Update Communication"}, {"name": "CEM: Create/Update Opportunity"}, {"name": "Policy: Policy Premium Override Read"}, {"name": "Policy: Policy Premium Override Delete"}, {"name": "Policy: Create/Update Premiums"}, {"name": "Policy: Job Processing"}, {"name": "Policy: Rate Effective Date Access"}, {"name": "Adjudication: Adjudicate Settlement"}, {"name": "Adjudication: Initialize Settlement"}, {"name": "Adjudication: Approve Settlement"}, {"name": "Adjudication: Close Settlement"}, {"name": "Adjudication: Override Eligibility Result"}, {"name": "Adjudication: Override Settlement Calculation Attributes"}, {"name": "LossIntake: Update Loss"}, {"name": "LossIntake: Reopen Loss"}, {"name": "LossIntake: Close Loss"}, {"name": "LossIntake: Adjudicate Loss"}, {"name": "LossIntake: Submit Loss"}, {"name": "LossIntake: Initialize Loss"}, {"name": "LossIntake: Create Loss"}, {"name": "LossIntake: Set Loss Sub-Status"}, {"name": "Claim: Access Appeal Queue"}, {"name": "Claim: Control Appeal Queue"}, {"name": "Claim: Access Medical Review Queue"}, {"name": "Claim: Control Medical Review Queue"}, {"name": "Claim: Access SIU Queue"}, {"name": "Claim: Control SIU Queue"}, {"name": "Claim: Access Supervisor Queue"}, {"name": "Claim: Control Supervisor Queue"}, {"name": "Claim: Access Vocational Queue"}, {"name": "Claim: Control Vocational Queue"}, {"name": "Claim: Access Claim Management Queue"}, {"name": "Claim: Control Claim Management Queue"}, {"name": "Financial: Post Payment"}, {"name": "Financial: Update Payment"}, {"name": "Financial: Approve Payment"}, {"name": "Financial: Cancel Payment"}, {"name": "Financial: Issue Payment"}, {"name": "Financial: Post Recovery"}, {"name": "Financial: Issue Recovery"}, {"name": "Financial: Update Recovery"}, {"name": "Financial: Cancel Recovery"}, {"name": "Financial: Add Recurrent Payment"}, {"name": "Financial: Activate Recurrent Payment"}, {"name": "Financial: Cancel Active Recurrent Payment"}, {"name": "Financial: Cancel Opened Recurrent Payment"}, {"name": "Financial: Complete Recurrent Payment"}, {"name": "Financial: Suspend Recurrent Payment"}, {"name": "Financial: Unsuspend Recurrent Payment"}, {"name": "Financial: Update Recurrent Payment"}, {"name": "Financial: Cancel Overpayment Waive"}, {"name": "Financial: Post Overpayment Waive"}, {"name": "Financial: Update Overpayment Waive"}, {"name": "CAPPolicy: Write Policy"}, {"name": "CAPParty: Update Claim Party"}]