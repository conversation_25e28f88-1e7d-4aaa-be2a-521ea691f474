BaseType CapDentalProcedureCoordinationOfBenefitsEntity {

    @Description("Other Carrier Allowed.")
    Attr allowed: Money

    @Description("Other Carrier Considered.")
    Attr considered: Money

    @Description("Type of Coverage.")
    @Lookup("CapDNCoverageType")
    Attr coverageType: String

    @Description("Paid INN/Paid OON.")
    @Lookup("CapDNInnOnn")
    Attr innOnn: String

    @Description("Other Carrier Paid.")
    Attr paid: Money

    @Description("Primary Coverage Status.")
    @Lookup("CapDNPrimaryCoverageStatus")
    Attr primaryCoverageStatus: String
}