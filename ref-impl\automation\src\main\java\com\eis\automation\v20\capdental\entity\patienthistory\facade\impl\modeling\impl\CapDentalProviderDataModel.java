/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalProviderDataModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalProviderDataModel extends TypeModel implements ICapDentalProviderDataModel {

    private String providerBusinessName;
    private String inOutNetwork;
    private String dentistID;
    private String providerTIN;
    private UriModel provider;

    public String getProviderBusinessName() {
        return providerBusinessName;
    }

    public void setProviderBusinessName(String providerBusinessName) {
        this.providerBusinessName = providerBusinessName;
    }

    public String getInOutNetwork() {
        return inOutNetwork;
    }

    public void setInOutNetwork(String inOutNetwork) {
        this.inOutNetwork = inOutNetwork;
    }

    public String getDentistID() {
        return dentistID;
    }

    public void setDentistID(String dentistID) {
        this.dentistID = dentistID;
    }

    public String getProviderTIN() {
        return providerTIN;
    }

    public void setProviderTIN(String providerTIN) {
        this.providerTIN = providerTIN;
    }

    public UriModel getProvider() {
        return provider;
    }

    public void setProvider(UriModel provider) {
        this.provider = provider;
    }
}
