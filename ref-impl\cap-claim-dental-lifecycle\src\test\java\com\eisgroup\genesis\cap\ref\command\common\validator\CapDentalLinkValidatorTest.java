/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.common.validator;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.initMocks;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.TargetEntityNotFoundException;
import com.eisgroup.genesis.test.utils.TestStreamable;

public class CapDentalLinkValidatorTest {

    private static final String LOSS_URI = "gentity://CapLoss/CapDentalLoss//01ffe821-cd68-41c9-a9d8-d78a5d2da981/1";
    private static final EntityLink<RootEntity> LOSS_LINK = new EntityLink<>(RootEntity.class, LOSS_URI);
    private static final String LOSS_MODEL_TYPE = "CapLoss";
    private static final String SETTLEMENT_MODEL_TYPE = "CapSettlement";

    @InjectMocks
    private CapDentalLinkValidator capDentalLinkValidator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private EntityLinkResolver<RootEntity> entityLinkResolver;

    @Mock
    private ErrorHolder errorHolder;

    @Mock
    private RootEntity entity;

    @Before
    public void setUp() {
        initMocks(this);
    }

    @Test
    public void testWhenLinkIsValid() {
        // given
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.of(entity));

        // when
        TestStreamable.create(capDentalLinkValidator.validateLink(LOSS_LINK, LOSS_MODEL_TYPE, errorHolder))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWhenLinkIsNull() {
        // when
        TestStreamable.create(capDentalLinkValidator.validateLink(null, LOSS_MODEL_TYPE, errorHolder))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWhenModelTypeIsIncorrect() {
        // when
        TestStreamable.create(capDentalLinkValidator.validateLink(LOSS_LINK, SETTLEMENT_MODEL_TYPE, errorHolder))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(errorHolder);
    }

    @Test
    public void testWhenEntityNotFound() {
        // given
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
        when(entityLinkResolver.resolve(any(EntityLink.class), any())).thenReturn(Lazy.error(TargetEntityNotFoundException::new));

        // when
        TestStreamable.create(capDentalLinkValidator.validateLink(LOSS_LINK, LOSS_MODEL_TYPE, errorHolder))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(errorHolder);
    }
}