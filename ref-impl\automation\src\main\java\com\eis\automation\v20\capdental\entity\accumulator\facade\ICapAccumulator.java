/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.accumulator.facade;

import com.eis.automation.tzappa.rest.modeling.IModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorLoadModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

import java.util.List;

public interface ICapAccumulator extends IModel {

    PostModelAction<ICapAccumulatorLoadModel, List<ICapAccumulatorContainerModel>> load();
}
