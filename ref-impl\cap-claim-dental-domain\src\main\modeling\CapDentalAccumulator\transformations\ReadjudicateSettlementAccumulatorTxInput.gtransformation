@CapEndpoint("readjudicateSettlementAccumulatorTxInput")
Transformation ReadjudicateSettlementAccumulatorTxInput {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity as output
    }

    Var loss is ExtLink(settlement.claimLossIdentification)

    Attr transactionTimestamp is Now()
    Attr policyURI is loss.policyId
    //GENESIS-366362
    Attr customerURI is loss.lossDetail.claimData.policyholderRole.registryId
    Attr sourceURI is ToExtLink(settlement)._uri
    Attr data is SafeInvoke(settlement.settlementResult.entries.reservedAccumulators, createTxData(settlement.settlementResult.entries.reservedAccumulators))

    Producer createTxData(reservedAccum) {
        Var firstProcedureCategory is First(reservedAccum.appliesToProcedureCategories)
        Var suffix is Ternary(Equals(firstProcedureCategory, "Basic") || Equals(firstProcedureCategory, "Major") || Equals(firstProcedureCategory, "Preventive"), "Dental", firstProcedureCategory)
        Attr amount is 0 - reservedAccum.reservedAmount.amount
        Attr type is "DentalSettlement" + "_" + reservedAccum.accumulatorType + "_" + reservedAccum.renewalType + "_" + suffix
        Attr transactionDate is Now()
        //GENESIS-366362
        Attr party is Ternary(Equals(reservedAccum.accumulatorType, "FamilyDeductible"), Null(), AsExtLink(loss.lossDetail.claimData.patientRole.registryId))
        Attr resource is Ternary(Equals(reservedAccum.accumulatorType, "FamilyDeductible"), createExtLink(Root().loss.policyId), Null())
        Attr extension is New() {
            Attr _type is "JsonType"
            Attr term is Super().reservedAccum.term
            Attr appliedToServices is Super().reservedAccum.appliesToProcedureCategories
            Attr networkType is Super().reservedAccum.networkType
        }
    }

    Producer createExtLink(uri) {
        Attr _uri is uri
    }
}