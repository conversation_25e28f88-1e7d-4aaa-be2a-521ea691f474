// tslint:disable
export const KRAKEN_MODEL_TREE_CAPDENTALLOSS = {"contexts":{"Reserve":{"name":"Reserve","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"expenseAmount":{"name":"expenseAmount","fieldType":"MONEY","fieldPath":"expenseAmount","cardinality":"SINGLE"},"indemnityAmount":{"name":"indemnityAmount","fieldType":"MONEY","fieldPath":"indemnityAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"recoveryAmount":{"name":"recoveryAmount","fieldType":"MONEY","fieldPath":"recoveryAmount","cardinality":"SINGLE"}},"inheritedContexts":["ClaimReserveAmounts","MessageTypeHolder"],"system":false},"CapPaymentAddition":{"name":"CapPaymentAddition","children":{},"fields":{"additionSource":{"name":"additionSource","fieldType":"ExternalLink","fieldPath":"additionSource","cardinality":"SINGLE"},"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapAlternatePayeeRole":{"name":"CapAlternatePayeeRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"AccessTrackableEntity":{"name":"AccessTrackableEntity","children":{"AccessTrackInfo":{"targetName":"AccessTrackInfo","navigationExpression":{"expressionString":"accessTrackInfo","originalExpressionString":"accessTrackInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"accessTrackInfo":{"name":"accessTrackInfo","fieldType":"AccessTrackInfo","fieldPath":"accessTrackInfo","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"InsuredAware":{"name":"InsuredAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"CapDentalPaymentDetailsEntity":{"name":"CapDentalPaymentDetailsEntity","children":{"CapDentalPaymentAllocationEntity":{"targetName":"CapDentalPaymentAllocationEntity","navigationExpression":{"expressionString":"paymentAllocations","originalExpressionString":"paymentAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPaymentPayeeDetailsEntity":{"targetName":"CapDentalPaymentPayeeDetailsEntity","navigationExpression":{"expressionString":"payeeDetails","originalExpressionString":"payeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payeeDetails":{"name":"payeeDetails","fieldType":"CapDentalPaymentPayeeDetailsEntity","fieldPath":"payeeDetails","cardinality":"SINGLE"},"paymentAllocations":{"name":"paymentAllocations","fieldType":"CapDentalPaymentAllocationEntity","fieldPath":"paymentAllocations","cardinality":"MULTIPLE","forbidTarget":true},"paymentAdditions":{"name":"paymentAdditions","fieldType":"CapPaymentAddition","fieldPath":"paymentAdditions","cardinality":"MULTIPLE","forbidTarget":true},"paymentReductions":{"name":"paymentReductions","fieldType":"CapPaymentReduction","fieldPath":"paymentReductions","cardinality":"MULTIPLE","forbidTarget":true},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentTaxes":{"name":"paymentTaxes","fieldType":"CapPaymentTax","fieldPath":"paymentTaxes","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CapPaymentDetails"],"system":false},"CapPayment":{"name":"CapPayment","children":{"CapPaymentDetails":{"targetName":"CapPaymentDetails","navigationExpression":{"expressionString":"paymentDetails","originalExpressionString":"paymentDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"paymentDetails":{"name":"paymentDetails","fieldType":"CapPaymentDetails","fieldPath":"paymentDetails","cardinality":"SINGLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentInfo","RootEntity"],"system":false},"CapBalanceItem":{"name":"CapBalanceItem","children":{"CapBalanceItemAllocation":{"targetName":"CapBalanceItemAllocation","navigationExpression":{"expressionString":"actualAllocations","originalExpressionString":"actualAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"actualAllocations":{"name":"actualAllocations","fieldType":"CapBalanceItemAllocation","fieldPath":"actualAllocations","cardinality":"MULTIPLE","forbidTarget":true},"scheduledNetAmount":{"name":"scheduledNetAmount","fieldType":"MONEY","fieldPath":"scheduledNetAmount","cardinality":"SINGLE"},"actualNetAmount":{"name":"actualNetAmount","fieldType":"MONEY","fieldPath":"actualNetAmount","cardinality":"SINGLE"},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"balancedNetAmount":{"name":"balancedNetAmount","fieldType":"MONEY","fieldPath":"balancedNetAmount","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"TimedCoverableItemCondition":{"name":"TimedCoverableItemCondition","children":{},"fields":{"period":{"name":"period","fieldType":"STRING","fieldPath":"period","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"ExternalReference":{"name":"ExternalReference","children":{},"fields":{"sourceId":{"name":"sourceId","fieldType":"STRING","fieldPath":"sourceId","cardinality":"SINGLE"},"reference":{"name":"reference","fieldType":"ExternalLink","fieldPath":"reference","cardinality":"SINGLE"},"sourceType":{"name":"sourceType","fieldType":"STRING","fieldPath":"sourceType","cardinality":"SINGLE"},"sourceSystem":{"name":"sourceSystem","fieldType":"STRING","fieldPath":"sourceSystem","cardinality":"SINGLE"},"sourceSubType":{"name":"sourceSubType","fieldType":"STRING","fieldPath":"sourceSubType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"MainInsuredInfo":{"name":"MainInsuredInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"CapDentalPaymentAllocationAccumulatorDetailsEntity":{"name":"CapDentalPaymentAllocationAccumulatorDetailsEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"appliesToProcedureCategories":{"name":"appliesToProcedureCategories","fieldType":"STRING","fieldPath":"appliesToProcedureCategories","cardinality":"MULTIPLE"},"renewalType":{"name":"renewalType","fieldType":"STRING","fieldPath":"renewalType","cardinality":"SINGLE"},"appliesToProcedureCategory":{"name":"appliesToProcedureCategory","fieldType":"STRING","fieldPath":"appliesToProcedureCategory","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"networkType":{"name":"networkType","fieldType":"STRING","fieldPath":"networkType","cardinality":"SINGLE"},"accumulatorAmount":{"name":"accumulatorAmount","fieldType":"MONEY","fieldPath":"accumulatorAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPaymentPayeeDetailsEntity":{"name":"CapDentalPaymentPayeeDetailsEntity","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"paymentMethodDetails":{"name":"paymentMethodDetails","fieldType":"CapPaymentMethodDetails","fieldPath":"paymentMethodDetails","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["CapPaymentPayeeDetails"],"system":false},"CapDentalProviderFeesEntity":{"name":"CapDentalProviderFeesEntity","children":{},"fields":{"fee":{"name":"fee","fieldType":"MONEY","fieldPath":"fee","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"PolicyIdentifierHolder":{"name":"PolicyIdentifierHolder","children":{},"fields":{"policySystemId":{"name":"policySystemId","fieldType":"ExternalLink","fieldPath":"policySystemId","cardinality":"SINGLE"},"policySystem":{"name":"policySystem","fieldType":"STRING","fieldPath":"policySystem","cardinality":"SINGLE"},"policySystemRevisionNo":{"name":"policySystemRevisionNo","fieldType":"INTEGER","fieldPath":"policySystemRevisionNo","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"EligibilityOverride":{"name":"EligibilityOverride","children":{},"fields":{"ruleCd":{"name":"ruleCd","fieldType":"STRING","fieldPath":"ruleCd","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyInfo":{"name":"CapPolicyInfo","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapPaymentDetailsTemplate":{"name":"CapPaymentDetailsTemplate","children":{"CapPaymentAllocationTemplate":{"targetName":"CapPaymentAllocationTemplate","navigationExpression":{"expressionString":"paymentAllocationTemplates","originalExpressionString":"paymentAllocationTemplates","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentAllocationTemplates":{"name":"paymentAllocationTemplates","fieldType":"CapPaymentAllocationTemplate","fieldPath":"paymentAllocationTemplates","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapTimelineClaimInfo":{"name":"CapTimelineClaimInfo","children":{},"fields":{"appliedCoverage":{"name":"appliedCoverage","fieldType":"STRING","fieldPath":"appliedCoverage","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"examinerName":{"name":"examinerName","fieldType":"STRING","fieldPath":"examinerName","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"self":{"name":"self","fieldType":"ExternalLink","fieldPath":"self","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":["SelfAware"],"system":false},"CapPatientRole":{"name":"CapPatientRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"PrimaryEntityLinkAware":{"name":"PrimaryEntityLinkAware","children":{},"fields":{"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationTemplateLossInfo":{"name":"CapPaymentAllocationTemplateLossInfo","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPaymentAllocationLossInfoEntity":{"name":"CapDentalPaymentAllocationLossInfoEntity","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAllocationLossInfo"],"system":false},"CoverableItemHolder":{"name":"CoverableItemHolder","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"RequestedReserve":{"name":"RequestedReserve","children":{"RequestedReserveItem":{"targetName":"RequestedReserveItem","navigationExpression":{"expressionString":"items","originalExpressionString":"items","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"modificationReason":{"name":"modificationReason","fieldType":"STRING","fieldPath":"modificationReason","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"items":{"name":"items","fieldType":"RequestedReserveItem","fieldPath":"items","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapDentalPaymentAllocationPayableItemEntity":{"name":"CapDentalPaymentAllocationPayableItemEntity","children":{},"fields":{"orthoMonth":{"name":"orthoMonth","fieldType":"INTEGER","fieldPath":"orthoMonth","cardinality":"SINGLE"},"claimSource":{"name":"claimSource","fieldType":"ExternalLink","fieldPath":"claimSource","cardinality":"SINGLE"},"procedureID":{"name":"procedureID","fieldType":"STRING","fieldPath":"procedureID","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAllocationPayableItem"],"system":false},"InsuredInfoAware":{"name":"InsuredInfoAware","children":{},"fields":{"isMain":{"name":"isMain","fieldType":"BOOLEAN","fieldPath":"isMain","cardinality":"SINGLE"},"registryTypeId":{"name":"registryTypeId","fieldType":"STRING","fieldPath":"registryTypeId","cardinality":"SINGLE"},"insuredRoleNameCd":{"name":"insuredRoleNameCd","fieldType":"STRING","fieldPath":"insuredRoleNameCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"DistanceBoundCoverableItemCondition":{"name":"DistanceBoundCoverableItemCondition","children":{},"fields":{"distanceMoreThan":{"name":"distanceMoreThan","fieldType":"DECIMAL","fieldPath":"distanceMoreThan","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapPaymentTaxTemplate":{"name":"CapPaymentTaxTemplate","children":{},"fields":{"taxSource":{"name":"taxSource","fieldType":"ExternalLink","fieldPath":"taxSource","cardinality":"SINGLE"},"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentTax"],"system":false},"CapScheduledPayment":{"name":"CapScheduledPayment","children":{"CapPaymentDetails":{"targetName":"CapPaymentDetails","navigationExpression":{"expressionString":"paymentDetails","originalExpressionString":"paymentDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"paymentDetails":{"name":"paymentDetails","fieldType":"CapPaymentDetails","fieldPath":"paymentDetails","cardinality":"SINGLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentInfo"],"system":false},"Applicability":{"name":"Applicability","children":{"CoverableItemHolder":{"targetName":"CoverableItemHolder","navigationExpression":{"expressionString":"itemHolder","originalExpressionString":"itemHolder","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"itemHolder":{"name":"itemHolder","fieldType":"CoverableItemHolder","fieldPath":"itemHolder","cardinality":"SINGLE","forbidTarget":true},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapPaymentInfo":{"name":"CapPaymentInfo","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"paymentNetAmount":{"name":"paymentNetAmount","fieldType":"MONEY","fieldPath":"paymentNetAmount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CorrelationIdHolder":{"name":"CorrelationIdHolder","children":{},"fields":{"correlationId":{"name":"correlationId","fieldType":"STRING","fieldPath":"correlationId","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentSchedule":{"name":"CapPaymentSchedule","children":{"CapScheduledPayment":{"targetName":"CapScheduledPayment","navigationExpression":{"expressionString":"payments","originalExpressionString":"payments","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"payments":{"name":"payments","fieldType":"CapScheduledPayment","fieldPath":"payments","cardinality":"MULTIPLE","forbidTarget":true},"paymentTemplate":{"name":"paymentTemplate","fieldType":"ExternalLink","fieldPath":"paymentTemplate","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"scheduleNumber":{"name":"scheduleNumber","fieldType":"STRING","fieldPath":"scheduleNumber","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"Retriable":{"name":"Retriable","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapProviderRole":{"name":"CapProviderRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"},"providerLink":{"name":"providerLink","fieldType":"STRING","fieldPath":"providerLink","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false},"JsonType":{"name":"JsonType","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapClaimHeaderCase":{"name":"CapClaimHeaderCase","children":{},"fields":{"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocation":{"name":"CapPaymentAllocation","children":{"CapPaymentAllocationTaxSplitResult":{"targetName":"CapPaymentAllocationTaxSplitResult","navigationExpression":{"expressionString":"taxSplitResults","originalExpressionString":"taxSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationLossInfo":{"targetName":"CapPaymentAllocationLossInfo","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPaymentAllocationReductionSplitResult":{"targetName":"CapPaymentAllocationReductionSplitResult","navigationExpression":{"expressionString":"reductionSplitResults","originalExpressionString":"reductionSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationAdditionSplitResult":{"targetName":"CapPaymentAllocationAdditionSplitResult","navigationExpression":{"expressionString":"additionSplitResults","originalExpressionString":"additionSplitResults","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationPayableItem":{"targetName":"CapPaymentAllocationPayableItem","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapPaymentAllocationLossInfo","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapPaymentAllocationPayableItem","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"taxSplitResults":{"name":"taxSplitResults","fieldType":"CapPaymentAllocationTaxSplitResult","fieldPath":"taxSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"reserveType":{"name":"reserveType","fieldType":"STRING","fieldPath":"reserveType","cardinality":"SINGLE"},"additionSplitResults":{"name":"additionSplitResults","fieldType":"CapPaymentAllocationAdditionSplitResult","fieldPath":"additionSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"reductionSplitResults":{"name":"reductionSplitResults","fieldType":"CapPaymentAllocationReductionSplitResult","fieldPath":"reductionSplitResults","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapPolicy":{"name":"CapPolicy","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPolicyExclusion":{"targetName":"CapPolicyExclusion","navigationExpression":{"expressionString":"exclusions","originalExpressionString":"exclusions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsurableRisk":{"targetName":"InsurableRisk","navigationExpression":{"expressionString":"risks","originalExpressionString":"risks","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"exclusions":{"name":"exclusions","fieldType":"CapPolicyExclusion","fieldPath":"exclusions","cardinality":"MULTIPLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"risks":{"name":"risks","fieldType":"InsurableRisk","fieldPath":"risks","cardinality":"MULTIPLE","forbidTarget":true},"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemHolder","CapPolicyInfo","RootEntity","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapDentalProcedureCoordinationOfBenefitsEntity":{"name":"CapDentalProcedureCoordinationOfBenefitsEntity","children":{},"fields":{"coverageType":{"name":"coverageType","fieldType":"STRING","fieldPath":"coverageType","cardinality":"SINGLE"},"primaryCoverageStatus":{"name":"primaryCoverageStatus","fieldType":"STRING","fieldPath":"primaryCoverageStatus","cardinality":"SINGLE"},"allowed":{"name":"allowed","fieldType":"MONEY","fieldPath":"allowed","cardinality":"SINGLE"},"considered":{"name":"considered","fieldType":"MONEY","fieldPath":"considered","cardinality":"SINGLE"},"innOnn":{"name":"innOnn","fieldType":"STRING","fieldPath":"innOnn","cardinality":"SINGLE"},"paid":{"name":"paid","fieldType":"MONEY","fieldPath":"paid","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"TimeZoneInfo":{"name":"TimeZoneInfo","children":{},"fields":{"timeZoneId":{"name":"timeZoneId","fieldType":"STRING","fieldPath":"timeZoneId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"AbsenceRefHolder":{"name":"AbsenceRefHolder","children":{},"fields":{"absence":{"name":"absence","fieldType":"ExternalLink","fieldPath":"absence","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationAdditionSplitResult":{"name":"CapPaymentAllocationAdditionSplitResult","children":{},"fields":{"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceItemAllocationTax":{"name":"CapBalanceItemAllocationTax","children":{},"fields":{"taxSubType":{"name":"taxSubType","fieldType":"STRING","fieldPath":"taxSubType","cardinality":"SINGLE"},"taxState":{"name":"taxState","fieldType":"STRING","fieldPath":"taxState","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalDiagnosisCodeEntity":{"name":"CapDentalDiagnosisCodeEntity","children":{},"fields":{"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"qualifier":{"name":"qualifier","fieldType":"STRING","fieldPath":"qualifier","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"RetriableMessage":{"name":"RetriableMessage","children":{},"fields":{"severity":{"name":"severity","fieldType":"STRING","fieldPath":"severity","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":["MessageType"],"system":false},"CapDentalTreatmentReasonEntity":{"name":"CapDentalTreatmentReasonEntity","children":{},"fields":{"treatmentResultingFrom":{"name":"treatmentResultingFrom","fieldType":"STRING","fieldPath":"treatmentResultingFrom","cardinality":"SINGLE"},"autoAccidentState":{"name":"autoAccidentState","fieldType":"STRING","fieldPath":"autoAccidentState","cardinality":"SINGLE"},"dateOfAccident":{"name":"dateOfAccident","fieldType":"DATE","fieldPath":"dateOfAccident","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceItemAllocationAddition":{"name":"CapBalanceItemAllocationAddition","children":{},"fields":{"additionSubType":{"name":"additionSubType","fieldType":"STRING","fieldPath":"additionSubType","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapTimelineMetric":{"name":"CapTimelineMetric","children":{},"fields":{"unit":{"name":"unit","fieldType":"STRING","fieldPath":"unit","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"value":{"name":"value","fieldType":"DECIMAL","fieldPath":"value","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalDetailEntity":{"name":"CapDentalDetailEntity","children":{"CapDentalClaimDataEntity":{"targetName":"CapDentalClaimDataEntity","navigationExpression":{"expressionString":"claimData","originalExpressionString":"claimData","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalProcedureEntity":{"targetName":"CapDentalProcedureEntity","navigationExpression":{"expressionString":"submittedProcedures","originalExpressionString":"submittedProcedures","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"submittedProcedures":{"name":"submittedProcedures","fieldType":"CapDentalProcedureEntity","fieldPath":"submittedProcedures","cardinality":"MULTIPLE"},"lossDesc":{"name":"lossDesc","fieldType":"STRING","fieldPath":"lossDesc","cardinality":"SINGLE"},"claimData":{"name":"claimData","fieldType":"CapDentalClaimDataEntity","fieldPath":"claimData","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["LossDetail"],"system":false},"CapDentalBaseClaimData":{"name":"CapDentalBaseClaimData","children":{"CapProviderRole":{"targetName":"CapProviderRole","navigationExpression":{"expressionString":"providerRole","originalExpressionString":"providerRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapAlternatePayeeRole":{"targetName":"CapAlternatePayeeRole","navigationExpression":{"expressionString":"alternatePayeeRole","originalExpressionString":"alternatePayeeRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimCoordinationOfBenefitsEntity":{"targetName":"CapDentalClaimCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderFeesEntity":{"targetName":"CapDentalProviderFeesEntity","navigationExpression":{"expressionString":"providerFees","originalExpressionString":"providerFees","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderDiscountEntity":{"targetName":"CapDentalProviderDiscountEntity","navigationExpression":{"expressionString":"providerDiscount","originalExpressionString":"providerDiscount","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalTreatmentReasonEntity":{"targetName":"CapDentalTreatmentReasonEntity","navigationExpression":{"expressionString":"treatmentReason","originalExpressionString":"treatmentReason","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPatientRole":{"targetName":"CapPatientRole","navigationExpression":{"expressionString":"patientRole","originalExpressionString":"patientRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyholderRole":{"targetName":"CapPolicyholderRole","navigationExpression":{"expressionString":"policyholderRole","originalExpressionString":"policyholderRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"providerFees":{"name":"providerFees","fieldType":"CapDentalProviderFeesEntity","fieldPath":"providerFees","cardinality":"MULTIPLE"},"providerRole":{"name":"providerRole","fieldType":"CapProviderRole","fieldPath":"providerRole","cardinality":"SINGLE"},"digitalImageNumbers":{"name":"digitalImageNumbers","fieldType":"STRING","fieldPath":"digitalImageNumbers","cardinality":"MULTIPLE"},"payeeType":{"name":"payeeType","fieldType":"STRING","fieldPath":"payeeType","cardinality":"SINGLE"},"cleanClaimDate":{"name":"cleanClaimDate","fieldType":"DATE","fieldPath":"cleanClaimDate","cardinality":"SINGLE"},"patientRole":{"name":"patientRole","fieldType":"CapPatientRole","fieldPath":"patientRole","cardinality":"SINGLE"},"isUnknownOrIntProvider":{"name":"isUnknownOrIntProvider","fieldType":"BOOLEAN","fieldPath":"isUnknownOrIntProvider","cardinality":"SINGLE"},"treatmentReason":{"name":"treatmentReason","fieldType":"CapDentalTreatmentReasonEntity","fieldPath":"treatmentReason","cardinality":"SINGLE","forbidTarget":true},"dateOfBirth":{"name":"dateOfBirth","fieldType":"DATE","fieldPath":"dateOfBirth","cardinality":"SINGLE"},"remark":{"name":"remark","fieldType":"STRING","fieldPath":"remark","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"providerDiscount":{"name":"providerDiscount","fieldType":"CapDentalProviderDiscountEntity","fieldPath":"providerDiscount","cardinality":"SINGLE"},"missingTooths":{"name":"missingTooths","fieldType":"STRING","fieldPath":"missingTooths","cardinality":"MULTIPLE"},"alternatePayeeRole":{"name":"alternatePayeeRole","fieldType":"CapAlternatePayeeRole","fieldPath":"alternatePayeeRole","cardinality":"SINGLE"},"transactionType":{"name":"transactionType","fieldType":"STRING","fieldPath":"transactionType","cardinality":"SINGLE"},"policyholderRole":{"name":"policyholderRole","fieldType":"CapPolicyholderRole","fieldPath":"policyholderRole","cardinality":"SINGLE"},"initialDateOfService":{"name":"initialDateOfService","fieldType":"DATETIME","fieldPath":"initialDateOfService","cardinality":"SINGLE"},"placeOfTreatment":{"name":"placeOfTreatment","fieldType":"STRING","fieldPath":"placeOfTreatment","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalClaimCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"MULTIPLE","forbidTarget":true},"receivedDate":{"name":"receivedDate","fieldType":"DATE","fieldPath":"receivedDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalLossPolicyEntity":{"name":"CapDentalLossPolicyEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"planName":{"name":"planName","fieldType":"STRING","fieldPath":"planName","cardinality":"SINGLE"},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"plan":{"name":"plan","fieldType":"STRING","fieldPath":"plan","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapPolicyInfo","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"CapPaymentAllocationTaxSplitResult":{"name":"CapPaymentAllocationTaxSplitResult","children":{},"fields":{"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"SelfAware":{"name":"SelfAware","children":{},"fields":{"self":{"name":"self","fieldType":"ExternalLink","fieldPath":"self","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationReductionSplitResult":{"name":"CapPaymentAllocationReductionSplitResult","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPartyInfo":{"name":"ClaimPartyInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"CapDentalProviderDiscountsFeesEntity":{"name":"CapDentalProviderDiscountsFeesEntity","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapTimelineEvent":{"name":"CapTimelineEvent","children":{},"fields":{"manuallyAdded":{"name":"manuallyAdded","fieldType":"BOOLEAN","fieldPath":"manuallyAdded","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"eventDate":{"name":"eventDate","fieldType":"DATETIME","fieldPath":"eventDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceChangeLogDetails":{"name":"CapBalanceChangeLogDetails","children":{},"fields":{"totalBalanceAmount":{"name":"totalBalanceAmount","fieldType":"MONEY","fieldPath":"totalBalanceAmount","cardinality":"SINGLE"},"transactionNumber":{"name":"transactionNumber","fieldType":"STRING","fieldPath":"transactionNumber","cardinality":"SINGLE"},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"transactionTypeCd":{"name":"transactionTypeCd","fieldType":"STRING","fieldPath":"transactionTypeCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementResult":{"name":"CapSettlementResult","children":{},"fields":{"reserve":{"name":"reserve","fieldType":"DECIMAL","fieldPath":"reserve","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAllocationPayableItem":{"name":"CapPaymentAllocationPayableItem","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapPaymentPayeeDetails":{"name":"CapPaymentPayeeDetails","children":{"CapPaymentMethodDetails":{"targetName":"CapPaymentMethodDetails","navigationExpression":{"expressionString":"paymentMethodDetails","originalExpressionString":"paymentMethodDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"paymentMethodDetails":{"name":"paymentMethodDetails","fieldType":"CapPaymentMethodDetails","fieldPath":"paymentMethodDetails","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"PaymentDetailAwareSettlementResult":{"name":"PaymentDetailAwareSettlementResult","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"allocationPeriod","originalExpressionString":"allocationPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"allocationPeriod":{"name":"allocationPeriod","fieldType":"Period","fieldPath":"allocationPeriod","cardinality":"SINGLE","forbidTarget":true},"initiatePayment":{"name":"initiatePayment","fieldType":"BOOLEAN","fieldPath":"initiatePayment","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalance":{"name":"CapBalance","children":{"CapBalanceSuspenseItem":{"targetName":"CapBalanceSuspenseItem","navigationExpression":{"expressionString":"suspenseItems","originalExpressionString":"suspenseItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItem":{"targetName":"CapBalanceItem","navigationExpression":{"expressionString":"balanceItems","originalExpressionString":"balanceItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"totalBalanceAmount":{"name":"totalBalanceAmount","fieldType":"MONEY","fieldPath":"totalBalanceAmount","cardinality":"SINGLE"},"balanceItems":{"name":"balanceItems","fieldType":"CapBalanceItem","fieldPath":"balanceItems","cardinality":"MULTIPLE","forbidTarget":true},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"suspenseItems":{"name":"suspenseItems","fieldType":"CapBalanceSuspenseItem","fieldPath":"suspenseItems","cardinality":"MULTIPLE","forbidTarget":true},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"ExternalReferenceHolder":{"name":"ExternalReferenceHolder","children":{"ExternalReference":{"targetName":"ExternalReference","navigationExpression":{"expressionString":"externalReference","originalExpressionString":"externalReference","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"externalReference":{"name":"externalReference","fieldType":"ExternalReference","fieldPath":"externalReference","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"Generatable":{"name":"Generatable","children":{},"fields":{"isGenerated":{"name":"isGenerated","fieldType":"BOOLEAN","fieldPath":"isGenerated","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"UnverifiedPolicy":{"name":"UnverifiedPolicy","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"term","originalExpressionString":"term","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapInsuredInfo":{"targetName":"CapInsuredInfo","navigationExpression":{"expressionString":"insureds","originalExpressionString":"insureds","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"currencyCd":{"name":"currencyCd","fieldType":"STRING","fieldPath":"currencyCd","cardinality":"SINGLE"},"riskStateCd":{"name":"riskStateCd","fieldType":"STRING","fieldPath":"riskStateCd","cardinality":"SINGLE"},"isVerified":{"name":"isVerified","fieldType":"BOOLEAN","fieldPath":"isVerified","cardinality":"SINGLE"},"insureds":{"name":"insureds","fieldType":"CapInsuredInfo","fieldPath":"insureds","cardinality":"MULTIPLE","forbidTarget":true},"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyType":{"name":"policyType","fieldType":"STRING","fieldPath":"policyType","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"term":{"name":"term","fieldType":"Term","fieldPath":"term","cardinality":"SINGLE","forbidTarget":true},"policyStatus":{"name":"policyStatus","fieldType":"STRING","fieldPath":"policyStatus","cardinality":"SINGLE"},"txEffectiveDate":{"name":"txEffectiveDate","fieldType":"STRING","fieldPath":"txEffectiveDate","cardinality":"SINGLE"},"capPolicyVersionId":{"name":"capPolicyVersionId","fieldType":"STRING","fieldPath":"capPolicyVersionId","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["CapPolicyInfo","RootEntity","CapClaimHeaderPolicy","ClaimPolicyAware"],"system":false},"PartyInfo":{"name":"PartyInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["Retriable","MessageTypeHolder"],"system":false},"SubstateAware":{"name":"SubstateAware","children":{},"fields":{"substate":{"name":"substate","fieldType":"STRING","fieldPath":"substate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalPaymentAllocationDentalDetailsEntity":{"name":"CapDentalPaymentAllocationDentalDetailsEntity","children":{"CapDentalPaymentAllocationAccumulatorDetailsEntity":{"targetName":"CapDentalPaymentAllocationAccumulatorDetailsEntity","navigationExpression":{"expressionString":"accumulatorDetails","originalExpressionString":"accumulatorDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"accumulatorDetails":{"name":"accumulatorDetails","fieldType":"CapDentalPaymentAllocationAccumulatorDetailsEntity","fieldPath":"accumulatorDetails","cardinality":"MULTIPLE","forbidTarget":true},"patient":{"name":"patient","fieldType":"ExternalLink","fieldPath":"patient","cardinality":"SINGLE"},"transactionTypeCd":{"name":"transactionTypeCd","fieldType":"STRING","fieldPath":"transactionTypeCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPartyRole":{"name":"ClaimPartyRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"CapDentalPaymentAllocationEntity":{"name":"CapDentalPaymentAllocationEntity","children":{"CapDentalPaymentAllocationLossInfoEntity":{"targetName":"CapDentalPaymentAllocationLossInfoEntity","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPaymentAllocationPayableItemEntity":{"targetName":"CapDentalPaymentAllocationPayableItemEntity","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalPaymentAllocationDentalDetailsEntity":{"targetName":"CapDentalPaymentAllocationDentalDetailsEntity","navigationExpression":{"expressionString":"allocationDentalDetails","originalExpressionString":"allocationDentalDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapDentalPaymentAllocationLossInfoEntity","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapDentalPaymentAllocationPayableItemEntity","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"taxSplitResults":{"name":"taxSplitResults","fieldType":"CapPaymentAllocationTaxSplitResult","fieldPath":"taxSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"reserveType":{"name":"reserveType","fieldType":"STRING","fieldPath":"reserveType","cardinality":"SINGLE"},"allocationDentalDetails":{"name":"allocationDentalDetails","fieldType":"CapDentalPaymentAllocationDentalDetailsEntity","fieldPath":"allocationDentalDetails","cardinality":"SINGLE","forbidTarget":true},"additionSplitResults":{"name":"additionSplitResults","fieldType":"CapPaymentAllocationAdditionSplitResult","fieldPath":"additionSplitResults","cardinality":"MULTIPLE","forbidTarget":true},"reductionSplitResults":{"name":"reductionSplitResults","fieldType":"CapPaymentAllocationReductionSplitResult","fieldPath":"reductionSplitResults","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CapPaymentAllocation"],"system":false},"InternalLink":{"name":"InternalLink","children":{},"fields":{},"inheritedContexts":[],"system":true},"CapPaymentMethodDetails":{"name":"CapPaymentMethodDetails","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapEventCaseRefHolder":{"name":"CapEventCaseRefHolder","children":{},"fields":{"eventCaseLink":{"name":"eventCaseLink","fieldType":"ExternalLink","fieldPath":"eventCaseLink","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentAdditionTemplate":{"name":"CapPaymentAdditionTemplate","children":{},"fields":{"additionSource":{"name":"additionSource","fieldType":"ExternalLink","fieldPath":"additionSource","cardinality":"SINGLE"},"additionNumber":{"name":"additionNumber","fieldType":"STRING","fieldPath":"additionNumber","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentAddition"],"system":false},"CapDentalProviderDiscountEntity":{"name":"CapDentalProviderDiscountEntity","children":{},"fields":{"discountPercentage":{"name":"discountPercentage","fieldType":"DECIMAL","fieldPath":"discountPercentage","cardinality":"SINGLE"},"discountName":{"name":"discountName","fieldType":"STRING","fieldPath":"discountName","cardinality":"SINGLE"},"discountAmount":{"name":"discountAmount","fieldType":"MONEY","fieldPath":"discountAmount","cardinality":"SINGLE"},"discountType":{"name":"discountType","fieldType":"STRING","fieldPath":"discountType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyAware":{"name":"CapPolicyAware","children":{},"fields":{"policyIds":{"name":"policyIds","fieldType":"STRING","fieldPath":"policyIds","cardinality":"MULTIPLE"}},"inheritedContexts":[],"system":false},"CapClaimHeaderPolicy":{"name":"CapClaimHeaderPolicy","children":{},"fields":{"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"},"productCd":{"name":"productCd","fieldType":"STRING","fieldPath":"productCd","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPolicyAware"],"system":false},"CoverableItemCondition":{"name":"CoverableItemCondition","children":{},"fields":{},"inheritedContexts":[],"system":false},"LimitedCoverableItemCondition":{"name":"LimitedCoverableItemCondition","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition","Accumulative"],"system":false},"ExternalClaimData":{"name":"ExternalClaimData","children":{"ExternalCapClaimHeader":{"targetName":"ExternalCapClaimHeader","navigationExpression":{"expressionString":"claim","originalExpressionString":"claim","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"sourceId":{"name":"sourceId","fieldType":"STRING","fieldPath":"sourceId","cardinality":"SINGLE"},"sourceType":{"name":"sourceType","fieldType":"STRING","fieldPath":"sourceType","cardinality":"SINGLE"},"sourceSystem":{"name":"sourceSystem","fieldType":"STRING","fieldPath":"sourceSystem","cardinality":"SINGLE"},"claim":{"name":"claim","fieldType":"ExternalCapClaimHeader","fieldPath":"claim","cardinality":"SINGLE","forbidTarget":true},"sourceSubType":{"name":"sourceSubType","fieldType":"STRING","fieldPath":"sourceSubType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Eligibility":{"name":"Eligibility","children":{"CoverableItemHolder":{"targetName":"CoverableItemHolder","navigationExpression":{"expressionString":"itemHolder","originalExpressionString":"itemHolder","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"itemHolder":{"name":"itemHolder","fieldType":"CoverableItemHolder","fieldPath":"itemHolder","cardinality":"SINGLE","forbidTarget":true},"eligibilityCd":{"name":"eligibilityCd","fieldType":"STRING","fieldPath":"eligibilityCd","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapDentalBaseBalanceItemAllocationAddition":{"name":"CapDentalBaseBalanceItemAllocationAddition","children":{},"fields":{"additionSubType":{"name":"additionSubType","fieldType":"STRING","fieldPath":"additionSubType","cardinality":"SINGLE"},"additionType":{"name":"additionType","fieldType":"STRING","fieldPath":"additionType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CoverableItem":{"name":"CoverableItem","children":{"DelayableCoverableItemCondition":{"targetName":"DelayableCoverableItemCondition","navigationExpression":{"expressionString":"delayableCoverableItemCondition","originalExpressionString":"delayableCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"LimitedCoverableItemCondition":{"targetName":"LimitedCoverableItemCondition","navigationExpression":{"expressionString":"limitedCoverableItemCondition","originalExpressionString":"limitedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"RestrictedCoverableItemCondition":{"targetName":"RestrictedCoverableItemCondition","navigationExpression":{"expressionString":"restrictedCoverableItemCondition","originalExpressionString":"restrictedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"PreExistingCoverableItemCondition":{"targetName":"PreExistingCoverableItemCondition","navigationExpression":{"expressionString":"preExistingCoverableItemCondition","originalExpressionString":"preExistingCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyExclusion":{"targetName":"CapPolicyExclusion","navigationExpression":{"expressionString":"exclusions","originalExpressionString":"exclusions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"DistanceBoundCoverableItemCondition":{"targetName":"DistanceBoundCoverableItemCondition","navigationExpression":{"expressionString":"distanceBoundCoverableItemCondition","originalExpressionString":"distanceBoundCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CompositeCoverableItemCondition":{"targetName":"CompositeCoverableItemCondition","navigationExpression":{"expressionString":"compositeCoverableItemCondition","originalExpressionString":"compositeCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"TimedCoverableItemCondition":{"targetName":"TimedCoverableItemCondition","navigationExpression":{"expressionString":"timedCoverableItemCondition","originalExpressionString":"timedCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"OccurrableCoverableItemCondition":{"targetName":"OccurrableCoverableItemCondition","navigationExpression":{"expressionString":"occurrableCoverableItemCondition","originalExpressionString":"occurrableCoverableItemCondition","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"compositeCoverableItemCondition":{"name":"compositeCoverableItemCondition","fieldType":"CompositeCoverableItemCondition","fieldPath":"compositeCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"distanceBoundCoverableItemCondition":{"name":"distanceBoundCoverableItemCondition","fieldType":"DistanceBoundCoverableItemCondition","fieldPath":"distanceBoundCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"occurrableCoverableItemCondition":{"name":"occurrableCoverableItemCondition","fieldType":"OccurrableCoverableItemCondition","fieldPath":"occurrableCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"delayableCoverableItemCondition":{"name":"delayableCoverableItemCondition","fieldType":"DelayableCoverableItemCondition","fieldPath":"delayableCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"exclusions":{"name":"exclusions","fieldType":"CapPolicyExclusion","fieldPath":"exclusions","cardinality":"MULTIPLE","forbidTarget":true},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"preExistingCoverableItemCondition":{"name":"preExistingCoverableItemCondition","fieldType":"PreExistingCoverableItemCondition","fieldPath":"preExistingCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"restrictedCoverableItemCondition":{"name":"restrictedCoverableItemCondition","fieldType":"RestrictedCoverableItemCondition","fieldPath":"restrictedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"timedCoverableItemCondition":{"name":"timedCoverableItemCondition","fieldType":"TimedCoverableItemCondition","fieldPath":"timedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true},"limitedCoverableItemCondition":{"name":"limitedCoverableItemCondition","fieldType":"LimitedCoverableItemCondition","fieldPath":"limitedCoverableItemCondition","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapClaim":{"name":"CapClaim","children":{},"fields":{},"inheritedContexts":[],"system":false},"MemberAware":{"name":"MemberAware","children":{},"fields":{"memberRegistryTypeId":{"name":"memberRegistryTypeId","fieldType":"STRING","fieldPath":"memberRegistryTypeId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentTax":{"name":"CapPaymentTax","children":{},"fields":{"taxSource":{"name":"taxSource","fieldType":"ExternalLink","fieldPath":"taxSource","cardinality":"SINGLE"},"taxNumber":{"name":"taxNumber","fieldType":"STRING","fieldPath":"taxNumber","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ExternalClaimDataHolder":{"name":"ExternalClaimDataHolder","children":{"ExternalClaimData":{"targetName":"ExternalClaimData","navigationExpression":{"expressionString":"externalClaims","originalExpressionString":"externalClaims","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"externalClaims":{"name":"externalClaims","fieldType":"ExternalClaimData","fieldPath":"externalClaims","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CompositeCoverableItemCondition":{"name":"CompositeCoverableItemCondition","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"items","originalExpressionString":"items","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"items":{"name":"items","fieldType":"CoverableItem","fieldPath":"items","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["CoverableItemCondition"],"system":false},"AccessTrackInfo":{"name":"AccessTrackInfo","children":{},"fields":{"updatedBy":{"name":"updatedBy","fieldType":"STRING","fieldPath":"updatedBy","cardinality":"SINGLE"},"createdBy":{"name":"createdBy","fieldType":"STRING","fieldPath":"createdBy","cardinality":"SINGLE"},"updatedOn":{"name":"updatedOn","fieldType":"DATETIME","fieldPath":"updatedOn","cardinality":"SINGLE"},"createdOn":{"name":"createdOn","fieldType":"DATETIME","fieldPath":"createdOn","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlementDetail":{"name":"CapSettlementDetail","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimPolicyAware":{"name":"ClaimPolicyAware","children":{},"fields":{"capPolicyId":{"name":"capPolicyId","fieldType":"STRING","fieldPath":"capPolicyId","cardinality":"SINGLE"},"policyNumber":{"name":"policyNumber","fieldType":"STRING","fieldPath":"policyNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapClaimEvent":{"name":"CapClaimEvent","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimReserveAmounts":{"name":"ClaimReserveAmounts","children":{},"fields":{"expenseAmount":{"name":"expenseAmount","fieldType":"MONEY","fieldPath":"expenseAmount","cardinality":"SINGLE"},"indemnityAmount":{"name":"indemnityAmount","fieldType":"MONEY","fieldPath":"indemnityAmount","cardinality":"SINGLE"},"recoveryAmount":{"name":"recoveryAmount","fieldType":"MONEY","fieldPath":"recoveryAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapSettlement":{"name":"CapSettlement","children":{"CapSettlementDetail":{"targetName":"CapSettlementDetail","navigationExpression":{"expressionString":"settlementDetail","originalExpressionString":"settlementDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"AccessTrackInfo":{"targetName":"AccessTrackInfo","navigationExpression":{"expressionString":"accessTrackInfo","originalExpressionString":"accessTrackInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementLossInfo":{"targetName":"CapSettlementLossInfo","navigationExpression":{"expressionString":"settlementLossInfo","originalExpressionString":"settlementLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementAbsenceInfo":{"targetName":"CapSettlementAbsenceInfo","navigationExpression":{"expressionString":"settlementAbsenceInfo","originalExpressionString":"settlementAbsenceInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapSettlementResult":{"targetName":"CapSettlementResult","navigationExpression":{"expressionString":"settlementResult","originalExpressionString":"settlementResult","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"settlementType":{"name":"settlementType","fieldType":"STRING","fieldPath":"settlementType","cardinality":"SINGLE"},"settlementDetail":{"name":"settlementDetail","fieldType":"CapSettlementDetail","fieldPath":"settlementDetail","cardinality":"SINGLE","forbidTarget":true},"claimLossIdentification":{"name":"claimLossIdentification","fieldType":"ExternalLink","fieldPath":"claimLossIdentification","cardinality":"SINGLE"},"accessTrackInfo":{"name":"accessTrackInfo","fieldType":"AccessTrackInfo","fieldPath":"accessTrackInfo","cardinality":"SINGLE","forbidTarget":true},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"settlementNumber":{"name":"settlementNumber","fieldType":"STRING","fieldPath":"settlementNumber","cardinality":"SINGLE"},"settlementResult":{"name":"settlementResult","fieldType":"CapSettlementResult","fieldPath":"settlementResult","cardinality":"SINGLE","forbidTarget":true},"settlementAbsenceInfo":{"name":"settlementAbsenceInfo","fieldType":"CapSettlementAbsenceInfo","fieldPath":"settlementAbsenceInfo","cardinality":"SINGLE","forbidTarget":true},"settlementLossInfo":{"name":"settlementLossInfo","fieldType":"CapSettlementLossInfo","fieldPath":"settlementLossInfo","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentAllocationTemplate":{"name":"CapPaymentAllocationTemplate","children":{"CapPaymentAllocationTemplateLossInfo":{"targetName":"CapPaymentAllocationTemplateLossInfo","navigationExpression":{"expressionString":"allocationLossInfo","originalExpressionString":"allocationLossInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPaymentAllocationTemplatePayeeDetails":{"targetName":"CapPaymentAllocationTemplatePayeeDetails","navigationExpression":{"expressionString":"allocationPayeeDetails","originalExpressionString":"allocationPayeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationLobCd":{"name":"allocationLobCd","fieldType":"STRING","fieldPath":"allocationLobCd","cardinality":"SINGLE"},"allocationPayeeDetails":{"name":"allocationPayeeDetails","fieldType":"CapPaymentAllocationTemplatePayeeDetails","fieldPath":"allocationPayeeDetails","cardinality":"SINGLE","forbidTarget":true},"allocationLossInfo":{"name":"allocationLossInfo","fieldType":"CapPaymentAllocationTemplateLossInfo","fieldPath":"allocationLossInfo","cardinality":"SINGLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"MessageType":{"name":"MessageType","children":{},"fields":{"severity":{"name":"severity","fieldType":"STRING","fieldPath":"severity","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"message":{"name":"message","fieldType":"STRING","fieldPath":"message","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Accumulative":{"name":"Accumulative","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"OccurrableCoverableItemCondition":{"name":"OccurrableCoverableItemCondition","children":{},"fields":{"accumulatorType":{"name":"accumulatorType","fieldType":"STRING","fieldPath":"accumulatorType","cardinality":"SINGLE"},"amount":{"name":"amount","fieldType":"DECIMAL","fieldPath":"amount","cardinality":"SINGLE"},"maxOccurrences":{"name":"maxOccurrences","fieldType":"INTEGER","fieldPath":"maxOccurrences","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition","Accumulative"],"system":false},"CapTimeline":{"name":"CapTimeline","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineRow":{"targetName":"CapTimelineRow","navigationExpression":{"expressionString":"rows","originalExpressionString":"rows","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"rows":{"name":"rows","fieldType":"CapTimelineRow","fieldPath":"rows","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CompensationSchedule":{"name":"CompensationSchedule","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"scheduleTypeCd":{"name":"scheduleTypeCd","fieldType":"STRING","fieldPath":"scheduleTypeCd","cardinality":"SINGLE"},"schedule":{"name":"schedule","fieldType":"STRING","fieldPath":"schedule","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"initiatePayment":{"name":"initiatePayment","fieldType":"BOOLEAN","fieldPath":"initiatePayment","cardinality":"SINGLE"},"paymentAmount":{"name":"paymentAmount","fieldType":"MONEY","fieldPath":"paymentAmount","cardinality":"SINGLE"}},"inheritedContexts":["MessageTypeHolder"],"system":false},"CapSettlementAbsenceInfo":{"name":"CapSettlementAbsenceInfo","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimSubjectInfo":{"name":"ClaimSubjectInfo","children":{},"fields":{"subject":{"name":"subject","fieldType":"STRING","fieldPath":"subject","cardinality":"SINGLE"},"subjectId":{"name":"subjectId","fieldType":"STRING","fieldPath":"subjectId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"PreExistingCoverableItemCondition":{"name":"PreExistingCoverableItemCondition","children":{},"fields":{"duration":{"name":"duration","fieldType":"INTEGER","fieldPath":"duration","cardinality":"SINGLE"},"continuouslyInsuredPeriod":{"name":"continuouslyInsuredPeriod","fieldType":"INTEGER","fieldPath":"continuouslyInsuredPeriod","cardinality":"SINGLE"},"lookBackPeriod":{"name":"lookBackPeriod","fieldType":"INTEGER","fieldPath":"lookBackPeriod","cardinality":"SINGLE"},"isApplied":{"name":"isApplied","fieldType":"BOOLEAN","fieldPath":"isApplied","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"treatmentFreePeriod":{"name":"treatmentFreePeriod","fieldType":"INTEGER","fieldPath":"treatmentFreePeriod","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"LossDetail":{"name":"LossDetail","children":{},"fields":{"lossDesc":{"name":"lossDesc","fieldType":"STRING","fieldPath":"lossDesc","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Period":{"name":"Period","children":{},"fields":{"endDate":{"name":"endDate","fieldType":"DATETIME","fieldPath":"endDate","cardinality":"SINGLE"},"startDate":{"name":"startDate","fieldType":"DATETIME","fieldPath":"startDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocationTax":{"name":"CapDentalBaseBalanceItemAllocationTax","children":{},"fields":{"taxSubType":{"name":"taxSubType","fieldType":"STRING","fieldPath":"taxSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"taxType":{"name":"taxType","fieldType":"STRING","fieldPath":"taxType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"Term":{"name":"Term","children":{},"fields":{"effectiveDate":{"name":"effectiveDate","fieldType":"DATETIME","fieldPath":"effectiveDate","cardinality":"SINGLE"},"expirationDate":{"name":"expirationDate","fieldType":"DATETIME","fieldPath":"expirationDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ExternalLink":{"name":"ExternalLink","children":{},"fields":{},"inheritedContexts":[],"system":true},"ClaimantInfo":{"name":"ClaimantInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"lastName":{"name":"lastName","fieldType":"STRING","fieldPath":"lastName","cardinality":"SINGLE"},"city":{"name":"city","fieldType":"STRING","fieldPath":"city","cardinality":"MULTIPLE"},"stateProvinceCd":{"name":"stateProvinceCd","fieldType":"STRING","fieldPath":"stateProvinceCd","cardinality":"MULTIPLE"},"postalCode":{"name":"postalCode","fieldType":"STRING","fieldPath":"postalCode","cardinality":"MULTIPLE"},"legalId":{"name":"legalId","fieldType":"STRING","fieldPath":"legalId","cardinality":"SINGLE"},"customerNumber":{"name":"customerNumber","fieldType":"STRING","fieldPath":"customerNumber","cardinality":"SINGLE"},"legalName":{"name":"legalName","fieldType":"STRING","fieldPath":"legalName","cardinality":"SINGLE"},"firstName":{"name":"firstName","fieldType":"STRING","fieldPath":"firstName","cardinality":"SINGLE"},"phoneNumber":{"name":"phoneNumber","fieldType":"STRING","fieldPath":"phoneNumber","cardinality":"MULTIPLE"},"streetAddress":{"name":"streetAddress","fieldType":"STRING","fieldPath":"streetAddress","cardinality":"MULTIPLE"},"taxId":{"name":"taxId","fieldType":"STRING","fieldPath":"taxId","cardinality":"SINGLE"},"customerId":{"name":"customerId","fieldType":"STRING","fieldPath":"customerId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"email":{"name":"email","fieldType":"STRING","fieldPath":"email","cardinality":"MULTIPLE"}},"inheritedContexts":["PartyInfo","Retriable","MessageTypeHolder"],"system":false},"RootEntity":{"name":"RootEntity","children":{},"fields":{},"inheritedContexts":[],"system":false},"CapTimelinePeriod":{"name":"CapTimelinePeriod","children":{},"fields":{"endDate":{"name":"endDate","fieldType":"DATETIME","fieldPath":"endDate","cardinality":"SINGLE"},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"startDate":{"name":"startDate","fieldType":"DATETIME","fieldPath":"startDate","cardinality":"SINGLE"}},"inheritedContexts":["Period"],"system":false},"CapDentalPreauthorizationEntity":{"name":"CapDentalPreauthorizationEntity","children":{"Period":{"targetName":"Period","navigationExpression":{"expressionString":"authorizationPeriod","originalExpressionString":"authorizationPeriod","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"authorizedBy":{"name":"authorizedBy","fieldType":"STRING","fieldPath":"authorizedBy","cardinality":"SINGLE"},"authorizationPeriod":{"name":"authorizationPeriod","fieldType":"Period","fieldPath":"authorizationPeriod","cardinality":"SINGLE"},"isProcedureAuthorized":{"name":"isProcedureAuthorized","fieldType":"BOOLEAN","fieldPath":"isProcedureAuthorized","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalOrthodonticEntity":{"name":"CapDentalOrthodonticEntity","children":{},"fields":{"months":{"name":"months","fieldType":"INTEGER","fieldPath":"months","cardinality":"SINGLE"},"orthoFrequencyCd":{"name":"orthoFrequencyCd","fieldType":"STRING","fieldPath":"orthoFrequencyCd","cardinality":"SINGLE"},"orthoMonthQuantity":{"name":"orthoMonthQuantity","fieldType":"INTEGER","fieldPath":"orthoMonthQuantity","cardinality":"SINGLE"},"downPayment":{"name":"downPayment","fieldType":"MONEY","fieldPath":"downPayment","cardinality":"SINGLE"},"appliancePlacedDate":{"name":"appliancePlacedDate","fieldType":"DATE","fieldPath":"appliancePlacedDate","cardinality":"SINGLE"},"frequency":{"name":"frequency","fieldType":"STRING","fieldPath":"frequency","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalClaimCoordinationOfBenefitsEntity":{"name":"CapDentalClaimCoordinationOfBenefitsEntity","children":{"Term":{"targetName":"Term","navigationExpression":{"expressionString":"period","originalExpressionString":"period","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"period":{"name":"period","fieldType":"Term","fieldPath":"period","cardinality":"SINGLE","forbidTarget":true},"address":{"name":"address","fieldType":"STRING","fieldPath":"address","cardinality":"SINGLE"},"otherPolicyType":{"name":"otherPolicyType","fieldType":"STRING","fieldPath":"otherPolicyType","cardinality":"SINGLE"},"PolicyNumber":{"name":"PolicyNumber","fieldType":"STRING","fieldPath":"PolicyNumber","cardinality":"SINGLE"},"otherInsuranceCompany":{"name":"otherInsuranceCompany","fieldType":"STRING","fieldPath":"otherInsuranceCompany","cardinality":"SINGLE"},"policyholderFirstName":{"name":"policyholderFirstName","fieldType":"STRING","fieldPath":"policyholderFirstName","cardinality":"SINGLE"},"otherCoverageType":{"name":"otherCoverageType","fieldType":"STRING","fieldPath":"otherCoverageType","cardinality":"SINGLE"},"typeOfCob":{"name":"typeOfCob","fieldType":"STRING","fieldPath":"typeOfCob","cardinality":"SINGLE"},"policyholderRelationshipToPatient":{"name":"policyholderRelationshipToPatient","fieldType":"STRING","fieldPath":"policyholderRelationshipToPatient","cardinality":"SINGLE"},"policyholderGender":{"name":"policyholderGender","fieldType":"STRING","fieldPath":"policyholderGender","cardinality":"SINGLE"},"policyholderDateOfBirth":{"name":"policyholderDateOfBirth","fieldType":"DATE","fieldPath":"policyholderDateOfBirth","cardinality":"SINGLE"},"plan":{"name":"plan","fieldType":"STRING","fieldPath":"plan","cardinality":"SINGLE"},"policyholderLastName":{"name":"policyholderLastName","fieldType":"STRING","fieldPath":"policyholderLastName","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"RestrictedCoverableItemCondition":{"name":"RestrictedCoverableItemCondition","children":{},"fields":{"restriction":{"name":"restriction","fieldType":"STRING","fieldPath":"restriction","cardinality":"SINGLE"},"appliesTo":{"name":"appliesTo","fieldType":"STRING","fieldPath":"appliesTo","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapBalanceItemAllocationReduction":{"name":"CapBalanceItemAllocationReduction","children":{},"fields":{"reductionSubType":{"name":"reductionSubType","fieldType":"STRING","fieldPath":"reductionSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentReductionTemplate":{"name":"CapPaymentReductionTemplate","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"reductionSource":{"name":"reductionSource","fieldType":"ExternalLink","fieldPath":"reductionSource","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":["CapPaymentReduction"],"system":false},"CapDentalProcedureEntity":{"name":"CapDentalProcedureEntity","children":{"CapDentalPreauthorizationEntity":{"targetName":"CapDentalPreauthorizationEntity","navigationExpression":{"expressionString":"preauthorization","originalExpressionString":"preauthorization","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalOrthodonticEntity":{"targetName":"CapDentalOrthodonticEntity","navigationExpression":{"expressionString":"ortho","originalExpressionString":"ortho","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalProcedureCoordinationOfBenefitsEntity":{"targetName":"CapDentalProcedureCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDiagnosisCodeEntity":{"targetName":"CapDentalDiagnosisCodeEntity","navigationExpression":{"expressionString":"diagnosisCodes","originalExpressionString":"diagnosisCodes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"toothSystem":{"name":"toothSystem","fieldType":"STRING","fieldPath":"toothSystem","cardinality":"SINGLE"},"quantity":{"name":"quantity","fieldType":"INTEGER","fieldPath":"quantity","cardinality":"SINGLE"},"procedureCode":{"name":"procedureCode","fieldType":"STRING","fieldPath":"procedureCode","cardinality":"SINGLE"},"preauthorization":{"name":"preauthorization","fieldType":"CapDentalPreauthorizationEntity","fieldPath":"preauthorization","cardinality":"SINGLE","forbidTarget":true},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"ortho":{"name":"ortho","fieldType":"CapDentalOrthodonticEntity","fieldPath":"ortho","cardinality":"SINGLE"},"diagnosisCodes":{"name":"diagnosisCodes","fieldType":"CapDentalDiagnosisCodeEntity","fieldPath":"diagnosisCodes","cardinality":"MULTIPLE","forbidTarget":true},"preauthorizationNumber":{"name":"preauthorizationNumber","fieldType":"STRING","fieldPath":"preauthorizationNumber","cardinality":"SINGLE"},"surfaces":{"name":"surfaces","fieldType":"STRING","fieldPath":"surfaces","cardinality":"MULTIPLE"},"toothArea":{"name":"toothArea","fieldType":"STRING","fieldPath":"toothArea","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalProcedureCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"SINGLE","forbidTarget":true},"predetInd":{"name":"predetInd","fieldType":"BOOLEAN","fieldPath":"predetInd","cardinality":"SINGLE"},"submittedFee":{"name":"submittedFee","fieldType":"MONEY","fieldPath":"submittedFee","cardinality":"SINGLE"},"toothCodes":{"name":"toothCodes","fieldType":"STRING","fieldPath":"toothCodes","cardinality":"MULTIPLE"},"dateOfService":{"name":"dateOfService","fieldType":"DATE","fieldPath":"dateOfService","cardinality":"SINGLE"},"priorProsthesisPlacementDate":{"name":"priorProsthesisPlacementDate","fieldType":"DATE","fieldPath":"priorProsthesisPlacementDate","cardinality":"SINGLE"}},"inheritedContexts":["CapDentalBaseProcedure"],"system":false},"CapBalanceItemAllocation":{"name":"CapBalanceItemAllocation","children":{"CapBalanceItemAllocationTax":{"targetName":"CapBalanceItemAllocationTax","navigationExpression":{"expressionString":"allocationTaxes","originalExpressionString":"allocationTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItemAllocationReduction":{"targetName":"CapBalanceItemAllocationReduction","navigationExpression":{"expressionString":"allocationReductions","originalExpressionString":"allocationReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapBalanceItemAllocationAddition":{"targetName":"CapBalanceItemAllocationAddition","navigationExpression":{"expressionString":"allocationAdditions","originalExpressionString":"allocationAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocationPayableItem":{"targetName":"CapPaymentAllocationPayableItem","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationReductions":{"name":"allocationReductions","fieldType":"CapBalanceItemAllocationReduction","fieldPath":"allocationReductions","cardinality":"MULTIPLE","forbidTarget":true},"allocationTaxes":{"name":"allocationTaxes","fieldType":"CapBalanceItemAllocationTax","fieldPath":"allocationTaxes","cardinality":"MULTIPLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationAdditions":{"name":"allocationAdditions","fieldType":"CapBalanceItemAllocationAddition","fieldPath":"allocationAdditions","cardinality":"MULTIPLE","forbidTarget":true},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapPaymentAllocationPayableItem","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"},"isInterestOnly":{"name":"isInterestOnly","fieldType":"BOOLEAN","fieldPath":"isInterestOnly","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"DelayableCoverableItemCondition":{"name":"DelayableCoverableItemCondition","children":{},"fields":{"delay":{"name":"delay","fieldType":"STRING","fieldPath":"delay","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemCondition"],"system":false},"CapSettlementLossInfo":{"name":"CapSettlementLossInfo","children":{},"fields":{},"inheritedContexts":[],"system":false},"ExternalCapClaimHeader":{"name":"ExternalCapClaimHeader","children":{"CapClaimHeaderCase":{"targetName":"CapClaimHeaderCase","navigationExpression":{"expressionString":"claimCase","originalExpressionString":"claimCase","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"ClaimSubjectInfo":{"targetName":"ClaimSubjectInfo","navigationExpression":{"expressionString":"subjects","originalExpressionString":"subjects","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"ClaimantAware":{"targetName":"ClaimantAware","navigationExpression":{"expressionString":"claimant","originalExpressionString":"claimant","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapClaimHeaderPolicy":{"targetName":"CapClaimHeaderPolicy","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsuredAware":{"targetName":"InsuredAware","navigationExpression":{"expressionString":"insured","originalExpressionString":"insured","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"insured":{"name":"insured","fieldType":"InsuredAware","fieldPath":"insured","cardinality":"SINGLE","forbidTarget":true},"subjects":{"name":"subjects","fieldType":"ClaimSubjectInfo","fieldPath":"subjects","cardinality":"MULTIPLE","forbidTarget":true},"policies":{"name":"policies","fieldType":"CapClaimHeaderPolicy","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true},"totalIncurred":{"name":"totalIncurred","fieldType":"MONEY","fieldPath":"totalIncurred","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"claimCase":{"name":"claimCase","fieldType":"CapClaimHeaderCase","fieldPath":"claimCase","cardinality":"SINGLE","forbidTarget":true},"claimDate":{"name":"claimDate","fieldType":"DATETIME","fieldPath":"claimDate","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"reportedDate":{"name":"reportedDate","fieldType":"DATETIME","fieldPath":"reportedDate","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"},"claimant":{"name":"claimant","fieldType":"ClaimantAware","fieldPath":"claimant","cardinality":"SINGLE","forbidTarget":true},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyHolder":{"name":"CapPolicyHolder","children":{"CapPolicyInfo":{"targetName":"CapPolicyInfo","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"policies":{"name":"policies","fieldType":"CapPolicyInfo","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapFrequencyConfiguration":{"name":"CapFrequencyConfiguration","children":{},"fields":{"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocationReduction":{"name":"CapDentalBaseBalanceItemAllocationReduction","children":{},"fields":{"reductionSubType":{"name":"reductionSubType","fieldType":"STRING","fieldPath":"reductionSubType","cardinality":"SINGLE"},"appliedAmount":{"name":"appliedAmount","fieldType":"MONEY","fieldPath":"appliedAmount","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"LOBEntity":{"name":"LOBEntity","children":{},"fields":{"lobCd":{"name":"lobCd","fieldType":"STRING","fieldPath":"lobCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseProcedure":{"name":"CapDentalBaseProcedure","children":{"CapDentalPreauthorizationEntity":{"targetName":"CapDentalPreauthorizationEntity","navigationExpression":{"expressionString":"preauthorization","originalExpressionString":"preauthorization","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalOrthodonticEntity":{"targetName":"CapDentalOrthodonticEntity","navigationExpression":{"expressionString":"ortho","originalExpressionString":"ortho","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalProcedureCoordinationOfBenefitsEntity":{"targetName":"CapDentalProcedureCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDiagnosisCodeEntity":{"targetName":"CapDentalDiagnosisCodeEntity","navigationExpression":{"expressionString":"diagnosisCodes","originalExpressionString":"diagnosisCodes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"procedureType":{"name":"procedureType","fieldType":"STRING","fieldPath":"procedureType","cardinality":"SINGLE"},"toothSystem":{"name":"toothSystem","fieldType":"STRING","fieldPath":"toothSystem","cardinality":"SINGLE"},"quantity":{"name":"quantity","fieldType":"INTEGER","fieldPath":"quantity","cardinality":"SINGLE"},"procedureCode":{"name":"procedureCode","fieldType":"STRING","fieldPath":"procedureCode","cardinality":"SINGLE"},"preauthorization":{"name":"preauthorization","fieldType":"CapDentalPreauthorizationEntity","fieldPath":"preauthorization","cardinality":"SINGLE","forbidTarget":true},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"},"ortho":{"name":"ortho","fieldType":"CapDentalOrthodonticEntity","fieldPath":"ortho","cardinality":"SINGLE"},"diagnosisCodes":{"name":"diagnosisCodes","fieldType":"CapDentalDiagnosisCodeEntity","fieldPath":"diagnosisCodes","cardinality":"MULTIPLE","forbidTarget":true},"preauthorizationNumber":{"name":"preauthorizationNumber","fieldType":"STRING","fieldPath":"preauthorizationNumber","cardinality":"SINGLE"},"surfaces":{"name":"surfaces","fieldType":"STRING","fieldPath":"surfaces","cardinality":"MULTIPLE"},"toothArea":{"name":"toothArea","fieldType":"STRING","fieldPath":"toothArea","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalProcedureCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"SINGLE","forbidTarget":true},"predetInd":{"name":"predetInd","fieldType":"BOOLEAN","fieldPath":"predetInd","cardinality":"SINGLE"},"submittedFee":{"name":"submittedFee","fieldType":"MONEY","fieldPath":"submittedFee","cardinality":"SINGLE"},"toothCodes":{"name":"toothCodes","fieldType":"STRING","fieldPath":"toothCodes","cardinality":"MULTIPLE"},"dateOfService":{"name":"dateOfService","fieldType":"DATE","fieldPath":"dateOfService","cardinality":"SINGLE"},"priorProsthesisPlacementDate":{"name":"priorProsthesisPlacementDate","fieldType":"DATE","fieldPath":"priorProsthesisPlacementDate","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalLossEntity":{"name":"CapDentalLossEntity","children":{"CapDentalLossPolicyEntity":{"targetName":"CapDentalLossPolicyEntity","navigationExpression":{"expressionString":"policy","originalExpressionString":"policy","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalDetailEntity":{"targetName":"CapDentalDetailEntity","navigationExpression":{"expressionString":"lossDetail","originalExpressionString":"lossDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"lossSubStatusCd":{"name":"lossSubStatusCd","fieldType":"STRING","fieldPath":"lossSubStatusCd","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"reasonDescription":{"name":"reasonDescription","fieldType":"STRING","fieldPath":"reasonDescription","cardinality":"SINGLE"},"policyId":{"name":"policyId","fieldType":"STRING","fieldPath":"policyId","cardinality":"SINGLE"},"lossNumber":{"name":"lossNumber","fieldType":"STRING","fieldPath":"lossNumber","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"lossDetail":{"name":"lossDetail","fieldType":"CapDentalDetailEntity","fieldPath":"lossDetail","cardinality":"SINGLE","forbidTarget":true},"policy":{"name":"policy","fieldType":"CapDentalLossPolicyEntity","fieldPath":"policy","cardinality":"SINGLE","forbidTarget":true},"reasonCd":{"name":"reasonCd","fieldType":"STRING","fieldPath":"reasonCd","cardinality":"SINGLE"}},"inheritedContexts":["CapLoss","SinglePolicyHolder","RootEntity"],"system":false},"CapDentalBalance":{"name":"CapDentalBalance","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentDetails":{"name":"CapPaymentDetails","children":{"CapPaymentAddition":{"targetName":"CapPaymentAddition","navigationExpression":{"expressionString":"paymentAdditions","originalExpressionString":"paymentAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentAllocation":{"targetName":"CapPaymentAllocation","navigationExpression":{"expressionString":"paymentAllocations","originalExpressionString":"paymentAllocations","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentReduction":{"targetName":"CapPaymentReduction","navigationExpression":{"expressionString":"paymentReductions","originalExpressionString":"paymentReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentTax":{"targetName":"CapPaymentTax","navigationExpression":{"expressionString":"paymentTaxes","originalExpressionString":"paymentTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapPaymentPayeeDetails":{"targetName":"CapPaymentPayeeDetails","navigationExpression":{"expressionString":"payeeDetails","originalExpressionString":"payeeDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payeeDetails":{"name":"payeeDetails","fieldType":"CapPaymentPayeeDetails","fieldPath":"payeeDetails","cardinality":"SINGLE","forbidTarget":true},"paymentAllocations":{"name":"paymentAllocations","fieldType":"CapPaymentAllocation","fieldPath":"paymentAllocations","cardinality":"MULTIPLE","forbidTarget":true},"paymentAdditions":{"name":"paymentAdditions","fieldType":"CapPaymentAddition","fieldPath":"paymentAdditions","cardinality":"MULTIPLE","forbidTarget":true},"paymentReductions":{"name":"paymentReductions","fieldType":"CapPaymentReduction","fieldPath":"paymentReductions","cardinality":"MULTIPLE","forbidTarget":true},"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentTaxes":{"name":"paymentTaxes","fieldType":"CapPaymentTax","fieldPath":"paymentTaxes","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapBalanceChangeLog":{"name":"CapBalanceChangeLog","children":{"CapBalanceChangeLogDetails":{"targetName":"CapBalanceChangeLogDetails","navigationExpression":{"expressionString":"balanceChangeLogDetails","originalExpressionString":"balanceChangeLogDetails","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"},"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"balanceChangeLogDetails":{"name":"balanceChangeLogDetails","fieldType":"CapBalanceChangeLogDetails","fieldPath":"balanceChangeLogDetails","cardinality":"SINGLE","forbidTarget":true},"eventCaseLink":{"name":"eventCaseLink","fieldType":"ExternalLink","fieldPath":"eventCaseLink","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"InsurableRisk":{"name":"InsurableRisk","children":{"CoverableItem":{"targetName":"CoverableItem","navigationExpression":{"expressionString":"coverableItems","originalExpressionString":"coverableItems","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"coverableItems":{"name":"coverableItems","fieldType":"CoverableItem","fieldPath":"coverableItems","cardinality":"MULTIPLE","forbidTarget":true},"id":{"name":"id","fieldType":"STRING","fieldPath":"id","cardinality":"SINGLE"}},"inheritedContexts":["CoverableItemHolder"],"system":false},"CapDentalClaimDataEntity":{"name":"CapDentalClaimDataEntity","children":{"CapProviderRole":{"targetName":"CapProviderRole","navigationExpression":{"expressionString":"providerRole","originalExpressionString":"providerRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapAlternatePayeeRole":{"targetName":"CapAlternatePayeeRole","navigationExpression":{"expressionString":"alternatePayeeRole","originalExpressionString":"alternatePayeeRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalClaimCoordinationOfBenefitsEntity":{"targetName":"CapDentalClaimCoordinationOfBenefitsEntity","navigationExpression":{"expressionString":"cob","originalExpressionString":"cob","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderFeesEntity":{"targetName":"CapDentalProviderFeesEntity","navigationExpression":{"expressionString":"providerFees","originalExpressionString":"providerFees","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalProviderDiscountEntity":{"targetName":"CapDentalProviderDiscountEntity","navigationExpression":{"expressionString":"providerDiscount","originalExpressionString":"providerDiscount","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalTreatmentReasonEntity":{"targetName":"CapDentalTreatmentReasonEntity","navigationExpression":{"expressionString":"treatmentReason","originalExpressionString":"treatmentReason","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPatientRole":{"targetName":"CapPatientRole","navigationExpression":{"expressionString":"patientRole","originalExpressionString":"patientRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapPolicyholderRole":{"targetName":"CapPolicyholderRole","navigationExpression":{"expressionString":"policyholderRole","originalExpressionString":"policyholderRole","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"providerFees":{"name":"providerFees","fieldType":"CapDentalProviderFeesEntity","fieldPath":"providerFees","cardinality":"MULTIPLE"},"providerRole":{"name":"providerRole","fieldType":"CapProviderRole","fieldPath":"providerRole","cardinality":"SINGLE"},"digitalImageNumbers":{"name":"digitalImageNumbers","fieldType":"STRING","fieldPath":"digitalImageNumbers","cardinality":"MULTIPLE"},"payeeType":{"name":"payeeType","fieldType":"STRING","fieldPath":"payeeType","cardinality":"SINGLE"},"cleanClaimDate":{"name":"cleanClaimDate","fieldType":"DATE","fieldPath":"cleanClaimDate","cardinality":"SINGLE"},"patientRole":{"name":"patientRole","fieldType":"CapPatientRole","fieldPath":"patientRole","cardinality":"SINGLE"},"isUnknownOrIntProvider":{"name":"isUnknownOrIntProvider","fieldType":"BOOLEAN","fieldPath":"isUnknownOrIntProvider","cardinality":"SINGLE"},"treatmentReason":{"name":"treatmentReason","fieldType":"CapDentalTreatmentReasonEntity","fieldPath":"treatmentReason","cardinality":"SINGLE","forbidTarget":true},"dateOfBirth":{"name":"dateOfBirth","fieldType":"DATE","fieldPath":"dateOfBirth","cardinality":"SINGLE"},"remark":{"name":"remark","fieldType":"STRING","fieldPath":"remark","cardinality":"SINGLE"},"source":{"name":"source","fieldType":"STRING","fieldPath":"source","cardinality":"SINGLE"},"providerDiscount":{"name":"providerDiscount","fieldType":"CapDentalProviderDiscountEntity","fieldPath":"providerDiscount","cardinality":"SINGLE"},"missingTooths":{"name":"missingTooths","fieldType":"STRING","fieldPath":"missingTooths","cardinality":"MULTIPLE"},"alternatePayeeRole":{"name":"alternatePayeeRole","fieldType":"CapAlternatePayeeRole","fieldPath":"alternatePayeeRole","cardinality":"SINGLE"},"transactionType":{"name":"transactionType","fieldType":"STRING","fieldPath":"transactionType","cardinality":"SINGLE"},"policyholderRole":{"name":"policyholderRole","fieldType":"CapPolicyholderRole","fieldPath":"policyholderRole","cardinality":"SINGLE"},"initialDateOfService":{"name":"initialDateOfService","fieldType":"DATETIME","fieldPath":"initialDateOfService","cardinality":"SINGLE"},"placeOfTreatment":{"name":"placeOfTreatment","fieldType":"STRING","fieldPath":"placeOfTreatment","cardinality":"SINGLE"},"cob":{"name":"cob","fieldType":"CapDentalClaimCoordinationOfBenefitsEntity","fieldPath":"cob","cardinality":"MULTIPLE","forbidTarget":true},"receivedDate":{"name":"receivedDate","fieldType":"DATE","fieldPath":"receivedDate","cardinality":"SINGLE"}},"inheritedContexts":["CapDentalBaseClaimData"],"system":false},"Claimant":{"name":"Claimant","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimantAware","ClaimPartyAware"],"system":false},"EntityRef":{"name":"EntityRef","children":{},"fields":{"rootId":{"name":"rootId","fieldType":"STRING","fieldPath":"rootId","cardinality":"SINGLE"},"revisionNo":{"name":"revisionNo","fieldType":"INTEGER","fieldPath":"revisionNo","cardinality":"SINGLE"},"id":{"name":"id","fieldType":"STRING","fieldPath":"id","cardinality":"SINGLE"},"parentId":{"name":"parentId","fieldType":"STRING","fieldPath":"parentId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapTimelineRow":{"name":"CapTimelineRow","children":{"CapTimelinePeriod":{"targetName":"CapTimelinePeriod","navigationExpression":{"expressionString":"periods","originalExpressionString":"periods","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineEvent":{"targetName":"CapTimelineEvent","navigationExpression":{"expressionString":"rowEvents","originalExpressionString":"rowEvents","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapTimelineClaimInfo":{"targetName":"CapTimelineClaimInfo","navigationExpression":{"expressionString":"claimInfo","originalExpressionString":"claimInfo","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapTimelineMetric":{"targetName":"CapTimelineMetric","navigationExpression":{"expressionString":"metrics","originalExpressionString":"metrics","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"claimInfo":{"name":"claimInfo","fieldType":"CapTimelineClaimInfo","fieldPath":"claimInfo","cardinality":"SINGLE","forbidTarget":true},"periods":{"name":"periods","fieldType":"CapTimelinePeriod","fieldPath":"periods","cardinality":"MULTIPLE","forbidTarget":true},"metrics":{"name":"metrics","fieldType":"CapTimelineMetric","fieldPath":"metrics","cardinality":"MULTIPLE","forbidTarget":true},"type":{"name":"type","fieldType":"STRING","fieldPath":"type","cardinality":"SINGLE"},"rowEvents":{"name":"rowEvents","fieldType":"CapTimelineEvent","fieldPath":"rowEvents","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"CapLoss":{"name":"CapLoss","children":{"LossDetail":{"targetName":"LossDetail","navigationExpression":{"expressionString":"lossDetail","originalExpressionString":"lossDetail","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"lossSubStatusCd":{"name":"lossSubStatusCd","fieldType":"STRING","fieldPath":"lossSubStatusCd","cardinality":"SINGLE"},"reasonDescription":{"name":"reasonDescription","fieldType":"STRING","fieldPath":"reasonDescription","cardinality":"SINGLE"},"lossNumber":{"name":"lossNumber","fieldType":"STRING","fieldPath":"lossNumber","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"lossDetail":{"name":"lossDetail","fieldType":"LossDetail","fieldPath":"lossDetail","cardinality":"SINGLE","forbidTarget":true},"reasonCd":{"name":"reasonCd","fieldType":"STRING","fieldPath":"reasonCd","cardinality":"SINGLE"}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentAllocationLossInfo":{"name":"CapPaymentAllocationLossInfo","children":{},"fields":{"lossSource":{"name":"lossSource","fieldType":"ExternalLink","fieldPath":"lossSource","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentTemplate":{"name":"CapPaymentTemplate","children":{"CapPaymentDetailsTemplate":{"targetName":"CapPaymentDetailsTemplate","navigationExpression":{"expressionString":"paymentDetailsTemplate","originalExpressionString":"paymentDetailsTemplate","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"originSource":{"name":"originSource","fieldType":"ExternalLink","fieldPath":"originSource","cardinality":"SINGLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"creationDate":{"name":"creationDate","fieldType":"DATETIME","fieldPath":"creationDate","cardinality":"SINGLE"},"paymentDetailsTemplate":{"name":"paymentDetailsTemplate","fieldType":"CapPaymentDetailsTemplate","fieldPath":"paymentDetailsTemplate","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["RootEntity"],"system":false},"CapPaymentAllocationTemplatePayeeDetails":{"name":"CapPaymentAllocationTemplatePayeeDetails","children":{},"fields":{"payee":{"name":"payee","fieldType":"ExternalLink","fieldPath":"payee","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"WorkflowInfo":{"name":"WorkflowInfo","children":{"RetriableMessage":{"targetName":"RetriableMessage","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"workStatusCd":{"name":"workStatusCd","fieldType":"STRING","fieldPath":"workStatusCd","cardinality":"SINGLE"},"workQueueCd":{"name":"workQueueCd","fieldType":"STRING","fieldPath":"workQueueCd","cardinality":"SINGLE"},"workUserId":{"name":"workUserId","fieldType":"STRING","fieldPath":"workUserId","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"RetriableMessage","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true},"workCaseId":{"name":"workCaseId","fieldType":"STRING","fieldPath":"workCaseId","cardinality":"SINGLE"}},"inheritedContexts":["Retriable","MessageTypeHolder"],"system":false},"CapRootIdentifier":{"name":"CapRootIdentifier","children":{},"fields":{"links":{"name":"links","fieldType":"ExternalLink","fieldPath":"links","cardinality":"MULTIPLE"}},"inheritedContexts":["RootEntity"],"system":false},"ClaimSystemIdentifierHolder":{"name":"ClaimSystemIdentifierHolder","children":{},"fields":{"caseSystemId":{"name":"caseSystemId","fieldType":"ExternalLink","fieldPath":"caseSystemId","cardinality":"SINGLE"},"claimSystemId":{"name":"claimSystemId","fieldType":"ExternalLink","fieldPath":"claimSystemId","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"AdjudicationResult":{"name":"AdjudicationResult","children":{"Reserve":{"targetName":"Reserve","navigationExpression":{"expressionString":"reserve","originalExpressionString":"reserve","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CompensationSchedule":{"targetName":"CompensationSchedule","navigationExpression":{"expressionString":"compensationSchedule","originalExpressionString":"compensationSchedule","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"Eligibility":{"targetName":"Eligibility","navigationExpression":{"expressionString":"eligibility","originalExpressionString":"eligibility","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"EstimatedLoss":{"targetName":"EstimatedLoss","navigationExpression":{"expressionString":"estimatedLoss","originalExpressionString":"estimatedLoss","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"Applicability":{"targetName":"Applicability","navigationExpression":{"expressionString":"applicability","originalExpressionString":"applicability","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"error","originalExpressionString":"error","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"substate":{"name":"substate","fieldType":"STRING","fieldPath":"substate","cardinality":"SINGLE"},"reserve":{"name":"reserve","fieldType":"Reserve","fieldPath":"reserve","cardinality":"SINGLE","forbidTarget":true},"eligibility":{"name":"eligibility","fieldType":"Eligibility","fieldPath":"eligibility","cardinality":"SINGLE","forbidTarget":true},"estimatedLoss":{"name":"estimatedLoss","fieldType":"EstimatedLoss","fieldPath":"estimatedLoss","cardinality":"SINGLE","forbidTarget":true},"applicability":{"name":"applicability","fieldType":"Applicability","fieldPath":"applicability","cardinality":"SINGLE","forbidTarget":true},"error":{"name":"error","fieldType":"MessageType","fieldPath":"error","cardinality":"SINGLE","forbidTarget":true},"compensationSchedule":{"name":"compensationSchedule","fieldType":"CompensationSchedule","fieldPath":"compensationSchedule","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":["SubstateAware"],"system":false},"MessageTypeHolder":{"name":"MessageTypeHolder","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"RequestedReserveItem":{"name":"RequestedReserveItem","children":{},"fields":{"amount":{"name":"amount","fieldType":"MONEY","fieldPath":"amount","cardinality":"SINGLE"},"description":{"name":"description","fieldType":"STRING","fieldPath":"description","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapEstimationResult":{"name":"CapEstimationResult","children":{},"fields":{},"inheritedContexts":[],"system":false},"ClaimHeaderLink":{"name":"ClaimHeaderLink","children":{},"fields":{"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"AddressInfo":{"name":"AddressInfo","children":{},"fields":{"addressType":{"name":"addressType","fieldType":"STRING","fieldPath":"addressType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimPartyAware":{"name":"ClaimPartyAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapBalanceSuspenseItem":{"name":"CapBalanceSuspenseItem","children":{},"fields":{"paymentDate":{"name":"paymentDate","fieldType":"DATETIME","fieldPath":"paymentDate","cardinality":"SINGLE"},"paymentNumber":{"name":"paymentNumber","fieldType":"STRING","fieldPath":"paymentNumber","cardinality":"SINGLE"},"suspenseAmount":{"name":"suspenseAmount","fieldType":"MONEY","fieldPath":"suspenseAmount","cardinality":"SINGLE"},"direction":{"name":"direction","fieldType":"STRING","fieldPath":"direction","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPaymentReduction":{"name":"CapPaymentReduction","children":{},"fields":{"reductionNumber":{"name":"reductionNumber","fieldType":"STRING","fieldPath":"reductionNumber","cardinality":"SINGLE"},"reductionSource":{"name":"reductionSource","fieldType":"ExternalLink","fieldPath":"reductionSource","cardinality":"SINGLE"},"reductionType":{"name":"reductionType","fieldType":"STRING","fieldPath":"reductionType","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapDentalBaseBalanceItemAllocation":{"name":"CapDentalBaseBalanceItemAllocation","children":{"CapDentalBaseBalanceItemAllocationAddition":{"targetName":"CapDentalBaseBalanceItemAllocationAddition","navigationExpression":{"expressionString":"allocationAdditions","originalExpressionString":"allocationAdditions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalPaymentAllocationPayableItemEntity":{"targetName":"CapDentalPaymentAllocationPayableItemEntity","navigationExpression":{"expressionString":"allocationPayableItem","originalExpressionString":"allocationPayableItem","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapDentalBaseBalanceItemAllocationReduction":{"targetName":"CapDentalBaseBalanceItemAllocationReduction","navigationExpression":{"expressionString":"allocationReductions","originalExpressionString":"allocationReductions","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"CapDentalBaseBalanceItemAllocationTax":{"targetName":"CapDentalBaseBalanceItemAllocationTax","navigationExpression":{"expressionString":"allocationTaxes","originalExpressionString":"allocationTaxes","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"allocationGrossAmount":{"name":"allocationGrossAmount","fieldType":"MONEY","fieldPath":"allocationGrossAmount","cardinality":"SINGLE"},"allocationReductions":{"name":"allocationReductions","fieldType":"CapDentalBaseBalanceItemAllocationReduction","fieldPath":"allocationReductions","cardinality":"MULTIPLE","forbidTarget":true},"allocationTaxes":{"name":"allocationTaxes","fieldType":"CapDentalBaseBalanceItemAllocationTax","fieldPath":"allocationTaxes","cardinality":"MULTIPLE","forbidTarget":true},"allocationSource":{"name":"allocationSource","fieldType":"ExternalLink","fieldPath":"allocationSource","cardinality":"SINGLE"},"allocationAdditions":{"name":"allocationAdditions","fieldType":"CapDentalBaseBalanceItemAllocationAddition","fieldPath":"allocationAdditions","cardinality":"MULTIPLE","forbidTarget":true},"allocationPayableItem":{"name":"allocationPayableItem","fieldType":"CapDentalPaymentAllocationPayableItemEntity","fieldPath":"allocationPayableItem","cardinality":"SINGLE","forbidTarget":true},"allocationNetAmount":{"name":"allocationNetAmount","fieldType":"MONEY","fieldPath":"allocationNetAmount","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapPolicyExclusion":{"name":"CapPolicyExclusion","children":{},"fields":{"isExclusion":{"name":"isExclusion","fieldType":"BOOLEAN","fieldPath":"isExclusion","cardinality":"SINGLE"},"code":{"name":"code","fieldType":"STRING","fieldPath":"code","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"ClaimantAware":{"name":"ClaimantAware","children":{},"fields":{"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyAware"],"system":false},"EstimatedLoss":{"name":"EstimatedLoss","children":{"MessageType":{"targetName":"MessageType","navigationExpression":{"expressionString":"messages","originalExpressionString":"messages","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"}},"fields":{"amount":{"name":"amount","fieldType":"MONEY","fieldPath":"amount","cardinality":"SINGLE"},"messages":{"name":"messages","fieldType":"MessageType","fieldPath":"messages","cardinality":"MULTIPLE","forbidTarget":true}},"inheritedContexts":["MessageTypeHolder"],"system":false},"SinglePolicyHolder":{"name":"SinglePolicyHolder","children":{"CapPolicyInfo":{"targetName":"CapPolicyInfo","navigationExpression":{"expressionString":"policy","originalExpressionString":"policy","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"policyId":{"name":"policyId","fieldType":"STRING","fieldPath":"policyId","cardinality":"SINGLE"},"policy":{"name":"policy","fieldType":"CapPolicyInfo","fieldPath":"policy","cardinality":"SINGLE","forbidTarget":true}},"inheritedContexts":[],"system":false},"BLOBEntity":{"name":"BLOBEntity","children":{},"fields":{"blobCd":{"name":"blobCd","fieldType":"STRING","fieldPath":"blobCd","cardinality":"SINGLE"}},"inheritedContexts":[],"system":false},"CapInsuredInfo":{"name":"CapInsuredInfo","children":{},"fields":{"isMain":{"name":"isMain","fieldType":"BOOLEAN","fieldPath":"isMain","cardinality":"SINGLE"},"registryTypeId":{"name":"registryTypeId","fieldType":"STRING","fieldPath":"registryTypeId","cardinality":"SINGLE"},"insuredRoleNameCd":{"name":"insuredRoleNameCd","fieldType":"STRING","fieldPath":"insuredRoleNameCd","cardinality":"SINGLE"}},"inheritedContexts":["InsuredInfoAware"],"system":false},"CapClaimHeader":{"name":"CapClaimHeader","children":{"CapClaimHeaderCase":{"targetName":"CapClaimHeaderCase","navigationExpression":{"expressionString":"claimCase","originalExpressionString":"claimCase","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"ClaimSubjectInfo":{"targetName":"ClaimSubjectInfo","navigationExpression":{"expressionString":"subjects","originalExpressionString":"subjects","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"ClaimantAware":{"targetName":"ClaimantAware","navigationExpression":{"expressionString":"claimant","originalExpressionString":"claimant","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"},"CapClaimHeaderPolicy":{"targetName":"CapClaimHeaderPolicy","navigationExpression":{"expressionString":"policies","originalExpressionString":"policies","expressionType":"PATH","expressionVariables":[]},"cardinality":"MULTIPLE"},"InsuredAware":{"targetName":"InsuredAware","navigationExpression":{"expressionString":"insured","originalExpressionString":"insured","expressionType":"PATH","expressionVariables":[]},"cardinality":"SINGLE"}},"fields":{"insured":{"name":"insured","fieldType":"InsuredAware","fieldPath":"insured","cardinality":"SINGLE","forbidTarget":true},"subjects":{"name":"subjects","fieldType":"ClaimSubjectInfo","fieldPath":"subjects","cardinality":"MULTIPLE","forbidTarget":true},"policies":{"name":"policies","fieldType":"CapClaimHeaderPolicy","fieldPath":"policies","cardinality":"MULTIPLE","forbidTarget":true},"totalIncurred":{"name":"totalIncurred","fieldType":"MONEY","fieldPath":"totalIncurred","cardinality":"SINGLE"},"primaryURI":{"name":"primaryURI","fieldType":"ExternalLink","fieldPath":"primaryURI","cardinality":"SINGLE"},"claimType":{"name":"claimType","fieldType":"STRING","fieldPath":"claimType","cardinality":"SINGLE"},"claimCase":{"name":"claimCase","fieldType":"CapClaimHeaderCase","fieldPath":"claimCase","cardinality":"SINGLE","forbidTarget":true},"claimDate":{"name":"claimDate","fieldType":"DATETIME","fieldPath":"claimDate","cardinality":"SINGLE"},"caseNumber":{"name":"caseNumber","fieldType":"STRING","fieldPath":"caseNumber","cardinality":"SINGLE"},"reportedDate":{"name":"reportedDate","fieldType":"DATETIME","fieldPath":"reportedDate","cardinality":"SINGLE"},"mappingType":{"name":"mappingType","fieldType":"STRING","fieldPath":"mappingType","cardinality":"SINGLE"},"claimDates":{"name":"claimDates","fieldType":"DATETIME","fieldPath":"claimDates","cardinality":"MULTIPLE"},"state":{"name":"state","fieldType":"STRING","fieldPath":"state","cardinality":"SINGLE"},"claimModelName":{"name":"claimModelName","fieldType":"STRING","fieldPath":"claimModelName","cardinality":"SINGLE"},"claimant":{"name":"claimant","fieldType":"ClaimantAware","fieldPath":"claimant","cardinality":"SINGLE","forbidTarget":true},"claimNumber":{"name":"claimNumber","fieldType":"STRING","fieldPath":"claimNumber","cardinality":"SINGLE"}},"inheritedContexts":["ClaimHeaderLink"],"system":false},"CapPolicyholderRole":{"name":"CapPolicyholderRole","children":{},"fields":{"roleCd":{"name":"roleCd","fieldType":"STRING","fieldPath":"roleCd","cardinality":"MULTIPLE"},"registryId":{"name":"registryId","fieldType":"STRING","fieldPath":"registryId","cardinality":"SINGLE"}},"inheritedContexts":["ClaimPartyRole","ClaimPartyAware"],"system":false}},"pathsToNodes":{"CapProviderRole":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapProviderRole"]}],"CapAlternatePayeeRole":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]}],"CapDentalProcedureCoordinationOfBenefitsEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity","CapDentalProcedureCoordinationOfBenefitsEntity"]}],"CapDentalDiagnosisCodeEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity","CapDentalDiagnosisCodeEntity"]}],"CapDentalTreatmentReasonEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapDentalTreatmentReasonEntity"]}],"CapDentalBaseProcedure":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity"]}],"CapDentalDetailEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity"]}],"CapDentalLossEntity":[{"path":["CapDentalLossEntity"]}],"CapDentalBaseClaimData":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity"]}],"ClaimPolicyAware":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity"]}],"CapDentalLossPolicyEntity":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity"]}],"CapDentalProviderFeesEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapDentalProviderFeesEntity"]}],"CapDentalClaimDataEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity"]}],"CapPolicyInfo":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity"]}],"CapLoss":[{"path":["CapDentalLossEntity"]}],"LossDetail":[{"path":["CapDentalLossEntity","CapDentalDetailEntity"]}],"Period":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity","Period"]}],"Term":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity","Term"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapDentalClaimCoordinationOfBenefitsEntity","Term"]}],"CapPatientRole":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPatientRole"]}],"RootEntity":[{"path":["CapDentalLossEntity"]}],"ClaimPartyRole":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapProviderRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPatientRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}],"CapDentalPreauthorizationEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity","CapDentalPreauthorizationEntity"]}],"CapDentalOrthodonticEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity","CapDentalOrthodonticEntity"]}],"CapDentalClaimCoordinationOfBenefitsEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapDentalClaimCoordinationOfBenefitsEntity"]}],"ClaimPartyAware":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapProviderRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapAlternatePayeeRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPatientRole"]},{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}],"CapDentalProviderDiscountEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapDentalProviderDiscountEntity"]}],"InsuredInfoAware":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity","CapInsuredInfo"]}],"CapClaimHeaderPolicy":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity"]}],"SinglePolicyHolder":[{"path":["CapDentalLossEntity"]}],"CapDentalProcedureEntity":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalProcedureEntity"]}],"CapInsuredInfo":[{"path":["CapDentalLossEntity","CapDentalLossPolicyEntity","CapInsuredInfo"]}],"CapPolicyholderRole":[{"path":["CapDentalLossEntity","CapDentalDetailEntity","CapDentalClaimDataEntity","CapPolicyholderRole"]}]},"metadata":{"namespace":"CapDentalLoss","targetEnvironment":"JAVASCRIPT"}}