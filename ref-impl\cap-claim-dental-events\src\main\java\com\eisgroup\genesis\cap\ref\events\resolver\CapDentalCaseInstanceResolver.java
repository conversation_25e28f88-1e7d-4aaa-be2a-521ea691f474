/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.resolver;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.http.entity.JsonRequestEntity;
import com.eisgroup.genesis.http.errors.HttpRequestFailedExcpetion;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.google.gson.JsonObject;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Optional;

/**
 * Case Instance Id Resolver
 *
 * <AUTHOR>
 * @since 23.2
 */
public class CapDentalCaseInstanceResolver {

    private static final Logger LOGGER = LoggerFactory.getLogger(CapDentalCaseInstanceResolver.class);
    private static final String CASE_INSTANCE_SEARCH_LINK_API = "api/common/CaseInstance/v1/search";
    private final HttpClient httpClient;
    private final String workflowUrl;
    private final ServiceAccessRunner serviceAccessRunner;

    public CapDentalCaseInstanceResolver(HttpClient httpClient, String workflowUrl, ServiceAccessRunner serviceAccessRunner) {
        this.httpClient = httpClient;
        this.workflowUrl = workflowUrl;
        this.serviceAccessRunner = serviceAccessRunner;
    }

    /**
     * Resolves Case Instance by lossRootId
     *
     * @param lossRootId
     * @return
     */
    public Lazy<String> resolveCaseInstanceId(EntityLink<RootEntity> lossRootId) {
        HttpPost httpRequest = new HttpPost(createFullUrl());
        httpRequest.setEntity(new JsonRequestEntity(new FacadeRequest(lossRootId.getURIString()).toJson()));
            return serviceAccessRunner.runLazy(() -> {
                try {
                    String caseInstanceId = httpClient.execute(httpRequest, new JsonResponseHandler())
                            .getAsJsonObject()
                            .getAsJsonObject("body")
                            .getAsJsonObject("success")
                            .getAsJsonArray("results")
                            .get(0)
                            .getAsJsonObject()
                            .get("id")
                            .getAsString();
                    return Lazy.of(caseInstanceId);
                } catch (IOException | HttpRequestFailedExcpetion e) {
                    LOGGER.error("Can't resolve case because:", e);
                    return Lazy.error(() -> e);
                }
            });
    }

    private String createFullUrl() {
        return Optional.ofNullable(workflowUrl)
                .map(url -> url.endsWith("/") ? url : url + "/")
                .map(url -> url + CASE_INSTANCE_SEARCH_LINK_API)
                .orElseThrow(() -> new UnsupportedOperationException("workflowUrl is not configured for CapDentalResolveCaseInstanceOperationExecutor through rest"));
    }

    private static class FacadeRequest extends AbstractJsonEntity {
        private static final String BODY_ATTR = "body";
        private static final String ENTITY_URI_ATTR = "entityURI";

        private FacadeRequest(String entityUri) {
            JsonObject body = new JsonObject();
            body.addProperty(ENTITY_URI_ATTR, entityUri);
            this.setChildObject(BODY_ATTR, body);
        }
    }
}