<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="refreshSettlementCall" name="Refresh settlement" isExecutable="true">
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="refreshSettlementEventTask" name="call refreshSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${settlementKey}" target="_key" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelName}" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="refreshSettlement" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="settlementPayload"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="string" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-2ACA7F75-E74F-431F-BB1F-10AFE36CB25D" sourceRef="startEvent1" targetRef="refreshSettlementEventTask"></sequenceFlow>
    <endEvent id="sid-78593001-449A-45C3-8B57-9EEA415F1EBC"></endEvent>
    <sequenceFlow id="sid-E8588ABA-EB94-49FC-BB72-936E73804609" sourceRef="refreshSettlementEventTask" targetRef="sid-78593001-449A-45C3-8B57-9EEA415F1EBC"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_refreshSettlementCall">
    <bpmndi:BPMNPlane bpmnElement="refreshSettlementCall" id="BPMNPlane_refreshSettlementCall">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="refreshSettlementEventTask" id="BPMNShape_refreshSettlementEventTask">
        <omgdc:Bounds height="80.0" width="100.0" x="300.0" y="140.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-78593001-449A-45C3-8B57-9EEA415F1EBC" id="BPMNShape_sid-78593001-449A-45C3-8B57-9EEA415F1EBC">
        <omgdc:Bounds height="28.0" width="28.0" x="576.0" y="166.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-E8588ABA-EB94-49FC-BB72-936E73804609" id="BPMNEdge_sid-E8588ABA-EB94-49FC-BB72-936E73804609" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="399.9499999999477" y="180.0"></omgdi:waypoint>
        <omgdi:waypoint x="576.0" y="180.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2ACA7F75-E74F-431F-BB1F-10AFE36CB25D" id="BPMNEdge_sid-2ACA7F75-E74F-431F-BB1F-10AFE36CB25D" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="129.94944327907265" y="178.12722968082483"></omgdi:waypoint>
        <omgdi:waypoint x="299.9999999999957" y="179.57446808510636"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>