//Envelope entity to hold dental claim information
@MetadataIndexable("claimHeader")
Entity CapDentalLossEntity is CapLoss, SinglePolicyHolder {

    @CapBusinessNumber
    @CapClaimNumber
    @Searchable
    @Description("A unique loss number.")
    Attr lossNumber: String

    Ref lossDetail: CapDentalDetailEntity

    //EISDEVTS-40723, EISDEVTS-39836, EISDEVTS-42914
    @Description("Loss Sub Status.")
    @Lookup("CapDNSubStatus")
    Attr lossSubStatusCd: String

    @NonComparable
    Attr policy: CapDentalLossPolicyEntity

    //EISDEVTS-40723, EISDEVTS-39836, EISDEVTS-42914
    @Description("Loss Sub Status Reason.")
    @Lookup("CapDNReason")
    Attr reasonCd: String

    @Searchable
    @CapType
    @Description("Claim Type.")
    Attr claimType: String
}
//Claim details that is entered during intake
Entity CapDentalDetailEntity is LossDetail {

    @Description("Claim Header information common for dental procedures.")
    Attr claimData: CapDentalClaimDataEntity

    @Description("Planned or applied dental procedure List.")
    @KrakenChildContext
    @KrakenField
    Attr submittedProcedures: *CapDentalProcedureEntity
}
//Dental procedure information
Entity CapDentalProcedureEntity is CapDentalBaseProcedure {

}
//Header information that is common for all dental procedures
Entity CapDentalClaimDataEntity is CapDentalBaseClaimData {

}
//Policy Header information
Entity CapDentalLossPolicyEntity is CapPolicyInfo {

    @Description("Policy Plan")
    Attr plan: String

    @Description("Policy Plan Name")
    Attr planName: String
}

