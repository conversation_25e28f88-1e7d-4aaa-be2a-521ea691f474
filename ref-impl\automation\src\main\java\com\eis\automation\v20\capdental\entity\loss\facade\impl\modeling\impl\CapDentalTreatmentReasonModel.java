/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalTreatmentReasonModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDate;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalTreatmentReasonModel extends TypeModel implements ICapDentalTreatmentReasonModel {

    private String treatmentResultingFrom;
    private String autoAccidentState;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate dateOfAccident;

    public String getTreatmentResultingFrom() {
        return treatmentResultingFrom;
    }

    public void setTreatmentResultingFrom(String treatmentResultingFrom) {
        this.treatmentResultingFrom = treatmentResultingFrom;
    }

    public String getAutoAccidentState() {
        return autoAccidentState;
    }

    public void setAutoAccidentState(String autoAccidentState) {
        this.autoAccidentState = autoAccidentState;
    }

    public LocalDate getDateOfAccident() {
        return dateOfAccident;
    }

    public void setDateOfAccident(LocalDate dateOfAccident) {
        this.dateOfAccident = dateOfAccident;
    }
}
