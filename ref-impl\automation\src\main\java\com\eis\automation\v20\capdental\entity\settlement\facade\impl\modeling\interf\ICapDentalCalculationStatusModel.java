/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf;

import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.interf.IMessageTypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;
import org.javamoney.moneta.Money;

import java.util.List;

public interface ICapDentalCalculationStatusModel extends ITypeModel {

    String getFlag();

    String getStatusReason();

    String getCode();

    Integer getPercentage();

    Money getFee();

    List<String> getQuestions();

    Boolean getPredetInd();

    String getReasonCode();

    String getProcedureID();

    String getSubmittedCode();

    String getCoveredCode();

    IMessageTypeModel getMessage();
}
