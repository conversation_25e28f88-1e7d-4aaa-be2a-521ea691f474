/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa.retry.Retry;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.tzappa_v20.analytics.NamedPredicate;
import com.eis.automation.v20.capdental.entity.loss.facade.ICapDentalLoss;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.impl.CustomerModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IProviderModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import static com.eis.automation.tzappa.rest.modeling.utils.ModelRetryPredicate.success;

@Lazy
@Component("capDentalLoss")
public class CapDentalLossService implements ICapDentalLossService {

    @Autowired
    private ICapDentalUnverifiedPolicyService capDentalPolicy;
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdCapDentalLossLocation;
    private TestData tdSpecificCapDentalLossLocation;
    @Autowired
    private ICapDentalLoss capDentalLoss;

    public CapDentalLossService(ICapDentalLoss capDentalLoss) {
        this.capDentalLoss = capDentalLoss;
    }

    @Override
    public ICapDentalLoss getFacade() {
        return capDentalLoss;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdCapDentalLossLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificCapDentalLossLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdCapDentalLossLocation = testDataProvider.getJSONTestData("/capdental/loss");
        tdSpecificCapDentalLossLocation = testDataProvider.getJSONTestData("/capdental/loss/specific");
    }

    public ICapDentalLossModel initDentalLoss(String policyId) {
        return getFacade().init().performNoSuccessAnalysis(b -> b.setModel(createDentalLossModel(policyId)),
                success()).safeGetResponseBody();
    }

    public ICapDentalLossModel initDentalLoss(ICapDentalLossModel dentalLossModel) {
        return getFacade().init().performNoSuccessAnalysis(b -> b.setModel(dentalLossModel), success()).safeGetResponseBody();
    }

    public ICapDentalLossModel createDentalLossModel(String policyId) {
        return modelUtils.create(getTestData("Write", "TestData")
                .adjust("policyId", policyId));
    }

    public ICapDentalLossModel createDentalLossModel(String policyId, CustomerModel customerModel, IProviderModel providerModel) {
        return createDentalLossModel(getTestData("Write", "TestData_Full"), policyId, customerModel, providerModel);
    }

    public ICapDentalLossModel createDentalLossModel(TestData td, String policyId, CustomerModel customerModel, IProviderModel providerModel) {
        ICapDentalLossModel dentalClaimModel = modelUtils.create(td);
        dentalClaimModel.setPolicyId(policyId);
        ICapDentalClaimDataModel claimDataModel = dentalClaimModel.getEntity().getClaimData();
        if (claimDataModel.getProviderRole() != null) {
            claimDataModel.getProviderRole().setRegistryId(customerModel.getGerootUri().getUri());
            claimDataModel.getProviderRole().setProviderLink(providerModel.getGerootUri().getUri());
        }
        if (claimDataModel.getPolicyholderRole() != null) {
            claimDataModel.getPolicyholderRole().setRegistryId(customerModel.getGerootUri().getUri());
        }
        if (claimDataModel.getPatientRole() != null) {
            claimDataModel.getPatientRole().setRegistryId(customerModel.getGerootUri().getUri());
        }
        return dentalClaimModel;
    }

    public ICapDentalLossModel submitDentalLoss(ICapDentalLossModel initDentalLossModel) {
        return getFacade().submit().perform(b -> b.setModel(initDentalLossModel.getKey()));
    }

    public ICapDentalLossModel loadDentalLoss(ICapDentalLossModel dentalLossModel) {
        return getFacade().entities().perform(b -> b
                .setRootId(dentalLossModel.rootId())
                .setRevisionNumber(dentalLossModel.revisionNumber()));
    }

    public ICapDentalLossModel loadDentalLoss(ICapDentalLossModel dentalLossModel, String state) {
        NamedPredicate<ICapDentalLossModel> predicate = new NamedPredicate<>(
                String.format("Loaded Dental Loss state is NOT %s as expected", state),
                response -> response.getState().equals(state));
        return Retry.run(predicate, () -> loadDentalLoss(dentalLossModel), 10);
    }

    public ICapDentalLossModel createUpdateDentalLossModel(ICapDentalLossModel dentalLossModel) {
        ICapDentalLossModel loadDentalLoss = loadDentalLoss(dentalLossModel);
        ICapDentalLossModel updateModel = modelUtils.create(getTestData("Update", "TestData"));
        updateModel.setKey(loadDentalLoss.getKey());
        updateModel.setEntity(loadDentalLoss.getLossDetail());
        updateModel.getEntity().setTimestamp(loadDentalLoss.getTimestamp());
        return updateModel;
    }
}
