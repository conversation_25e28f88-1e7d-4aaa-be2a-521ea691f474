/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentIssueHandler;
import com.eisgroup.genesis.cap.financial.repository.ClaimPaymentRepository;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for issuing a {@link CapDentalPaymentEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentIssueHandler extends CapPaymentIssueHandler<IdentifierRequest, CapDentalPaymentEntity> {
    @Autowired
    private ClaimPaymentRepository claimPaymentRepository;
    @Autowired
    private ModelResolver modelResolver;

    public CapDentalPaymentIssueHandler() {
    }

    @Nonnull
    public CapDentalPaymentEntity load(@Nonnull IdentifierRequest input) {
        return (CapDentalPaymentEntity)this.claimPaymentRepository.load(input.getKey(), this.modelResolver.getModelName(), this.getVariation()).get();
    }
}
