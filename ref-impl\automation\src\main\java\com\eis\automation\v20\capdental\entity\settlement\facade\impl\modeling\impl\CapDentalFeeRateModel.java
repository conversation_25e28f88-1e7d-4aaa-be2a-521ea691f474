/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalFeeRateModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalFeeRateModel extends TypeModel implements ICapDentalFeeRateModel {

    private Money feeAmount;
    private String feeCode;

    public Money getFeeAmount() {
        return feeAmount;
    }

    public void setFeeAmount(Money feeAmount) {
        this.feeAmount = feeAmount;
    }

    public String getFeeCode() {
        return feeCode;
    }

    public void setFeeCode(String feeCode) {
        this.feeCode = feeCode;
    }
}
