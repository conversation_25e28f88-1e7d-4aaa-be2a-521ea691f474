/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.smoke;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import com.exigen.istf.webdriver.controls.waiters.Waiters;
import org.javamoney.moneta.Money;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa.common.DateUtils.getCurrentDate;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.PRIMARY_INSURED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0210;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;
import static com.eis.automation.tzappa_v20.TestGroup.SMOKE;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.CLOSED;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.OPEN;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.PENDING;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimSource.NONEDI;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.SERVICE_PROVIDER;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPaymentScheduleState.ACTIVE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementProposal.PAY;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.policybenefits.constant.PolicyConstant.Currency.USD;

public class TestDentalClaimProduct extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Lazy
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IClaimSearchService capClaimSearch;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;

    @Test(description = "Steps 1-5", groups = {SMOKE})
    @TestInfo(testCaseId = "GENESIS-182889", component = CAP_DENTAL_LOSS)
    public void testDentalClaimSettlementAutomation() {
        // Preconditions
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoWrapperModel dentalPolicyModel = capPolicy.createDentalUnverifiedPolicyModel(individualCustomer);
        ((ICapDentalPolicyInfoModel) dentalPolicyModel.getEntity()).setPolicyPaidToDate(getCurrentDate().plusDays(1));
        ((ICapDentalPolicyInfoModel) dentalPolicyModel.getEntity()).setPolicyPaidToDateWithGracePeriod(getCurrentDate().plusDays(1));
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(dentalPolicyModel);
        Waiters.SLEEP(10000).go();
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(D0210);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setSubmittedFee(Money.of(100, USD));
        dentalClaimModel.getEntity().getClaimData().setSource(NONEDI);
        dentalClaimModel.getEntity().getClaimData().setPayeeType(PRIMARY_INSURED);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(ACTUAL_SERVICES);
        dentalClaimModel.getEntity().getClaimData().setReceivedDate(getCurrentDate());
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setDateOfService(getCurrentDate());
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);

        // Step 1
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);
        Waiters.SLEEP(10000).go();

        // Step 2
        ICapDentalSettlementModel searchSettlement = capClaimSearch.searchDentalSettlement(submitDentalClaim, 1).get(0);
        ICapDentalSettlementModel approvedDentalSettlement = capDentalSettlement.loadDentalSettlement(searchSettlement, APPROVED);
        assertSoftly(softly -> {
            softly.assertThat(approvedDentalSettlement.getState()).isEqualTo(APPROVED);
            softly.assertThat(approvedDentalSettlement.getSettlementResult().getProposal()).isEqualTo(PAY);
            softly.assertThat(approvedDentalSettlement.revisionNumber()).isEqualTo(1);
        });

        // Step 3
        ICapDentalLossModel openDentalClaim = capDentalLoss.loadDentalLoss(submitDentalClaim, OPEN);
        Waiters.SLEEP(10000).go();

        // Step 4
        ICapDentalPaymentTemplateModel loadPaymentTemplate = capClaimSearch.searchDentalPaymentTemplate(submitDentalClaim, 1).get(0);
        assertThat(loadPaymentTemplate.getState()).isEqualTo(CLOSED);

        // Step 5
        ICapDentalPaymentScheduleModel loadPaymentSchedule = capClaimSearch.searchDentalPaymentSchedule(submitDentalClaim, 1).get(0);
        assertThat(loadPaymentSchedule.getState()).isEqualTo(ACTIVE);
    }
}