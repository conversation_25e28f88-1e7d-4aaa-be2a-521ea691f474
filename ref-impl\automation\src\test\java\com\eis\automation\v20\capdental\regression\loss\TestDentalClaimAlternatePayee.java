/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.loss;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.cap.entity.casesearch.service.ICapLifeSearchService;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.service.ICapDentalPaymentTemplateService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.ALTERNATE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.ALTERNATE_PAYEE_IS_MANDATORY;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;

public class TestDentalClaimAlternatePayee extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    private IndividualCustomerModel individualCustomer;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IClaimSearchService claimSearchService;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private ICapDentalPaymentTemplateService capDentalPaymentTemplate;
    private ICapDentalPolicyInfoModel createdDentalPolicy;
    private IIndividualProviderModel individualProvider;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private IProviderService provider;
    @Lazy
    @Autowired
    private IPartyRoleService partyRole;

    @BeforeClass(groups = {REGRESSION}, alwaysRun = true)
    public void createPreconditions() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-160691", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_AlternatePayee() {
        // Create IndividualCustomer2
        IndividualCustomerModel individualCustomer2 = (IndividualCustomerModel) indCustomer.createCustomer();

        // Step 2.1
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(individualCustomer2.getGerootUri().getUriModel());
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertSoftly(softly -> {
            softly.assertThat(initDentalClaim.getLossDetail().getClaimData().getPayeeType()).isEqualTo(ALTERNATE);
            softly.assertThat(initDentalClaim.getLossDetail().getClaimData().getAlternatePayee()).isEqualTo(individualCustomer2.getGerootUri().getUriModel());
        });

        // Step 3.1
        ICapDentalLossModel dentalClaimModel2 = createDentalClaimModel(null);
        ICapDentalLossModel initDentalClaim2 = capDentalLoss.initDentalLoss(dentalClaimModel2);
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().submit().perform(b -> b.setModel(initDentalClaim2.getKey())));
        FailureAssertion.assertThat(failure).hasError(ALTERNATE_PAYEE_IS_MANDATORY);
    }

    private ICapDentalLossModel createDentalClaimModel(UriModel alternatePayee) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(D0330);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setPredetInd(false);
        dentalClaimModel.getEntity().getClaimData().setPayeeType(ALTERNATE);
        dentalClaimModel.getEntity().getClaimData().setAlternatePayee(alternatePayee);
        return dentalClaimModel;
    }
}