package com.eisgroup.genesis.cap.ref.module.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossLifecycleConfigTest {

    @InjectMocks
    private CapDentalLossLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Mock
    private CapDentalLossValidator capDentalLossValidator;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;


    @Test
    public void testReturnLossNumberGenerator() {
        var result = config.lossNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnLossPolicyProjectionTypeResolver() {
        var result = config.lossPolicyProjectionTypeResolver();
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnClaimLossCloseInputValidator() {
        var result = config.claimLossCloseInputValidator();
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnClaimLossReopenInputValidator() {
        var result = config.claimLossReopenInputValidator();
        assertThat(result, notNullValue());
    }


    @Test
    public void testReturnCapDentalClaimLossCloseValidator() {
        var result = config.capDentalClaimLossCloseValidator(entityLinkResolverRegistry, capDentalSettlementIndexResolver);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.capDentalLinkValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }
    @Test
    public void testReturnCapDentalLossValidator() {
        var result = config.capDentalLossValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLossUpdateInputValidator() {
        var result = config.capDentalLossUpdateInputValidator(capDentalLossValidator);
        assertThat(result, notNullValue());
    }
}





