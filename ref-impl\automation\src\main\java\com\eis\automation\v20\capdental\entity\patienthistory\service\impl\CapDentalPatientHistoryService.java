/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.ICapDentalPatientHistory;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.capdental.entity.patienthistory.service.ICapDentalPatientHistoryService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Lazy
@Component("capDentalPatientHistory")
public class CapDentalPatientHistoryService implements ICapDentalPatientHistoryService {

    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdPatientHistoryLocation;
    private TestData tdSpecificPatientHistoryLocation;
    @Autowired
    private ICapDentalPatientHistory capDentalPatientHistory;

    public CapDentalPatientHistoryService(ICapDentalPatientHistory capDentalPatientHistory) {
        this.capDentalPatientHistory = capDentalPatientHistory;
    }

    @Override
    public ICapDentalPatientHistory getFacade() {
        return capDentalPatientHistory;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdPatientHistoryLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificPatientHistoryLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdPatientHistoryLocation = testDataProvider.getJSONTestData("/capdental/patienthistory");
        tdSpecificPatientHistoryLocation = testDataProvider.getJSONTestData("/capdental/patienthistory/specific");
    }

    public ICapDentalPatientHistoryModel initPatientHistory(ICapDentalPatientHistoryWrapperModel patientHistoryWrapperModel) {
        return getFacade().init().perform(b -> b.setModel(patientHistoryWrapperModel));
    }

    public ICapDentalPatientHistoryWrapperModel createPatientHistoryModel(ICustomerModel customerModel) {
        ICapDentalPatientHistoryWrapperModel patientHistoryModel = modelUtils.create(getTestData("Write", "TestData"));
        patientHistoryModel.getEntity().getPatientHistoryData().getClaimData().setPatient(customerModel.getGerootUri().getUriModel());
        return patientHistoryModel;
    }

    public ICapDentalPatientHistoryModel loadPatientHistory(ICapDentalPatientHistoryModel patientHistoryModel) {
        return getFacade().entities().perform(b -> b
                .setRootId(patientHistoryModel.rootId())
                .setRevisionNumber(patientHistoryModel.revisionNumber()));
    }
}
