<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="dentalCompletePaymentSchedule" name="Dental complete Payment schedule" isExecutable="true">
    <startEvent id="issuePaymentEntryPoint">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentDefinition_issuePayment]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <serviceTask id="calculateIsLastPayment" name="Calculate is issued payment last in schedule" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('payment_capLatestPaymentInSchedule').serviceGroup('CapDentalPaymentDefinition').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{body:{"_key" : ${execution.getVariable('_key')}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[lastPayment]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="fromIssueEventToEndpoint" sourceRef="issuePaymentEntryPoint" targetRef="calculateIsLastPayment"></sequenceFlow>
    <exclusiveGateway id="sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9"></exclusiveGateway>
    <sequenceFlow id="sid-B5CC84D6-C83B-4646-A9E9-B182231C2150" sourceRef="calculateIsLastPayment" targetRef="sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9"></sequenceFlow>
    <endEvent id="sid-450D8611-3EFF-44E7-8C46-52EA8385672E"></endEvent>
    <serviceTask id="completePaymentSchedule" name="Complete Payment Schedule" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${lastPayment.body.success.scheduleToComplete._key}" target="_key" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="completePaymentSchedule" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-CF937E9F-B8C8-4A04-B195-8965AEAFA164" sourceRef="completePaymentSchedule" targetRef="sid-450D8611-3EFF-44E7-8C46-52EA8385672E"></sequenceFlow>
    <sequenceFlow id="sid-A0785471-80FC-42B3-97BB-636988C53FDA" name="not last payment" sourceRef="sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9" targetRef="sid-450D8611-3EFF-44E7-8C46-52EA8385672E">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${lastPayment.body.success.isLastPayment == null ||
lastPayment.body.success.isLastPayment == false}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-BC22CED9-E415-4AA5-94FB-B95585E436AB" sourceRef="sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9" targetRef="completePaymentSchedule">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${lastPayment.body.success.isLastPayment != null &&
lastPayment.body.success.isLastPayment == true}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_dentalCompletePaymentSchedule">
    <bpmndi:BPMNPlane bpmnElement="dentalCompletePaymentSchedule" id="BPMNPlane_dentalCompletePaymentSchedule">
      <bpmndi:BPMNShape bpmnElement="issuePaymentEntryPoint" id="BPMNShape_issuePaymentEntryPoint">
        <omgdc:Bounds height="30.0" width="30.5" x="180.0" y="210.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="calculateIsLastPayment" id="BPMNShape_calculateIsLastPayment">
        <omgdc:Bounds height="131.0" width="180.0" x="287.5" y="159.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9" id="BPMNShape_sid-49719F26-DB06-416D-AB0C-5B6CBAD113F9">
        <omgdc:Bounds height="40.0" width="40.0" x="570.0" y="205.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-450D8611-3EFF-44E7-8C46-52EA8385672E" id="BPMNShape_sid-450D8611-3EFF-44E7-8C46-52EA8385672E">
        <omgdc:Bounds height="28.0" width="28.0" x="827.5" y="211.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="completePaymentSchedule" id="BPMNShape_completePaymentSchedule">
        <omgdc:Bounds height="80.0" width="100.0" x="540.0" y="315.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-B5CC84D6-C83B-4646-A9E9-B182231C2150" id="BPMNEdge_sid-B5CC84D6-C83B-4646-A9E9-B182231C2150" flowable:sourceDockerX="90.0" flowable:sourceDockerY="65.5" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="467.4499999999996" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="570.0" y="225.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CF937E9F-B8C8-4A04-B195-8965AEAFA164" id="BPMNEdge_sid-CF937E9F-B8C8-4A04-B195-8965AEAFA164" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="639.9499999999999" y="329.15506958250495"></omgdi:waypoint>
        <omgdi:waypoint x="829.0606275943229" y="231.40693037720163"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-A0785471-80FC-42B3-97BB-636988C53FDA" id="BPMNEdge_sid-A0785471-80FC-42B3-97BB-636988C53FDA" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="609.9460353736088" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="827.5" y="225.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="fromIssueEventToEndpoint" id="BPMNEdge_fromIssueEventToEndpoint" flowable:sourceDockerX="15.25" flowable:sourceDockerY="15.0" flowable:targetDockerX="90.0" flowable:targetDockerY="65.5">
        <omgdi:waypoint x="210.44999942162946" y="225.0"></omgdi:waypoint>
        <omgdi:waypoint x="287.5" y="225.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BC22CED9-E415-4AA5-94FB-B95585E436AB" id="BPMNEdge_sid-BC22CED9-E415-4AA5-94FB-B95585E436AB" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="590.0" y="244.942332820907"></omgdi:waypoint>
        <omgdi:waypoint x="590.0" y="315.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>