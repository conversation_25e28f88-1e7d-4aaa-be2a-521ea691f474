/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.ICapUnverifiedPolicy;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.service.ICapUnverifiedPolicyService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;

public interface ICapDentalUnverifiedPolicyService extends ITestDataService, ICapUnverifiedPolicyService, IFacadeService<ICapUnverifiedPolicy> {

    ICapDentalPolicyInfoWrapperModel createDentalUnverifiedPolicyModel(ICustomerModel customerModel);

    ICapDentalPolicyInfoWrapperModel createDentalUnverifiedPolicyModel(TestData td, ICustomerModel customerModel);
}
