/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleMessageModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;


@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentScheduleModel extends TypeModel implements ICapDentalPaymentScheduleModel {

    private List<ICapDentalPaymentScheduleMessageModel> scheduleMessages;
    private List<ICapDentalPaymentModel> payments;
    private String state;
    private String scheduleNumber;
    private UriModel originSource;
    private UriModel paymentTemplate;
    @JsonProperty(value = "_modelName")
    private String modelName;
    @JsonProperty(value = "_modelVersion")
    private String modelVersion;
    @JsonProperty(value = "_modelType")
    private String modelType;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    @JsonProperty(value = "_timestamp")
    private LocalDateTime timestamp;
    @JsonProperty(value = "_archived")
    protected Boolean archived;

    @JsonSerialize(as = List.class, contentAs = CapDentalPaymentScheduleMessageModel.class)
    public List<ICapDentalPaymentScheduleMessageModel> getScheduleMessages() {
        return scheduleMessages;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalPaymentScheduleMessageModel.class)
    public void setScheduleMessages(List<ICapDentalPaymentScheduleMessageModel> scheduleMessages) {
        this.scheduleMessages = scheduleMessages;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalPaymentModel.class)
    public List<ICapDentalPaymentModel> getPayments() {
        return payments;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalPaymentModel.class)
    public void setPayments(List<ICapDentalPaymentModel> payments) {
        this.payments = payments;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getScheduleNumber() {
        return scheduleNumber;
    }

    public void setScheduleNumber(String scheduleNumber) {
        this.scheduleNumber = scheduleNumber;
    }

    public UriModel getOriginSource() {
        return originSource;
    }

    public void setOriginSource(UriModel originSource) {
        this.originSource = originSource;
    }

    public UriModel getPaymentTemplate() {
        return paymentTemplate;
    }

    public void setPaymentTemplate(UriModel paymentTemplate) {
        this.paymentTemplate = paymentTemplate;
    }

    public String getModelName() {
        return this.modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    @Override
    public String entityName() {
        return "CapPaymentSchedule";
    }

    @Override
    public String endpointName() {
        return "CapDentalPaymentSchedule";
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }

    public Boolean getArchived() {
        return archived;
    }

    public void setArchived(Boolean archived) {
        this.archived = archived;
    }
}