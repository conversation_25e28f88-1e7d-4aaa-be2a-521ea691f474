/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf;

import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import org.javamoney.moneta.Money;

import java.util.List;

public interface ICapDentalSettlementInfoProcedureModel extends ICapDentalProcedureModel {

    String getProcedureStatus();

    List<ICapDentalFeeRateModel> getFeeUCRME();

    List<ICapDentalFeeRateModel> getFeeSchedule();

    Integer getCount();

    ICapDentalConsultantReviewModel getConsultantReview();

    ICapDentalDentistModel getDentist();

    Money getSubmittedFeeUCR();

    List<ICapDentalFeeRateModel> getFeeUCR();

    Money getCoveredFeeUCR();

    Money getSubmittedFeeSchedule();

    Money getSubmittedFeeUCRME();

    Money getCoveredFeeUCRME();

    Money getCoveredFeeSchedule();

    ICapDentalDHMOFrequencyModel getFrequencyDHMO();

    ICapDentalPPOFrequencyModel getFrequencyPPO();

    Money getPaidAmount();

    ICapDentalCalculationStatusModel getStatus();
}
