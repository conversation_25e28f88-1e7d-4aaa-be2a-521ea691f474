<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>ms-claim-dental-applications-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-commands-app</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-lifecycle</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-repository</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-dental-transformation</artifactId>
        </dependency>

        <!-- CAP Common -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-rest</artifactId>
        </dependency>

        <!-- Security -->
        <dependency>
            <groupId>com.eisgroup.genesis.security</groupId>
            <artifactId>security-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <!-- BAM -->
        <dependency>
            <groupId>com.eisgroup.genesis.bam</groupId>
            <artifactId>bam-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>

        <!-- Versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.versioning</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>


        <!-- versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-versioning-ms-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Job -->
        <dependency>
            <groupId>com.eisgroup.genesis.jobs</groupId>
            <artifactId>jobs-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>

    </dependencies>
</project>