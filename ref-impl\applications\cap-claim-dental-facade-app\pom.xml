<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <artifactId>ms-claim-dental-applications-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-facade-app</artifactId>

    <profiles>
        <profile>
            <id>swagger-generation</id>
            <activation>
                <property>
                    <name>!dev</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.eisgroup.genesis.tools</groupId>
                        <artifactId>fgenerator-maven-plugin</artifactId>
                        <version>${fgenerator.maven.plugin.version}</version>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <excludes>SwaggerGenerator</excludes>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


    <dependencies>

        <dependency>
            <groupId>com.eisgroup.genesis.jobs</groupId>
            <artifactId>jobs-bundle</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-facade</artifactId>
        </dependency>

        <!-- versioning -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-versioning-ms-bundle</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>
</project>
