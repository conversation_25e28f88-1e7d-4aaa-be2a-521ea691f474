/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

/**
 * Holds command names of Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleCommands {

    public static final String PREVIEW_PAYMENT_SCHEDULE = "previewPaymentSchedule";
    public static final String CANCEL_LOSS_PAYMENT_SCHEDULES = "cancelLossPaymentSchedules";
    public static final String SUSPEND_LOSS_PAYMENT_SCHEDULES = "suspendLossPaymentSchedules";
    public static final String UNSUSPEND_LOSS_PAYMENT_SCHEDULES = "unsuspendLossPaymentSchedules";

    /**
     * Class is for constants only, no constructor to be exposed.
     */
    private CapDentalPaymentScheduleCommands() {
    }
}
