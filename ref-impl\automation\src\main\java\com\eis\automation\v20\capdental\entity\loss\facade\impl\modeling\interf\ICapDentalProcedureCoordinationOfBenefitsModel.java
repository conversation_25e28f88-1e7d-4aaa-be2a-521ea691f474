/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf;

import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;
import org.javamoney.moneta.Money;

public interface ICapDentalProcedureCoordinationOfBenefitsModel extends ITypeModel {

    String getCoverageType();

    String getPrimaryCoverageStatus();

    String getInnOnn();

    Money getAllowed();

    Money getConsidered();

    Money getPaid();
}
