@KeyStrategy("NO_MODIFICATION")
@CapEndpoint("adjudicationInput")
Transformation CapDentalSettlementAdjudicationInput {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        CapDentalSettlement.CapDentalRuleInputEntity
    }


    Attr loss is settlement.settlementLossInfo
    Attr details is settlement.settlementDetail

    Var issuedPayments is FlatMap(getIssuedPayments().resolvedIssuedPayments, Null())
    Var issuedPaymentAllocations is FlatMap(issuedPayments.paymentDetails.paymentAllocations)

    Attr entryPreviouslyUsed is produceEntryUsedAmount(issuedPaymentAllocations)

   Producer produceEntryUsedAmount(paymentAllocation) {
        Attr procedureID is paymentAllocation.allocationPayableItem.procedureID
        Attr maximumUsedAmount is paymentAllocation.allocationGrossAmount

        Var deductibleAccumulator is First(paymentAllocation.allocationDentalDetails.accumulatorDetails[deductibleAccumulatorFilter])
        Attr deductibleUsedAmount is deductibleAccumulator.accumulatorAmount
   }

    Producer mapAccumulatorDetails(accumulatorDetail) {
        Attr appliesToProcedureCategories is accumulatorDetail.appliesToProcedureCategories
        Attr usedAmount is accumulatorDetail.accumulatorAmount
        Attr accumulatorType is accumulatorDetail.accumulatorType
        Attr renewalType is accumulatorDetail.renewalType
        Attr networkType is accumulatorDetail.networkType
        Attr term is accumulatorDetail.term
    }

    Producer getIssuedPayments() {
        Var allIndexedPayments is Load(createOriginKey(), "CapDentalPaymentIndex", "CapDentalPaymentIdxEntity")
        Var issuedIndexedPayments is allIndexedPayments[issuedIndexdPaymentFilter]

        Attr resolvedIssuedPayments is resolvePayments(issuedIndexedPayments).resolvedPayment

        Producer createOriginKey() {
            Attr originSource is Root().settlement.claimLossIdentification._uri
        }

        Producer resolvePayments(indexedPayment) {
            Attr resolvedPayment is ExtLink(AsExtLink(indexedPayment.paymentId))
        }

        Filter issuedIndexdPaymentFilter {
            Equals(state, "Issued")
        }
    }

    Filter deductibleAccumulatorFilter {
        Equals(accumulatorType, "IndividualDeductible") || Equals(accumulatorType, "FamilyDeductible")
        || Equals(accumulatorType, "OrthoDeductible") || Equals(accumulatorType, "TMJDeductible")
        || Equals(accumulatorType, "CosmeticDeductible")
    }
}

