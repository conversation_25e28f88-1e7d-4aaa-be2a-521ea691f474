/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.handlers;

import com.eisgroup.genesis.cap.common.versioning.CapChangeHistoryEntity;
import com.eisgroup.genesis.cap.common.versioning.events.handlers.CapChangeTrackingEventHandler;
import com.eisgroup.genesis.cap.common.versioning.repository.CapChangeHistoryRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.events.EventEnvelope;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.json.wrapper.JsonWrapper;
import com.eisgroup.genesis.lifecycle.events.CommandExecutedEvent;
import com.eisgroup.genesis.streams.consumer.ErrorHandlingStrategy;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Dental specific {@link CapChangeTrackingEventHandler} which expands
 * {@link CapChangeHistoryEntity#getFilter()} attribute with additional identifier values
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalChangeTrackingEventHandler extends CapChangeTrackingEventHandler {

    private static final String UPDATE_LOSS_DRAFT_COMMAND = "updateLossDraft";

    private final CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;
    private final EntityLinkBuilderRegistry linkBuilderRegistry;

    public CapDentalChangeTrackingEventHandler(
            CapChangeHistoryRepository repository, EntityLinkBuilderRegistry linkBuilderRegistry,
            ErrorHandlingStrategy deadLetterQueueStrategy, List<String> applicableActions,
            List<String> applicableModelNames, String groupPrefix, CapDentalSettlementIndexResolver capDentalSettlementIndexResolver) {
        super(repository, linkBuilderRegistry, deadLetterQueueStrategy, applicableActions, applicableModelNames, groupPrefix);
        this.capDentalSettlementIndexResolver = capDentalSettlementIndexResolver;
        this.linkBuilderRegistry = linkBuilderRegistry;
    }

    @Override
    public void handle(EventEnvelope<CommandExecutedEvent> envelope) {
//        if (UPDATE_LOSS_DRAFT_COMMAND.equals(envelope.getMessage().getCommand().getName())) {
//            handleIndexingOnUpdateLossDraft(envelope);
//            return;
//        }
        super.handle(envelope);//TODO-For:GENESIS-358942
    }

    @Override
    protected Map<String, String> createFilterMap(CommandExecutedEvent message) {
        Map<String, String> initialFilterMap = super.createFilterMap(message);
        initialFilterMap.remove(CapChangeHistoryEntity.ATTRIBUTE_SUBTYPE);
        Optional<JsonObject> outputJson = Optional.ofNullable(message.getOutput())
                .map(JsonWrapper::toJson);
        Optional<JsonObject> keyAttr = outputJson
                .map(output -> output.get(BaseKey.ATTRIBUTE_NAME))
                .map(JsonElement::getAsJsonObject);
        keyAttr.map(key -> key.get(BaseKey.ROOT_ID))
                .map(JsonElement::getAsString)
                .ifPresent(rootId -> initialFilterMap.put(BaseKey.ROOT_ID, rootId));
        keyAttr.map(key -> key.get(BaseKey.ROOT_REVISION_NO))
                .map(JsonElement::getAsString)
                .ifPresent(revisionNo -> initialFilterMap.put(BaseKey.ROOT_REVISION_NO, revisionNo));
        return initialFilterMap;
    }

//    private Completable handleIndexingOnUpdateLossDraft(EventEnvelope<CommandExecutedEvent> envelope) {
//        EntityLink<RootEntity> lossUri = createLinkFromOutput(envelope.getMessage().getOutput());
//        return capDentalSettlementIndexResolver.resolveSettlementIndex(lossUri)
//            .firstElement()
//            //should handle loss change tracking, when settlement exists on update loss draft command
//            .flatMapCompletable(index -> super.handle(envelope));
//    }


    private <T> EntityLink<T> createLinkFromOutput(JsonEntity output) {
        return linkBuilderRegistry.<T>getByType(output.getClass()).createLink((T) output);
    }

}
