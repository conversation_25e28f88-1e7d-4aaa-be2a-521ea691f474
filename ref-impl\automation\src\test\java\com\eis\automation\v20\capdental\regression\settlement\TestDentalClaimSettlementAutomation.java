/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.settlement;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossCloseModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossReopenModel;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalOrthodonticModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.service.ICapDentalPaymentTemplateService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.impl.CustomerModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.util.List;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.*;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.CLOSED;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.DISAPPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_AUTO_ADJUDICATION;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimReasonCd.PREDETERMINED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.PREDETERMINATION_ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.PREDETERMINATION_ORTHODONTICS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPlanCategory.PPO;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementProposal.PAY;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementProposal.PEND;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementProposal.PREDET_DENY;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0110;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D2542;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_SETTLEMENT_IS_IN_APPROVED_STATE;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalClaimSettlementAutomation extends CapDentalBaseTest {
    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private IClaimSearchService claimSearch;
    @Autowired
    private ICapDentalPaymentTemplateService capDentalPaymentTemplate;

    public ICapDentalLossModel createClaimPreconditions(String procedureCode, String transactionType, Boolean predetInd) {
        // Preconditions
        // Individual Customer
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        // Dental Unverified Policy
        ICapDentalPolicyInfoWrapperModel dentalPolicyModel = capPolicy.createDentalUnverifiedPolicyModel(individualCustomer);
        ((ICapDentalPolicyInfoModel) dentalPolicyModel.getEntity()).setPlanCategory(PPO);
        String createdDentalPolicyId = capPolicy.writeCapUnverifiedPolicy(dentalPolicyModel).getCapPolicyId();
        // Dental Claim
        return initDentalClaim(createdDentalPolicyId, individualCustomer,
                individualProvider, procedureCode, transactionType, predetInd);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153257", component = CAP_DENTAL_AUTO_ADJUDICATION)
    public void testApproveDentalSettlementAutomatically() {
        // Preconditions
        // Dental Claim1
        ICapDentalLossModel initDentalClaim =  createClaimPreconditions(D2542, ACTUAL_SERVICES, null);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);

        // Step 1
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);

        // Step 2
        ICapDentalSettlementModel dentalSettlementModel = claimSearch.searchDentalSettlement(submitDentalClaim, 1).get(0);
        assertThat(dentalSettlementModel.getState()).isEqualTo(PENDING);
        assertThat(dentalSettlementModel.getSettlementResult().getProposal()).isEqualTo(PEND);

        // Step 3
        ICapDentalLossModel loadDentalClaim = capDentalLoss.loadDentalLoss(submitDentalClaim);
        assertThat(loadDentalClaim.getState()).isEqualTo(PENDING);

        // TODO oreva: Steps 4, 5: workflow ms should be implemented

        // Step 6
        ICapDentalLossModel dentalClaimUpdateModel = capDentalLoss.createUpdateDentalLossModel(loadDentalClaim);
        dentalClaimUpdateModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(D0330);
        ICapDentalLossModel updateDentalClaim = capDentalLoss.getFacade().update().perform(b -> b.setModel(dentalClaimUpdateModel));
        assertThat(updateDentalClaim.getState()).isEqualTo(PENDING);

        // Step 7
        List<ICapDentalSettlementModel> dentalSettlements = claimSearch.searchDentalSettlement(updateDentalClaim, 2);
        assertThat(dentalSettlements.size()).isEqualTo(2);
        ICapDentalSettlementModel dentalSettlement1 = getSettlementWithProposal(dentalSettlements, PEND);
        assertThat(dentalSettlement1.getState()).isEqualTo(PENDING);
        assertThat(dentalSettlement1.rootId()).isEqualTo(dentalSettlementModel.rootId());
        assertThat(dentalSettlement1.revisionNumber()).isEqualTo(1);

        ICapDentalSettlementModel dentalSettlement2 = getSettlementWithProposal(dentalSettlements, PAY);
        assertThat(dentalSettlement2.getState()).isEqualTo(APPROVED);
        assertThat(dentalSettlement2.rootId()).isEqualTo(dentalSettlementModel.rootId());
        assertThat(dentalSettlement2.revisionNumber()).isEqualTo(2);

        // Step 8
        ICapDentalLossModel loadUpdatedDentalClaim = capDentalLoss.loadDentalLoss(updateDentalClaim);
        assertThat(loadUpdatedDentalClaim.getState()).isEqualTo(OPEN);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-171527", component = CAP_DENTAL_AUTO_ADJUDICATION)
    public void testClaimSettlementCloseAutomatically() {
        // Preconditions
        // Dental Claim1
        ICapDentalLossModel initDentalClaim1 = createClaimPreconditions(D0330, PREDETERMINATION_ORTHODONTICS, true);
        assertThat(initDentalClaim1.getState()).isEqualTo(INCOMPLETE);

        // Step 1
        ICapDentalLossModel submitDentalClaim1 = capDentalLoss.submitDentalLoss(initDentalClaim1);
        assertThat(submitDentalClaim1.getState()).isEqualTo(PENDING);

        // Step 2
        ICapDentalSettlementModel dentalSettlement1 = claimSearch.searchDentalSettlement(submitDentalClaim1, 1).get(0);
        ICapDentalSettlementModel loadDentalSettlement1 = capDentalSettlement.loadDentalSettlement(dentalSettlement1, CLOSED);
        assertThat(loadDentalSettlement1.getState()).isEqualTo(CLOSED);

        // Step 3
        ICapDentalLossModel closedDentalClaim1 = capDentalLoss.loadDentalLoss(submitDentalClaim1);
        assertThat(closedDentalClaim1.getState()).isEqualTo(CLOSED);
        assertThat(closedDentalClaim1.getReasonCd()).isEqualTo(PREDETERMINED);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-171527", component = CAP_DENTAL_AUTO_ADJUDICATION)
    public void testCloseApprovedSettlement() {
        // Preconditions
        // Dental Claim2
        ICapDentalLossModel initDentalClaim2 = createClaimPreconditions(D0330, ACTUAL_SERVICES, false);
        assertThat(initDentalClaim2.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim2 = capDentalLoss.submitDentalLoss(initDentalClaim2);
        assertThat(submitDentalClaim2.getState()).isEqualTo(PENDING);
        ICapDentalLossModel dentalClaim2 = capDentalLoss.loadDentalLoss(submitDentalClaim2, OPEN);
        // Dental Settlement2
        ICapDentalSettlementModel dentalSettlement2 = claimSearch.searchDentalSettlement(dentalClaim2, 1).get(0);
        assertThat(dentalSettlement2.getState()).isEqualTo(APPROVED);
        assertThat(dentalSettlement2.getSettlementResult().getProposal()).isEqualTo(PAY);

        // Step 4
        ICapLossCloseModel dentalClaimCloseModel = modelUtils.create(capDentalLoss.getTestData("Close", "TestData"));
        dentalClaimCloseModel.setKey(dentalClaim2.getKey());
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().close().perform(b -> b.setModel(dentalClaimCloseModel)));
        FailureAssertion.assertThat(failure).hasError(THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_SETTLEMENT_IS_IN_APPROVED_STATE);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-169506", component = CAP_DENTAL_LOSS)
    public void testDentalClaimSettlementReopenLoss() {
        // Preconditions
        // Dental Claim3
        ICapDentalLossModel initDentalClaim3 = createClaimPreconditions(D0110, PREDETERMINATION_ACTUAL_SERVICES, true);
        ICapDentalLossModel submitDentalClaim3 = capDentalLoss.submitDentalLoss(initDentalClaim3);
        ICapDentalLossModel closedDentalClaim3 = capDentalLoss.loadDentalLoss(submitDentalClaim3, CLOSED);
        ICapDentalSettlementModel dentalSettlement3 = claimSearch.searchDentalSettlement(closedDentalClaim3, 1).get(0);
        assertThat(dentalSettlement3.getState()).isEqualTo(DISAPPROVED);

        // Step 1
        ICapLossReopenModel dentalClaimReopenModel3 = modelUtils.create(capDentalLoss.getTestData("Reopen", "TestData"));
        dentalClaimReopenModel3.setKey((closedDentalClaim3.getKey()));
        dentalClaimReopenModel3.setReasonCd(null);
        dentalClaimReopenModel3.setReasonDescription(null);
        ICapDentalLossModel reopenDentalClaim3 = capDentalLoss.getFacade().reopen().perform(b -> b.setModel(dentalClaimReopenModel3));
        assertThat(reopenDentalClaim3.getState()).isEqualTo(INCOMPLETE);

        // Step 2
        ICapDentalLossModel submitDentalClaimNew3 = capDentalLoss.submitDentalLoss(reopenDentalClaim3);
        assertThat(submitDentalClaimNew3.getState()).isEqualTo(PENDING);

        //Step 3
        List<ICapDentalSettlementModel> dentalSettlements = claimSearch.searchDentalSettlement(submitDentalClaimNew3, 2);
        assertThat(dentalSettlements.size()).isEqualTo(2);
        ICapDentalSettlementModel dentalSettlement1 = getSettlementRevisions(dentalSettlements, 1);
        assertThat(dentalSettlement1.getSettlementResult().getProposal()).isEqualTo(PREDET_DENY);
        assertThat(dentalSettlement1.getState()).isEqualTo(DISAPPROVED);
        assertThat(dentalSettlement1.rootId()).isEqualTo(dentalSettlement3.rootId());
        assertThat(dentalSettlement1.revisionNumber()).isEqualTo(1);

        ICapDentalSettlementModel dentalSettlement2 = getSettlementRevisions(dentalSettlements, 2);
        assertThat(dentalSettlement2.getSettlementResult().getProposal()).isEqualTo(PREDET_DENY);
        assertThat(dentalSettlement2.getState()).isEqualTo(DISAPPROVED);
        assertThat(dentalSettlement2.rootId()).isEqualTo(dentalSettlement3.rootId());
        assertThat(dentalSettlement2.revisionNumber()).isEqualTo(2);

        // Step 4
        ICapDentalLossModel loadDentalLoss = capDentalLoss.loadDentalLoss(submitDentalClaim3, CLOSED);

        // TODO: Step 5: workflow ms should be implemented
    }




    private ICapDentalLossModel initDentalClaim(String createdDentalPolicyId,
                                                CustomerModel individualCustomer,
                                                IProviderModel individualProvider,
                                                String procedureCode,
                                                String transactionType,
                                                Boolean predetInd) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(createdDentalPolicyId, individualCustomer, individualProvider);
        ICapDentalProcedureModel submittedProcedureModel = dentalClaimModel.getEntity().getSubmittedProcedures().get(0);
        submittedProcedureModel.setProcedureCode(procedureCode);
        submittedProcedureModel.setToothCodes(List.of("6"));
        submittedProcedureModel.setToothArea(null);
        submittedProcedureModel.setPredetInd(predetInd);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(transactionType);
        if (transactionType.equals(PREDETERMINATION_ORTHODONTICS)) {
            ICapDentalOrthodonticModel orthodonticModel = modelUtils.create(capDentalLoss.getTestData("Write", "Ortho"));
            submittedProcedureModel.setOrtho(orthodonticModel);
        }
        return capDentalLoss.initDentalLoss(dentalClaimModel);
    }

    private ICapDentalSettlementModel getSettlementWithProposal(List<ICapDentalSettlementModel> dentalSettlements,
                                                                String proposal) {
        return dentalSettlements.stream()
                .filter(p -> proposal.equals(p.getSettlementResult().getProposal()))
                .findFirst().get();
    }

    private ICapDentalSettlementModel getSettlementRevisions(List<ICapDentalSettlementModel> dentalSettlements,
                                                                Integer revision) {
        return dentalSettlements.stream()
                .filter(p -> revision.equals(p.getSettlementResult().revisionNumber()))
                .findFirst().get();
    }
}
