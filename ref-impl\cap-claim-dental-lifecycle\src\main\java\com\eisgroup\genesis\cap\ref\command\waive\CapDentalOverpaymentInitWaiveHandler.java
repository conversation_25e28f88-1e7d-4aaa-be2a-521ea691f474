/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.waive;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.waive.CapOverpaymentWaiveInitHandler;
import com.eisgroup.genesis.cap.ref.command.waive.input.CapDentalOverpaymentWaiveInitInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for initialization of a new overpayment waive with provided input data.
 *
 * <AUTHOR>
 * @since 22.15
 */
@Modifying
public class CapDentalOverpaymentInitWaiveHandler extends CapOverpaymentWaiveInitHandler<CapDentalOverpaymentWaiveInitInput, CapDentalPaymentEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalOverpaymentWaiveInitInput request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

}

