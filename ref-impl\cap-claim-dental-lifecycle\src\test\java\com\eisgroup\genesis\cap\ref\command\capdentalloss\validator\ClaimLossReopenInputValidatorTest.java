/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.cap.loss.command.input.ClaimLossReopenInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.ClaimLossReopenInputValidator.ClaimLossReopenInputErrorDefinition.REASON_CD_PROVIDED;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.ClaimLossReopenInputValidator.ClaimLossReopenInputErrorDefinition.REASON_DESCRIPTION_PROVIDED;

public class ClaimLossReopenInputValidatorTest {

    @InjectMocks
    private ClaimLossReopenInputValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWithoutReasonAttributes() {
        //given
        ClaimLossReopenInput emptyRequest = new ClaimLossReopenInput(new JsonObject());

        //when
        TestStreamable.create(validator.validate(emptyRequest))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWithReasonAttributes() {
        //given
        ClaimLossReopenInput withReasonsRequest = new ClaimLossReopenInput(JsonUtils.load(
            "requestSamples/closeReopenWithReasonCdAndDescription.json"));

        //when
        TestStreamable.create(validator.validate(withReasonsRequest)
                .map(ErrorHolder::getCode))
            .assertValueCount(2)
            .assertNoErrors()
            .assertValues(REASON_CD_PROVIDED.getCode(), REASON_DESCRIPTION_PROVIDED.getCode());
    }
}