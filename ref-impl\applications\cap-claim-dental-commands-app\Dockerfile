# syntax=sfoeisgennexus01-docker-group.exigengroup.com/docker/dockerfile:1
ARG GENESIS_BASE_IMAGES=sfoeisgennexus01-docker-group.exigengroup.com/genesis-base:2.0

# Extract the layers from fat-jar
FROM --platform=$BUILDPLATFORM ${GENESIS_BASE_IMAGES} as builder
USER eisci
WORKDIR /tmp/fat
COPY --chown=eisci:root target/*-fat.jar fat.jar
RUN java -Djarmode=layertools -jar fat.jar extract

# Copy the layers from previous stage
FROM ${GENESIS_BASE_IMAGES}
USER eisci
WORKDIR /home/<USER>

# Configure PATH
ENV PATH /usr/lib/genesis/bin:$PATH

# Copy entrypoint
COPY --chown=eisci:root --chmod=0755 entrypoint /usr/lib/genesis/bin/run

# Layers are configured in platform's spring-boot-app-config
COPY --from=builder --link /tmp/fat/dependencies/ /usr/lib/genesis/
COPY --from=builder --link /tmp/fat/spring-boot-loader/ /usr/lib/genesis/
COPY --from=builder --link /tmp/fat/genesis-dependencies/ /usr/lib/genesis/
COPY --from=builder --link /tmp/fat/snapshot-dependencies/ /usr/lib/genesis/
COPY --from=builder --link /tmp/fat/application/ /usr/lib/genesis/

# Set default command
CMD [ "run", "--all" ]
