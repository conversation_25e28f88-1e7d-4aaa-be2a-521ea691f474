/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;


import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.common.repository.api.CapCommonRepository;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentCommands;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Internal command for generating Dental Payments.
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapDentalPaymentGenerationHandler implements ProductCommandHandler<IdentifierRequest, CapDentalPaymentScheduleEntity> {

    private static final String DENTAL_INTERNAL_MODEL = "DentalInternal";

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private CommandPublisher commandPublisher;


    @Autowired
    private CapCommonRepository<CapDentalPaymentScheduleEntity> capPaymentScheduleRepository;


    @Autowired
    private EntityLinkBuilderRegistry entityLinkBuilderRegistry;

    @Nonnull
    @Override
    public CapDentalPaymentScheduleEntity load(@Nonnull IdentifierRequest request) {
        return capPaymentScheduleRepository.load(request.getKey(), modelResolver.getModelName());
    }

    @Nonnull
    @Override
    public CapDentalPaymentScheduleEntity execute(@Nonnull IdentifierRequest request, @Nonnull CapDentalPaymentScheduleEntity entity) {
        return Lazy.of(entity).get();
    }

    @Nonnull
    @Override
    public CapDentalPaymentScheduleEntity save(@Nonnull IdentifierRequest request, @Nonnull CapDentalPaymentScheduleEntity entity) {
        return Lazy.of(entity).get();
    }

    @Override
    public String getName() {
        return CapDentalPaymentCommands.GENERATE_PAYMENTS;
    }





}