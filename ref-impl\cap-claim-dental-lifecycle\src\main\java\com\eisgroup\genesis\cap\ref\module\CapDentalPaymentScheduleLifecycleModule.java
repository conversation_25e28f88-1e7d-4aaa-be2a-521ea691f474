/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import java.util.ArrayList;
import java.util.Collection;

import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalBuildPaymentScheduleHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalCancelActivePaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentGenerationHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleActivateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleCompleteHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleSuspendHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleUnsuspendHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPreviewPaymentScheduleHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalSuspendLossPaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalUnsuspendLossPaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentScheduleLifecycleConfig;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentTemplateLifecycleConfig;
import javax.annotation.Nonnull;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleUpdateHandler;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;

/**
 * Lifecycle configuration of Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        Collection<CommandHandler<?, ?>> commands = new ArrayList<>();

        commands.add(new CapDentalPaymentScheduleInitHandler());
        commands.add(new CapDentalPaymentScheduleUpdateHandler());
        commands.add(new CapDentalPreviewPaymentScheduleHandler());
        commands.add(new CapDentalPaymentScheduleActivateHandler());
        commands.add(new CapDentalPaymentScheduleCancelHandler());
        commands.add(new CapDentalPaymentScheduleCompleteHandler());
        commands.add(new CapDentalPaymentScheduleSuspendHandler());
        commands.add(new CapDentalPaymentScheduleUnsuspendHandler());
        commands.add(new CapDentalPaymentGenerationHandler());
        commands.add(new CapDentalBuildPaymentScheduleHandler());
        commands.add(new CapDentalCancelActivePaymentSchedulesHandler());
        commands.add(new CapDentalSuspendLossPaymentSchedulesHandler());
        commands.add(new CapDentalUnsuspendLossPaymentSchedulesHandler());

        return commands;
    }

    @Override
    public String getModelType() {
        return "CapPaymentSchedule";
    }

    @Override
    public String getModelName() {
        return "CapDentalPaymentSchedule";
    }

    @Override
    public String getModelVersion() {
        return "1";
    }

    @Nonnull
    @Override
    public String getStateMachineName() {
        return getModelName();
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[]{
                CapBaseCommandConfig.class,
                CapDentalPaymentTemplateLifecycleConfig.class,
                CapDentalPaymentScheduleLifecycleConfig.class,
                CapTransformationCommandsConfig.class
        };
    }
}
