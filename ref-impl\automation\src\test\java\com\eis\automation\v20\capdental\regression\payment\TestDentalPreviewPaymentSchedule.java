/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.payment;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalOrthodonticModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentAllocationModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.service.ICapDentalPaymentScheduleService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.impl.CustomerModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.javamoney.moneta.Money;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_PAYMENT;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ORTHODONTIC_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalOrthoFrequencyCd.MONTHLY;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.PRIMARY_INSURED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.SERVICE_PROVIDER;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D8680;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.policybenefits.constant.PolicyConstant.Currency.USD;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalPreviewPaymentSchedule extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IClaimSearchService claimSearchService;
    @Autowired
    private ICapDentalPaymentScheduleService paymentScheduleService;
    IndividualCustomerModel individualCustomer;
    IIndividualProviderModel individualProvider;


    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-160989", component = CAP_DENTAL_PAYMENT)
    public void testDentalPreviewPaymentSchedule() {
        // Preconditions
        // Individual Customer
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalLossModel initDentalClaim = createClaimPreconditions(D0330, ACTUAL_SERVICES, false, "TestData_Preventive", SERVICE_PROVIDER);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        ICapDentalSettlementModel initSettlement = claimSearchService.searchDentalSettlement(submitDentalClaim, 1).get(0);
        assertThat(initSettlement.getState()).isEqualTo(APPROVED);
        ICapDentalLossModel initDentalClaim2 = createClaimPreconditions(D8680, ORTHODONTIC_SERVICES, false, "TestData_WO_Preventive", PRIMARY_INSURED);
        assertThat(initDentalClaim2.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim2 = capDentalLoss.submitDentalLoss(initDentalClaim2);
        ICapDentalSettlementModel initSettlement2 = claimSearchService.searchDentalSettlement(submitDentalClaim2, 1).get(0);
        assertThat(initSettlement2.getState()).isEqualTo(APPROVED);

        // Variables
        Integer orthoMonth1 = 1;
        Integer orthoMonth2 = 2;
        Integer orthoMonth3 = 3;
        LocalDateTime date1 = submitDentalClaim.getLossDetail().getSubmittedProcedures().get(0).getDateOfService().atStartOfDay();
        LocalDateTime date2 = submitDentalClaim.getLossDetail().getSubmittedProcedures().get(0).getDateOfService().plusMonths(1).withDayOfMonth(25).atStartOfDay();
        LocalDateTime date3 = submitDentalClaim.getLossDetail().getSubmittedProcedures().get(0).getDateOfService().plusMonths(2).withDayOfMonth(25).atStartOfDay();
        String provider0 = submitDentalClaim.getLossDetail().getClaimData().getProviderRole().getRegistryId();
        String policyHolder1 = submitDentalClaim.getLossDetail().getClaimData().getPolicyholderRole().getRegistryId();

        //Step 1
        ICapDentalPaymentSchedulePreviewModel previewPaymentSchedule1 = paymentScheduleService.previewPaymentSchedule(submitDentalClaim, initSettlement);
        verifyPaymentSchedule(previewPaymentSchedule1, 0, submitDentalClaim, initSettlement, ACTUAL_SERVICES, 0.0, date1, null, provider0);

        //Step 2
        ICapDentalPaymentSchedulePreviewModel previewPaymentSchedule2 = paymentScheduleService.previewPaymentSchedule(submitDentalClaim2, initSettlement2);
        //Step 2.1
        verifyPaymentSchedule(previewPaymentSchedule2, 0, submitDentalClaim2, initSettlement2, ORTHODONTIC_SERVICES, 66.66, date1, orthoMonth1, policyHolder1);
        //Step 2.2
        verifyPaymentSchedule(previewPaymentSchedule2, 1, submitDentalClaim2, initSettlement2, ORTHODONTIC_SERVICES, 66.66, date2, orthoMonth2, policyHolder1);
        //Step 2.3
        verifyPaymentSchedule(previewPaymentSchedule2, 2, submitDentalClaim2, initSettlement2, ORTHODONTIC_SERVICES, 66.67, date3, orthoMonth3, policyHolder1);
    }

    private void verifyPaymentSchedule(ICapDentalPaymentSchedulePreviewModel previewModel, Integer paymentInstance, ICapDentalLossModel dentalClaim, ICapDentalSettlementModel settlementModel, String transactionType, Double money, LocalDateTime date, Integer orthoMonth, String payee) {
        CapDentalPaymentAllocationModel allocationModel = (CapDentalPaymentAllocationModel) previewModel.getPayments().get(paymentInstance).getPaymentDetails().getPaymentAllocations().get(0);
        assertSoftly(softly -> {
            softly.assertThat(previewModel.getPayments().get(paymentInstance).getDirection()).isEqualTo("outgoing");
            softly.assertThat(previewModel.getPayments().get(paymentInstance).getPaymentNetAmount()).isEqualTo(Money.of(money, USD));
            softly.assertThat(allocationModel.getAllocationGrossAmount()).isEqualTo(Money.of(money, USD));
            softly.assertThat(allocationModel.getAllocationNetAmount()).isEqualTo(Money.of(money, USD));
            softly.assertThat(allocationModel.getAllocationSource().getUri()).isEqualTo(settlementModel.getGentityUri().getUriModel().getUri());
            softly.assertThat(allocationModel.getAllocationPayableItem().getClaimSource().getUri()).isEqualTo(dentalClaim.getGentityUri().getUriModel().getUri());
            softly.assertThat(allocationModel.getAllocationLossInfo().getLossSource().getUri()).isEqualTo(dentalClaim.getGentityUri().getUriModel().getUri());
            softly.assertThat(allocationModel.getAllocationDentalDetails().getTransactionTypeCd()).isEqualTo(transactionType);
            if (ACTUAL_SERVICES.equals(transactionType)) {
                softly.assertThat(previewModel.getPayments().get(paymentInstance).getPaymentDetails().getPaymentDate()).isEqualTo(date);
                softly.assertThat(previewModel.getPayments().get(paymentInstance).getPaymentDetails().getPayeeDetails().getPayee().getUri()).isEqualTo(payee);
            }
            if (ORTHODONTIC_SERVICES.equals(transactionType)) {
                softly.assertThat(previewModel.getPayments().get(paymentInstance).getPaymentDetails().getPaymentDate()).isEqualTo(date);
                softly.assertThat(allocationModel.getAllocationPayableItem().getOrthoMonth()).isEqualTo(orthoMonth);
                softly.assertThat(previewModel.getPayments().get(paymentInstance).getPaymentDetails().getPayeeDetails().getPayee().getUri()).isEqualTo(payee);
            }
        });
    }


    public ICapDentalLossModel createClaimPreconditions(String procedureCode, String transactionType, Boolean predetInd, String testData, String payeeType) {
        // Preconditions

        // Dental Unverified Policy
        ICapDentalPolicyInfoWrapperModel dentalPolicyModel = capPolicy.createDentalUnverifiedPolicyModel(
                capPolicy.getSpecificTestData("GENESIS-160989", testData), individualCustomer);
        String createdDentalPolicyId =  capPolicy.writeCapUnverifiedPolicy(dentalPolicyModel).getCapPolicyId();

        // Dental Claim
        return initDentalClaim(createdDentalPolicyId, individualCustomer,
                individualProvider, procedureCode, transactionType, predetInd, payeeType);
    }

    private ICapDentalLossModel initDentalClaim(String createdDentalPolicyId,
                                                CustomerModel individualCustomer,
                                                IProviderModel individualProvider,
                                                String procedureCode,
                                                String transactionType,
                                                Boolean predetInd, String payeeType) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(createdDentalPolicyId, individualCustomer, individualProvider);
        ICapDentalProcedureModel submittedProcedureModel = dentalClaimModel.getEntity().getSubmittedProcedures().get(0);
        submittedProcedureModel.setProcedureCode(procedureCode);
        submittedProcedureModel.setToothCodes(List.of("6"));
        submittedProcedureModel.setToothArea(null);
        submittedProcedureModel.setPredetInd(predetInd);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(transactionType);
        dentalClaimModel.getEntity().getClaimData().setPayeeType(payeeType);
        if (transactionType.equals(ORTHODONTIC_SERVICES)) {
            ICapDentalOrthodonticModel orthodonticModel = modelUtils.create(capDentalLoss.getTestData("Write", "Ortho"));
            submittedProcedureModel.setOrtho(orthodonticModel);
            submittedProcedureModel.getOrtho().setOrthoFrequencyCd(MONTHLY);
            submittedProcedureModel.getOrtho().setOrthoMonthQuantity(3);
        }
        return capDentalLoss.initDentalLoss(dentalClaimModel);
    }
}
