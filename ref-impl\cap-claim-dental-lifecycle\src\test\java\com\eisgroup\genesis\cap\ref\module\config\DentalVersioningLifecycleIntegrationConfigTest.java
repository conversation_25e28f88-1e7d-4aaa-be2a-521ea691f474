package com.eisgroup.genesis.cap.ref.module.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.versioning.VersioningReadRepository;
import com.eisgroup.genesis.versioning.VersioningWriteRepository;

@RunWith(MockitoJUnitRunner.class)
public class DentalVersioningLifecycleIntegrationConfigTest {

    @InjectMocks
    private DentalVersioningLifecycleIntegrationConfig config;

    @Mock
    private VersioningReadRepository versioningReadRepository;
    @Mock
    private VersioningWriteRepository versioningWriteRepository;


    @Test
    public void testReturnVersioningService() {
        var result = config.versioningService(versioningReadRepository, versioningWriteRepository);
        assertThat(result, notNullValue());
    }
}





