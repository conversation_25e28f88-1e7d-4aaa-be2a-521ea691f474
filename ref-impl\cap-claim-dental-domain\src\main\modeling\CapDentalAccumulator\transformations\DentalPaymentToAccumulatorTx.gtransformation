@EventListener("issuePayment")
@TargetEntityCommand("writeTransaction")
Transformation DentalPaymentToAccumulatorTx {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity
    }

    Var loss is ExtLink(payment.originSource)
    Var systemTime is Now()

    Attr transactionTimestamp is systemTime
    Attr policyURI is loss.policyId
    //GENESIS-366362
    Attr customerURI is loss.lossDetail.claimData.policyholderRole.registryId
    Attr sourceURI is ToExtLink(payment)._uri
    Attr data is SafeInvoke(payment.paymentDetails.paymentAllocations, accumulatorDataForAllocation(payment.paymentDetails.paymentAllocations).data)

    Producer accumulatorDataForAllocation(allocation) {
        Attr data is SafeInvoke(allocation.allocationDentalDetails.accumulatorDetails, createTxData(allocation.allocationDentalDetails.accumulatorDetails, allocation))
    }

    Producer createTxData(accumulatorDetails, allocation) {
        Var firstProcedureCategory is First(accumulatorDetails.appliesToProcedureCategories)
        Var suffix is Ternary(Equals(firstProcedureCategory, "Basic") || Equals(firstProcedureCategory, "Major") || Equals(firstProcedureCategory, "Preventive"), "Dental", firstProcedureCategory)
        Attr amount is accumulatorDetails.accumulatorAmount.amount
        Attr party is allocation.allocationDentalDetails.patient
        Attr type is "DentalPayment" + "_" + accumulatorDetails.accumulatorType + "_" +  accumulatorDetails.renewalType + "_" + suffix
        Attr transactionDate is systemTime
        Attr resource is Ternary(Equals(accumulatorDetails.accumulatorType, "FamilyDeductible"), createExtLink(Root().loss.policyId), Null())
        Attr extension is New() {
            Attr _type is "JsonType"
            Attr appliedToServices is Super().accumulatorDetails.appliesToProcedureCategories
            Attr networkType is Super().accumulatorDetails.networkType
            Attr term is Super().accumulatorDetails.term
        }
    }

    Producer createExtLink(uri) {
        Attr _uri is uri
    }

}
