<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="pendClaimCancelPaymentSchedule" name="Pend Claim Cancel Payment Schedules" isExecutable="true">
    <serviceTask id="cancelLossPaymentSchedulesAction" name="Cancel Loss Payment Schedules" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="cancelLossPaymentSchedules" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'originSource':{'_uri': '${_uri}'}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="setClaimSubStatusAction" name="Set Claim SubStatus" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"/>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"/>
        <flowable:eventInParameter source="CapDentalLoss" target="_modelName"/>
        <flowable:eventInParameter source="setLossSubStatus" target="commandName"/>
        <flowable:eventInParameter sourceExpression="{'_key': ${_key}, 'lossSubStatusCd': '${lossSubStatusCd}'}" target="payload"/>
        <flowable:eventOutParameter source="payload" sourceType="string" target="payload"/>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"/>
      </extensionElements>
    </serviceTask>
    <endEvent id="end"></endEvent>
    <startEvent id="startCancelAction" flowable:formFieldValidation="true"></startEvent>
    <sequenceFlow id="bpmnSequenceFlow_10" sourceRef="cancelLossPaymentSchedulesAction" targetRef="setClaimSubStatusAction"/>
    <sequenceFlow id="sid-CDA8C2CD-7151-465E-B56E-76C953E8AE2B" sourceRef="setClaimSubStatusAction" targetRef="end"/>
    <sequenceFlow id="sid-3587B8B5-9653-4845-B570-9F6BE8272830" sourceRef="startCancelAction" targetRef="cancelLossPaymentSchedulesAction"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_pendClaimCancelPaymentSchedule">
    <bpmndi:BPMNPlane bpmnElement="pendClaimCancelPaymentSchedule" id="BPMNPlane_pendClaimCancelPaymentSchedule">
      <bpmndi:BPMNShape bpmnElement="cancelLossPaymentSchedulesAction" id="BPMNShape_cancelLossPaymentSchedulesAction">
        <omgdc:Bounds height="80.0" width="100.0" x="402.0" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="setClaimSubStatusAction" id="BPMNShape_setClaimSubStatusAction">
        <omgdc:Bounds height="80.0" width="100.0" x="580.0" y="188.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startCancelAction" id="BPMNShape_startCancelAction">
        <omgdc:Bounds height="30.0" width="30.0" x="225.0" y="213.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="848.5" y="214.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="bpmnSequenceFlow_10" id="BPMNEdge_bpmnSequenceFlow_10" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="502.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="580.0" y="228.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-3587B8B5-9653-4845-B570-9F6BE8272830" id="BPMNEdge_sid-3587B8B5-9653-4845-B570-9F6BE8272830" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="255.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="402.0" y="228.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CDA8C2CD-7151-465E-B56E-76C953E8AE2B" id="BPMNEdge_sid-CDA8C2CD-7151-465E-B56E-76C953E8AE2B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="680.0" y="228.0"></omgdi:waypoint>
        <omgdi:waypoint x="848.5" y="228.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>