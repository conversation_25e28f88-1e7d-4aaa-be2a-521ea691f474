/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.jobs;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.dental.batch.commands.CapJobCommands;
import com.eisgroup.genesis.cap.financial.command.CapPaymentCommands;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentIndexRepository;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.jobs.lifecycle.api.commands.output.SubsequentCommand;
import com.eisgroup.genesis.jobs.lifecycle.api.handler.CommandCollectingHandler;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import org.springframework.beans.factory.annotation.Autowired;

import static com.eisgroup.genesis.cap.financial.model.PaymentVariations.PAYMENT;

/**
 * Payment Issue Job implementation
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalPaymentIssueJob extends CommandCollectingHandler {
    public static final String NAME = CapJobCommands.PAYMENT_ISSUE_JOB;
    private static final String CAP_DENTAL_PAYMENT_DEFINITION = "CapDentalPaymentDefinition";
    private static final String CAP_PAYMENT = "CapPayment";

    @Autowired
    private CapDentalPaymentIndexRepository capDentalPaymentIndexRepository;

    @Override
    protected Streamable<SubsequentCommand> execute() {
        return capDentalPaymentIndexRepository.loadPaymentIndexesByState("Approved")
                .self()
                .flatMap(capPaymentIdxEntities -> Streamable.from(capPaymentIdxEntities)
                        .map(paymentIndex -> {
                            FactoryLink factoryLink = new FactoryLink(new EntityLink<>(JsonEntity.class, paymentIndex.getPaymentId()));
                            RootEntityKey rootEntityKey = new RootEntityKey(factoryLink.getRootId(), factoryLink.getRevisionNo());
                            IdentifierRequest request = new IdentifierRequest(rootEntityKey);
                            return new SubsequentCommand(CapPaymentCommands.REQUEST_ISSUE_PAYMENT, request, PAYMENT, CAP_DENTAL_PAYMENT_DEFINITION, "1", CAP_PAYMENT);
                        }));
    }

    @Override
    public String getName() {
        return NAME;
    }
}
