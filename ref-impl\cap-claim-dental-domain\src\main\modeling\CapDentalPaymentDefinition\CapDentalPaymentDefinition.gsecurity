Secure using privilege ("Financial: Initiate Payment") {
    Model CapDentalPaymentDefinition {
        Command initPayment
    }
}

Secure using privilege ("Financial: Request Issue Payment") {
    Model CapDentalPaymentDefinition {
        Command requestIssuePayment
    }
}

Secure using privilege ("Financial: Request Issue Payment Failed") {
    Model CapDentalPaymentDefinition {
        Command requestIssueFailPayment
    }
}

Secure using privilege ("Financial: Issue Payment") {
    Model CapDentalPaymentDefinition {
        Command issuePayment
    }
}



