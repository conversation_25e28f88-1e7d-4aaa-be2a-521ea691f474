/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.common.repository.api.CapCommonRepository;
import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.test.utils.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.UUID;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentRequestIssueFailHandlerTest {

    @InjectMocks
    private CapDentalPaymentRequestIssueFailHandler handler;
    @Mock
    private CapCommonRepository<CapDentalPaymentEntity> capPaymentRepository;
    @Mock
    private ModelResolver modelResolver;

    @Before
    public void setUp() {
        Mockito.when(modelResolver.getModelName()).thenReturn("CapDentalPaymentDefinition");
    }

    @Test
    public void shouldReturnHandlerName() {
        assertThat(handler.getName(), equalTo("requestIssueFailPayment"));
    }

    @Test
    public void shouldReturnHandlerVariation() {
        assertThat(handler.getVariation(), equalTo(PaymentVariations.PAYMENT));
    }

    @Test
    public void shouldLoadEntity() {
        //given
        IdentifierRequest request = new IdentifierRequest(new RootEntityKey(UUID.randomUUID(), 1));
        CapDentalPaymentEntity payment = (CapDentalPaymentEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalPayment/payment.json"));
        Mockito.when(capPaymentRepository.load(request.getKey(), "CapDentalPaymentDefinition", PaymentVariations.PAYMENT))
                .thenReturn(payment);
        //when
        handler.load(request);
        //then
        verify(capPaymentRepository).load(request.getKey(), "CapDentalPaymentDefinition", PaymentVariations.PAYMENT);
    }

    @Test
    public void testHandlerExecute() {
        //given
        IdentifierRequest request = new IdentifierRequest(new RootEntityKey(UUID.randomUUID(), 1));
        CapDentalPaymentEntity payment = (CapDentalPaymentEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalPayment/payment.json"));
        assertThat(
            //when
            handler.execute(request, payment),
            //then
            is(payment));
    }

    @Test
    public void testHandlerSave() {
        //given
        IdentifierRequest request = new IdentifierRequest(new RootEntityKey(UUID.randomUUID(), 1));
        CapDentalPaymentEntity payment = (CapDentalPaymentEntity) ModelInstanceFactory.createInstance(
                JsonUtils.loadJson("json/capDentalPayment/payment.json"));
        Mockito.when(capPaymentRepository.save(payment))
                .thenReturn(payment);
        //when
        handler.save(request, payment);
        //then
        verify(capPaymentRepository).save(payment);
    }
}
