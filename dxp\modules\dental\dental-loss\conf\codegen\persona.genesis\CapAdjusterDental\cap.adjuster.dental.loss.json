{"swagger": "2.0", "x-dxp-spec": {"imports": {"dental.loss": {"schema": "integration.dental.loss.json", "path-filter": "exclude-all"}}}, "x-dxp-defaults": {"unwrapping-operation-body": "body", "unwrapping-operation-response": "body.success", "unwrapping-operation-response-failure": "body.failure.data", "responses": {"400": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "401": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "404": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "422": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}, "500": {"x-dxp-defaults-value-strategy": "apply-always", "x-dxp-import-strategy": "overwrite", "description": "No Description Provided", "schema": {"x-dxp-class": "genesis.core.exceptions.dto.GenesisCommonExceptionDTO"}}}}, "info": {"description": "CAP Adjuster - Dental - Loss", "version": "1", "title": "CAP Adjuster - Dental - Loss"}, "basePath": "/", "schemes": ["http", "https"], "tags": [{"name": "/cap-adjuster/v1/losses-dental", "description": "CAP Adjuster - Dental - Loss"}], "paths": {"/losses-dental/{rootId}/{revisionNo}": {"get": {"x-dxp-path": "/api/caploss/CapDentalLoss/v1/entities/{rootId}/{revisionNo}", "tags": ["/cap-adjuster/v1/losses-dental"]}}, "/losses-dental/rules/{entryPoint}": {"post": {"x-dxp-path": "/api/caploss/CapDentalLoss/v1/rules/{entryPoint}", "tags": ["/cap-adjuster/v1/losses-dental"]}}, "/losses-dental/draft": {"post": {"x-dxp-path": "/api/caploss/CapDentalLoss/v1/command/initLoss", "tags": ["/cap-adjuster/v1/losses-dental"]}}, "/losses-dental/{rootId}/{revisionNo}/submit": {"post": {"x-dxp-path": "/api/caploss/CapDentalLoss/v1/command/submitLoss", "tags": ["/cap-adjuster/v1/losses-dental"], "parameters": [{"name": "rootId", "in": "path", "x-dxp-mapping": "@body/body._key.rootId"}, {"name": "revisionNo", "in": "path", "x-dxp-mapping": "@body/body._key.revisionNo"}]}}, "/losses-dental": {"post": {"x-dxp-path": "/api/caploss/CapDentalLoss/v1/command/createLoss", "tags": ["/cap-adjuster/v1/losses-dental"]}, "put": {"x-dxp-method": "post", "x-dxp-path": "/api/caploss/CapDentalLoss/v1/command/updateLoss", "tags": ["/cap-adjuster/v1/losses-dental"]}}}}