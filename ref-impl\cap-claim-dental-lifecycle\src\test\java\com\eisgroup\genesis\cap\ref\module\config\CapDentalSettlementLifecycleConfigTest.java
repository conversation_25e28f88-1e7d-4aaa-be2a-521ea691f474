package com.eisgroup.genesis.cap.ref.module.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalSettlementLifecycleConfigTest {

    @InjectMocks
    private CapDentalSettlementLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Test
    public void testReturnSettlementNumberGenerator() {
        var result = config.settlementNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.capDentalLinkValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalSettlementValidator() {
        var result = config.capDentalSettlementValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }


    @Test
    public void testReturnCapDentalSettlementInitInputValidator() {
        var result = config.capDentalSettlementInitInputValidator(capDentalSettlementIndexResolver, entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }
}