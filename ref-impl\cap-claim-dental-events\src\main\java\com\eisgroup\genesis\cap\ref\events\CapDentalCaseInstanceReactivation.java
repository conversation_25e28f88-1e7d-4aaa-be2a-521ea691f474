/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events;

import static com.eisgroup.genesis.json.key.BaseKey.ATTRIBUTE_NAME;
import static com.eisgroup.genesis.json.key.BaseKey.ROOT_ID;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.events.resolver.CapDentalCaseInstanceResolver;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.http.errors.HttpRequestFailedException;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;

import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.Optional;

/**
 * Case Instance reactivation
 *
 * <AUTHOR>
 * @since 23.2
 */
public class CapDentalCaseInstanceReactivation {

    private static final Logger LOGGER = LoggerFactory.getLogger(CapDentalCaseInstanceReactivation.class);

    private static final String CASE_INSTANCE_REACTIVATE_LINK_API = "api/common/CaseInstance/v1/%s/reactivation";
    private final HttpClient client;
    private final String workflowUrl;
    private final ServiceAccessRunner serviceAccessRunner;
    private final CapDentalCaseInstanceResolver capDentalCaseInstanceResolver;

    public CapDentalCaseInstanceReactivation(HttpClient client, String workflowUrl, ServiceAccessRunner serviceAccessRunner, CapDentalCaseInstanceResolver capDentalCaseInstanceResolver) {
        this.client = client;
        this.workflowUrl = workflowUrl;
        this.serviceAccessRunner = serviceAccessRunner;
        this.capDentalCaseInstanceResolver = capDentalCaseInstanceResolver;
    }

    /**
     * Resolves case instance and reactivate by lossId
     *
     * @param lossRootId
     */
    public Lazy reactivateCaseInstance(EntityLink<RootEntity> lossRootId) {
        return capDentalCaseInstanceResolver.resolveCaseInstanceId(lossRootId)
                .flatMap(this::reactivateCaseInstanceById);
    }

    private Lazy reactivateCaseInstanceById(String id) {
        HttpPost httpRequest = new HttpPost(createFullUrl(id));
        return serviceAccessRunner.runLazy(() -> {
            try {
                JsonElement rootId = client.execute(httpRequest, new JsonResponseHandler())
                    .getAsJsonObject()
                    .getAsJsonObject("body")
                    .getAsJsonObject("success")
                    .getAsJsonObject(ATTRIBUTE_NAME)
                    .get(ROOT_ID);
                return Lazy.of(rootId);
            } catch (IOException | HttpRequestFailedException e) {
                LOGGER.info("Exception caught:", e);
                return Lazy.of(JsonNull.INSTANCE);
            }
        });
    }

    private String createFullUrl(String caseInstanceId) {
        return Optional.ofNullable(workflowUrl)
                .map(url -> url.endsWith("/") ? url : url + "/")
                .map(url -> url + String.format(CASE_INSTANCE_REACTIVATE_LINK_API, caseInstanceId))
                .orElseThrow(() -> new UnsupportedOperationException("workflowUrl is not configured for CapDentalReactivateCaseInstanceOperationExecutor through rest"));
    }

    public static class CapDentalCaseInstanceReactivationErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalCaseInstanceReactivationErrorDefinition PAYMENT_MESSAGE_CONTAINS_ERRORS = new CapDentalCaseInstanceReactivationErrorDefinition(
                "cdcir-001", "Case reactivation was not successful");

        protected CapDentalCaseInstanceReactivationErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}