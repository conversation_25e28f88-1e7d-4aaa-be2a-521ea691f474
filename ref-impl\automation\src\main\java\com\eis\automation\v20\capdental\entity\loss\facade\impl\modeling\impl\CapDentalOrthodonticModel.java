/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalOrthodonticModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.javamoney.moneta.Money;

import java.time.LocalDate;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@<PERSON>sonFilter(AbstractFilter.NAME)
public class CapDentalOrthodonticModel extends TypeModel implements ICapDentalOrthodonticModel {

    private Money downPayment;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate appliancePlacedDate;
    private String orthoFrequencyCd;
    private Integer orthoMonthQuantity;

    public Money getDownPayment() {
        return downPayment;
    }

    public void setDownPayment(Money downPayment) {
        this.downPayment = downPayment;
    }

    public LocalDate getAppliancePlacedDate() {
        return appliancePlacedDate;
    }

    public void setAppliancePlacedDate(LocalDate appliancePlacedDate) {
        this.appliancePlacedDate = appliancePlacedDate;
    }

    public String getOrthoFrequencyCd() {
        return orthoFrequencyCd;
    }

    public void setOrthoFrequencyCd(String orthoFrequencyCd) {
        this.orthoFrequencyCd = orthoFrequencyCd;
    }

    public Integer getOrthoMonthQuantity() {
        return orthoMonthQuantity;
    }

    public void setOrthoMonthQuantity(Integer orthoMonthQuantity) {
        this.orthoMonthQuantity = orthoMonthQuantity;
    }
}