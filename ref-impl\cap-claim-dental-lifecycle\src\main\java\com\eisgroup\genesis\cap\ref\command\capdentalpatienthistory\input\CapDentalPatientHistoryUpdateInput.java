/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.input;

import java.util.Optional;

import com.eisgroup.genesis.commands.request.EntityCarryingRequest;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

/**
 * Dental patient history update request.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalPatientHistoryUpdateInput extends IdentifierRequest implements EntityCarryingRequest {

    private static final String ENTITY = "entity";

    public CapDentalPatientHistoryUpdateInput(JsonObject original) {
        super(original);
    }

    @Override
    public CapDentalPatientHistoryEntity getEntity() {
        return Optional.ofNullable(getRawChild(ENTITY))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(ModelInstanceFactory::createInstance)
                .map(CapDentalPatientHistoryEntity.class::cast)
                .orElse(null);
    }
}
