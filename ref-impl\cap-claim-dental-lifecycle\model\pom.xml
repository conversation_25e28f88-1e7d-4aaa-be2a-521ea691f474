<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
		<relativePath>../../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-lifecycle-model</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-bundle</artifactId>
            <classifier>metadata</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-lifecycle</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>
</project>
