package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentDefinitionLifecycleConfigTest {

    @InjectMocks
    private CapDentalPaymentDefinitionLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;

    @Test
    public void testReturnPaymentNumberGenerator() {
        var result = config.paymentNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnRecoveryNumberGenerator() {
        var result = config.recoveryNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnUnderpaymentNumberGenerator() {
        var result = config.underpaymentNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnOverpaymentWaiveNumberGenerator() {
        var result = config.overpaymentWaiveNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.claimSettlementInputValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPaymentInitInputValidator() {
        var result = config.capDentalPaymentInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }


    @Test
    public void testReturnCapDentalPaymentUpdateInputValidator() {
        var result = config.capDentalPaymentUpdateInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalRecoveryInitInputValidator() {
        var result = config.capDentalRecoveryInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }
    @Test
    public void testReturnCapDentalRecoveryUpdateInputValidator() {
        var result = config.capDentalRecoveryUpdateInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalOverpaymentWaiveInitInputValidator() {
        var result = config.capDentalOverpaymentWaiveInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalOverpaymentWaiveUpdateValidator() {
        var result = config.capDentalOverpaymentWaiveUpdateValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalUnderpaymentInitInputValidator() {
        var result = config.capDentalUnderpaymentInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalUnderpaymentUpdateInputValidator() {
        var result = config.capDentalUnderpaymentUpdateInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }
}





