/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import javax.annotation.Nonnull;

import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalPaymentUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;


/**
 * Command handler for updating {@link CapDentalPaymentEntity}.
 *
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalPaymentUp<PERSON><PERSON>and<PERSON> extends CapPaymentUpdateHandler<CapDentalPaymentUpdateInput, CapDentalPaymentEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPaymentUpdateInput request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull CapDentalPaymentUpdateInput input, @Nonnull CapDentalPaymentEntity entity) {
        var updateEntity = populateCapPaymentAttributes(input, entity).get();
        updateEntity.setPaymentNetAmount(input.getPaymentNetAmount());
        return updateEntity;
    }
}
