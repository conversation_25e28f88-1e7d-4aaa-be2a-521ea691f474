/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalAccumulatorModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalCalculationResultModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalCalculationStatusModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalResultEntryModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalResultEntryModel extends TypeModel implements ICapDentalResultEntryModel {

    private String serviceSource;
    private ICapDentalCalculationResultModel calculationResult;
    private ICapDentalCalculationStatusModel status;
    private List<ICapDentalAccumulatorModel> reservedAccumulators;

    public String getServiceSource() {
        return serviceSource;
    }

    public void setServiceSource(String serviceSource) {
        this.serviceSource = serviceSource;
    }

    @JsonSerialize(as = CapDentalCalculationResultModel.class)
    public ICapDentalCalculationResultModel getCalculationResult() {
        return calculationResult;
    }

    @JsonDeserialize(as = CapDentalCalculationResultModel.class)
    public void setCalculationResult(ICapDentalCalculationResultModel calculationResult) {
        this.calculationResult = calculationResult;
    }

    @JsonSerialize(as = CapDentalCalculationStatusModel.class)
    public ICapDentalCalculationStatusModel getStatus() {
        return status;
    }

    @JsonDeserialize(as = CapDentalCalculationStatusModel.class)
    public void setStatus(ICapDentalCalculationStatusModel status) {
        this.status = status;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalAccumulatorModel.class)
    public List<ICapDentalAccumulatorModel> getReservedAccumulators() {
        return reservedAccumulators;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalAccumulatorModel.class)
    public void setReservedAccumulators(List<ICapDentalAccumulatorModel> reservedAccumulators) {
        this.reservedAccumulators = reservedAccumulators;
    }
}
