@CapEndpoint("capLatestPaymentInSchedule")
Transformation CapLatestPaymentInSchedule {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        ?  //as out
    }
    Var schedule is ExtLink(payment.paymentSchedule)
    Var scheduleIndexes is Load(createOriginSource(schedule), "CapDentalPaymentScheduleIndex", "CapDentalPaymentScheduleIdxEntity")
    Var filteredSheduleIndex is First(scheduleIndexes[filterActive])
    Var loadedShedule is ExtLink(SafeInvoke(filteredSheduleIndex.paymentSchedule, AsExtLink(filteredSheduleIndex.paymentSchedule)))
    Var latestScheduledPaymentDate is Max(loadedShedule.payments.paymentDetails.paymentDate)

    Attr isLastPayment is Ternary(Equals(latestScheduledPaymentDate, Null()), Equals(1,2), Gte(CompareDates(payment.paymentDetails.paymentDate, latestScheduledPaymentDate), 0))
    Attr scheduleToComplete is loadedShedule

    Producer createOriginSource(schedule) {
        Attr originSource is schedule.originSource._uri
    }

    Filter filterActive {
        state == "Active"
    }

}