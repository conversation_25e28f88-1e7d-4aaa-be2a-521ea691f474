/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.ORIGIN_SOURCE_URI_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.OTHER_SETTLEMENT_ORIGIN_SOURCE;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.PROPOSAL_NOT_PAY;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_NOT_APPROVED;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalBuildPaymentScheduleInputValidator.CapDentalBuildPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_URI_INCORRECT;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalBuildPaymentScheduleInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.exception.JsonValueFormatError;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

/**
 * Class for validating {@link CapDentalBuildPaymentScheduleInput}.
 *
 * <AUTHOR>
 * @since 22.11
 */
public class CapDentalBuildPaymentScheduleInputValidator extends CapInputValidator<CapDentalBuildPaymentScheduleInput> {

    private static final String STATE_APPROVED = "Approved";
    private static final String PROPOSAL_PAY = "PAY";

    private final EntityLinkResolverRegistry entityLinkResolverRegistry;
    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalBuildPaymentScheduleInputValidator(EntityLinkResolverRegistry entityLinkResolverRegistry, CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalBuildPaymentScheduleInput.class);
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalBuildPaymentScheduleInput input) {
        return Streamable.from(input.getSettlements())
                .flatMap(settlementLink ->Streamable.concat(validateSettlement(settlementLink, input.getOriginSource())
                ,validateOriginSource(input.getOriginSource())));
    }

    private Streamable<ErrorHolder> validateOriginSource(EntityLink<RootEntity> originSource) {
        return Optional.ofNullable(originSource)
                .map(origin -> capDentalLinkValidator.validateLink(origin, "CapLoss", ORIGIN_SOURCE_URI_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    /**
     * Validates settlement link. If link is valid, loads settlement and checks whether it has
     * provided origin source, Approved state, and proposal PAY.
     *
     * @param settlementLink link of a settlement to validate
     * @param originSource origin source to check whether it is set in settlement
     *
     * @return observable with error messages if validation fails, empty observable otherwise
     */
    private Streamable<ErrorHolder> validateSettlement(EntityLink<RootEntity> settlementLink, EntityLink<RootEntity> originSource) {
        if (!isSettlementLinkValid(settlementLink)) {
            return Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build());
        }
        return loadSettlement(settlementLink)
            .flatMapMany(settlement -> {
                    if (!(settlement instanceof CapDentalSettlementEntity)) {
                        return Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build());
                    }
                    return validateSettlementData((CapDentalSettlementEntity) settlement, settlementLink, originSource);
                })
            .onErrorResume(e -> Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build()));
    }

    private boolean isSettlementLinkValid(EntityLink<RootEntity> settlementLink) {
        try {
            return new FactoryLink(settlementLink).getTypeName().equals("CapSettlement");
        } catch (JsonValueFormatError e) {
            return false;
        }
    }

    private Streamable<ErrorHolder> validateSettlementData(CapDentalSettlementEntity settlement,
            EntityLink<RootEntity> settlementLink, EntityLink<RootEntity> originSource) {

        List<ErrorHolder> messages = new ArrayList<>();

        if (!originSource.getURIString().equals(settlement.getClaimLossIdentification().getURIString())) {
            messages.add(OTHER_SETTLEMENT_ORIGIN_SOURCE.builder().params(settlementLink.getURIString()).build());
        }
        if (!STATE_APPROVED.equals(settlement.getState())) {
            messages.add(SETTLEMENT_NOT_APPROVED.builder().params(settlementLink.getURIString()).build());
        }
        if (settlement.getSettlementResult() == null || !PROPOSAL_PAY.equals(settlement.getSettlementResult().getProposal())) {
            messages.add(PROPOSAL_NOT_PAY.builder().params(settlementLink.getURIString()).build());
        }
        return Streamable.from(messages);
    }

    private Lazy<RootEntity> loadSettlement(EntityLink<RootEntity> settlementLink) {
        return entityLinkResolverRegistry.getByURIScheme(settlementLink.getSchema())
                .resolve(settlementLink, ReadContext.empty());
    }

    public static class CapDentalBuildPaymentScheduleInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalBuildPaymentScheduleInputValidatorErrorDefinition OTHER_SETTLEMENT_ORIGIN_SOURCE = new CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(
                "dbps-001", "Settlement is from a different origin source: {0}");
        public static final CapDentalBuildPaymentScheduleInputValidatorErrorDefinition SETTLEMENT_NOT_APPROVED = new CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(
                "dbps-002", "Cannot resolve financial data for settlement {0}. Settlement state is not 'Approved'.");
        public static final CapDentalBuildPaymentScheduleInputValidatorErrorDefinition PROPOSAL_NOT_PAY = new CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(
                "dbps-003", "Cannot resolve financial data for settlement {0}. Settlement proposal is not 'PAY'.");
        public static final CapDentalBuildPaymentScheduleInputValidatorErrorDefinition SETTLEMENT_URI_INCORRECT = new CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(
                "dbps-004", "settlement {0} URI is not valid.");
        public static final CapDentalBuildPaymentScheduleInputValidatorErrorDefinition ORIGIN_SOURCE_URI_INCORRECT = new CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(
                "dbps-005", "originSource URI is not valid");

        protected CapDentalBuildPaymentScheduleInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
