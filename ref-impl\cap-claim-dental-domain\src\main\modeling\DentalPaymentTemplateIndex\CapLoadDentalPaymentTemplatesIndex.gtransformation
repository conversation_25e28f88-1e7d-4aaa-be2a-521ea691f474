Transformation CapLoadDentalPaymentTemplatesIndex {
    Input {
        ? as originSourceKeyFilter
    }
    Output {
        *CapDentalPaymentTemplateIndex.CapDentalPaymentTemplateIdxEntity as out
    }

    Mapping out is Load(originSourceKeyFilter, "CapDentalPaymentTemplateIndex", "CapDentalPaymentTemplateIdxEntity") {
        Attr originSource is dentalLoss
        Attr paymentTemplate is paymentTemplate
        Attr state is state
    }

}