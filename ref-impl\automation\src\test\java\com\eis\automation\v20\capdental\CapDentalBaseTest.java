/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental;

import com.eis.automation.tzappa_v20.BaseTest;
import org.springframework.test.context.ContextConfiguration;

@ContextConfiguration(
        locations = {"classpath:platform-v20-beans.xml", "classpath:gravitzappa-v20-beans.xml"})
public class CapDentalBaseTest extends BaseTest {
}
