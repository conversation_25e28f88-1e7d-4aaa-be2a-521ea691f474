/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentApproveHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import javax.annotation.Nonnull;

/**
 * Command handler for approving a {@link CapDentalPaymentEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentApproveHandler extends CapPaymentApproveHandler<IdentifierRequest, CapDentalPaymentEntity> {

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull IdentifierRequest request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return Streamable.empty();
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull IdentifierRequest identifierRequest, @Nonnull CapDentalPaymentEntity entity) {
        return Lazy.of(entity).get();
    }
}
