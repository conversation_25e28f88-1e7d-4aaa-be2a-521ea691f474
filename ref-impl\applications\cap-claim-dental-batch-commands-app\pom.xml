<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ms-claim-dental-applications-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-batch-commands-app</artifactId>

    <profiles>
        <profile>
            <activation>
                <property>
                    <name>
                        naio
                    </name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-dependency-plugin</artifactId>
                        <executions>
                            <execution>
                                <id>dependency-list</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>list</goal>
                                </goals>
                                <configuration>
                                    <outputFile>target/classes/dependency.list</outputFile>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <goals>
                                    <goal>repackage</goal>
                                </goals>
                            </execution>
                        </executions>
                        <configuration>
                            <classifier>fat</classifier>
                            <attach>false</attach>
                            <mainClass>com.eisgroup.genesis.boot.Bootstrap</mainClass>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>com.eisgroup.genesis.tools</groupId>
                        <artifactId>fintrospector-maven-plugin</artifactId>
                        <executions>
                            <execution>
                                <configuration>
                                    <introspectors>
                                        <introspector>
                                            <groupId>com.eisgroup.genesis.lifecycle</groupId>
                                            <artifactId>lifecycle-fintrospector</artifactId>
                                            <version>${lifecycle.framework.version}</version>
                                        </introspector>
                                    </introspectors>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- redefined for ordering purposes,
                        have to be executed after spring boot package plugin -->
                    <plugin>
                        <groupId>com.spotify</groupId>
                        <artifactId>dockerfile-maven-plugin</artifactId>
                        <version>${dockerfile-maven-plugin.version}</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-batch-commands</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.jobs</groupId>
            <artifactId>jobs-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>

        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis</groupId>
            <artifactId>timeshifter</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>
    </dependencies>

</project>