//transformation which is responsible to convert individual dental policy information to Dental Loss policy header
Transformation DNIndividualToCapDentalLoss {
    Input {
        Ext CapPolicyHolder.CapPolicyEntity as policy
    }
    Output {
        CapDentalLoss.CapDentalLossPolicyEntity
    }

    Attr policyNumber is policy.policyNumber
    Attr isVerified is true
    Attr policyType is policy.policyType
    Attr term is produceTerm(policy.termDetails)
    Attr policyStatus is policy.state
    Attr txEffectiveDate is policy.transactionDetails.txEffectiveDate
    Attr riskStateCd is policy.riskStateCd
    Attr capPolicyId is policy.capPolicyId
    Attr productCd is policy.productCd
    Attr capPolicyVersionId is policy.capPolicyVersionId
    Attr plan is policy.individualPackagingDetail.plan.planCd
    Attr planName is policy.individualPackagingDetail.plan.planName

    Producer produceTerm(termDetails) {
        Attr effectiveDate is termDetails.termEffectiveDate
        Attr expirationDate is termDetails.termExpirationDate
    }

}