<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="dentalFireEventCloseSettlementLossOnCompleteSchedule" name="Dental Close Settlement Loss on Complete Schedule" isExecutable="true">
    <startEvent id="completePaymentScheduleEventReceived" name="On complete Payment schedule">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentSchedule_completePaymentSchedule]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <serviceTask id="findSettlementLossEndpointCall" name="Find settlement and loss for schedule" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('capDentalFindSettlementForSchedule').serviceGroup('CapDentalPaymentSchedule').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{body:{"_key" : ${execution.getVariable('_key')}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[scheduleData]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <callActivity id="callCloseSettlementSubProcess" name="Call close settlement subProcess" calledElement="closeSettlement" flowable:calledElementType="key" flowable:fallbackToDefaultTenant="false">
      <extensionElements>
        <flowable:in sourceExpression="${scheduleData.body.success.settlement._key}" target="settlementKey"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.settlement._modelName}" target="settlementModelName"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.settlement._modelVersion}" target="settlementModelVersion"></flowable:in>
      </extensionElements>
    </callActivity>
    <callActivity id="callRequestCloseLossSubProc" name="Call request close Losss subProcess" calledElement="requestCloseLossCall" flowable:calledElementType="key" flowable:fallbackToDefaultTenant="false">
      <extensionElements>
        <flowable:in sourceExpression="Paid" target="reasonCd"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.lossUri._uri}" target="_uri"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.loss._key}" target="_key"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.loss._modelName}" target="_modelName"></flowable:in>
        <flowable:in sourceExpression="${scheduleData.body.success.loss._modelVersion}" target="_modelVersion"></flowable:in>
      </extensionElements>
    </callActivity>
    <sequenceFlow id="sid-8B164DCF-0E0A-487B-8B86-0B9AEE6C9088" sourceRef="completePaymentScheduleEventReceived" targetRef="findSettlementLossEndpointCall"></sequenceFlow>
    <sequenceFlow id="sid-5D89EFBD-FBD0-442C-8DE0-7A6B66D97044" sourceRef="findSettlementLossEndpointCall" targetRef="callCloseSettlementSubProcess"></sequenceFlow>
    <sequenceFlow id="sid-B3D208D7-4CAE-47EA-991D-3320462B4980" sourceRef="callCloseSettlementSubProcess" targetRef="callRequestCloseLossSubProc"></sequenceFlow>
    <endEvent id="sid-B53BC7F8-8DB4-44DE-8E8B-95E0E0C7CF42"></endEvent>
    <sequenceFlow id="sid-37023A89-CFAF-44ED-9598-903EFB1B6875" sourceRef="callRequestCloseLossSubProc" targetRef="sid-B53BC7F8-8DB4-44DE-8E8B-95E0E0C7CF42"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_dentalFireEventCloseSettlementLossOnCompleteSchedule">
    <bpmndi:BPMNPlane bpmnElement="dentalFireEventCloseSettlementLossOnCompleteSchedule" id="BPMNPlane_dentalFireEventCloseSettlementLossOnCompleteSchedule">
      <bpmndi:BPMNShape bpmnElement="completePaymentScheduleEventReceived" id="BPMNShape_completePaymentScheduleEventReceived">
        <omgdc:Bounds height="30.0" width="30.5" x="192.25" y="235.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="findSettlementLossEndpointCall" id="BPMNShape_findSettlementLossEndpointCall">
        <omgdc:Bounds height="80.0" width="100.0" x="350.5" y="210.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="callCloseSettlementSubProcess" id="BPMNShape_callCloseSettlementSubProcess">
        <omgdc:Bounds height="80.0" width="100.0" x="595.5" y="210.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="callRequestCloseLossSubProc" id="BPMNShape_callRequestCloseLossSubProc">
        <omgdc:Bounds height="80.0" width="100.0" x="800.5" y="210.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B53BC7F8-8DB4-44DE-8E8B-95E0E0C7CF42" id="BPMNShape_sid-B53BC7F8-8DB4-44DE-8E8B-95E0E0C7CF42">
        <omgdc:Bounds height="28.0" width="28.0" x="1054.5" y="236.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-B3D208D7-4CAE-47EA-991D-3320462B4980" id="BPMNEdge_sid-B3D208D7-4CAE-47EA-991D-3320462B4980" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="695.4499999998857" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="800.4999999999557" y="250.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-37023A89-CFAF-44ED-9598-903EFB1B6875" id="BPMNEdge_sid-37023A89-CFAF-44ED-9598-903EFB1B6875" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="900.4499999999213" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="1054.5" y="250.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5D89EFBD-FBD0-442C-8DE0-7A6B66D97044" id="BPMNEdge_sid-5D89EFBD-FBD0-442C-8DE0-7A6B66D97044" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="450.45000000000005" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="595.5" y="250.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8B164DCF-0E0A-487B-8B86-0B9AEE6C9088" id="BPMNEdge_sid-8B164DCF-0E0A-487B-8B86-0B9AEE6C9088" flowable:sourceDockerX="15.25" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="222.69999948424888" y="250.0"></omgdi:waypoint>
        <omgdi:waypoint x="350.4999999999716" y="250.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>