/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalBypassClaimLogicModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalBypassClaimLogicModel extends TypeModel implements ICapDentalBypassClaimLogicModel {

    private Boolean bypassOverpaymentLogic;
    private Boolean bypassCobLogic;
    private Boolean bypassDuplicateServiceLogic;
    private Boolean bypassToothExtractionRules;
    private Boolean bypassGracePeriodLogic;
    private Boolean bypassClinicalReview;
    private Boolean bypassMissingToothLogic;
    private Boolean bypassInterestLogic;
    private Boolean bypassAllRules;

    public Boolean getBypassOverpaymentLogic() {
        return bypassOverpaymentLogic;
    }

    public void setBypassOverpaymentLogic(Boolean bypassOverpaymentLogic) {
        this.bypassOverpaymentLogic = bypassOverpaymentLogic;
    }

    public Boolean getBypassCobLogic() {
        return bypassCobLogic;
    }

    public void setBypassCobLogic(Boolean bypassCobLogic) {
        this.bypassCobLogic = bypassCobLogic;
    }

    public Boolean getBypassDuplicateServiceLogic() {
        return bypassDuplicateServiceLogic;
    }

    public void setBypassDuplicateServiceLogic(Boolean bypassDuplicateServiceLogic) {
        this.bypassDuplicateServiceLogic = bypassDuplicateServiceLogic;
    }

    public Boolean getBypassToothExtractionRules() {
        return bypassToothExtractionRules;
    }

    public void setBypassToothExtractionRules(Boolean bypassToothExtractionRules) {
        this.bypassToothExtractionRules = bypassToothExtractionRules;
    }

    public Boolean getBypassGracePeriodLogic() {
        return bypassGracePeriodLogic;
    }

    public void setBypassGracePeriodLogic(Boolean bypassGracePeriodLogic) {
        this.bypassGracePeriodLogic = bypassGracePeriodLogic;
    }

    public Boolean getBypassClinicalReview() {
        return bypassClinicalReview;
    }

    public void setBypassClinicalReview(Boolean bypassClinicalReview) {
        this.bypassClinicalReview = bypassClinicalReview;
    }

    public Boolean getBypassMissingToothLogic() {
        return bypassMissingToothLogic;
    }

    public void setBypassMissingToothLogic(Boolean bypassMissingToothLogic) {
        this.bypassMissingToothLogic = bypassMissingToothLogic;
    }

    public Boolean getBypassInterestLogic() {
        return bypassInterestLogic;
    }

    public void setBypassInterestLogic(Boolean bypassInterestLogic) {
        this.bypassInterestLogic = bypassInterestLogic;
    }

    public Boolean getBypassAllRules() {
        return bypassAllRules;
    }

    public void setBypassAllRules(Boolean bypassAllRules) {
        this.bypassAllRules = bypassAllRules;
    }
}