// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_DENTALINTERNAL } from "./kraken_model_tree_DentalInternal"

let name = "DentalInternal"

let namespace = "DentalInternal"

let currencyCd = "USD"

export type DentalInternalEntryPointName = never

let entryPointNames = [
] as DentalInternalEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_DENTALINTERNAL as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_DentalInternal = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
