@Library("ci-config@master")
@Library("ci-library@master")
@Library("cd-library@master")
@Library("automation-library@master") _
import ReleaseAssembly

def RELEASE_VERSION
def RELEASE_TYPE
def ENVIRONMENTS = k8s.environments()
def SLACK_THREAD
def SLACK_PRE_RELEASE_THREAD
def PUBLISH_RELEASE_NOTES

pipeline {
    environment {
        COMPONENT = "dxp-claim-benefits-dental"
    }

    parameters {
        gitParameter(name: 'SOURCE_BRANCH', branchFilter: 'origin/(.*)', type: 'PT_BRANCH', sortMode: 'ASCENDING_SMART', defaultValue: 'master',
                listSize: '1', quickFilterEnabled: true, description: '<b style="color:red">Required:</b> SCM branch to create release from, e.g. "release-23.16"')
        string(name: 'RELEASE_VERSION', defaultValue: '', description: '<b style="color:gray">Optional:</b>  Artifact version to publish, e.g. "23.16-RC1"<tr/><i style="color:gray">Default behaviour: Creates staging version using current timestamp.</i>')
        choice(name: 'ENVIRONMENT', choices: ENVIRONMENTS, description: 'Environment name to deploy component and run etcs tests')
        booleanParam(name: 'SKIP_DEPLOY', defaultValue: false, description: 'Set to true if deployment must be skipped')
        //Logic is missing, param should be commented out
        //booleanParam(name: 'REUPLOAD', defaultValue: false, description: '<b style="color:gray">Optional:</b> Whether to allow artifact to be re-uploaded')
    }

    options {
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        lock resource: env.JOB_NAME + '-' + params.SOURCE_BRANCH
        timeout(time: 3, unit: 'HOURS')
    }

    agent {
        kubernetes {
            inheritFrom 'default'
            defaultContainer 'sbt'
            yaml new PodTemplateGenerator(this)
                    .kind(templateKind: 'sbt21')
                    //.withParams(container: 'sbt', params: [ephemeralStorageRequests: '18.5Gi', ephemeralStorageLimits: '18.5Gi', memoryRequests: '18.5Gi', memoryLimits: '18.5Gi'])
                    .generate()
        }
    }

    stages {
        stage('Pre-Release check') {
            steps {
                script {
                    try {
                        if (preReleaseCheck.isHotfixVersion(params.RELEASE_VERSION)) {
                            SLACK_PRE_RELEASE_THREAD = slack.createThread(channel: k8s.getSlackChannel(params.ENVIRONMENT), message: "Pre-release check for $COMPONENT component started for version: ${params.RELEASE_VERSION}...")
                            if (SLACK_PRE_RELEASE_THREAD == null) {
                                error("Failed to create Slack thread.")
                            }

                            withVault(vaultSecrets: [
                                [path: config.VAULT_PATH_TO_DEFAULT_COMMON_BUILD_SECRET,
                                 secretValues: [[envVar: 'JIRA_RELEASE_NOTES_TOKEN', vaultKey: 'jira_release_notes_token']]
                                ]
                            ]) {
                                preReleaseCheck.runPreReleaseCheck(
                                    jiraToken: env.JIRA_RELEASE_NOTES_TOKEN,
                                    releaseNotesVersion: params.RELEASE_VERSION,
                                    component: preReleaseCheck.getJiraComponent(component: env.COMPONENT)
                                )
                            }

                            PUBLISH_RELEASE_NOTES = true
                            slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_PRE_RELEASE_THREAD, status: 'success')

                        } else {
                            println "Skipping the pre-release check."
                            PUBLISH_RELEASE_NOTES = false
                        }

                    } catch (Exception e) {
                        echo "[Error]: ${STAGE_NAME}: ${e.message}"
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_PRE_RELEASE_THREAD, status: 'failed')
                        error(message: "${STAGE_NAME} is failed")
                    }
                }
            }
        }           
        stage('Release and build') {
            steps {
                script {
                    try {
                        dir('dxp') {
                        RELEASE_VERSION = (params.RELEASE_VERSION == null || params.RELEASE_VERSION.isEmpty()
                                ? dxpProjectVersion.full(project: './') + "-" + generateVersionPostfix('')
                                : params.RELEASE_VERSION)
                        RELEASE_TYPE = versions.getReleaseType(RELEASE_VERSION)

                        currentBuild.displayName = "#${BUILD_NUMBER} Release ${RELEASE_VERSION} from ${SOURCE_BRANCH}"

                        println '######Checking version in version.sbt file and checkout to required branch######'

                        sh "git checkout ${SOURCE_BRANCH}"

                        def currentVersion = (readFile('version.sbt') =~ /ThisBuild \/ version := "(.+?)"/)[0][1]

                        // building command with version to build and version from version.sbt file, command pushes artifacts to nexus
                        sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR -Dsbt.log.noformat=true \"release with-defaults default-tag-exists-answer o release-version $RELEASE_VERSION next-version $currentVersion \""
                        // backuping file and replacing content with build version form job, required to build files for docker image
                        sh "mv version.sbt version.bkp"
                        sh "echo 'ThisBuild / version := \"$RELEASE_VERSION\"' > $workspace/version.sbt"
                        // building files for docker image, command is for dxp
                        sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR dist"
                        // reverting version.sbt
                        sh "mv -f version.bkp version.sbt"
                        }
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch(Exception e) {
                        echo "[WARN] ${STAGE_NAME}: ${e.message}"
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_HREAD, status: 'failed')
                        error(message: "${STAGE_NAME} is failed")
                    }
                }
            }
        }

        stage('Build image') {
            steps {
                script {
                    try {
                        image.build(dockerfile: 'dxp/', name: 'dxp-claim-benefits-dental-app', tag: RELEASE_VERSION, registry: RELEASE_TYPE, push: true)
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch(Exception e) {
                        echo "[WARN] ${STAGE_NAME}: ${e.message}"
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_HREAD, status: 'failed')
                        error(message: "${STAGE_NAME} is failed")
                    }    
                }
            }
        }

        stage('Push Git tag and Update RA') {
            steps {
                script {
                    try {
                        def revision = gitlib.getCommitHash(tag: RELEASE_VERSION)

                        new ReleaseAssembly(this).publish(
                            name: "dxp-claim-benefits-dental",
                            version: RELEASE_VERSION,
                            branch: SOURCE_BRANCH,
                            imageTag: RELEASE_VERSION,
                            revsion: revision,
                            images: ["dxp-claim-benefits-dental-app"]
                        )
                        //fix for dxp api as it pushing ./ folder not ref-impl
                        sh "rm -rf release-assembly"
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch(Exception e) {
                        echo "[WARN] ${STAGE_NAME}: ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        }

/*         stage('Send email to genesis engineering') {
            steps {
                script {
                    try {
                        if (RELEASE_TYPE == "release" || RELEASE_TYPE == "candidate") {
                            mail to: config.RELEASE_SUCCESS_MAIL_NOTIFICATION,
                            subject: "$COMPONENT $RELEASE_VERSION released",
                            mimeType: "text/html",
                            body: "Please be notified that <b>$COMPONENT $RELEASE_VERSION</b> has been released."

                            slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                        }

                    } catch (Exception e) {
                        echo "[WARN] ${STAGE_NAME}: ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        }
 */
        stage('Deploy') {
            when {
                expression {
                    (!params.SKIP_DEPLOY)
                }
            }
            steps {
                script {
                    try {
                        build(
                            job: "${env.COMPONENT}-deploy",
                            parameters: [
                                string(name: 'BRANCH', value: SOURCE_BRANCH),
                                string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                string(name: 'VERSION', value: RELEASE_VERSION),
                                booleanParam(name: 'CLEAN', value: true)
                            ],
                            propagate: true,
                            wait: true
                        )
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch (Exception e) {
                        echo "[WARN]: ${STAGE_NAME} ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        }

/*         stage('Publish UI models') {
            when {
                expression {
                    (!params.SKIP_DEPLOY)
                }
            }
            steps {
                script {
                    try {
                        def publishUiModelsJob = build(
                            job: 'publish-ui-models',
                            parameters: [
                                booleanParam(name: 'SKIP_PUBLISH', value: false),
                                string(name: 'MODELS', value: 'base-cssr-app'),
                                string(name: 'BUILD_BRANCH', value: SOURCE_BRANCH),
                                booleanParam(name: 'CollectPublishedUiModelsInfo', value: true),
                                booleanParam(name: 'UPDATE_MODELS', value: true),
				                string(name: 'UPDATE_ENV_JS', value: params.ENVIRONMENT),
                                string(name: 'VERSION', value: versions.appendVersionForPublishUiModels(RELEASE_VERSION)),
                                string(name: 'BACKEND_VERSION', value: RELEASE_VERSION),
                            ],
                            propagate: true,
                            wait: true
                        )

                        if (publishUiModelsJob.getResult() == 'SUCCESS') {
                            copyArtifacts(
                                projectName: 'publish-ui-models',
                                selector: specific(publishUiModelsJob.getNumber().toString())
                            )

                            publishedUiModels = readYaml(file: "published-ui-models.yaml")
                            echo("[INFO] The following models were successfully published: ${publishedUiModels}")
                            slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                        } else {
                            echo("[WARN] The publish-ui-models job did not succeed.")
                            unstable(message: "${STAGE_NAME} is unstable")
                            slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                        }
                        //fix for dxp api as it pushing ./ folder not ref-impl
                        sh "rm -rf published-ui-models.yaml"

                    } catch (Exception err) {
                        println "[ERROR]: Failed to trigger publish-ui-models job: ${err.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                    }
                }
            }
        } */

        stage('Upload ref-impl') {
		    when {
                expression {
                    (RELEASE_TYPE == "release")
                }
            }
            steps {
                script {
                    try {
                        uploadRefImpl('sbt', env.COMPONENT, RELEASE_VERSION)
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch (Exception e) {
                        echo("[WARN]: ${STAGE_NAME} not succeed ${e.message}")
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        }
        
/*         stage('Publish Doc API Schemas') {
		    when {
                expression {
                    (!params.SKIP_DEPLOY && RELEASE_TYPE == "release")
                }
            }
            steps {
                script {
                    try {
                        build(
                            job: 'api-dochub-publish-schemas',
                            parameters: [
                                string(name: 'COMPONENT', value: "${COMPONENT}"),
                                string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                string(name: 'VERSION', value: RELEASE_VERSION),
                                string(name: 'RELEASE_TYPE', value: RELEASE_TYPE)
                            ],
                            propagate: true,
                            wait: true
                        )

                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch (Exception e) {
                        echo("[WARN]: ${STAGE_NAME} not succeed ${e.message}")
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        } */

/*         stage('Run ETCS') {
            when {
                expression {
                    (!params.SKIP_DEPLOY)
                }
            }
            steps {
                script {
                    automationLauncher.setEtcsJob("${env.COMPONENT}-etcs")
                    automationLauncher.setTestProfiles(['SMOKE', 'REGRESSION'])
                    automationLauncher.setEnvSlackThread(SLACK_THREAD)
                    automationLauncher.setReleaseVersion(RELEASE_VERSION)
                    automationLauncher()
                }
            }
        } */

/*         stage('Publish Release Notes') {
		    when {
                expression {
                     ( PUBLISH_RELEASE_NOTES == true )
                }
            }
            steps {
                script {
                    try {
                        build(
                            job: 'publish-release-notes',
                            parameters: [
                                string(name: 'COMPONENT', value: "${COMPONENT}" ),
                                string(name: 'RELEASE_VERSION', value: params.RELEASE_VERSION),
                                string(name: 'PUBLISH_TYPE', value: "release")
                            ],
                            propagate: true,
                            wait: true
                        )

                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'success')
                    } catch (Exception e) {
                        echo "[WARN] ${STAGE_NAME}: ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                        slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD, status: 'unstable')
                    }
                }
            }
        } */
    }

    post {
        failure {
            script {
                slack.updateStage(channel: k8s.getSlackChannel(params.ENVIRONMENT), thread: SLACK_THREAD ?: SLACK_PRE_RELEASE_THREAD, status: 'failed')
            }
        }
        always {
            cleanWs()
        }
    }
}