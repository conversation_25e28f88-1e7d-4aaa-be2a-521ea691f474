/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade;

import com.eis.automation.tzappa.rest.modeling.IModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewBuildModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public interface ICapDentalPaymentSchedule {
    PostModelAction<ICapDentalPaymentSchedulePreviewBuildModel, ICapDentalPaymentSchedulePreviewModel> preview();
}
