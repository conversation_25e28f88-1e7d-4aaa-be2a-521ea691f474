//EISDEVTS-40063
@StateMachine
Entity CapDentalPaymentTemplateEntity is CapPaymentTemplate {

    //EISDEVTS-40063
    @Description("URI to related Claim.")
    @Searchable
    ExtLink originSource: RootEntity

    //EISDEVTS-45698
    @Description("Object for payment template details.")
    Ref paymentDetailsTemplate: CapDentalPaymentDetailsTemplateEntity

    //GENESIS-182571
    @Description("Payment Template state in the lifecycle.")
    @Searchable
    Attr state: String
}
//EISDEVTS-40063, EISDEVTS-45698
@Description("Object for payment template details.")
Entity CapDentalPaymentDetailsTemplateEntity is CapPaymentDetailsTemplate {

    //EISDEVTS-45698
    @Description("Object for payment template allocations details.")
    Attr paymentAllocationTemplates: *CapDentalPaymentAllocationTemplateEntity
}
//EISDEVTS-40063, EISDEVTS-45698
@Description("Object for payment template allocation details.")
Entity CapDentalPaymentAllocationTemplateEntity is CapPaymentAllocationTemplate {

    //EISDEVTS-40063, EISDEVTS-45698
    @Description("Object for payment template allocation dental lob details.")
    Attr allocationDentalDetails: CapDentalPaymentAllocationTemplateDentalDetailsEntity

    //EISDEVTS-40063, EISDEVTS-45698
    @Description("Allocation Line Of Business.")
    Attr allocationLobCd: String

    //EISDEVTS-45698
    @Description("Object for payment template allocation loss details")
    Attr allocationLossInfo: CapDentalPaymentAllocationTemplateLossInfoEntity

    //EISDEVTS-40063, EISDEVTS-45698
    @Description("Object for payment template allocation payee details.")
    Attr allocationPayeeDetails: CapDentalPaymentAllocationTemplatePayeeDetailsEntity
}
//EISDEVTS-40063, EISDEVTS-45698
@Description("Object for template allocation payee details.")
Entity CapDentalPaymentAllocationTemplatePayeeDetailsEntity is CapPaymentAllocationTemplatePayeeDetails {

    //EISDEVTS-40063, EISDEVTS-45698
    @Description("Allocation payee type.")
    Attr payeeTypeCd: String
}
//EISDEVTS-45698
@Description("Object for template allocation loss details.")
Entity CapDentalPaymentAllocationTemplateLossInfoEntity is CapPaymentAllocationTemplateLossInfo {

}
//EISDEVTS-44919
Entity CapDentalStartBuildPaymentTemplateFlowOutput {

    //EISDEVTS-44919
    @Description("Attribute requred for Automated Build Payment Template Flow.")
    ExtLink originSource: RootEntity

    //EISDEVTS-44919
    @Description("Attribute requred for Automated Build Payment Template Flow.")
    ExtLink settlements: *RootEntity
}
//EISDEVTS-45698
@Description("Object for Payment Scheduling rules input.")
Entity CapDentalPaymentTemplateSchedulerRulesInputEntity {

    //EISDEVTS-45698
    @Description("Payment Scheduling rules input.")
    Attr paymentTemplate: CapDentalPaymentTemplateEntity
}
//EISDEVTS-40063, EISDEVTS-45698
@Description("Object for dental details for allocation.")
Entity CapDentalPaymentAllocationTemplateDentalDetailsEntity {

    //EISDEVTS-40079
    @Description("Object for payment template allocation accumulator details.")
    Attr accumulatorDetails: *CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity

    //EISDEVTS-45698
    @Description("Date of service.")
    Attr dateOfService: Date

    //EISDEVTS-40055
    @Description("Gross amount calculated during claim adjudication process.")
    Attr grossBenefitAmount: Money

    //EISDEVTS-45698
    @Description("Payment frequency for Ortho payments.")
    @Lookup("CapDNFrequency")
    Attr orthoFrequencyCd: String

    //EISDEVTS-45698
    @Description("Number of Months of Treatment.")
    Attr orthoMonthQuantity: Integer

    //EISDEVTS-40079
    @Description("URI to the patient.")
    ExtLink patient: RootEntity

    //EISDEVTS-45698
    @Deprecated
    @Description("NOT USED")
    Attr paymentAmount: Money

    //EISDEVTS-40055
    @Description("Related Settlement Result entry's procedure ID.")
    Attr procedureID: String

    //EISDEVTS-45698
    @Description("The date the claim is received.")
    Attr receivedDate: Date

    //EISDEVTS-40063, EISDEVTS-45698
    @Description("Dental claim transaction type.")
    Attr transactionTypeCd: String
}
//EISDEVTS-40079
@Description("Object for accumulator details for allocation.")
Entity CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity {

    //EISDEVTS-40055
    @Description("Defines accumulator amount.")
    Attr accumulatorAmount: Money

    //EISDEVTS-40079
    @Description("Accumulator type.")
    Attr accumulatorType: String

    //EISDEVTS-40079
    @Description("Defines to which procedure category accumulator applies to.")
    @Deprecated
    Attr appliesToProcedureCategory: String

    //EISDEVTS-40079
    @Description("Network type for accumulator.")
    Attr networkType: String

    //EISDEVTS-40079
    @Description("Renewal type for accumulator.")
    Attr renewalType: String

    @Description("Defines to which procedure categories accumulator applies to.")
    Attr appliesToProcedureCategories: *String

    @Description("Accumulator Term.")
    @NonComparable
    Attr term: Term
}