/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input;

import com.eisgroup.genesis.commands.request.EntityCarryingRequest;
import com.eisgroup.genesis.commands.request.PartialRequest;
import com.eisgroup.genesis.common.Required;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementDetailEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Optional;

/**
 * Input for readjudication lifecycle action
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalSettlementReadjudicateInput extends AbstractJsonEntity implements EntityCarryingRequest, PartialRequest {

    public CapDentalSettlementReadjudicateInput(JsonObject original) {
        super(original);
    }

    @Override
    @Required
    public CapDentalSettlementDetailEntity getEntity() {
        return Optional.ofNullable(getRawChild("entity"))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(ModelInstanceFactory::createInstance)
                .map(CapDentalSettlementDetailEntity.class::cast)
                .orElse(null);
    }

    @Required
    public EntityLink<RootEntity> getClaimLossIdentification() {
        return Optional.ofNullable(getRawChild("claimLossIdentification"))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(claimLossIdentification -> new EntityLink<>(RootEntity.class, claimLossIdentification))
                .orElse(null);
    }
}
