<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-facade</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-lifecycle-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.facade</groupId>
            <artifactId>facade-model-api</artifactId>
        </dependency>

        <!-- Libraries -->
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>column-store-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>search-index-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <classifier>producer</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.security</groupId>
            <artifactId>security-bundle</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.eisgroup.genesis.facade</groupId>-->
<!--            <artifactId>lifecycle-facade</artifactId>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.eisgroup.genesis.facade</groupId>-->
<!--            <artifactId>entity-facade</artifactId>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.confidential-data</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.search</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.kraken</groupId>
            <artifactId>kraken-repository-facade</artifactId>
        </dependency>

        <!-- Loss -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.loss</groupId>
            <artifactId>cap-loss-facade</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.versioning</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-versioning-facade</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.discovery</groupId>
            <artifactId>service-discovery-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <!--- Testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.utils</groupId>
            <artifactId>testing-utils</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>