/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.accumulator.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.interf.ICapPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.ICapAccumulator;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorLoadModel;
import com.eis.automation.v20.capdental.entity.accumulator.service.ICapAccumulatorService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

import static com.eis.automation.tzappa.rest.modeling.utils.ModelRetryPredicate.success;

@Lazy
@Component("loadAccumulator")
public class CapAccumulatorService implements ICapAccumulatorService {
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    @Autowired
    private ICapAccumulator capAccumulatorContainer;
    private TestData tdAccumulatorLocation;
    private final String policyUri = "capPolicy://CapUP/";

    public TestData getTestData(String... strings) {
        return tdAccumulatorLocation.getTestData(strings);
    }

    public CapAccumulatorService(ICapAccumulator capAccumulatorContainer) {
        this.capAccumulatorContainer = capAccumulatorContainer;
    }

    public ICapAccumulator getFacade() {
        return capAccumulatorContainer;
    }

    @PostConstruct
    private void testDataResolver() {
        tdAccumulatorLocation = testDataProvider.getJSONTestData("/capdental/accumulator");
    }

    public ICapAccumulatorLoadModel createCapAccumulatorLoadModel(ICustomerModel customerModel, ICapPolicyInfoModel capPolicyModel) {
        ICapAccumulatorLoadModel loadCapAccumulator = modelUtils.create(getTestData("Write", "TestData"));
        String capPolicyId = policyUri + capPolicyModel.getGerootUri().getUri();
        loadCapAccumulator.setCustomerURI(customerModel.getGerootUri().getUri());
        loadCapAccumulator.setPolicyURI(capPolicyId);
        return loadCapAccumulator;
    }

    public List<ICapAccumulatorContainerModel> loadAccumulator(ICustomerModel customerModel, ICapPolicyInfoModel capPolicyModel) {
        return getFacade().load().performNoSuccessAnalysis(b -> b.setModel(createCapAccumulatorLoadModel(customerModel, capPolicyModel)), success()).safeGetResponseBody();
    }
}