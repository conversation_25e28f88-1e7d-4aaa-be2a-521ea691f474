/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapFinancialDataResolver;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPreviewPaymentScheduleInput;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity;
import com.eisgroup.genesis.factory.model.dentalinternal.CapDentalFinancialDataInput;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentTemplate;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

/**
 * Default {@link CapFinancialDataResolver} implementation for preview
 * Uses {@link ModeledTransformationService} to resolve financial data and create {@link CapPaymentTemplate}
 *
 * <AUTHOR>
 * @since 22.16
 */
public class DefaultCapDentalFinancialDataPreviewResolver implements CapFinancialDataResolver<CapDentalPreviewPaymentScheduleInput, CapDentalPaymentTemplateEntity> {

    private static final String TEMPLATE_FINANCIAL_DATA = "CapTemplateFinancialData";

    private final ModeledTransformationService modeledTransformationService;
    private final ModelRepository<TransformationModel> transformationRepository;

    public DefaultCapDentalFinancialDataPreviewResolver(ModeledTransformationService modeledTransformationService,
                                                        ModelRepository<TransformationModel> transformationRepository) {
        this.modeledTransformationService = modeledTransformationService;
        this.transformationRepository = transformationRepository;
    }

    @Override
    public Lazy<CapDentalPaymentTemplateEntity> resolveTemplateFinancialData(CapDentalPreviewPaymentScheduleInput request) {
        CapDentalFinancialDataInput financialDataInput = (CapDentalFinancialDataInput) ModelInstanceFactory.createInstance("DentalInternal", "1", "CapDentalFinancialDataInput");
        financialDataInput.setOriginSource(request.getOriginSource());
        financialDataInput.setSettlements(request.getSettlements());
        return Lazy.of(modeledTransformationService.transform(transformationRepository.getActiveModel(TEMPLATE_FINANCIAL_DATA), financialDataInput));
    }
}
