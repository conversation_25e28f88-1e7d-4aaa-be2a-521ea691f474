/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalAccumulatorModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalAccumulatorModel extends TypeModel implements ICapDentalAccumulatorModel {

    private Money remainingAmount;
    private Money reservedAmount;
    private String accumulatorType;
    private String renewalType;
    private String appliesToProcedureCategory;
    private String networkType;

    public Money getRemainingAmount() {
        return remainingAmount;
    }

    public void setRemainingAmount(Money remainingAmount) {
        this.remainingAmount = remainingAmount;
    }

    public Money getReservedAmount() {
        return reservedAmount;
    }

    public void setReservedAmount(Money reservedAmount) {
        this.reservedAmount = reservedAmount;
    }

    @Override
    public String getAccumulatorType() {
        return accumulatorType;
    }

    public void setAccumulatorType(String accumulatorType) {
        this.accumulatorType = accumulatorType;
    }

    public String getRenewalType() {
        return renewalType;
    }

    public void setRenewalType(String renewalType) {
        this.renewalType = renewalType;
    }

    public String getAppliesToProcedureCategory() {
        return appliesToProcedureCategory;
    }

    public void setAppliesToProcedureCategory(String appliesToProcedureCategory) {
        this.appliesToProcedureCategory = appliesToProcedureCategory;
    }

    public String getNetworkType() {
        return networkType;
    }

    public void setNetworkType(String networkType) {
        this.networkType = networkType;
    }
}
