package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentApproveHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentClearHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentDeclineHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentIssueHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentRequestIssueFailHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentRequestIssueHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentRequestStopHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentStopHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentVoidHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalProcessPaymentLifecycleHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentApproveHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentCancelHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentClearHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentDeclineHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentDisapproveHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentInitHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentIssueHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentRequestIssueHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentRequestStopHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentStopHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentVoidHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalCancelOverpaymentWaiveHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalOverpaymentInitWaiveHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalOverpaymentUpdateWaiveHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentDefinitionLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalPaymentDefinitionLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalPaymentDefinition";

    private static final String MODEL_TYPE = "CapPayment";

    private CapDentalPaymentDefinitionLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalPaymentDefinitionLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalPaymentApproveHandler.class,
                CapDentalPaymentCancelHandler.class,
                CapDentalPaymentClearHandler.class,
                CapDentalPaymentInitHandler.class,
                CapDentalPaymentIssueHandler.class,
                CapDentalPaymentRequestStopHandler.class,
                CapDentalPaymentStopHandler.class,
                CapDentalPaymentUpdateHandler.class,
                CapDentalPaymentVoidHandler.class,
                CapDentalPaymentRequestIssueFailHandler.class,
                CapDentalPaymentRequestIssueHandler.class,
                CapDentalProcessPaymentLifecycleHandler.class,
                CapDentalPaymentDeclineHandler.class,
                CapDentalRecoveryInitHandler.class,
                CapDentalRecoveryUpdateHandler.class,
                CapDentalRecoveryCancelHandler.class,
                CapDentalOverpaymentInitWaiveHandler.class,
                CapDentalOverpaymentUpdateWaiveHandler.class,
                CapDentalCancelOverpaymentWaiveHandler.class,
                CapDentalUnderpaymentApproveHandler.class,
                CapDentalUnderpaymentCancelHandler.class,
                CapDentalUnderpaymentClearHandler.class,
                CapDentalUnderpaymentDeclineHandler.class,
                CapDentalUnderpaymentDisapproveHandler.class,
                CapDentalUnderpaymentInitHandler.class,
                CapDentalUnderpaymentIssueHandler.class,
                CapDentalUnderpaymentRequestIssueHandler.class,
                CapDentalUnderpaymentRequestStopHandler.class,
                CapDentalUnderpaymentStopHandler.class,
                CapDentalUnderpaymentUpdateHandler.class,
                CapDentalUnderpaymentVoidHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(module.getModelVersion(), equalTo("1"));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalPaymentDefinitionLifecycleConfig.class), equalTo(true));
    }
}
