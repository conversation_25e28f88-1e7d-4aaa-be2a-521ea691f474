/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment.input;

import com.eisgroup.genesis.cap.financial.command.payment.input.CapPaymentInitInput;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Optional;

import javax.money.MonetaryAmount;

/**
 * {@link CapDentalPaymentEntity} init input
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentInitInput extends CapPaymentInitInput {

    private static final String ORIGIN_SOURCE = "originSource";
    private static final String ENTITY = "entity";
    private static final String PAYMENT_SCHEDULE = "paymentSchedule";
    private static final String PAYMENT_NET_AMOUNT = "paymentNetAmount";

    public CapDentalPaymentInitInput(JsonObject original) {
        super(original);
    }

    public CapDentalPaymentInitInput(CapPaymentDetails detailsEntity, EntityLink<RootEntity> originSource,
                                     EntityLink<RootEntity> paymentSchedule, MonetaryAmount paymentNetAmount) {
        super(new JsonObject());
        setChildObject(ENTITY, detailsEntity);
        setChildObject(ORIGIN_SOURCE, originSource);
        setChildObject(PAYMENT_SCHEDULE, paymentSchedule);
        setChildObject(PAYMENT_NET_AMOUNT, paymentNetAmount);
    }

    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(originSource -> new EntityLink<>(RootEntity.class, originSource))
                .orElse(null);
    }

    public EntityLink<RootEntity> getPaymentSchedule() {
        return Optional.ofNullable(getRawChild(PAYMENT_SCHEDULE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(schedule -> new EntityLink<>(RootEntity.class, schedule))
                .orElse(null);
    }

    public MonetaryAmount getPaymentNetAmount() {
        return getMonetaryAmount(PAYMENT_NET_AMOUNT);
    }
}
