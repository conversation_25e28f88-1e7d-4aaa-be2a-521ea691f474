{"TestData": {"entity": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 114.17, "currency": "USD"}, "dateOfService": "$<today-1d:yyyy-MM-dd>", "toothCodes": ["6"], "predetInd": false, "quantity": 1, "ortho": {"downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "2022-02-18", "orthoMonthQuantity": 5, "_type": "CapDentalOrthodonticEntity"}, "diagnosisCodes": [{"code": "string1", "qualifier": "AB", "_type": "CapDentalDiagnosisCodeEntity"}], "_type": "CapDentalProcedureEntity"}], "claimData": {"patient": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{Customer_rootId}}"}, "provider": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{Customer_rootId}}"}, "policyholder": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{Customer_rootId}}"}, "payeeType": "Provider", "dateOfBirth": "$<today-20y:yyyy-MM-dd>", "source": "EDI", "transactionType": "OrthodonticServices", "receivedDate": "$<today:yyyy-MM-dd>", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//{{Dental_policyId}}", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel"}}