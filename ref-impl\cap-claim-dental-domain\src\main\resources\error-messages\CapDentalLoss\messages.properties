AllowedCannotBeNegative=cob.allowed cannot be negative.
AuthorizationPeriodValidation=authorizationPeriod.endDate must be later than authorizationPeriod.startDate.
CleanClaimDateCannotBeBeforeFutureDate=cleanClaimDate cannot be in the future.
CleanClaimDateCannotBeBeforeReceivedDate=cleanClaimDate cannot be before the receivedDate.
ConsideredCannotBeNegative=cob.considered cannot be negative.
DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet=dateOfService cannot be after the cleanClaimDate.
DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank=Date of Service cannot be after the Received Date.
DateOfServiceCannotBeInFuture=Date of Service cannot be future date.
DiscountAmountCannotBeNegative=discountAmount cannot be negative.
DownPaymentCannotBeNegative=downPayment cannot be negative.
MandatoryActualOrPredeterminationActualServicesForClaim=submittedProcedures details are required.
MandatoryAlternatePayee=alternatePayee is mandatory.
MandatoryAuthorizationBy=authorizationBy is mandatory.
MandatoryAuthorizationPeriod=authorizationPeriod is mandatory.
MandatoryClaimDiscountAmount=discountAmount is mandatory.
MandatoryClaimDiscountName=discountName is mandatory.
MandatoryClaimDiscountPercentage=discountPercentage is mandatory.
MandatoryClaimDiscountType=discountType is mandatory.
MandatoryClaimPatient=patient is mandatory.
MandatoryClaimPayeeType=payeeType is mandatory.
MandatoryClaimPolicyHolder=policyHolder is mandatory.
MandatoryClaimPolicyId=policyId is mandatory.
MandatoryClaimProvider=provider is mandatory when the isUnknownOrIntProvider flag is not true.
MandatoryClaimProviderFee=providerFee.fee is mandatory.
MandatoryClaimProviderFeeType=providerFee.type is mandatory.
MandatoryClaimReceivedDate=receivedDate is mandatory.
MandatoryClaimSource=source for the claim is mandatory.
MandatoryClaimTransactionType=transactionType is mandatory.
MandatoryDiagnosisQualifier=Diagnosis List Qualifier is required.
MandatoryOneOrthoServiceForClaim=ortho details are required and only one Ortho service can be provided.
MandatoryOrthodonticFrequency=orthoFrequencyCd is mandatory.
MandatoryProcedureCode=Procedure Code is required.
MandatoryProcedureDateOfService=Date of Service is required.
MandatorySubmittedFee=Charges is required.
OrthodonticMonthsMax=orthoMonthQuantity cannot be longer than 36 months.
OrthodonticMonthsMin=orthoMonthQuantity has to be equal or more than 1.
PaidCannotBeNegative=cob.paid cannot be negative.
ProcedureQuantityMax=Quantity must not be greater than 99.
ProcedureQuantityMin=Quantity has to be equal or more than 1.
ReceivedDateCannotBeInFuture=receivedDate cannot be in the future.
SubmittedFeeCannotBeNegative=Charges cannot be a negative value.
SuspendLossOnlyAvailableForOrtho=suspendLoss command cannot be performed if transactionType is not OrthodonticServices.
MandatoryDiagnosisCode=Diagnosis Code is required.
MandatoryProcedureQuantity=Quantity is required.