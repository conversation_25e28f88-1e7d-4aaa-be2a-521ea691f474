/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.cap.loss.command.ClaimLossInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossInitInput;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;

import javax.annotation.Nonnull;

/**
 * Command handler for Dental Loss initiation with custom request supporting policy
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalLossInitHandler extends ClaimLossInitHandler<CapDentalLossInitInput, CapLoss> {
    private static final String DENTAL = "dental";

    @Nonnull
    @Override
    public CapLoss execute(@Nonnull CapDentalLossInitInput input, @Nonnull CapLoss entity) {
        var loss = super.execute(input, entity);
        if (loss instanceof CapDentalLossEntity dentalLoss) {
            dentalLoss.setClaimType(DENTAL);
        }
        return loss;
    }
}
