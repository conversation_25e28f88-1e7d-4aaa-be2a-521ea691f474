<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.0">
  <process id="claim.dental.bpmn.adjudication.subprocess" name="Claim Dental Process - Adjudication Sub-Process" isExecutable="true">
    <endEvent id="dental_readjudication_subprocess_end"></endEvent>
    <serviceTask id="sid-B5B2969B-0037-4637-9A22-271F52E1BACA" name="Adjudication Rules" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelName}" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="adjudicateSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" target="settlementAdjudicationResult"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <startEvent id="dental_readjudication_subprocess_start" flowable:formFieldValidation="true"></startEvent>
    <sequenceFlow id="sid-52CB6598-2F62-4180-A8C0-087D648FEEE5" sourceRef="dental_readjudication_subprocess_start" targetRef="sid-B5B2969B-0037-4637-9A22-271F52E1BACA"></sequenceFlow>
    <sequenceFlow id="sid-21DB2575-C3C6-42E4-9396-7A8C50AC25F0" sourceRef="sid-B5B2969B-0037-4637-9A22-271F52E1BACA" targetRef="dental_readjudication_subprocess_end"></sequenceFlow>
    <textAnnotation id="sid-CE289338-44E3-48CE-B642-F3C079C4F29F">
      <text>(api: adjudicateSettlement)
Enrich settlement, call OpenL, transform data back </text>
    </textAnnotation>
    <textAnnotation id="sid-EB0B3F83-8B08-432C-B6F3-0D7A07CA83B6">
      <text>Input params:
Settlement
*  _key
* _modelName
* _modelVersion
* _uri</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_claim.dental.bpmn.adjudication.subprocess">
    <bpmndi:BPMNPlane bpmnElement="claim.dental.bpmn.adjudication.subprocess" id="BPMNPlane_claim.dental.bpmn.adjudication.subprocess">
      <bpmndi:BPMNShape bpmnElement="dental_readjudication_subprocess_end" id="BPMNShape_dental_readjudication_subprocess_end">
        <omgdc:Bounds height="28.0" width="28.0" x="915.0" y="161.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-B5B2969B-0037-4637-9A22-271F52E1BACA" id="BPMNShape_sid-B5B2969B-0037-4637-9A22-271F52E1BACA">
        <omgdc:Bounds height="80.0" width="100.0" x="675.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dental_readjudication_subprocess_start" id="BPMNShape_dental_readjudication_subprocess_start">
        <omgdc:Bounds height="30.0" width="30.0" x="405.0" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-CE289338-44E3-48CE-B642-F3C079C4F29F" id="BPMNShape_sid-CE289338-44E3-48CE-B642-F3C079C4F29F">
        <omgdc:Bounds height="105.99999999999997" width="154.0" x="648.0000000000001" y="240.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-EB0B3F83-8B08-432C-B6F3-0D7A07CA83B6" id="BPMNShape_sid-EB0B3F83-8B08-432C-B6F3-0D7A07CA83B6">
        <omgdc:Bounds height="106.0" width="154.0" x="240.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-21DB2575-C3C6-42E4-9396-7A8C50AC25F0" id="BPMNEdge_sid-21DB2575-C3C6-42E4-9396-7A8C50AC25F0" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="774.9499999999294" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="915.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-52CB6598-2F62-4180-A8C0-087D648FEEE5" id="BPMNEdge_sid-52CB6598-2F62-4180-A8C0-087D648FEEE5" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="434.9499998034685" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="674.9999999998878" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>