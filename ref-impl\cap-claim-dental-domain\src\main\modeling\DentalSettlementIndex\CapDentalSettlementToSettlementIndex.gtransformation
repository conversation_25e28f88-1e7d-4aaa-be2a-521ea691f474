@Persistable
@CommandListener("Save", "initSettlement,readjudicateSettlement,refreshSettlement")
Transformation CapDentalSettlementToSettlementIndex {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        CapDentalSettlementIndex.CapDentalSettlementIdx
    }

    Var now is Now()

    Attr lossId is settlement.claimLossIdentification._uri
    Attr settlementId is ToExtLink(settlement)._uri
    Attr createdOn is now
}
