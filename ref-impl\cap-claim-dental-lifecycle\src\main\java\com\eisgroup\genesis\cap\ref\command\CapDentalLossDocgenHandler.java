/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command;

import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.loss.repository.ClaimLossRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.model.ModelResolver;


/**
 * Command handler for Dental Loss related documents generation
 *
 * <AUTHOR>
 * @since 21.15
 * @deprecated 22.6
 */
public class CapDentalLossDocgenHandler<I extends IdentifierRequest, O extends CapLoss> implements
    ProductCommandHandler<I, O> {

    @Autowired
    private ClaimLossRepository claimLossRepository;

    @Autowired
    private ModelResolver modelResolver;

    /**
     * {@inheritDoc}
     * Loads from database {@link CapLoss}.
     *
     * @param input used to retrieve {@link RootEntityKey} object to determine which {@link CapLoss} to load
     * @return loaded {@link CapLoss}.
     * <AUTHOR>
     * @since 21.15
     */
    @Nonnull
    @Override
    public O load(@Nonnull I input) {
        return (O) claimLossRepository.load(input.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public O execute(@Nonnull I input, @Nonnull O entity) {
        return Lazy.of(entity).get();
    }

    /**
     * {@inheritDoc}
     * Saves {@link CapLoss} with updated data.
     *
     * @param input  method does not use this parameter
     * @param entity that will be saved
     * @return saved {@link CapLoss}
     * <AUTHOR>
     * @since 21.15
     */
    @Nonnull
    @Override
    public O save(@Nonnull I input, @Nonnull O entity) {
        return (O) claimLossRepository.save(entity).get();
    }

    /**
     * {@inheritDoc}
     * Name of command handler.
     *
     * <AUTHOR>
     * @since 21.15
     */
    @Override
    public String getName() {
        return "documentGeneration";
    }
}
