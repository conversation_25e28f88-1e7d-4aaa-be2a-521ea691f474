/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPatientHistoryWrapperModel extends TypeModel implements ICapDentalPatientHistoryWrapperModel {

    private ICapDentalPatientHistoryModel entity;

    @JsonSerialize(as = CapDentalPatientHistoryModel.class)
    public ICapDentalPatientHistoryModel getEntity() {
        return entity;
    }

    @JsonDeserialize(as = CapDentalPatientHistoryModel.class)
    public void setEntity(ICapDentalPatientHistoryModel entity) {
        this.entity = entity;
    }
}
