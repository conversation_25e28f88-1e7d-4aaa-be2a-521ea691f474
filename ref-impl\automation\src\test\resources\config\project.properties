##########>> Gravitzappa related properties << ############
platform.revision.service=CLAIM_DENTAL
platform.revision.port=8104
platform.revision.schema=http
talisker.retry.maxCount=1
talisker.reporting.metrics.enabled=true
tzappa.listener.collect.retry=true
tzappa.listener.ta3.retry.attach=false
tzappa.listener.abort.run.shutdown.hook=true
tzappa-v20.loglevel=debug
tzappa.rest.CLAIM_DENTAL.host=ms-claim-benefits-dental-app
tzappa.rest.app.host=ms-claim-benefits-dental-app
tzappa.rest.client.connect.timeout=300000
tzappa.rest.client.read.timeout=300000
tzappa.retry.repeat.timeout=300000
tzappa.retry.repeat.delay=7000
platform.jersey.config.log.level=INFO
tzappa.rest.logging.prettyprint=true
tzappa.rest.logging.verbosity=REQUEST_AND_RESPONSE_PAYLOAD_TEXT

#ELK logs retrieval props
tzappa.kibana.host=dev2eisgenelk02.sjclab.exigengroup.com
tzappa.kibana.port=9200
tzappa.kibana.schema=http
tzappa.listener.onsuccess.disable.logs=true
tzappa.logs.elk.stopstrategy.timeout=90000