AllowedCannotBeNegative=MT+cob.allowed cannot be negative.
AuthorizationPeriodValidation=MT+authorizationPeriod.endDate must be later than authorizationPeriod.startDate.
CleanClaimDateCannotBeBeforeFutureDate=MT+cleanClaimDate cannot be in the future.
CleanClaimDateCannotBeBeforeReceivedDate=MT+cleanClaimDate cannot be before the receivedDate.
ConsideredCannotBeNegative=MT+cob.considered cannot be negative.
DateOfServiceCannotBeAfterCleanClaimDateWhenCleanClaimDateIsSet=MT+dateOfService cannot be after the cleanClaimDate.
DateOfServiceCannotBeAfterReceivedDateWhenCleanClaimDateIsBlank=MT+Date of Service cannot be after the Received Date.
DateOfServiceCannotBeInFuture=MT+Date of Service cannot be future date.
DiscountAmountCannotBeNegative=MT+discountAmount cannot be negative.
DownPaymentCannotBeNegative=MT+downPayment cannot be negative.
MandatoryActualOrPredeterminationActualServicesForClaim=MT+submittedProcedures details are required.
MandatoryAlternatePayee=MT+alternatePayee is mandatory.
MandatoryAuthorizationBy=MT+authorizationBy is mandatory.
MandatoryAuthorizationPeriod=MT+authorizationPeriod is mandatory.
MandatoryClaimDiscountAmount=MT+discountAmount is mandatory.
MandatoryClaimDiscountName=MT+discountName is mandatory.
MandatoryClaimDiscountPercentage=MT+discountPercentage is mandatory.
MandatoryClaimDiscountType=MT+discountType is mandatory.
MandatoryClaimPatient=MT+patient is mandatory.
MandatoryClaimPayeeType=MT+payeeType is mandatory.
MandatoryClaimPolicyHolder=MT+policyHolder is mandatory.
MandatoryClaimPolicyId=MT+policyId is mandatory.
MandatoryClaimProvider=MT+provider is mandatory when the isUnknownOrIntProvider flag is not true.
MandatoryClaimProviderFee=MT+providerFee.fee is mandatory.
MandatoryClaimProviderFeeType=MT+providerFee.type is mandatory.
MandatoryClaimReceivedDate=MT+receivedDate is mandatory.
MandatoryClaimSource=MT+source for the claim is mandatory.
MandatoryClaimTransactionType=MT+transactionType is mandatory.
MandatoryDiagnosisQualifier=MT+Diagnosis List Qualifier is required.
MandatoryOneOrthoServiceForClaim=MT+ortho details are required and only one Ortho service can be provided.
MandatoryOrthodonticFrequency=MT+orthoFrequencyCd is mandatory.
MandatoryProcedureCode=MT+Procedure Code is required.
MandatoryProcedureDateOfService=MT+Date of Service is required.
MandatorySubmittedFee=MT+Charges is required.
OrthodonticMonthsMax=MT+orthoMonthQuantity cannot be longer than 36 months.
OrthodonticMonthsMin=MT+orthoMonthQuantity has to be equal or more than 1.
PaidCannotBeNegative=MT+cob.paid cannot be negative.
ProcedureQuantityMax=MT+Quantity must not be greater than 99.
ProcedureQuantityMin=MT+Quantity has to be equal or more than 1.
ReceivedDateCannotBeInFuture=MT+receivedDate cannot be in the future.
SubmittedFeeCannotBeNegative=MT+Charges cannot be a negative value.
SuspendLossOnlyAvailableForOrtho=MT+suspendLoss command cannot be performed if transactionType is not OrthodonticServices.
MandatoryDiagnosisCode=MT+Diagnosis Code is required.
MandatoryProcedureQuantity=MT+Quantity is required.