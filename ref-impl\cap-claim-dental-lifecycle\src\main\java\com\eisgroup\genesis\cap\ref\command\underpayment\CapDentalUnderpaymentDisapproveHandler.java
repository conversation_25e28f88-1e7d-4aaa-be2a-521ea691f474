/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.underpayment;

import com.eisgroup.genesis.cap.financial.command.CapUnderpaymentCommands;
import com.eisgroup.genesis.cap.financial.command.underpayment.CapUnderpaymentBaseCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapPayment;

/**
 * Command handler for disapproving a {@link CapPayment}.
 *
 * <AUTHOR>
 * @since 22.15
 */
@Modifying
public class CapDentalUnderpaymentDisapproveHand<PERSON> extends CapUnderpaymentBaseCommandHandler<IdentifierRequest, CapDentalPaymentEntity> {

    @Override
    public String getName() {
        return CapUnderpaymentCommands.DISAPPROVE_UNDERPAYMENT;
    }
}
