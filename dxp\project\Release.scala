import sbt.Keys._
import sbt.{Credentials, Path, _}
import sbtrelease.ReleasePlugin.autoImport._

object Release {

  // Settings for release tag information
  lazy val releaseSettings = Seq(
    releaseTagName := s"${(ThisBuild / version).value}",
    releaseIgnoreUntrackedFiles := true,
    releaseTagComment    := s"Releasing ${(ThisBuild / version).value}",
    releaseCommitMessage := s"Setting version to ${(ThisBuild / version).value}"
  )

  // Settings for 'publish' task
  lazy val publishSettings = Seq(

    // Nexus repository configuration for publishing
    publishTo := {
      if (version.value.trim.matches("\\d{1,}(.\\d{1,})+(-BETA)?"))
        Some("dxp-release" at sys.env.getOrElse("DXP_NEXUS_RELEASE_REPO_URL", "https://sfoeisgennexus01.exigengroup.com/repository/genesis-release"))
      else
        Some("dxp-snapshot" at sys.env.getOrElse("DXP_NEXUS_STAGING_REPO_URL", "https://sfoeisgennexus01.exigengroup.com/repository/genesis-staging"))
    },

    // Check if "~/.ivy2/.credentials" exists and adding it to credentials. Otherwise adding a stub to avoid warnings.
    credentials += {
      val file = Path.userHome / ".ivy2" / ".credentials.genesis"
      if (file exists)
        Credentials(file)
      else
        Credentials("", "", "", "")
    }
  )
}
