/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

/**
 * Holds command names of Dental Loss.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossCommands {

    public static final String OPEN_LOSS = "openLoss";
    public static final String PEND_LOSS = "pendLoss";
    public static final String SUSPEND_LOSS = "suspendLoss";
    public static final String UNSUSPEND_LOSS = "unsuspendLoss";
    public static final String REQUEST_CLOSE_LOSS = "requestCloseLoss";


    /**
     * Class is for constants only, no constructor to be exposed.
     */
    public CapDentalLossCommands() {
    }
}
