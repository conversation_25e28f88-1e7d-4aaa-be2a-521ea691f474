/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.capsettlement.base.facade.impl.modeling.impl.CapSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalClaimInfoModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementDetailModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementResultModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalSettlementModel extends CapSettlementModel implements ICapDentalSettlementModel {

    private ICapDentalSettlementDetailModel entity;
    private ICapDentalSettlementDetailModel settlementDetail;
    private ICapDentalSettlementResultModel settlementResult;
    private ICapDentalClaimInfoModel settlementLossInfo;

    @JsonSerialize(as = CapDentalSettlementDetailModel.class)
    public ICapDentalSettlementDetailModel getEntity() {
        return entity;
    }

    @JsonDeserialize(as = CapDentalSettlementDetailModel.class)
    public void setEntity(ICapDentalSettlementDetailModel entity) {
        this.entity = entity;
    }

    @JsonSerialize(as = CapDentalSettlementDetailModel.class)
    public ICapDentalSettlementDetailModel getSettlementDetail() {
        return settlementDetail;
    }

    @JsonDeserialize(as = CapDentalSettlementDetailModel.class)
    public void setSettlementDetail(ICapDentalSettlementDetailModel settlementDetail) {
        this.settlementDetail = settlementDetail;
    }

    @JsonSerialize(as = CapDentalSettlementResultModel.class)
    public ICapDentalSettlementResultModel getSettlementResult() {
        return settlementResult;
    }

    @JsonDeserialize(as = CapDentalSettlementResultModel.class)
    public void setSettlementResult(ICapDentalSettlementResultModel settlementResult) {
        this.settlementResult = settlementResult;
    }

    @JsonSerialize(as = CapDentalClaimInfoModel.class)
    public ICapDentalClaimInfoModel getSettlementLossInfo() {
        return settlementLossInfo;
    }

    @JsonDeserialize(as = CapDentalClaimInfoModel.class)
    public void setSettlementLossInfo(ICapDentalClaimInfoModel settlementLossInfo) {
        this.settlementLossInfo = settlementLossInfo;
    }

    @Override
    public String entityName() {
        return "CapDentalSettlement";
    }

    @Override
    public String endpointName() {
        return "CapSettlement";
    }
}
