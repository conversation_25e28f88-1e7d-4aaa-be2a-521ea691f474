/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.service;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceCalculationRulesOutput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Service used to calculate dental Balance
 *
 * <AUTHOR>
 * @since 22.14
 */
public interface CapDentalBalanceCalculator {

    /**
     * Calculates the CapDentalBalance for a given originSource
     *
     * @param originSource
     * @return
     * @since 22.14
     */
    Lazy<CapDentalBalanceCalculationRulesOutput> calculateLossBalance(EntityLink<RootEntity> originSource);
}
