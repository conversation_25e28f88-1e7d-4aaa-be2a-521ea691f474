/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalWaiveClaimValueModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalWaiveClaimValueModel extends TypeModel implements ICapDentalWaiveClaimValueModel {

    private Boolean waiveServiceFrequencyLimit;
    private Boolean waivePreventiveWaitingPeriod;
    private Boolean waiveOrthoWaitingPeriod;
    private Boolean waiveDeductible;
    private Boolean waiveMaximumLimitAmounts;
    private Boolean waiveMajorWaitingPeriod;
    private Boolean waiveReplacementLimit;
    private Boolean waiveBasicWaitingPeriod;
    private Boolean waiveEligibilityPriorStartDate;
    private Boolean waiveEligibilityAfterStartDate;
    private Boolean waiveLateEntrantWaitingPeriod;

    public Boolean getWaiveServiceFrequencyLimit() {
        return waiveServiceFrequencyLimit;
    }

    public void setWaiveServiceFrequencyLimit(Boolean waiveServiceFrequencyLimit) {
        this.waiveServiceFrequencyLimit = waiveServiceFrequencyLimit;
    }

    public Boolean getWaivePreventiveWaitingPeriod() {
        return waivePreventiveWaitingPeriod;
    }

    public void setWaivePreventiveWaitingPeriod(Boolean waivePreventiveWaitingPeriod) {
        this.waivePreventiveWaitingPeriod = waivePreventiveWaitingPeriod;
    }

    public Boolean getWaiveOrthoWaitingPeriod() {
        return waiveOrthoWaitingPeriod;
    }

    public void setWaiveOrthoWaitingPeriod(Boolean waiveOrthoWaitingPeriod) {
        this.waiveOrthoWaitingPeriod = waiveOrthoWaitingPeriod;
    }

    public Boolean getWaiveDeductible() {
        return waiveDeductible;
    }

    public void setWaiveDeductible(Boolean waiveDeductible) {
        this.waiveDeductible = waiveDeductible;
    }

    public Boolean getWaiveMaximumLimitAmounts() {
        return waiveMaximumLimitAmounts;
    }

    public void setWaiveMaximumLimitAmounts(Boolean waiveMaximumLimitAmounts) {
        this.waiveMaximumLimitAmounts = waiveMaximumLimitAmounts;
    }

    public Boolean getWaiveMajorWaitingPeriod() {
        return waiveMajorWaitingPeriod;
    }

    public void setWaiveMajorWaitingPeriod(Boolean waiveMajorWaitingPeriod) {
        this.waiveMajorWaitingPeriod = waiveMajorWaitingPeriod;
    }

    public Boolean getWaiveReplacementLimit() {
        return waiveReplacementLimit;
    }

    public void setWaiveReplacementLimit(Boolean waiveReplacementLimit) {
        this.waiveReplacementLimit = waiveReplacementLimit;
    }

    public Boolean getWaiveBasicWaitingPeriod() {
        return waiveBasicWaitingPeriod;
    }

    public void setWaiveBasicWaitingPeriod(Boolean waiveBasicWaitingPeriod) {
        this.waiveBasicWaitingPeriod = waiveBasicWaitingPeriod;
    }

    public Boolean getWaiveEligibilityPriorStartDate() {
        return waiveEligibilityPriorStartDate;
    }

    public void setWaiveEligibilityPriorStartDate(Boolean waiveEligibilityPriorStartDate) {
        this.waiveEligibilityPriorStartDate = waiveEligibilityPriorStartDate;
    }

    public Boolean getWaiveEligibilityAfterStartDate() {
        return waiveEligibilityAfterStartDate;
    }

    public void setWaiveEligibilityAfterStartDate(Boolean waiveEligibilityAfterStartDate) {
        this.waiveEligibilityAfterStartDate = waiveEligibilityAfterStartDate;
    }

    public Boolean getWaiveLateEntrantWaitingPeriod() {
        return waiveLateEntrantWaitingPeriod;
    }

    public void setWaiveLateEntrantWaitingPeriod(Boolean waiveLateEntrantWaitingPeriod) {
        this.waiveLateEntrantWaitingPeriod = waiveLateEntrantWaitingPeriod;
    }
}
