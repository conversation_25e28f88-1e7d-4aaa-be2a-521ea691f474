/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.jobs;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.jobs.lifecycle.api.commands.output.SubsequentCommand;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasSize;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentGenerationJobTest {

    private static final String SCHEDULE_ROOT_ID_1 = "f4f92937-11e2-4f32-afc3-a80f25075da0";
    @Mock
    private CapDentalPaymentScheduleIndexRepository capPaymentScheduleIndexRepository;

    @InjectMocks
    private CapDentalPaymentGenerationJob paymentGenerationJob;

    @Test
    public void shouldCreateSubsequentCommand() {
        //given
        Mockito.when(capPaymentScheduleIndexRepository.load("Active"))
                .thenReturn(Streamable.of(List.of(createScheduleIndex(SCHEDULE_ROOT_ID_1))));

        //when
        List<SubsequentCommand> subsequentCommands = TestStreamable.create(paymentGenerationJob.execute())
                .assertNoErrors()
                .values();
        //then
        assertThat(subsequentCommands, hasSize(1));
        assertThat(getRootId(subsequentCommands.getFirst()), equalTo(SCHEDULE_ROOT_ID_1));
    }

    @Test
    public void shouldReturnJobCommandName() {
        assertThat(paymentGenerationJob.getName(), equalTo("paymentGenerationJob"));
    }

    private CapDentalPaymentScheduleIdxEntity createScheduleIndex(String scheduleRootId) {
        CapDentalPaymentScheduleIdxEntity scheduleIndex = (CapDentalPaymentScheduleIdxEntity) ModelInstanceFactory.createInstance("CapDentalPaymentScheduleIndex", "1", "CapDentalPaymentScheduleIdxEntity");
        scheduleIndex.setPaymentSchedule("gentity://CapPaymentSchedule/CapDentalPaymentSchedule//" + scheduleRootId +"/1");
        return scheduleIndex;
    }

    private String getRootId(SubsequentCommand subsequentCommand) {
        return subsequentCommand.getCommand().getData().getAsJsonObject()
                .get("_key").getAsJsonObject().get("rootId").getAsString();
    }
}
