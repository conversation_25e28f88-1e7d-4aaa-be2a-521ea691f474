/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalInitPaymentScheduleInputValidator.CapDentalInitPaymentScheduleInputValidatorErrorDefinition.ORIGIN_SOURCE_URI_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalInitPaymentScheduleInputValidator.CapDentalInitPaymentScheduleInputValidatorErrorDefinition.PAYEE_URI_INCORRECT_FOR_PAYMENT;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalInitPaymentScheduleInputValidator.CapDentalInitPaymentScheduleInputValidatorErrorDefinition.PAYMENT_TEMPLATE_URI_INCORRECT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.function.BinaryOperator;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPaymentScheduleInitInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalScheduledPaymentEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentPayeeDetailsEntity;
import com.eisgroup.genesis.factory.modeling.types.CapScheduledPayment;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Class for {@link CapDentalPaymentScheduleInitInput} related validations.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalInitPaymentScheduleInputValidator extends CapInputValidator<CapDentalPaymentScheduleInitInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalInitPaymentScheduleInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPaymentScheduleInitInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPaymentScheduleInitInput input) {
        return Streamable.concat(validateOriginSource(input.getOriginSource()),
                validatePaymentTemplate(input.getPaymentTemplate()),
                validatePayments(input));
    }

    private Streamable<ErrorHolder> validatePayments(CapDentalPaymentScheduleInitInput input) {
        List<ErrorHolder> errorHolders = Streamable.from(input.getPayments())
                .flatMap(capScheduledPayment -> validatePayee((CapDentalScheduledPaymentEntity) capScheduledPayment))
                .collect(Collectors.toList());
        return Streamable.concat(errorHolders);
    }

    private Streamable<ErrorHolder> validateOriginSource(EntityLink<RootEntity> originSource) {
        return Optional.ofNullable(originSource)
                .map(origin -> capDentalLinkValidator.validateLink(origin, "CapLoss", ORIGIN_SOURCE_URI_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePaymentTemplate(EntityLink<RootEntity> paymentTemplate) {
        return Optional.ofNullable(paymentTemplate)
                .map(template -> capDentalLinkValidator.validateLink(template, "CapPaymentTemplate", PAYMENT_TEMPLATE_URI_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePayee(CapDentalScheduledPaymentEntity payment) {
        return Optional.ofNullable(payment)
                .map(CapDentalScheduledPaymentEntity::getPaymentDetails)
                .map(CapDentalPaymentDetailsEntity::getPayeeDetails)
                .map(CapDentalPaymentPayeeDetailsEntity::getPayee)
                .map(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_URI_INCORRECT_FOR_PAYMENT.builder().params(payee.getURIString()).build()))
                .orElse(Streamable.empty());
    }


    private Stream<CapDentalScheduledPaymentEntity> getDentalPaymentsStream(Collection<CapScheduledPayment> scheduledPayments) {
        return Optional.ofNullable(scheduledPayments)
                .orElse(new ArrayList<>())
                .stream()
                .filter(CapDentalScheduledPaymentEntity.class::isInstance)
                .map(CapDentalScheduledPaymentEntity.class::cast);
    }

    public static class CapDentalInitPaymentScheduleInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalInitPaymentScheduleInputValidatorErrorDefinition ORIGIN_SOURCE_URI_INCORRECT = new CapDentalInitPaymentScheduleInputValidatorErrorDefinition(
                "dapsv-002", "originSource URI is not valid");
        public static final CapDentalInitPaymentScheduleInputValidatorErrorDefinition PAYMENT_TEMPLATE_URI_INCORRECT = new CapDentalInitPaymentScheduleInputValidatorErrorDefinition(
                "dapsv-003", "paymentTemplate URI is not valid.");
        public static final CapDentalInitPaymentScheduleInputValidatorErrorDefinition PAYEE_URI_INCORRECT_FOR_PAYMENT = new CapDentalInitPaymentScheduleInputValidatorErrorDefinition(
                "dapsv-004", "payeeDetails.payee {0} URI is not valid.");

        protected CapDentalInitPaymentScheduleInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
