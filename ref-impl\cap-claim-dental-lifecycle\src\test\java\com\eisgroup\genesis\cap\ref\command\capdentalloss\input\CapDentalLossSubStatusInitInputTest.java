/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.input;

import com.eisgroup.genesis.test.utils.JsonUtils;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalLossSubStatusInitInputTest {
    private CapDentalLossSubStatusInitInput input = new CapDentalLossSubStatusInitInput(JsonUtils.load(
            "requestSamples/lossSubStatusInitInput.json"));

    @Test
    public void shouldReturnLossSubStatusCd() {
        //when
        String result = input.getLossSubStatusCd();

        //then
        assertThat(result, equalTo("ProviderData"));
    }
}
