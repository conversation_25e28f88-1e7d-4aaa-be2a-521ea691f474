/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.claimsearch.facade;

import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.platform.common.action.CommonSearchAction;

public interface IClaimSearch {

    CommonSearchAction<ICapDentalSettlementModel> searchDentalSettlement();

    CommonSearchAction<ICapDentalPaymentTemplateModel> searchDentalPaymentTemplate();

    CommonSearchAction<ICapDentalPaymentScheduleModel> searchDentalPaymentSchedule();
}
