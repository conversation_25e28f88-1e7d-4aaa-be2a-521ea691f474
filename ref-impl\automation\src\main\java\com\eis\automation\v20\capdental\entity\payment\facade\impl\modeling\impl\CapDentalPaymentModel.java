/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.impl.CapBasePaymentModel;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.interf.ICapBasePaymentDetailsModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentModel extends CapBasePaymentModel implements ICapDentalPaymentModel {

    public Money paymentNetAmount;

    public Money getPaymentNetAmount() {
        return paymentNetAmount;
    }

    public void setPaymentNetAmount(Money paymentNetAmount) {
        this.paymentNetAmount = paymentNetAmount;
    }

    @JsonSerialize(as = CapDentalPaymentDetailsModel.class)
    public ICapBasePaymentDetailsModel getPaymentDetails() {
        return super.getPaymentDetails();
    }

    @JsonDeserialize(as = CapDentalPaymentDetailsModel.class)
    public void setPaymentDetails(ICapBasePaymentDetailsModel paymentDetails) {
        super.setPaymentDetails(paymentDetails);
    }

    @JsonSerialize(as = CapDentalPaymentDetailsModel.class)
    public ICapBasePaymentDetailsModel getEntity() {
        return super.getEntity();
    }

    @JsonDeserialize(as = CapDentalPaymentDetailsModel.class)
    public void setEntity(ICapBasePaymentDetailsModel entity) {
        super.setEntity(entity);
    }

    @Override
    public String entityName() {
        return "CapDentalPaymentEntity";
    }

    @Override
    public String endpointName() {
        return "CapPayment";
    }
}
