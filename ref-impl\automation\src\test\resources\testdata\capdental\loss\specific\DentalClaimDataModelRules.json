{"TestData_Ortho1": {"entity": {"submittedProcedures": [{"procedureCode": "D0330", "submittedFee": {"amount": 114.17, "currency": "USD"}, "dateOfService": "$<today-2M:yyyy-MM-dd>", "toothCodes": ["6"], "predetInd": false, "quantity": 1, "ortho": {"orthoMonthQuantity": 1, "downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "$<today-2M:yyyy-MM-dd>", "orthoFrequencyCd": "Monthly", "_type": "CapDentalOrthodonticEntity"}, "preauthorization": {"isProcedureAuthorized": true, "authorizedBy": "1", "authorizationPeriod": {"startDate": "$<today-1d:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "endDate": "$<today:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "Period"}, "_type": "CapDentalPreauthorizationEntity"}, "_type": "CapDentalProcedureEntity"}], "claimData": {"providerFees": [{"fee": {"amount": 1, "currency": "USD"}, "type": "Other", "_type": "CapDentalProviderFeesEntity"}], "providerDiscount": {"discountType": "Percentage", "discountPercentage": 1, "discountName": "Other", "_type": "CapDentalProviderDiscountEntity"}, "patient": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}"}, "payeeType": "PrimaryInsured", "provider": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}"}, "policyholder": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{rootId_Individual}}"}, "dateOfBirth": "$<today-30y:yyyy-MM-dd>", "source": "NONEDI", "transactionType": "PredeterminationOrthodontics", "receivedDate": "$<today:yyyy-MM-dd>", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//{{Dental_policyId}}", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel"}, "TestData_Ortho2": {"entity": {"submittedProcedures": [{"toothCodes": ["6"], "quantity": 1, "ortho": {"downPayment": {"amount": 100, "currency": "USD"}, "appliancePlacedDate": "$<today-2M:yyyy-MM-dd>", "_type": "CapDentalOrthodonticEntity"}, "preauthorization": {"isProcedureAuthorized": true, "_type": "CapDentalPreauthorizationEntity"}, "_type": "CapDentalProcedureEntity"}], "claimData": {"providerFees": [{"_type": "CapDentalProviderFeesEntity"}], "providerDiscount": {"_type": "CapDentalProviderDiscountEntity"}, "dateOfBirth": "$<today-30y:yyyy-MM-dd>", "_type": "CapDentalClaimDataEntity"}, "lossDesc": "For notes", "_modelName": "CapDentalLoss", "_modelVersion": "1", "_modelType": "CapLoss", "_type": "CapDentalDetailEntity"}, "policyId": "capPolicy://CapUP/geroot://UnverifiedPolicy/CapDentalUnverifiedPolicy//{{Dental_policyId}}", "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalLossModel"}}