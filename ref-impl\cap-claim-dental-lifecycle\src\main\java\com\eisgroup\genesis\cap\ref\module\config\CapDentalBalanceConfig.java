/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;


import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.CapDentalBalanceCalculator;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.impl.DefaultCapDentalBalanceCalculator;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.context.annotation.Bean;

/**
 *  Claim Dental balance services configuration.
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalBalanceConfig {

    @Bean
    public CapDentalBalanceCalculator capDentalBalanceCalculator(ModeledTransformationService modeledTransformationService) {
        return new DefaultCapDentalBalanceCalculator(modeledTransformationService,
                ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }
}
