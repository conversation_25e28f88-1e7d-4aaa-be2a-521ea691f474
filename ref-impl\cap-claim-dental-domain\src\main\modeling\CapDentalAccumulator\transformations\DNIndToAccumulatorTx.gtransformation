@EventListener("issue")
@TargetEntityCommand("writeTransaction")
Transformation DNIndToAccumulatorTx {
    Input {
        Ext DNIndividual.DNIndividualPolicySummary as policy
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity
    }

    Var policyLink is Add("capPolicy://PAS/", ToExtLink(policy, "geroot")._uri)
    Attr transactionTimestamp is Now()
    Attr policyURI is policyLink
    Attr customerURI is resolveCustomer(policy).resolvedCustomer
    Attr sourceURI is ToExtLink(policy)._uri
    Var transactionsData is mapInsuredsToTx(policy.individualPackagingDetail.plan.covDef.insuredLink, policy,Null())

    Var covDef is policy.individualPackagingDetail.plan.covDef
    Var insuredLinks is FlatMap(policy.individualPackagingDetail.plan.covDef.insuredLink)
    Var maximumAccumulationPeriod is covDef.dentalMaximumAmount.maximumAccumulationPeriod
    Var deductibleAccumulationPeriod is covDef.deductibleDetails.deductibleAccumulationPeriod
    Var maximumAccumulationTerm is SafeInvoke(maximumAccumulationPeriod, produceTerm(policy, maximumAccumulationPeriod).term)
    Var deductibleAccumulationTerm is SafeInvoke(deductibleAccumulationPeriod, produceTerm(policy, deductibleAccumulationPeriod).term)

    // dental individual deductible
    Var dentalIndividualDeductibleInAmount is First(FlatMap(covDef.deductibleDetails.deductibles[filterIndividualDeductible])).deductibleInNetworkAmount.amount
    Var dentalIndividualDeductibleOutAmount is First(FlatMap(covDef.deductibleDetails.deductibles[filterIndividualDeductible])).deductibleOutOfNetworkAmount.amount

    // dental family deductible
    Var dentalFamilyDeductibleIn is First(FlatMap(covDef.deductibleDetails.deductibles[filterFamilyDeductible])).familyDeductibleInNetwork
    Var dentalFamilyDeductibleInAmount is Ternary(Equals(dentalFamilyDeductibleIn, "Unlimited"), Null(), Ternary(Equals(dentalFamilyDeductibleIn, "2X"), Mult(dentalIndividualDeductibleInAmount, 2), Mult(dentalIndividualDeductibleInAmount, 3)))
    Var dentalFamilyDeductibleOut is First(FlatMap(covDef.deductibleDetails.deductibles[filterFamilyDeductible])).familyDeductibleOutOfNetwork
    Var dentalFamilyDeductibleOutAmount is Ternary(Equals(dentalFamilyDeductibleOut, "Unlimited"), Null(), Ternary(Equals(dentalFamilyDeductibleOut, "2X"), Mult(dentalIndividualDeductibleOutAmount, 2), Mult(dentalIndividualDeductibleOutAmount, 3)))
    Var individualTxType is Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), "DentalPolicy_IndividualDeductible_Lifetime_Dental", "DentalPolicy_IndividualDeductible_Annual_Dental")
    Var familyTxType is Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), "DentalPolicy_FamilyDeductible_Lifetime_Dental", "DentalPolicy_FamilyDeductible_Annual_Dental")

    // Get INN applied to Services type: basic, major and preventive
    Var deductibleINNAppliedToServices is FlatMap(covDef.deductibleDetails.deductibleServicesInNetwork.deductibleAppliesToServicesInNetwork)
    Var dentalIndividualDeductibleINNTx is mapInsuredsToTx(insuredLinks, policy, individualTxType, "INN", dentalIndividualDeductibleInAmount, Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), Null(), deductibleAccumulationTerm), Null())
    Var dentalFamilyDeductibleINNTx is mapInsuredsToTx(insuredLinks, policy, familyTxType, "INN", dentalFamilyDeductibleInAmount, Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), Null(), deductibleAccumulationTerm), Null())

    // Get OON applied to Services type: basic, major and preventive
    Var deductibleOONAppliedToServices is FlatMap(covDef.deductibleDetails.deductibleServicesOutOfNetwork.deductibleAppliesToServicesOutOfNetwork)
    Var dentalIndividualDeductibleOONTx is mapInsuredsToTx(insuredLinks, policy, individualTxType, "OON", dentalIndividualDeductibleOutAmount, Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), Null(), deductibleAccumulationTerm), Null())
    Var dentalFamilyDeductibleOONTx is mapInsuredsToTx(insuredLinks, policy, familyTxType, "OON", dentalFamilyDeductibleOutAmount, Ternary(Equals(deductibleAccumulationPeriod, "Lifetime"), Null(), deductibleAccumulationTerm), Null())

    Var deductibleTx is FlatMap(dentalIndividualDeductibleINNTx, dentalFamilyDeductibleINNTx, dentalIndividualDeductibleOONTx, dentalFamilyDeductibleOONTx)

    // maximums
    Var dentalMaximumInAmount is First(FlatMap(covDef.dentalMaximumAmount.dentalMaximums[filterDentalMaximum])).maximumINAmount.amount
    Var dentalMaximumOutAmount is First(FlatMap(covDef.dentalMaximumAmount.dentalMaximums[filterDentalMaximum])).maximumOONAmount.amount
    Var individualMaximumTxType is Ternary(Equals(maximumAccumulationPeriod, "Lifetime"), "DentalPolicy_IndividualMaximum_Lifetime_Dental", "DentalPolicy_IndividualMaximum_Annual_Dental")

    // Get INN applied to Services type: basic, major and preventive
    Var maximumINNAppliedToServices is FlatMap(covDef.dentalMaximumAmount.maximumServicesInNetwork.maximumAppliesToServicesIN)
    Var dentalMaximumINNTx is mapInsuredsToTx(insuredLinks, policy, individualMaximumTxType, "INN", dentalMaximumInAmount, maximumAccumulationTerm, Null())


    // Get OON applied to Services type: basic, major and preventive
    Var maximumOONAppliedToServices is FlatMap(covDef.dentalMaximumAmount.maximumServicesOutOfNetwork.maximumAppliesToServicesOON)
    Var dentalMaximumOONTx is mapInsuredsToTx(insuredLinks, policy, individualMaximumTxType, "OON", dentalMaximumOutAmount, maximumAccumulationTerm, Null())

    //Implants Maximums AnnualsToTx(participants, policy, "DentalPolicy_ImplantsMaximum_Annual_Implants", "OON", implantsAnnualMaximumOutAmount, maximumAccumulationTerm, implantsAnnualMaximumAppliesTo)
                                  //Implants Maximums Lifetime
    Var implantsAnnualMaximumInAmount is covDef.implantsCoverage[filterImplantsMaximumAnnual].implantsAnnualMaximum.annualMaximumInNetworkAmount.amount
    Var implantsAnnualMaximumOutAmount is covDef.implantsCoverage[filterImplantsMaximumAnnual].implantsAnnualMaximum.annualMaximumOutOfNetworkAmount.amount
    Var implantsAnnualMaximumAppliesTo is covDef.implantsCoverage[filterImplantsMaximumAnnual].implantsAnnualMaximum.annualMaximumAppliesTo
    Var implantsAnnualMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_ImplantsMaximum_Annual_Implants", "INN", implantsAnnualMaximumInAmount, maximumAccumulationTerm, implantsAnnualMaximumAppliesTo)
    Var implantsAnnualMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_ImplantsMaximum_Annual_Implants", "OON", implantsAnnualMaximumOutAmount, maximumAccumulationTerm, implantsAnnualMaximumAppliesTo)
    Var implantsLifetimeMaximumInAmount is covDef.implantsCoverage[filterImplantsMaximumLifetime].implantsLifetimeMaximum.lifetimeMaximumInNetworkAmount.amount
    Var implantsLifetimeMaximumOutAmount is covDef.implantsCoverage[filterImplantsMaximumLifetime].implantsLifetimeMaximum.lifetimeMaximumOutOfNetworkAmount.amount
    Var implantsLifetimeMaximumAppliesTo is covDef.implantsCoverage[filterImplantsMaximumLifetime].implantsLifetimeMaximum.lifetimeMaximumAppliesTo
    Var implantsLifetimeMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_ImplantsMaximum_Lifetime_Implants", "INN", implantsLifetimeMaximumInAmount, Null(), implantsLifetimeMaximumAppliesTo)
    Var implantsLifetimeMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_ImplantsMaximum_Lifetime_Implants", "OON", implantsLifetimeMaximumOutAmount, Null(), implantsLifetimeMaximumAppliesTo)

    Var implantsTx is FlatMap(implantsAnnualMaximumInTx, implantsAnnualMaximumOutTx, implantsLifetimeMaximumInTx, implantsLifetimeMaximumOutTx)

    //Orthodontics Maximums Annual
    Var orthoAnnualMaximumInAmount is covDef.orthodonticCoverage[filterOrthoMaximumAnnual].orthoAnnualMaximum.annualMaximumInNetworkAmount.amount
    Var orthoAnnualMaximumOutAmount is covDef.orthodonticCoverage[filterOrthoMaximumAnnual].orthoAnnualMaximum.annualMaximumOutOfNetworkAmount.amount
    Var orthoAnnualMaximumAppliesTo is covDef.orthodonticCoverage[filterOrthoMaximumAnnual].orthoAnnualMaximum.annualMaximumAppliesTo
    Var orthoAnnualMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoMaximum_Annual_Orthodontics", "INN", orthoAnnualMaximumInAmount, maximumAccumulationTerm, orthoAnnualMaximumAppliesTo)
    Var orthoAnnualMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoMaximum_Annual_Orthodontics", "OON", orthoAnnualMaximumOutAmount, maximumAccumulationTerm, orthoAnnualMaximumAppliesTo)
    //Orthodontics Maximums Lifetime
    Var orthoLifetimeMaximumInAmount is covDef.orthodonticCoverage[filterOrthoMaximumLifetime].orthoLifetimeMaximum.lifetimeMaximumInNetworkAmount.amount
    Var orthoLifetimeMaximumOutAmount is covDef.orthodonticCoverage[filterOrthoMaximumLifetime].orthoLifetimeMaximum.lifetimeMaximumOutOfNetworkAmount.amount
    Var orthoLifetimeMaximumAppliesTo is covDef.orthodonticCoverage[filterOrthoMaximumLifetime].orthoLifetimeMaximum.lifetimeMaximumAppliesTo
    Var orthoLifetimeMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics", "INN", orthoLifetimeMaximumInAmount, Null(), orthoLifetimeMaximumAppliesTo)
    Var orthoLifetimeMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics", "OON", orthoLifetimeMaximumOutAmount, Null(), orthoLifetimeMaximumAppliesTo)
    //Orthodontics Deductible Annual
    Var orthoAnnualDeductibleInAmount is covDef.orthodonticCoverage[filterOrthoDeductibleAnnual].orthoAnnualDeductible.annualDeductibleInNetworkAmount.amount
    Var orthoAnnualDeductibleOutAmount is covDef.orthodonticCoverage[filterOrthoDeductibleAnnual].orthoAnnualDeductible.annualDeductibleOutOfNetworkAmount.amount
    Var orthoAnnualDeductibleAppliesTo is covDef.orthodonticCoverage[filterOrthoDeductibleAnnual].orthoAnnualDeductible.annualDeductibleAppliesTo
    Var orthoAnnualDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoDeductible_Annual_Orthodontics", "INN", orthoAnnualDeductibleInAmount, deductibleAccumulationTerm, orthoAnnualDeductibleAppliesTo)
    Var orthoAnnualDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoDeductible_Annual_Orthodontics", "OON", orthoAnnualDeductibleOutAmount, deductibleAccumulationTerm, orthoAnnualDeductibleAppliesTo)
    //Orthodontics Deductible Lifetime
    Var orthoLifetimeDeductibleInAmount is covDef.orthodonticCoverage[filterOrthoDeductibleLifetime].orthoLifetimeDeductible.lifetimeDeductibleInNetworkAmount.amount
    Var orthoLifetimeDeductibleOutAmount is covDef.orthodonticCoverage[filterOrthoDeductibleLifetime].orthoLifetimeDeductible.lifetimeDeductibleOutOfNetworkAmount.amount
    Var orthoLifetimeDeductibleAppliesTo is covDef.orthodonticCoverage[filterOrthoDeductibleLifetime].orthoLifetimeDeductible.lifetimeDeductibleAppliesTo
    Var orthoLifetimeDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoDeductible_Lifetime_Orthodontics", "INN", orthoLifetimeDeductibleInAmount, Null(), orthoLifetimeDeductibleAppliesTo)
    Var orthoLifetimeDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_OrthoDeductible_Lifetime_Orthodontics", "OON", orthoLifetimeDeductibleOutAmount, Null(), orthoLifetimeDeductibleAppliesTo)

    Var orthoTx is FlatMap(orthoAnnualMaximumInTx, orthoAnnualMaximumOutTx, orthoLifetimeMaximumInTx, orthoLifetimeMaximumOutTx, orthoAnnualDeductibleInTx, orthoAnnualDeductibleOutTx, orthoLifetimeDeductibleInTx, orthoLifetimeDeductibleOutTx)

    //TMJ Maximums Annual
    Var tmjAnnualMaximumInAmount is covDef.tmjCoverage[filterTMJMaximumAnnual].tmjAnnualMaximum.annualMaximumInNetworkAmount.amount
    Var tmjAnnualMaximumOutAmount is covDef.tmjCoverage[filterTMJMaximumAnnual].tmjAnnualMaximum.annualMaximumOutOfNetworkAmount.amount
    Var tmjAnnualMaximumAppliesTo is covDef.tmjCoverage[filterTMJMaximumAnnual].tmjAnnualMaximum.annualMaximumAppliesTo
    Var tmjAnnualMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJMaximum_Annual_TMJ", "INN", tmjAnnualMaximumInAmount, maximumAccumulationTerm, tmjAnnualMaximumAppliesTo)
    Var tmjAnnualMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJMaximum_Annual_TMJ", "OON", tmjAnnualMaximumOutAmount, maximumAccumulationTerm, tmjAnnualMaximumAppliesTo)
    //TMJ Maximums Lifetime
    Var tmjLifetimeMaximumInAmount is covDef.tmjCoverage[filterTMJMaximumLifetime].tmjLifetimeMaximum.lifetimeMaximumInNetworkAmount.amount
    Var tmjLifetimeMaximumOutAmount is covDef.tmjCoverage[filterTMJMaximumLifetime].tmjLifetimeMaximum.lifetimeMaximumOutOfNetworkAmount.amount
    Var tmjLifetimeMaximumAppliesTo is covDef.tmjCoverage[filterTMJMaximumLifetime].tmjLifetimeMaximum.lifetimeMaximumAppliesTo
    Var tmjLifetimeMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJMaximum_Lifetime_TMJ", "INN", tmjLifetimeMaximumInAmount, Null(), tmjLifetimeMaximumAppliesTo)
    Var tmjLifetimeMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJMaximum_Lifetime_TMJ", "OON", tmjLifetimeMaximumOutAmount, Null(), tmjLifetimeMaximumAppliesTo)
    //TMJ Deductible Annual
    Var tmjAnnualDeductibleInAmount is covDef.tmjCoverage[filterTMJDeductibleAnnual].tmjAnnualDeductible.annualDeductibleInNetworkAmount.amount
    Var tmjAnnualDeductibleOutAmount is covDef.tmjCoverage[filterTMJDeductibleAnnual].tmjAnnualDeductible.annualDeductibleOutOfNetworkAmount.amount
    Var tmjAnnualDeductibleAppliesTo is covDef.tmjCoverage[filterTMJDeductibleAnnual].tmjAnnualDeductible.annualDeductibleAppliesTo
    Var tmjAnnualDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJDeductible_Annual_TMJ", "INN", tmjAnnualDeductibleInAmount, deductibleAccumulationTerm, tmjAnnualDeductibleAppliesTo)
    Var tmjAnnualDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJDeductible_Annual_TMJ", "OON", tmjAnnualDeductibleOutAmount, deductibleAccumulationTerm, tmjAnnualDeductibleAppliesTo)
    //TMJ Deductible Lifetime
    Var tmjLifetimeDeductibleInAmount is covDef.tmjCoverage[filterTMJDeductibleLifetime].tmjLifetimeDeductible.lifetimeDeductibleInNetworkAmount.amount
    Var tmjLifetimeDeductibleOutAmount is covDef.tmjCoverage[filterTMJDeductibleLifetime].tmjLifetimeDeductible.lifetimeDeductibleOutOfNetworkAmount.amount
    Var tmjLifetimeDeductibleAppliesTo is covDef.tmjCoverage[filterTMJDeductibleLifetime].tmjLifetimeDeductible.lifetimeDeductibleAppliesTo
    Var tmjLifetimeDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJDeductible_Lifetime_TMJ", "INN", tmjLifetimeDeductibleInAmount, Null(), tmjLifetimeDeductibleAppliesTo)
    Var tmjLifetimeDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_TMJDeductible_Lifetime_TMJ", "OON", tmjLifetimeDeductibleOutAmount, Null(), tmjLifetimeDeductibleAppliesTo)

    Var tmjTx is FlatMap(tmjAnnualMaximumInTx, tmjAnnualMaximumOutTx, tmjLifetimeMaximumInTx, tmjLifetimeMaximumOutTx, tmjAnnualDeductibleInTx, tmjAnnualDeductibleOutTx, tmjLifetimeDeductibleInTx, tmjLifetimeDeductibleOutTx)

    //Cosmetic Maximums Annual
    Var cosmeticAnnualMaximumInAmount is covDef.cosmeticServicesCoverage[filterCosmeticMaximumAnnual].cosmeticAnnualMaximum.annualMaximumInNetworkAmount.amount
    Var cosmeticAnnualMaximumOutAmount is covDef.cosmeticServicesCoverage[filterCosmeticMaximumAnnual].cosmeticAnnualMaximum.annualMaximumOutOfNetworkAmount.amount
    Var cosmeticAnnualMaximumAppliesTo is covDef.cosmeticServicesCoverage[filterCosmeticMaximumAnnual].cosmeticAnnualMaximum.annualMaximumAppliesTo
    Var cosmeticAnnualMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticMaximum_Annual_CosmeticServices", "INN", cosmeticAnnualMaximumInAmount, maximumAccumulationTerm, cosmeticAnnualMaximumAppliesTo)
    Var cosmeticAnnualMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticMaximum_Annual_CosmeticServices", "OON", cosmeticAnnualMaximumOutAmount, maximumAccumulationTerm, cosmeticAnnualMaximumAppliesTo)
    //Cosmetic Maximums Lifetime
    Var cosmeticLifetimeMaximumInAmount is covDef.cosmeticServicesCoverage[filterCosmeticMaximumLifetime].cosmeticLifetimeMaximum.lifetimeMaximumInNetworkAmount.amount
    Var cosmeticLifetimeMaximumOutAmount is covDef.cosmeticServicesCoverage[filterCosmeticMaximumLifetime].cosmeticLifetimeMaximum.lifetimeMaximumOutOfNetworkAmount.amount
    Var cosmeticLifetimeMaximumAppliesTo is covDef.cosmeticServicesCoverage[filterCosmeticMaximumLifetime].cosmeticLifetimeMaximum.lifetimeMaximumAppliesTo
    Var cosmeticLifetimeMaximumInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices", "INN", cosmeticLifetimeMaximumInAmount, Null(), cosmeticLifetimeMaximumAppliesTo)
    Var cosmeticLifetimeMaximumOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices", "OON", cosmeticLifetimeMaximumOutAmount, Null(), cosmeticLifetimeMaximumAppliesTo)
    //Cosmetic Deductible Annual
    Var cosmeticAnnualDeductibleInAmount is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleAnnual].cosmeticAnnualDeductible.annualDeductibleInNetworkAmount.amount
    Var cosmeticAnnualDeductibleOutAmount is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleAnnual].cosmeticAnnualDeductible.annualDeductibleOutOfNetworkAmount.amount
    Var cosmeticAnnualDeductibleAppliesTo is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleAnnual].cosmeticAnnualDeductible.annualDeductibleAppliesTo
    Var cosmeticAnnualDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticDeductible_Annual_CosmeticServices", "INN", cosmeticAnnualDeductibleInAmount, deductibleAccumulationTerm, cosmeticAnnualDeductibleAppliesTo)
    Var cosmeticAnnualDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticDeductible_Annual_CosmeticServices", "OON", cosmeticAnnualDeductibleOutAmount, deductibleAccumulationTerm, cosmeticAnnualDeductibleAppliesTo)
    //Cosmetic Deductible Lifetime
    Var cosmeticLifetimeDeductibleInAmount is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleLifetime].cosmeticLifetimeDeductible.lifetimeDeductibleInNetworkAmount.amount
    Var cosmeticLifetimeDeductibleOutAmount is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleLifetime].cosmeticLifetimeDeductible.lifetimeDeductibleOutOfNetworkAmount.amount
    Var cosmeticLifetimeDeductibleAppliesTo is covDef.cosmeticServicesCoverage[filterCosmeticDeductibleLifetime].cosmeticLifetimeDeductible.lifetimeDeductibleAppliesTo
    Var cosmeticLifetimeDeductibleInTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticDeductible_Lifetime_CosmeticServices", "INN", cosmeticLifetimeDeductibleInAmount, Null(), cosmeticLifetimeDeductibleAppliesTo)
    Var cosmeticLifetimeDeductibleOutTx is mapInsuredsToTx(insuredLinks, policy, "DentalPolicy_CosmeticDeductible_Lifetime_CosmeticServices", "OON", cosmeticLifetimeDeductibleOutAmount, Null(), cosmeticLifetimeDeductibleAppliesTo)

    Var cosmeticTx is FlatMap(cosmeticAnnualMaximumInTx, cosmeticAnnualMaximumOutTx, cosmeticLifetimeMaximumInTx, cosmeticLifetimeMaximumOutTx, cosmeticAnnualDeductibleInTx, cosmeticAnnualDeductibleOutTx, cosmeticLifetimeDeductibleInTx, cosmeticLifetimeDeductibleOutTx)

    Var transactionData is FlatMap(deductibleTx, dentalMaximumINNTx, dentalMaximumOONTx, implantsTx, orthoTx, tmjTx, cosmeticTx)
    Attr data is transactionData[filterNonNullAmount]


    Producer resolveCustomer(policy){
        Var insuredsRef is First(policy.insureds[filterByRelationshipToPrimaryInsuredCd]).insuredInfo._ref
        Var parties is policy.parties
        Var combinedInsureds is combineParties(parties, insuredsRef)
        Var filteredInsuredPartyInfo is First(combinedInsureds[filterByInsuredInfoRef].party.partyInfo)
        Attr resolvedCustomer is filteredInsuredPartyInfo.personBaseDetails.registryTypeId
    }

    // helper for resolving correct person party
    Producer combineParties(party, insuredInfoRef) {
        Attr party is party
        Attr insInfoRef is insuredInfoRef
    }

    Producer produceTerm(policy, accumulationPeriod) {
        Var period is producePeriod(policy.termDetails.termEffectiveDate, Null())
        Attr term is SafeInvoke(accumulationPeriod, First(CalculateAccumulatorTerm(policy.termDetails.termEffectiveDate, period, Ternary(Equals(accumulationPeriod, "Lifetime"), "BenefitYear", accumulationPeriod))))

        Producer producePeriod(startDate, endDate) {
           Attr startDate is startDate
           Attr endDate is endDate
       }
    }

    Producer mapInsuredsToTx(insuredLink, policy, txType, networkType, txAmount, accumulatorTerm, appliedToServices) {
       Var filterAppliedToServices is Ternary((Equals(txType, Root().individualTxType) || Equals(txType, Root().familyTxType)), Ternary(Equals(networkType, "INN"), Root().deductibleINNAppliedToServices, Root().deductibleOONAppliedToServices),
                                            Ternary(Equals(txType, Root().individualMaximumTxType) , Ternary(Equals(networkType, "INN"), Root().maximumINNAppliedToServices, Root().maximumOONAppliedToServices),
                                               FlatMap(appliedToServices)))
       Attr type is txType
       Attr amount is txAmount
       Attr party is Ternary(Equals(txType, Root().familyTxType), Null(), resolveParty(insuredLink, policy).resolvedParty)
       Attr resource is Ternary(Equals(txType, Root().familyTxType), createExtLink(policyLink), Null())
       Attr transactionDate is Now()
       Attr extension is New() {
           Attr term is Super().accumulatorTerm
           Attr networkType is Super().networkType
           Attr appliedToServices is Super().filterAppliedToServices
           Attr _type is "JsonType"
      }
    }

    Producer resolveParty(insuredLink, policy) {
       // iterate through insureds with insuredRef
        Var insuredRef is insuredLink._ref
        Var insureds is policy.insureds
        Var combinedInsureds is combineInsureds(insureds, insuredRef)
        Var filteredInsuredInfoRef is First(combinedInsureds[filterByInsuredRef]).insuredDetail.insuredInfo._ref

        // iterate through parties with filteredInsuredInfoRef
        Var parties is policy.parties
        Var combinedParties is combineParties(parties, filteredInsuredInfoRef)
        Var filteredPartyInfo is First(combinedParties[filterByInsuredInfoRef].party.partyInfo)
        Attr resolvedParty is createExtLink(filteredPartyInfo.personBaseDetails.registryTypeId)
    }

    Producer createExtLink(uri) {
        Attr _uri is uri
    }

    // helper for resolving correct insured
    Producer combineInsureds(insured, insuredRef) {
        Attr insuredDetail is insured
        Attr insRef is insuredRef
    }

    Filter filterByRelationshipToPrimaryInsuredCd{
        relationshipToPrimaryInsuredCd == "Self"
    }

    Filter filterMaximumBasic {
        maximumAppliesToServicesIN == "Basic"
    }

    Filter filterMaximumMajor {
        maximumAppliesToServicesIN == "Major"
    }

    Filter filterMaximumPreventive {
        maximumAppliesToServicesIN == "Preventive"
    }

    Filter filterIndividualDeductible {
        deductibleType == "IndividualAnnual" || deductibleType == "IndividualLifetime"
    }

    Filter filterFamilyDeductible {
        deductibleType == "FamilyAnnual" || deductibleType == "FamilyLifetime"
    }

    Filter filterAnnualMaximum {
        maximumType == "AnnualMaximum"
    }

    Filter filterByInsuredRef {
        insRef == insuredDetail._key.id
    }

    Filter filterByInsuredInfoRef {
        insInfoRef == party._key.id
    }

    Filter filterNonNullAmount {
        !Equals(amount, Null())
    }

    Filter filterImplantsMaximumAnnual {
        implantsMaximumType == "Annual" || implantsMaximumType == "AnnualAndLifetime"
    }

    Filter filterImplantsMaximumLifetime {
        implantsMaximumType == "Lifetime" || implantsMaximumType == "AnnualAndLifetime"
    }

    Filter filterOrthoMaximumAnnual {
        orthoMaximumType == "Annual" || orthoMaximumType == "AnnualAndLifetime"
    }

    Filter filterOrthoMaximumLifetime {
        orthoMaximumType == "Lifetime" || orthoMaximumType == "AnnualAndLifetime"
    }

    Filter filterOrthoDeductibleAnnual {
        orthoDeductibleType == "Annual" || orthoDeductibleType == "AnnualAndLifetime"
    }

    Filter filterOrthoDeductibleLifetime {
        orthoDeductibleType == "Lifetime" || orthoDeductibleType == "AnnualAndLifetime"
    }

    Filter filterTMJMaximumAnnual {
        tmjMaximumType == "Annual" || tmjMaximumType == "AnnualAndLifetime"
    }

    Filter filterTMJMaximumLifetime {
        tmjMaximumType == "Lifetime" || tmjMaximumType == "AnnualAndLifetime"
    }

    Filter filterTMJDeductibleAnnual {
        tmjDeductibleType == "Annual" || tmjDeductibleType == "AnnualAndLifetime"
    }

    Filter filterTMJDeductibleLifetime {
        tmjDeductibleType == "Lifetime" || tmjDeductibleType == "AnnualAndLifetime"
    }

    Filter filterCosmeticMaximumAnnual {
        cosmeticMaximumType == "Annual" || cosmeticMaximumType == "AnnualAndLifetime"
    }

    Filter filterCosmeticMaximumLifetime {
        cosmeticMaximumType == "Lifetime" || cosmeticMaximumType == "AnnualAndLifetime"
    }

    Filter filterCosmeticDeductibleAnnual {
        cosmeticDeductibleType == "Annual" || cosmeticDeductibleType == "AnnualAndLifetime"
    }

    Filter filterCosmeticDeductibleLifetime {
        cosmeticDeductibleType == "Lifetime" || cosmeticDeductibleType == "AnnualAndLifetime"
    }

    Filter filterDentalMaximum {
        (maximumType == "AnnualMaximum" && (Root().maximumAccumulationPeriod == "BenefitYear" || Root().maximumAccumulationPeriod == "CalendarYear")) ||
        (maximumType == "LifetimeMaximum" && Root().maximumAccumulationPeriod == "Lifetime")
    }
}