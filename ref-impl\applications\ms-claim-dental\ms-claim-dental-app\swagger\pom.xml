<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <artifactId>ms-claim-dental-app-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>ms-claim-dental-swagger</artifactId>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <classesDirectory>${project.basedir}/../target/classes</classesDirectory>
                    <includes>
                        <include>facade-schema/*</include>
                    </includes>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- Fake dependency just for ordering purposes -->
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>ms-claim-dental-app</artifactId>
            <scope>provided</scope>
            <version>${project.version}</version>
        </dependency>
    </dependencies>

</project>
