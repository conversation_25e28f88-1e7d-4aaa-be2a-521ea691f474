@Library("ci-config@master")
@Library("ci-library@master")
@Library("cd-library@master") _

def ENVIRONMENTS = k8s.environments()

pipeline {
    agent any

    parameters {
        gitParameter(name: 'SOURCE_BRANCH', branchFilter: 'origin/(.*)', type: 'PT_BRANCH', sortMode: 'ASCENDING_SMART', defaultValue: 'master',
                listSize: '1', quickFilterEnabled: true, description: '<b style="color:red">Required:</b> SCM branch to create release from, e.g. "release-23.16"')
        string(name: 'RELEASE_VERSION', defaultValue: '', description: '<b style="color:gray">Optional:</b>  Artifact version to publish, e.g. "23.16-RC1"<tr/><i style="color:gray">Default behaviour: Creates staging version using current timestamp.</i>')
        choice(name: 'ENVIRONMENT', choices: ENVIRONMENTS, description: 'Environment name to deploy component and run etcs tests')
        booleanParam(name: 'SKIP_DEPLOY', defaultValue: false, description: 'Set to true if deployment must be skipped')
        string(name: 'EXTERNAL_VERSIONS', defaultValue: '', description: 'External versions. Example: "comp1:1.1.1,comp2:1.1.2,..."')
        //Logic is missing, param should be commented out
        //booleanParam(name: 'REUPLOAD', defaultValue: false, description: '<b style="color:gray">Optional:</b> Whether to allow artifact to be re-uploaded')
    }

    options {
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        lock resource: env.JOB_NAME + '-' + params.SOURCE_BRANCH
        timeout(time: 3, unit: 'HOURS')
    }

    stages {
        stage('Release DXP Claim Benefits Dental') {
            steps {
                script {
                    try {
                        build(
                            job: "dxp-claim-benefits-dental-release",
                            parameters: [
                                string(name: 'SOURCE_BRANCH', value: params.SOURCE_BRANCH),
                                string(name: 'RELEASE_VERSION', value: params.RELEASE_VERSION),                                
                                string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                booleanParam(name: 'SKIP_DEPLOY', value: params.SKIP_DEPLOY)
                            ],
                            propagate: true,
                            wait: true
                        )
                    } catch (Exception e) {
                        echo "[WARN]: ${STAGE_NAME} ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                    }
                }
            }
        }

        stage('Release MS Claim Benefits Dental') {
            steps {
                script {
                    try {
                        build(
                            job: "ms-claim-benefits-dental-release",
                            parameters: [
                                string(name: 'SOURCE_BRANCH', value: params.SOURCE_BRANCH),
                                string(name: 'RELEASE_VERSION', value: params.RELEASE_VERSION),                                
                                string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                booleanParam(name: 'SKIP_DEPLOY', value: params.SKIP_DEPLOY)
                            ],
                            propagate: true,
                            wait: true
                        )
                    } catch (Exception e) {
                        echo "[WARN]: ${STAGE_NAME} ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                    }
                }
            }
        }

        stage('Release UI Claim Benefits Dental') {
            steps {
                script {
                    try {
                        build(
                            job: "ui-claim-benefits-dental-release",
                            parameters: [
                                string(name: 'SOURCE_BRANCH', value: params.SOURCE_BRANCH),
                                string(name: 'RELEASE_VERSION', value: params.RELEASE_VERSION),                                
                                string(name: 'ENVIRONMENT', value: params.ENVIRONMENT),
                                booleanParam(name: 'SKIP_DEPLOY', value: params.SKIP_DEPLOY),
                                string(name: 'EXTERNAL_VERSIONS', value: params.EXTERNAL_VERSIONS)
                            ],
                            propagate: true,
                            wait: true
                        )
                    } catch (Exception e) {
                        echo "[WARN]: ${STAGE_NAME} ${e.message}"
                        unstable(message: "${STAGE_NAME} is unstable")
                    }
                }
            }
        }
    }

    post {
        always {
            cleanWs()
        }
    }
}