/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment.input;

import javax.money.MonetaryAmount;

import com.eisgroup.genesis.cap.financial.command.payment.input.CapPaymentUpdateInput;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.google.gson.JsonObject;

/**
 * {@link CapDentalPaymentEntity} update input
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentUpdateInput extends CapPaymentUpdateInput {

    private static final String PAYMENT_NET_AMOUNT = "paymentNetAmount";

    public CapDentalPaymentUpdateInput(JsonObject original) {
        super(original);
    }

    public MonetaryAmount getPaymentNetAmount() {
        return getMonetaryAmount(PAYMENT_NET_AMOUNT);
    }
}
