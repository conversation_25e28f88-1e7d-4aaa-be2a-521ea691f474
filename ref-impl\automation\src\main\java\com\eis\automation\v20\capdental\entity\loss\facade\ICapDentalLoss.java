/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade;

import com.eis.automation.v20.cap.entity.caseIntake.lifeIntakeLoss.facade.impl.action.CommonKeyAction;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossCloseModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossReopenModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.platform.common.action.CommonLoadAction;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public interface ICapDentalLoss {

    PostModelAction<ICapDentalLossModel, ICapDentalLossModel> init();

    PostModelAction<ICapDentalLossModel, ICapDentalLossModel> update();

    PostModelAction<ICapDentalLossModel, ICapDentalLossModel> updateDraft();

    CommonKeyAction<ICapDentalLossModel> submit();

    CommonLoadAction<ICapDentalLossModel> entities();

    PostModelAction<ICapLossCloseModel, ICapDentalLossModel> close();

    PostModelAction<ICapLossReopenModel, ICapDentalLossModel> reopen();
}
