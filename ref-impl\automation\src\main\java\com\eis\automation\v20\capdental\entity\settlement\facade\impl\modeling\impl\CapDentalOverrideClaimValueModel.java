/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.databinding.serializer.annotation.JsonMonetarySerializeFormat;
import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.common.modeling.impl.PeriodModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.IPeriodModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalOverrideClaimValueModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalOverrideClaimValueModel extends TypeModel implements ICapDentalOverrideClaimValueModel {

    private Money overridePaymentInterestAmount;
    private String overridePaymentInterestState;
    private Integer overrideBasicWaitingPeriod;
    private Integer overridePreventiveWaitingPeriod;
    private Integer overrideOrthoWaitingPeriod;
    private Integer overrideGracePeriod;
    private IPeriodModel overrideEligibilityPeriod;
    private Integer overridePaymentInterestDays;
    private Integer overrideMajorWaitingPeriod;
    private Integer overrideLateEntrantWaitingPeriod;
    private String overrideStudentDependentStatus;
    private String overrideFeeSchedule;

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getOverridePaymentInterestAmount() {
        return overridePaymentInterestAmount;
    }

    public void setOverridePaymentInterestAmount(Money overridePaymentInterestAmount) {
        this.overridePaymentInterestAmount = overridePaymentInterestAmount;
    }

    public String getOverridePaymentInterestState() {
        return overridePaymentInterestState;
    }

    public void setOverridePaymentInterestState(String overridePaymentInterestState) {
        this.overridePaymentInterestState = overridePaymentInterestState;
    }

    public Integer getOverrideBasicWaitingPeriod() {
        return overrideBasicWaitingPeriod;
    }

    public void setOverrideBasicWaitingPeriod(Integer overrideBasicWaitingPeriod) {
        this.overrideBasicWaitingPeriod = overrideBasicWaitingPeriod;
    }

    public Integer getOverridePreventiveWaitingPeriod() {
        return overridePreventiveWaitingPeriod;
    }

    public void setOverridePreventiveWaitingPeriod(Integer overridePreventiveWaitingPeriod) {
        this.overridePreventiveWaitingPeriod = overridePreventiveWaitingPeriod;
    }

    public Integer getOverrideOrthoWaitingPeriod() {
        return overrideOrthoWaitingPeriod;
    }

    public void setOverrideOrthoWaitingPeriod(Integer overrideOrthoWaitingPeriod) {
        this.overrideOrthoWaitingPeriod = overrideOrthoWaitingPeriod;
    }

    public Integer getOverrideGracePeriod() {
        return overrideGracePeriod;
    }

    public void setOverrideGracePeriod(Integer overrideGracePeriod) {
        this.overrideGracePeriod = overrideGracePeriod;
    }

    @JsonSerialize(as = PeriodModel.class)
    public IPeriodModel getOverrideEligibilityPeriod() {
        return overrideEligibilityPeriod;
    }

    @JsonDeserialize(as = PeriodModel.class)
    public void setOverrideEligibilityPeriod(IPeriodModel overrideEligibilityPeriod) {
        this.overrideEligibilityPeriod = overrideEligibilityPeriod;
    }

    public Integer getOverridePaymentInterestDays() {
        return overridePaymentInterestDays;
    }

    public void setOverridePaymentInterestDays(Integer overridePaymentInterestDays) {
        this.overridePaymentInterestDays = overridePaymentInterestDays;
    }

    public Integer getOverrideMajorWaitingPeriod() {
        return overrideMajorWaitingPeriod;
    }

    public void setOverrideMajorWaitingPeriod(Integer overrideMajorWaitingPeriod) {
        this.overrideMajorWaitingPeriod = overrideMajorWaitingPeriod;
    }

    public Integer getOverrideLateEntrantWaitingPeriod() {
        return overrideLateEntrantWaitingPeriod;
    }

    public void setOverrideLateEntrantWaitingPeriod(Integer overrideLateEntrantWaitingPeriod) {
        this.overrideLateEntrantWaitingPeriod = overrideLateEntrantWaitingPeriod;
    }

    public String getOverrideStudentDependentStatus() {
        return overrideStudentDependentStatus;
    }

    public void setOverrideStudentDependentStatus(String overrideStudentDependentStatus) {
        this.overrideStudentDependentStatus = overrideStudentDependentStatus;
    }

    public String getOverrideFeeSchedule() {
        return overrideFeeSchedule;
    }

    public void setOverrideFeeSchedule(String overrideFeeSchedule) {
        this.overrideFeeSchedule = overrideFeeSchedule;
    }
}
