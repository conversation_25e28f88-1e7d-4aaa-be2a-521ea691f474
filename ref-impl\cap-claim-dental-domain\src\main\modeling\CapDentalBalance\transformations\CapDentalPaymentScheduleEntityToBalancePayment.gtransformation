Transformation CapDetanlScheduledPaymentToBalancePayment {
    Input {
        CapDentalPaymentSchedule.CapDentalScheduledPaymentEntity as payment
    }
    Output {
        CapDentalBalance.CapDentalBalancePaymentEntity
    }

    Attr paymentDetails is payment.paymentDetails
    Attr paymentNetAmount is payment.paymentNetAmount
    Attr messages is payment.messages
    Attr creationDate is payment.creationDate
    Attr paymentNumber is payment.paymentNumber
    Attr direction is payment.direction
    Attr _modelName is "CapDentalBalance"
}