<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="initial_adjudicate_settlement" name="Initial settlement adjudication" isExecutable="true">
    <startEvent id="startAdjudicateClaim" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="initSettlement" name="call initSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalSettlement" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="initSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'claimLossIdentification': {'_uri': '${_uri}'},'entity': {'_modelName': 'CapDentalSettlement','_modelVersion': '1','_modelType': 'CapSettlement','_type': 'CapDentalSettlementDetailEntity'}}" target="payload"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="settlementCreationResult"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" sourceType="string" target="settlementKey"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="revisionNo" sourceType="string" target="settlementRevisionNo"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="initialAdjudicateSettlement" name="adjudicateSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${settlementKey}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalSettlement" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="adjudicateSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="adjudicationResult"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_uri" target="settlementURI"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" target="settlementKey"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelName" target="settlementModelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelVersion" target="settlementModelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="rootId" sourceType="string" target="rootId"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <endEvent id="end"></endEvent>
    <sequenceFlow id="toAdjudicateSettlement" sourceRef="initSettlement" targetRef="initialAdjudicateSettlement"></sequenceFlow>
    <sequenceFlow id="toEnd" sourceRef="initialAdjudicateSettlement" targetRef="end"></sequenceFlow>
    <exclusiveGateway id="sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C"></exclusiveGateway>
    <sequenceFlow id="sid-632CD5FE-C00D-471D-9AD2-0A1BC8181FA6" sourceRef="startAdjudicateClaim" targetRef="sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C"></sequenceFlow>
    <callActivity id="callRefreshSettlement" name="refresh settlement" calledElement="refreshSettlementCall" flowable:calledElementType="key" flowable:fallbackToDefaultTenant="false">
      <extensionElements>
        <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
        <flowable:in source="settlementModelName" target="_modelName"></flowable:in>
        <flowable:in source="settlementModelVersion" target="_modelVersion"></flowable:in>
        <flowable:out source="settlementPayload" target="settlementCreationResult"></flowable:out>
        <flowable:out source="_key" target="settlementKey"></flowable:out>
      </extensionElements>
    </callActivity>
    <sequenceFlow id="sid-0397FF07-4AF4-4BFA-9F75-BE47F30C037B" sourceRef="callRefreshSettlement" targetRef="initialAdjudicateSettlement"></sequenceFlow>
    <sequenceFlow id="sid-8C3F91D2-34AB-4418-B7ED-EC3B56F0F163" name="settlement exists" sourceRef="sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C" targetRef="callRefreshSettlement">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{settlementURI != null}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-480EE909-E92D-464A-8116-BF0CC06F6405" name="settlement not exist" sourceRef="sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C" targetRef="initSettlement">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[#{settlementURI == null}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_initial_adjudicate_settlement">
    <bpmndi:BPMNPlane bpmnElement="initial_adjudicate_settlement" id="BPMNPlane_initial_adjudicate_settlement">
      <bpmndi:BPMNShape bpmnElement="startAdjudicateClaim" id="BPMNShape_startAdjudicateClaim">
        <omgdc:Bounds height="30.0" width="30.0" x="90.0" y="185.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initSettlement" id="BPMNShape_initSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="405.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initialAdjudicateSettlement" id="BPMNShape_initialAdjudicateSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="105.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="990.0" y="131.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C" id="BPMNShape_sid-AD60ECFD-EB6D-46B1-8EB8-0D930FEE7D8C">
        <omgdc:Bounds height="40.0" width="40.0" x="210.0" y="180.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="callRefreshSettlement" id="BPMNShape_callRefreshSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="405.0" y="225.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-480EE909-E92D-464A-8116-BF0CC06F6405" id="BPMNEdge_sid-480EE909-E92D-464A-8116-BF0CC06F6405" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="246.49562500000002" y="196.5357142857143"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="157.34844097995546"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toAdjudicateSettlement" id="BPMNEdge_toAdjudicateSettlement" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="504.94999999995434" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="720.0" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toEnd" id="BPMNEdge_toEnd" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="819.9499999999273" y="145.0"></omgdi:waypoint>
        <omgdi:waypoint x="990.0" y="145.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-632CD5FE-C00D-471D-9AD2-0A1BC8181FA6" id="BPMNEdge_sid-632CD5FE-C00D-471D-9AD2-0A1BC8181FA6" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="119.94999883049303" y="200.0"></omgdi:waypoint>
        <omgdi:waypoint x="210.0" y="200.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8C3F91D2-34AB-4418-B7ED-EC3B56F0F163" id="BPMNEdge_sid-8C3F91D2-34AB-4418-B7ED-EC3B56F0F163" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="1.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="244.35536234902122" y="205.59281250000004"></omgdi:waypoint>
        <omgdi:waypoint x="405.0" y="264.63247863247864"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0397FF07-4AF4-4BFA-9F75-BE47F30C037B" id="BPMNEdge_sid-0397FF07-4AF4-4BFA-9F75-BE47F30C037B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="504.95000000000005" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="265.0"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="184.95"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>