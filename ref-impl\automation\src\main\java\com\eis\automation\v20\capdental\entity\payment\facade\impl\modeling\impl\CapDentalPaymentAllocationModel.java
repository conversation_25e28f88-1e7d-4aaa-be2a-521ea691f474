/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.impl.CapBasePaymentAllocationModel;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.impl.CapPaymentLossInfoModel;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.interf.ICapPaymentLossInfoModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentAllocationDentalDetailsModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentAllocationModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentAllocationPayableItemModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentAllocationModel extends CapBasePaymentAllocationModel implements ICapDentalPaymentAllocationModel {

    private ICapPaymentLossInfoModel allocationLossInfo;
    private ICapDentalPaymentAllocationPayableItemModel allocationPayableItem;
    private ICapDentalPaymentAllocationDentalDetailsModel allocationDentalDetails;

    @JsonSerialize(as = CapPaymentLossInfoModel.class)
    public ICapPaymentLossInfoModel getAllocationLossInfo() {
        return allocationLossInfo;
    }

    @JsonDeserialize(as = CapPaymentLossInfoModel.class)
    public void setAllocationLossInfo(ICapPaymentLossInfoModel allocationLossInfo) {
        this.allocationLossInfo = allocationLossInfo;
    }

    @JsonSerialize(as = CapDentalPaymentAllocationPayableItemModel.class)
    public ICapDentalPaymentAllocationPayableItemModel getAllocationPayableItem() {
        return allocationPayableItem;
    }

    @JsonDeserialize(as = CapDentalPaymentAllocationPayableItemModel.class)
    public void setAllocationPayableItem(ICapDentalPaymentAllocationPayableItemModel allocationPayableItem) {
        this.allocationPayableItem = allocationPayableItem;
    }

    @JsonSerialize(as = CapDentalPaymentAllocationDentalDetailsModel.class)
    public ICapDentalPaymentAllocationDentalDetailsModel getAllocationDentalDetails() {
        return allocationDentalDetails;
    }

    @JsonDeserialize(as = CapDentalPaymentAllocationDentalDetailsModel.class)
    public void setAllocationDentalDetails(ICapDentalPaymentAllocationDentalDetailsModel allocationDentalDetails) {
        this.allocationDentalDetails = allocationDentalDetails;
    }
}
