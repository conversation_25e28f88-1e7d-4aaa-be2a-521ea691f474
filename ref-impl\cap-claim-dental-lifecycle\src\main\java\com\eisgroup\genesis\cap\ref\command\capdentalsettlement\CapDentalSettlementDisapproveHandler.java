/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.cap.adjudication.command.CapDisapproveSettlementHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;

/**
 * The command that disapproves adjudicated settlement
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalSettlementDisapproveHandler extends CapDisapproveSettlementHandler<IdentifierRequest, CapSettlement> {

}
