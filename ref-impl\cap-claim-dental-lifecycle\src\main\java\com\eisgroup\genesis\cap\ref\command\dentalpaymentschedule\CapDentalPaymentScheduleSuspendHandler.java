/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.cap.financial.command.paymentschedule.CapPaymentScheduleSuspendHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;

/**
 * Command for suspending Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.9
 */
@Modifying
public class CapDentalPaymentScheduleSuspendHandler extends CapPaymentScheduleSuspendHandler<IdentifierRequest, CapDentalPaymentScheduleEntity> {

}