/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.loss;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossCloseModel;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.javamoney.moneta.Money;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa.common.DateUtils.getCurrentDate;
import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.*;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimReasonCd.*;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.PREDETERMINATION_ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalPayeeType.SERVICE_PROVIDER;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.*;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.CAPDNREASON_LOOKUP_CODE_MUST_BE_ONE_OF_THEESE_VALUES_DENIED_PAID_CANCELED_PREDETERMINED;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.policybenefits.constant.PolicyConstant.Currency.USD;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalClaimReasons extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    private IndividualCustomerModel individualCustomer;
    @Autowired
    private IProviderService provider;
    @Lazy
    @Autowired
    private IPartyRoleService partyRole;
    private IIndividualProviderModel individualProvider;
    private ICapDentalPolicyInfoModel createdDentalPolicy;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IClaimSearchService claimSearch;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-161947", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_Close_Reason() {
        createUpPolicy();
        ICapDentalLossModel initDentalClaim = initDentalClaim(D0330, false, ACTUAL_SERVICES);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        ICapDentalLossModel openDentalClaim = capDentalLoss.loadDentalLoss(submitDentalClaim, OPEN);

        //Step 1
        ICapLossCloseModel dentalClaimCloseModel = modelUtils.create(capDentalLoss.getTestData("Close", "TestData"));
        dentalClaimCloseModel.setKey(openDentalClaim.getKey());
        dentalClaimCloseModel.setReasonCd("Approved");
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().close().perform(b -> b.setModel(dentalClaimCloseModel)));
        FailureAssertion.assertThat(failure).hasError(CAPDNREASON_LOOKUP_CODE_MUST_BE_ONE_OF_THEESE_VALUES_DENIED_PAID_CANCELED_PREDETERMINED);

        //Close Settlement - cannot close dentalClaim while Settlement is in Open state
        ICapDentalSettlementModel dentalSettlement = claimSearch.searchDentalSettlement(openDentalClaim, 1).get(0);
        ICapDentalSettlementModel closeSettlement = (
                ICapDentalSettlementModel) capDentalSettlement.getFacade().close().perform(k -> k.setModel(dentalSettlement.getKey()));
        assertThat(closeSettlement.getState()).isEqualTo(CLOSED);

        //Step 2
        ICapLossCloseModel dentalClaimCloseModel2 = modelUtils.create(capDentalLoss.getTestData("Close", "TestData"));
        dentalClaimCloseModel2.setKey(openDentalClaim.getKey());
        dentalClaimCloseModel2.setReasonCd(CANCELED);
        ICapDentalLossModel closeDentalClaim = capDentalLoss.getFacade().close().perform(b -> b.setModel(dentalClaimCloseModel2));
        assertThat(closeDentalClaim.getState()).isEqualTo(CLOSED);
        assertThat(closeDentalClaim.getReasonCd()).isEqualTo(CANCELED);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-162167", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_SetReasonOnClaimSubmit() {
        createUpPolicy();

        ICapDentalLossModel initDentalClaim1 = initDentalClaim(D0110, false, ACTUAL_SERVICES);
        ICapDentalLossModel initDentalClaim2 = initDentalClaim(D0110, true, PREDETERMINATION_ACTUAL_SERVICES);
        ICapDentalLossModel initDentalClaim3 = initDentalClaim(D0330, false, ACTUAL_SERVICES);

        //Step1
        verifyDentalClaim(initDentalClaim1, CLOSED, DENIED);

        //Step2
        verifyDentalClaim(initDentalClaim2, CLOSED, PREDETERMINED);

        //Step3
        verifyDentalClaim(initDentalClaim3, OPEN, null);
    }

    private ICapDentalLossModel initDentalClaim(String procedureCode, Boolean predetInd, String transactionType) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(procedureCode);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setSubmittedFee(Money.of(100, USD));
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setPredetInd(predetInd);
        dentalClaimModel.getEntity().getClaimData().setPayeeType(SERVICE_PROVIDER);
        dentalClaimModel.getEntity().getClaimData().setReceivedDate(getCurrentDate());
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setDateOfService(getCurrentDate());
        dentalClaimModel.getEntity().getClaimData().setTransactionType(transactionType);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        return capDentalLoss.initDentalLoss(dentalClaimModel);
    }

    private void verifyDentalClaim(ICapDentalLossModel dentalClaim, String status, String reason) {
        ICapDentalLossModel submitDentalClaim1 = capDentalLoss.submitDentalLoss(dentalClaim);
        ICapDentalLossModel closeDentalClaim1 = capDentalLoss.loadDentalLoss(submitDentalClaim1, status);
        assertThat(closeDentalClaim1.getReasonCd()).isEqualTo(reason);
    }

    private ICapDentalPolicyInfoModel createUpPolicy() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoWrapperModel dentalPolicyModel = capPolicy.createDentalUnverifiedPolicyModel(individualCustomer);
        ((ICapDentalPolicyInfoModel) dentalPolicyModel.getEntity()).setPolicyPaidToDate(getCurrentDate().plusDays(1));
        ((ICapDentalPolicyInfoModel) dentalPolicyModel.getEntity()).setPolicyPaidToDateWithGracePeriod(getCurrentDate().plusDays(1));
        createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(dentalPolicyModel);
        return createdDentalPolicy;
    }
}

