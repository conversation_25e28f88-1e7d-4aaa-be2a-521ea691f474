/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapLookupValidator;
import com.eisgroup.genesis.cap.loss.command.ClaimLossSubStatusHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossSubStatusInitInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

public class CapDentalLossSubStatusHandler extends ClaimLossSubStatusHandler<CapDentalLossSubStatusInitInput, CapLoss>  {

    @Autowired
    private CapLookupValidator lookupValidator;

    private static final String CAP_DN_SUBSTATUS_LOOKUP_CODE = "CapDNSubStatus";
    private static final String CAP_DN_SUBSTATUS_AVAILABLE_ON_STATE = "availableOnState";

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalLossSubStatusInitInput input, @Nonnull CapLoss loadedEntity) {
        return Streamable.concat(super.validateAsync(input, loadedEntity), validateStatusWithState(input.getLossSubStatusCd(), loadedEntity));
    }

    private Streamable<ErrorHolder> validateStatusWithState(String lossSubStatusCd, CapLoss entity) {
        return lookupValidator.validateAdditionalLookupField(CAP_DN_SUBSTATUS_LOOKUP_CODE, lossSubStatusCd, CAP_DN_SUBSTATUS_AVAILABLE_ON_STATE, entity.getState());
    }
}
