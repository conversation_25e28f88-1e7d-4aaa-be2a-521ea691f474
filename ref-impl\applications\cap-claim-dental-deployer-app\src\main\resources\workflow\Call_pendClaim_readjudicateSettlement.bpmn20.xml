<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="pendclaimReadjudicateSettlement" name="Call pendClaim readjudicateSettlement" isExecutable="true">
    <startEvent id="startAdjudicateClaim" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="openClaimPendLoss" name="call pendLoss" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelName}" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="pendLoss" target="commandName"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="lossPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="openClaimAdjudicateSettlement" name="adjudicateSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${settlementKey}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalSettlement" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="adjudicateSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="adjudicationResult"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_uri" target="settlementURI"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" target="settlementKey"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelName" target="settlementModelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelVersion" target="settlementVersion"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <endEvent id="end"></endEvent>
    <sequenceFlow id="toEnd" sourceRef="openClaimAdjudicateSettlement" targetRef="end"></sequenceFlow>
    <serviceTask id="constructAccumTxPayload" name="Construct Accumulator Tx Payload" flowable:parallelInSameTransaction="true"  flowable:async="false" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('readjudicateSettlementAccumulatorTxInput').serviceGroup('CapDentalSettlement').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{"body":{"_key" : { "rootId": "${execution.getVariable('settlementKeyJson').rootId}","revisionNo": ${execution.getVariable('settlementKeyJson').revisionNo - 1}}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[txInput]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <serviceTask id="writeAccumTransaction" name="Write Accumulator Transaction" flowable:async="false" flowable:type="send-event">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'entity': ${txInput.body.success.output}}" target="payload"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapAccumulatorTransaction" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="writeTransaction" target="commandName"></flowable:eventInParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-6F895796-7149-4728-92E3-8E4566955793" sourceRef="constructAccumTxPayload" targetRef="writeAccumTransaction"></sequenceFlow>
    <sequenceFlow id="sid-2894F192-23A9-4C1F-8E63-D5DB46BEF104" sourceRef="writeAccumTransaction" targetRef="openClaimPendLoss"></sequenceFlow>
    <sequenceFlow id="sid-27A7C30D-2AAD-4B38-AD18-37EBBF29F366" sourceRef="openClaimPendLoss" targetRef="openClaimAdjudicateSettlement"></sequenceFlow>
    <scriptTask id="string_to_json" name="Transform Settlement key to Json" flowable:autoStoreVariables="false" flowable:async="false" scriptFormat="javascript">
      <script>
        <![CDATA[
          key = JSON.parse(settlementKey);
          execution.setTransientVariable("settlementKeyJson", key );
        ]]>
      </script>
    </scriptTask>
    <sequenceFlow id="sid-F29F7F30-4728-4639-9587-2B689ED96A49" sourceRef="startAdjudicateClaim" targetRef="string_to_json"></sequenceFlow>
    <sequenceFlow id="sid-53F40851-3AA0-4618-A6D7-C5248D0DBCB4" sourceRef="string_to_json" targetRef="constructAccumTxPayload"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_pendclaimReadjudicateSettlement">
    <bpmndi:BPMNPlane bpmnElement="pendclaimReadjudicateSettlement" id="BPMNPlane_pendclaimReadjudicateSettlement">
      <bpmndi:BPMNShape bpmnElement="startAdjudicateClaim" id="BPMNShape_startAdjudicateClaim">
        <omgdc:Bounds height="30.0" width="30.0" x="150.0" y="162.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="openClaimPendLoss" id="BPMNShape_openClaimPendLoss">
        <omgdc:Bounds height="80.0" width="100.0" x="675.0" y="137.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="openClaimAdjudicateSettlement" id="BPMNShape_openClaimAdjudicateSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="813.5" y="139.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="958.5" y="165.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="constructAccumTxPayload" id="BPMNShape_constructAccumTxPayload">
        <omgdc:Bounds height="80.0" width="100.0" x="390.0" y="138.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="writeAccumTransaction" id="BPMNShape_writeAccumTransaction">
        <omgdc:Bounds height="80.0" width="100.0" x="525.0" y="137.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="string_to_json" id="BPMNShape_string_to_json">
        <omgdc:Bounds height="80.0" width="100.0" x="240.0" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-6F895796-7149-4728-92E3-8E4566955793" id="BPMNEdge_sid-6F895796-7149-4728-92E3-8E4566955793" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="489.94999999999675" y="178.12962962962962"></omgdi:waypoint>
        <omgdi:waypoint x="524.9999999999964" y="177.87"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-27A7C30D-2AAD-4B38-AD18-37EBBF29F366" id="BPMNEdge_sid-27A7C30D-2AAD-4B38-AD18-37EBBF29F366" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="774.949999999999" y="178.04097472924187"></omgdi:waypoint>
        <omgdi:waypoint x="813.4999999999993" y="178.45848375451263"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-F29F7F30-4728-4639-9587-2B689ED96A49" id="BPMNEdge_sid-F29F7F30-4728-4639-9587-2B689ED96A49" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="179.94999098868064" y="177.5"></omgdi:waypoint>
        <omgdi:waypoint x="210.0" y="177.5"></omgdi:waypoint>
        <omgdi:waypoint x="210.0" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="240.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toEnd" id="BPMNEdge_toEnd" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="913.4499999999999" y="179.0"></omgdi:waypoint>
        <omgdi:waypoint x="958.5" y="179.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2894F192-23A9-4C1F-8E63-D5DB46BEF104" id="BPMNEdge_sid-2894F192-23A9-4C1F-8E63-D5DB46BEF104" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="624.9499999998727" y="177.5"></omgdi:waypoint>
        <omgdi:waypoint x="675.0" y="177.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-53F40851-3AA0-4618-A6D7-C5248D0DBCB4" id="BPMNEdge_sid-53F40851-3AA0-4618-A6D7-C5248D0DBCB4" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="339.95000000000005" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="365.0" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="365.0" y="178.5"></omgdi:waypoint>
        <omgdi:waypoint x="390.0" y="178.5"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
