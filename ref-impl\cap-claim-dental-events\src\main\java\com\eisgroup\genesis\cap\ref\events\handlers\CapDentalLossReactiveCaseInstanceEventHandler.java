/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.handlers;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.eisgroup.genesis.cap.ref.events.CapDentalCaseInstanceReactivation;
import com.eisgroup.genesis.events.handler.StreamEventHandler;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.lifecycle.events.CommandExecutedEvent;
import com.eisgroup.genesis.streams.CommandExecutedMessageMetadata;
import com.eisgroup.genesis.streams.MessageMetadata;


/**
 * Event handler which reactivates Case Instance after reopenLoss command
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalLossReactiveCaseInstanceEventHandler implements StreamEventHandler<CommandExecutedEvent> {
    private static final Logger logger = LoggerFactory.getLogger(CapDentalLossReactiveCaseInstanceEventHandler.class);
    private static final String REOPEN_COMMAND = "reopenLoss";
    private static final String DENTAL_LOSS_MODEL_NAME = "CapDentalLoss";

    private final EntityLinkBuilderRegistry entityLinkBuilderRegistry;
    private final CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation;

    public CapDentalLossReactiveCaseInstanceEventHandler(EntityLinkBuilderRegistry entityLinkBuilderRegistry, CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation) {
        this.entityLinkBuilderRegistry = entityLinkBuilderRegistry;
        this.capDentalCaseInstanceReactivation = capDentalCaseInstanceReactivation;
    }

    @Override
    public void handle(CommandExecutedEvent event) {
        capDentalCaseInstanceReactivation.reactivateCaseInstance(createLinkFromOutput(event.getOutput()))
            .onError(err -> logger.error("Failed to index based on metadata", err))
            .get();
        return ;

    }

    @Override
    public boolean supports(MessageMetadata message) {
        if (message instanceof CommandExecutedMessageMetadata) {
            return REOPEN_COMMAND.equals(((CommandExecutedMessageMetadata) message).getCommandName()) &&
                    DENTAL_LOSS_MODEL_NAME.equals(((CommandExecutedMessageMetadata) message).getModelName());
        }
        return false;
    }

    private <T> EntityLink<T> createLinkFromOutput(JsonEntity output) {
        return entityLinkBuilderRegistry.<T>getByType(output.getClass()).createLink((T) output);
    }
}
