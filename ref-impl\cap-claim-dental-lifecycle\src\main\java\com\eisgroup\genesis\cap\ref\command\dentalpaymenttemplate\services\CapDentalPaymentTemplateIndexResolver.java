/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplateindex.CapDentalPaymentTemplateIdxEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

import java.util.List;

/**
 * {@link CapDentalPaymentTemplateIdxEntity} entities resolver.
 *
 * <AUTHOR>
 * @since 22.3
 */
public interface CapDentalPaymentTemplateIndexResolver {

    /**
     * Resolves {@link CapDentalPaymentTemplateIdxEntity} entities
     *
     * @param originSource     {@link com.eisgroup.genesis.factory.model.dentalloss.DentalLoss} link
     * @param applicableStates applicable states for {@link CapDentalPaymentTemplateIdxEntity#getState()}
     * @return resolved {@link CapDentalPaymentTemplateIdxEntity}
     */
    Streamable<CapDentalPaymentTemplateIdxEntity> resolvePaymentTemplateIndex(EntityLink<RootEntity> originSource, List<String> applicableStates);

}
