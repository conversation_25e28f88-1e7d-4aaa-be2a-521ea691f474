// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "ModelType"
  ],
  "moduleType":"CapPaymentTemplate",
  "name":"CapDentalPaymentTemplate",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalPaymentTemplateEntity"
  },
  "storeDeterminants":[
    "ModelName"
  ],
  "types":{
    "CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity":{
      "attributes":{
        "accumulatorAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines accumulator amount."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines accumulator amount.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"accumulatorAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "accumulatorType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator type."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator type.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"accumulatorType",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategories":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure categories accumulator applies to."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure categories accumulator applies to.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"appliesToProcedureCategories",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategory":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure category accumulator applies to."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure category accumulator applies to.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            }
          },
          "name":"appliesToProcedureCategory",
          "type":{
            "type":"STRING"
          }
        },
        "networkType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Network type for accumulator."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Network type for accumulator.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"networkType",
          "type":{
            "type":"STRING"
          }
        },
        "renewalType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Renewal type for accumulator."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Renewal type for accumulator.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"renewalType",
          "type":{
            "type":"STRING"
          }
        },
        "term":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator Term."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator Term.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"term",
          "type":{
            "type":"Term"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for accumulator details for allocation."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for accumulator details for allocation.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationTemplateDentalDetailsEntity":{
      "attributes":{
        "accumulatorDetails":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template allocation accumulator details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template allocation accumulator details.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"accumulatorDetails",
          "type":{
            "type":"CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity"
          }
        },
        "dateOfService":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Date of service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Date of service.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"dateOfService",
          "type":{
            "type":"DATE"
          }
        },
        "grossBenefitAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Gross amount calculated during claim adjudication process."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Gross amount calculated during claim adjudication process.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"grossBenefitAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "orthoFrequencyCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment frequency for Ortho payments."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNFrequency"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment frequency for Ortho payments.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNFrequency"
            }
          },
          "name":"orthoFrequencyCd",
          "type":{
            "type":"STRING"
          }
        },
        "orthoMonthQuantity":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Number of Months of Treatment."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Number of Months of Treatment.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"orthoMonthQuantity",
          "type":{
            "type":"NUMBER"
          }
        },
        "paymentAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "NOT USED"
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"NOT USED",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            }
          },
          "name":"paymentAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "procedureID":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Related Settlement Result entry's procedure ID."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Related Settlement Result entry's procedure ID.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"procedureID",
          "type":{
            "type":"STRING"
          }
        },
        "receivedDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The date the claim is received."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The date the claim is received.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"receivedDate",
          "type":{
            "type":"DATE"
          }
        },
        "transactionTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental claim transaction type."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental claim transaction type.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"transactionTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "patient":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "URI to the patient."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"URI to the patient.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"patient",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for dental details for allocation."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for dental details for allocation.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationTemplateDentalDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationTemplateEntity":{
      "attributes":{
        "allocationDentalDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template allocation dental lob details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template allocation dental lob details.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"allocationDentalDetails",
          "type":{
            "type":"CapDentalPaymentAllocationTemplateDentalDetailsEntity"
          }
        },
        "allocationLobCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Line Of Business."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Line Of Business.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"allocationLobCd",
          "type":{
            "type":"STRING"
          }
        },
        "allocationLossInfo":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template allocation loss details"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template allocation loss details",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"allocationLossInfo",
          "type":{
            "type":"CapDentalPaymentAllocationTemplateLossInfoEntity"
          }
        },
        "allocationPayeeDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template allocation payee details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template allocation payee details.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"allocationPayeeDetails",
          "type":{
            "type":"CapDentalPaymentAllocationTemplatePayeeDetailsEntity"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines payment allocation amount for payment template."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines payment allocation amount for payment template.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapPaymentAllocationTemplate"
        }
      ],
      "baseTypes":[
        "CapPaymentAllocationTemplate"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim settlement."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for payment template allocation details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for payment template allocation details.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationTemplateEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationTemplateLossInfoEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines Claim information for a payment template."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines Claim information for a payment template.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapPaymentAllocationTemplateLossInfo"
        }
      ],
      "baseTypes":[
        "CapPaymentAllocationTemplateLossInfo"
      ],
      "extLinks":{
        "lossSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim Loss."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim Loss.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"lossSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for template allocation loss details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for template allocation loss details.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationTemplateLossInfoEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationTemplatePayeeDetailsEntity":{
      "attributes":{
        "payeeTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation payee type."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation payee type.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"payeeTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines payee details for payment template."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines payee details for payment template.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapPaymentAllocationTemplatePayeeDetails"
        }
      ],
      "baseTypes":[
        "CapPaymentAllocationTemplatePayeeDetails"
      ],
      "extLinks":{
        "payee":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the CEM."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the CEM.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"payee",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for template allocation payee details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for template allocation payee details.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationTemplatePayeeDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentDetailsTemplateEntity":{
      "attributes":{
        "paymentAllocationTemplates":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template allocations details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template allocations details.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"paymentAllocationTemplates",
          "type":{
            "type":"CapDentalPaymentAllocationTemplateEntity"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapPaymentDetailsTemplate"
        }
      ],
      "baseTypes":[
        "CapPaymentDetailsTemplate"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for payment template details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for payment template details.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentDetailsTemplateEntity",
      "references":{
      }
    },
    "CapDentalPaymentTemplateEntity":{
      "attributes":{
        "creationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The date when the template was created."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The date when the template was created.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"creationDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment Template state in the lifecycle."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment Template state in the lifecycle.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Main object for the CAP Payment Template Domain."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      [
                        "CapPaymentTemplate"
                      ]
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Main object for the CAP Payment Template Domain.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
                "CapPaymentTemplate"
              ]
            },
            "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "modelTypes":[
                  ]
                }
              },
              "parents":[
              ],
              "typeName":"RootEntity"
            }
          ],
          "typeName":"CapPaymentTemplate"
        }
      ],
      "baseTypes":[
        "CapPaymentTemplate"
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "URI to related Claim."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"URI to related Claim.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Main object for the CAP Payment Template Domain."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Main object for the CAP Payment Template Domain.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentTemplateEntity",
      "references":{
        "paymentDetailsTemplate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Object for payment template details."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Object for payment template details.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Embedded":{
            }
          },
          "name":"paymentDetailsTemplate",
          "type":"CapDentalPaymentDetailsTemplateEntity"
        }
      }
    },
    "CapDentalPaymentTemplateSchedulerRulesInputEntity":{
      "attributes":{
        "paymentTemplate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment Scheduling rules input."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment Scheduling rules input.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"paymentTemplate",
          "type":{
            "type":"CapDentalPaymentTemplateEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Object for Payment Scheduling rules input."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Object for Payment Scheduling rules input.",
          "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentTemplateSchedulerRulesInputEntity",
      "references":{
      }
    },
    "CapDentalStartBuildPaymentTemplateFlowOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Attribute requred for Automated Build Payment Template Flow."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Attribute requred for Automated Build Payment Template Flow.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "settlements":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Attribute requred for Automated Build Payment Template Flow."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Attribute requred for Automated Build Payment Template Flow.",
              "messageBundle":"domain-messages/CapDentalPaymentTemplate/description"
            }
          },
          "name":"settlements",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalStartBuildPaymentTemplateFlowOutput",
      "references":{
      }
    },
    "Term":{
      "attributes":{
        "effectiveDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"effectiveDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "expirationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"expirationDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"Term"
        }
      ],
      "baseTypes":[
        "Term"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"Term",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalPaymentTemplate {
    export type Variations = never


    export class CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity.name) }
        readonly accumulatorAmount?: Money
        readonly accumulatorType?: string
        readonly appliesToProcedureCategories: string[] = []
        readonly appliesToProcedureCategory?: string
        readonly networkType?: string
        readonly renewalType?: string
        readonly term?: Term
    }

    export class CapDentalPaymentAllocationTemplateDentalDetailsEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalPaymentAllocationTemplateDentalDetailsEntity.name) }
        readonly accumulatorDetails: CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity[] = []
        readonly dateOfService?: Date
        readonly grossBenefitAmount?: Money
        readonly orthoFrequencyCd?: string
        readonly orthoMonthQuantity?: number
        readonly patient?: MAPI.ExternalLink
        readonly paymentAmount?: Money
        readonly procedureID?: string
        readonly receivedDate?: Date
        readonly transactionTypeCd?: string
    }

    export class CapDentalPaymentAllocationTemplateEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentAllocationTemplate {
    constructor() { super(CapDentalPaymentAllocationTemplateEntity.name) }
        readonly allocationDentalDetails?: CapDentalPaymentAllocationTemplateDentalDetailsEntity
        readonly allocationLobCd?: string
        readonly allocationLossInfo?: CapDentalPaymentAllocationTemplateLossInfoEntity
        readonly allocationPayeeDetails?: CapDentalPaymentAllocationTemplatePayeeDetailsEntity
        readonly allocationSource?: MAPI.ExternalLink
    }

    export class CapDentalPaymentAllocationTemplateLossInfoEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentAllocationTemplateLossInfo {
    constructor() { super(CapDentalPaymentAllocationTemplateLossInfoEntity.name) }
        readonly lossSource?: MAPI.ExternalLink
    }

    export class CapDentalPaymentAllocationTemplatePayeeDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentAllocationTemplatePayeeDetails {
    constructor() { super(CapDentalPaymentAllocationTemplatePayeeDetailsEntity.name) }
        readonly payee?: MAPI.ExternalLink
        readonly payeeTypeCd?: string
    }

    export class CapDentalPaymentDetailsTemplateEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentDetailsTemplate, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentDetailsTemplateEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentTemplate'
        readonly _modelType: string = 'CapPaymentTemplate'
        readonly _modelVersion?: string = '1'
        readonly paymentAllocationTemplates: CapDentalPaymentAllocationTemplateEntity[] = []
    }

    export class CapDentalPaymentTemplateEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentTemplate, MAPI.RootBusinessType {
    constructor() { super(CapDentalPaymentTemplateEntity.name) }
        readonly _modelName: string = 'CapDentalPaymentTemplate'
        readonly _modelType: string = 'CapPaymentTemplate'
        readonly _modelVersion?: string = '1'
        readonly creationDate?: Date
        readonly originSource?: MAPI.ExternalLink
        readonly paymentDetailsTemplate?: CapDentalPaymentDetailsTemplateEntity
        readonly state?: string
    }

    export class CapDentalPaymentTemplateSchedulerRulesInputEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalPaymentTemplateSchedulerRulesInputEntity.name) }
        readonly paymentTemplate?: CapDentalPaymentTemplateEntity
    }

    export class CapDentalStartBuildPaymentTemplateFlowOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalStartBuildPaymentTemplateFlowOutput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly settlements: MAPI.ExternalLink[] = []
    }

    export class Term extends MAPI.BusinessEntity implements BusinessTypes.Term {
    constructor() { super(Term.name) }
        readonly effectiveDate?: Date
        readonly expirationDate?: Date
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity, ()=> new CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationTemplateDentalDetailsEntity, ()=> new CapDentalPaymentAllocationTemplateDentalDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationTemplateEntity, ()=> new CapDentalPaymentAllocationTemplateEntity)
    factory.registerEntity(CapDentalPaymentAllocationTemplateLossInfoEntity, ()=> new CapDentalPaymentAllocationTemplateLossInfoEntity)
    factory.registerEntity(CapDentalPaymentAllocationTemplatePayeeDetailsEntity, ()=> new CapDentalPaymentAllocationTemplatePayeeDetailsEntity)
    factory.registerEntity(CapDentalPaymentDetailsTemplateEntity, ()=> new CapDentalPaymentDetailsTemplateEntity)
    factory.registerEntity(CapDentalPaymentTemplateEntity, ()=> new CapDentalPaymentTemplateEntity)
    factory.registerEntity(CapDentalPaymentTemplateSchedulerRulesInputEntity, ()=> new CapDentalPaymentTemplateSchedulerRulesInputEntity)
    factory.registerEntity(CapDentalStartBuildPaymentTemplateFlowOutput, ()=> new CapDentalStartBuildPaymentTemplateFlowOutput)
    factory.registerEntity(Term, ()=> new Term)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}