<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.0">
  <process id="claim.dental.bpmn.readjudication" name="Claim Dental Process - Readjudication" isExecutable="true">
    <callActivity id="call.adjudication.subprocess" name="Adjudication sub-process" calledElement="claim.dental.bpmn.adjudication.subprocess" flowable:calledElementType="key" flowable:fallbackToDefaultTenant="false">
      <extensionElements>
        <flowable:in sourceExpression="${_key}" target="_key"></flowable:in>
        <flowable:in sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:in>
        <flowable:in sourceExpression="${_modelName}" target="_modelName"></flowable:in>
        <flowable:out sourceExpression="${settlementAdjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
      </extensionElements>
    </callActivity>
    <endEvent id="dental_readjudication_end"></endEvent>
    <sequenceFlow id="sid-7FAF8E45-D84B-4229-89A5-7DE58D3A3663" sourceRef="dental_readjudication_start" targetRef="call.adjudication.subprocess"></sequenceFlow>
    <sequenceFlow id="sid-19C2B596-332E-48B6-9ECE-4CA7E0342F9E" sourceRef="call.adjudication.subprocess" targetRef="dental_readjudication_end"></sequenceFlow>
    <startEvent id="dental_readjudication_start" flowable:initiator="initiator" flowable:formFieldValidation="true"></startEvent>
    <textAnnotation id="sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE">
      <text>Input params:
Settlement
*  _key
* _modelName
* _modelVersion
* _uri</text>
    </textAnnotation>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_claim.dental.bpmn.readjudication">
    <bpmndi:BPMNPlane bpmnElement="claim.dental.bpmn.readjudication" id="BPMNPlane_claim.dental.bpmn.readjudication">
      <bpmndi:BPMNShape bpmnElement="call.adjudication.subprocess" id="BPMNShape_call.adjudication.subprocess">
        <omgdc:Bounds height="80.0" width="100.0" x="627.25" y="135.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dental_readjudication_end" id="BPMNShape_dental_readjudication_end">
        <omgdc:Bounds height="28.0" width="28.0" x="840.0" y="161.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE" id="BPMNShape_sid-F971EC1D-EFBE-4CBD-BF15-A97FFC3124FE">
        <omgdc:Bounds height="100.0" width="119.0" x="270.0" y="125.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="dental_readjudication_start" id="BPMNShape_dental_readjudication_start">
        <omgdc:Bounds height="30.0" width="30.0" x="480.25" y="160.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-19C2B596-332E-48B6-9ECE-4CA7E0342F9E" id="BPMNEdge_sid-19C2B596-332E-48B6-9ECE-4CA7E0342F9E" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="727.1999999999239" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="840.0" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-7FAF8E45-D84B-4229-89A5-7DE58D3A3663" id="BPMNEdge_sid-7FAF8E45-D84B-4229-89A5-7DE58D3A3663" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="510.1999994481881" y="175.0"></omgdi:waypoint>
        <omgdi:waypoint x="627.2499999999737" y="175.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>