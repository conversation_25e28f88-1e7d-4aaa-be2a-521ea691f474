tzappa-v20.loglevel=debug
tzappa.retry.repeat.timeout=60000
#time.uselocal=false

####### REST #######
#Nightly
#istf.test.customprops=app/nightly.properties

#Automation
istf.test.customprops=app/automation.properties

#Preprod
#istf.test.customprops=app/preprod.properties

#Demo
#istf.test.customprops=app/demo-internal.properties
#istf.test.customprops=app/demo-external.properties

#locally
#istf.test.customprops=app/localhost.properties

#######   ANALYTICS PROPERTIES   #######
talisker.enabled=true
# Put your token taken from Test Analytics UI
talisker.accessToken=PuTYouRToKeNth3r3FromTestAnaLyTiCsUi
# Set false to send your results to Sandbox
talisker.run.isProper=false
talisker.retry.maxCount=0
#talisker.run.mergeId=
# With this tag you can filter runs in TA3 like this: https://dev2eisgenta02.sjclab.exigengroup.com/sandbox?tag=yourName
talisker.run.tags=yourName

# Demo TA
#talisker.url=https://eis-demo-genta03.eqxdev.exigengroup.com

####################################

#tzappa.rest.client.read.timeout=900000
tzappa.rest.logging.prettyprint=true

#Available values: HEADERS_ONLY, ERROR_RESPONSE_PAYLOAD_TEXT, REQUEST_AND_ERROR_RESPONSE_PAYLOAD_TEXT, REQUEST_AND_RESPONSE_PAYLOAD_TEXT, PAYLOAD_ANY
tzappa.rest.logging.verbosity=REQUEST_AND_RESPONSE_PAYLOAD_TEXT
tzappa.rest.logging.max.entitysize=102400
