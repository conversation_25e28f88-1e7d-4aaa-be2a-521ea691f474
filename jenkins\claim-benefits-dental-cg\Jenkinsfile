@Library("ci-config@master")
@Library("ci-library@master")
@Library("automation-library")
import PodTemplateGenerator

// Update initial pipeline status in gitlab MR
updateGitlabCommitStatus(name: 'CG pipeline', state: 'running')
// Creates slack thread so that pipeline update messages are spawned as child ones for this thread
def SLACK_THREAD = slack.createThread(message: "CG started $gitlabSourceRepoName $gitlabSourceBranch")
// Cancels previous CG run for the same MR on update/trigger
cancelConcurentCG()

//Map with component name as a key and component build subpath as a value
def repoComponentsMap = [
    "dxp-claim-benefits-dental": "dxp/",
    "ui-claim-benefits-dental": "ui/",
    "ms-claim-benefits-dental": "ref-impl/"
]

pipeline {
    triggers {
        gitlab(
            triggerOnPush: false,
            triggerOnMergeRequest: false,
            triggerOnNoteRequest: true,
            noteRegex: "[jJ]enkins.*build.*",
            branchFilterType: "NameBasedFilter",
            includeBranchesSpec: "**",
            secretToken: "ffdb8b41776f0f8b102f81524f837bf1"                    
        )
    }

    options {
        skipStagesAfterUnstable()
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        timeout(time: 2, unit: 'HOURS') 
    }

    agent any

    stages {
        stage('Feature freeze validation') {
            steps {
                script {
                    featureFreezeValidator()
                }
            }
        }

        stage('Resolve MR changes') {
            steps {
                script {
                    env.changedComponents = [].toSet()
                    changedComponents = changesResolverMR.verifyChanges(repoComponentsMap: repoComponentsMap)
                    println("Changed components are $changedComponents")
                }
            }
        }
        stage('Execute component CG') {
            steps {
                script {
                    def branches = changesResolverMR.createJobRuns(changedComponentsSet: changedComponents)
                    (branches.keySet() as List).collate(3).each{
                        parallel branches.subMap(it)
                    }
                }
            }
        }
    }

    post {
        failure {
            script {
                updateGitlabCommitStatus(name: 'CG pipeline', state: 'failed')
                slack.updateStage(thread: SLACK_THREAD, status: 'failed')
            }
        }
        success {
            script {
                updateGitlabCommitStatus(name: 'CG pipeline', state: 'success')
                slack.updateStage(thread: SLACK_THREAD, status: 'success')
            }
        }
        unstable {
            script {
                updateGitlabCommitStatus(name: 'CG pipeline', state: 'failed')
                slack.updateStage(thread: SLACK_THREAD, status: 'failed')
            }
        }
        aborted {
            script {
                updateGitlabCommitStatus(name: 'CG pipeline', state: 'canceled')
                slack.updateStage(thread: SLACK_THREAD, status: 'canceled')
            }
        }
    } 
}