package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceCalculateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalBalanceConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalBalanceLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalBalance";

    private static final String MODEL_TYPE = "CapBalance";

    private CapDentalBalanceLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalBalanceLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalBalanceInitHandler.class,
                CapDentalBalanceUpdateHandler.class,
                CapDentalBalanceCalculateHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalBalanceConfig.class), equalTo(true));
    }
}
