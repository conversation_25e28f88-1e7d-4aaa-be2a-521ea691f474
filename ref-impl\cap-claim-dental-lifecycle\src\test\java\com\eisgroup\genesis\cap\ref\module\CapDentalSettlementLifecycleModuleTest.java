package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementAdjudicateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementApproveHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementDisapproveHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementReadjudicateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.CapDentalSettlementRefreshHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalSettlementLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalSettlementLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalSettlement";

    private static final String MODEL_TYPE = "CapSettlement";

    private CapDentalSettlementLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalSettlementLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalSettlementAdjudicateHandler.class,
                CapDentalSettlementApproveHandler.class,
                CapDentalSettlementCloseHandler.class,
                CapDentalSettlementDisapproveHandler.class,
                CapDentalSettlementReadjudicateHandler.class,
                CapDentalSettlementInitHandler.class,
                CapDentalSettlementRefreshHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(module.getModelVersion(), equalTo("1"));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalSettlementLifecycleConfig.class), equalTo(true));
    }
}
