/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.handler;

import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalOutboundPaymentEventHandler;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.events.StreamEvent;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.streams.MessageMetadata;
import com.eisgroup.genesis.streams.TypeInfo;
import com.google.gson.JsonObject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CapDentalOutboundPaymentEventHandlerTest {

    @InjectMocks
    private CapDentalOutboundPaymentEventHandler eventHandler;
    @Mock
    private CommandPublisher commandPublisher;

    @ParameterizedTest
    @CsvSource({
            "OutboundPaymentFailedEvent, true",
            "OutboundPaymentCancelledEvent, true",
            "test.OutboundPaymentPaidEvent, true",
            "OutboundPaymentCreatedEvent, true",
            "com.eisgroup.genesis.OutboundPaymentPaidEvent, true",
            "IntegratedOutboundPaymentPaidEvent, false",
            "com.eisgroup.genesis.IntegratedOutboundPaymentPaidEvent, false",
            "Random, false"
    })
    void testOutboundPaymentSupportedEvents(String eventType, boolean isSupported) {
        //given
        MessageMetadata message = mock(MessageMetadata.class);
        TypeInfo typeInfo = mock(TypeInfo.class);
        when(message.getMessageType()).thenReturn(typeInfo);
        when(typeInfo.getTypeName()).thenReturn(eventType);

        //when
        boolean supports = eventHandler.supports(message);

        //then
        assertThat(supports, equalTo(isSupported));
    }

    @Test
    void shouldProcessCapDomainPHPayments() {
        //given
        TestStreamEvent event = new TestStreamEvent("CLAIM");
        //when
        eventHandler.handle(event);
        //then
        verify(commandPublisher, times(1)).publish(any(Command.class), anyString());
    }

    @Test
    void shouldNotProcessOtherDomainPHPayments() {
        //given
        TestStreamEvent event = new TestStreamEvent("BILLING");
        //when
        eventHandler.handle(event);
        //then
        verify(commandPublisher, times(0)).publish(any(Command.class), anyString());

    }

    @Test
    void shouldNotProcessNonPhPayment() {
        //given
        TestStreamEvent event = new TestStreamEvent(new JsonObject());
        //when
        eventHandler.handle(event);
        //then
        verify(commandPublisher, times(0)).publish(any(Command.class), anyString());

    }

    private static class TestStreamEvent extends AbstractJsonEntity implements StreamEvent {

        public TestStreamEvent(JsonObject jsonObject) {
            super(new JsonObject());
            setChildObject("random", jsonObject);
            setString("name", "TestEvent");
            setChildObject("original", getOriginal());
        }

        public TestStreamEvent(String domainName) {
            super(new JsonObject());
            JsonObject phPayment = new JsonObject();
            phPayment.addProperty("domainName", domainName);
            phPayment.addProperty("domainCorrelationId", "capMock://CapDentalPaymentDefinition/ce6bd39c-e0b4-4b25-9c3c-28f5d155e38e/1");
            phPayment.addProperty("paymentMethodType", "testPaymentMethodType");
            setChildObject("payment", phPayment);
            setString("name", "TestEvent");
            setChildObject("original", getOriginal());
        }

        @Override
        public String getName() {
            return "TestName";
        }

        @Override
        public LocalDateTime getOccurredAtTime() {
            return LocalDateTime.now();
        }
    }
}
