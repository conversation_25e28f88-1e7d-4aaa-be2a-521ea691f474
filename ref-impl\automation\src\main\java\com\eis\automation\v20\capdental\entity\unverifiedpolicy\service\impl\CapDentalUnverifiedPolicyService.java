/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.ICapUnverifiedPolicy;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.service.impl.CapUnverifiedPolicyService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.ICapDentalUnverifiedPolicy;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Lazy
@Component("capDentalUnverifiedPolicy")
public class CapDentalUnverifiedPolicyService extends CapUnverifiedPolicyService implements ICapDentalUnverifiedPolicyService {

    @Autowired
    private ICapDentalUnverifiedPolicy capDentalUnverifiedPolicy;
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdDentalUPLocation;
    private TestData tdSpecificDentalUPLocation;

    public CapDentalUnverifiedPolicyService(ICapDentalUnverifiedPolicy capDentalUnverifiedPolicy) {
        super(capDentalUnverifiedPolicy);
    }

    @Override
    public ICapUnverifiedPolicy getFacade() {
        return capDentalUnverifiedPolicy;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdDentalUPLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificDentalUPLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdDentalUPLocation = testDataProvider.getJSONTestData("/capdental/unverifiedpolicy");
        tdSpecificDentalUPLocation = testDataProvider.getJSONTestData("/capdental/unverifiedpolicy/specific");
    }

    public ICapDentalPolicyInfoWrapperModel createDentalUnverifiedPolicyModel(ICustomerModel customerModel) {
        ICapDentalPolicyInfoWrapperModel capDentalPolicyModel = modelUtils.create(getTestData("Write", "TestData"));
        if (capDentalPolicyModel.getEntity().getInsureds() != null) {
            capDentalPolicyModel.getEntity().getInsureds().get(0).setRegistryTypeId(customerModel.getGerootUri().getUri());
        }
        return capDentalPolicyModel;
    }

    public ICapDentalPolicyInfoWrapperModel createDentalUnverifiedPolicyModel(TestData td, ICustomerModel customerModel) {
        ICapDentalPolicyInfoWrapperModel capDentalPolicyModel = modelUtils.create(td);
        if (capDentalPolicyModel.getEntity().getInsureds() != null) {
            capDentalPolicyModel.getEntity().getInsureds().get(0).setRegistryTypeId(customerModel.getGerootUri().getUri());
        }
        return capDentalPolicyModel;
    }
}
