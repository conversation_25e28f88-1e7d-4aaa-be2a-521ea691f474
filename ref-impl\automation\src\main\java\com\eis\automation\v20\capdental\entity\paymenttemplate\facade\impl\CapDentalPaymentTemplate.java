/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.CapBasePaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.ICapDentalPaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public class CapDentalPaymentTemplate extends CapBasePaymentTemplate implements ICapDentalPaymentTemplate {

    private PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> buildAction;
    private PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> startBuildAction;
    private PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> financialDataAction;

    public CapDentalPaymentTemplate(RestActionConfiguration configuration) {
        super(configuration);
        buildAction = new PostModelAction<>(configuration);
        startBuildAction = new PostModelAction<>(configuration);
        financialDataAction = new PostModelAction<>(configuration);
    }

    @RestActionGetter("buildAction")
    @RestActionContext(target = "/api/cappaymenttemplate/{product}/{version}/command/buildPaymentTemplate")
    public PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> build() {
        return buildAction;
    }

    @RestActionGetter("startBuildAction")
    @RestActionContext(target = "/api/cappaymenttemplate/{product}/{version}/command/startBuildPaymentTemplateFlow")
    public PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> startBuild() {
        return startBuildAction;
    }

    @RestActionGetter("financialDataAction")
    @RestActionContext(target = "/api/cappaymenttemplate/{product}/{version}/financialData")
    public PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> financialData() {
        return financialDataAction;
    }
}
