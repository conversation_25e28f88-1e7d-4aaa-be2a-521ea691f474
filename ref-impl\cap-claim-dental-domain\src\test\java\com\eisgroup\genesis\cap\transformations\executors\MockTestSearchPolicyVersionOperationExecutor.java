/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations.executors;

import com.eisgroup.genesis.gson.GsonFactory;
import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.google.common.io.Resources;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * Testing operator executor which mocks SearchPolicyVersion operator
 *
 * <AUTHOR>
 * @since 25.8
 */
public class MockTestSearchPolicyVersionOperationExecutor implements OperationExecutor<GenericOperation, JsonElement> {

    @Override
    public JsonElement execute(GenericOperation genericOperation, TransformationContext<JsonElement> transformationContext) {
        try {
            var dentalUnverifiedPolicy = Resources.toString(Resources.getResource("mockJson/DentalUnverifiedPolicy.json"), StandardCharsets.UTF_8);
            return GsonFactory.gson().fromJson(dentalUnverifiedPolicy, JsonElement.class);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String getName() {
        return "SearchPolicyVersion";
    }
}
