/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalConsultantReviewModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalConsultantReviewModel extends TypeModel implements ICapDentalConsultantReviewModel {

    private String surface;
    private String consultantReplyLetter;
    private String consultantReply;
    private String alternateCDTCode;

    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    public String getConsultantReplyLetter() {
        return consultantReplyLetter;
    }

    public void setConsultantReplyLetter(String consultantReplyLetter) {
        this.consultantReplyLetter = consultantReplyLetter;
    }

    public String getConsultantReply() {
        return consultantReply;
    }

    public void setConsultantReply(String consultantReply) {
        this.consultantReply = consultantReply;
    }

    public String getAlternateCDTCode() {
        return alternateCDTCode;
    }

    public void setAlternateCDTCode(String alternateCDTCode) {
        this.alternateCDTCode = alternateCDTCode;
    }
}
