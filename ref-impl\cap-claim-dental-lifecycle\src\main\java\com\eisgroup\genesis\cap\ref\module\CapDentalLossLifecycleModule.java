/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.common.policy.populator.config.CapPolicyPopulatorConfig;
import com.eisgroup.genesis.cap.loss.command.ClaimLossCreateHandler;
import com.eisgroup.genesis.cap.loss.command.config.ClaimLossLifecycleChildConfig;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossOpenHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossPendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossReopenHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossRequestCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSubStatusHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSubmitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSuspendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUnsuspendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUpdateDraftHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalLossLifecycleConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;
import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.Collection;

/**
 * Claim Dental Loss lifecycle module configuration.
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalLossLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        Collection<CommandHandler<?, ?>> handlers = new ArrayList<>();

        handlers.add(new CapDentalLossInitHandler());
        handlers.add(new ClaimLossCreateHandler<>());
        handlers.add(new CapDentalLossReopenHandler());
        handlers.add(new CapDentalLossSubmitHandler());
        handlers.add(new CapDentalLossSubStatusHandler());
        handlers.add(new CapDentalLossUpdateHandler());
        handlers.add(new CapDentalLossCloseHandler());
        handlers.add(new CapDentalLossUpdateDraftHandler());
        handlers.add(new CapDentalLossOpenHandler());
        handlers.add(new CapDentalLossPendHandler());
        handlers.add(new CapDentalLossSuspendHandler());
        handlers.add(new CapDentalLossUnsuspendHandler());
        handlers.add(new CapDentalLossRequestCloseHandler());

        return handlers;
    }

    public String getModelName() {
        return "CapDentalLoss";
    }

    public String getModelVersion() {
        return "1";
    }

    public String getModelType() {
        return "CapLoss";
    }

    public String getStateMachineName() {
        return getModelName();
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[] {
            CapDentalLossLifecycleConfig.class,
            CapPolicyPopulatorConfig.class,
            ClaimLossLifecycleChildConfig.class };
    }
}
