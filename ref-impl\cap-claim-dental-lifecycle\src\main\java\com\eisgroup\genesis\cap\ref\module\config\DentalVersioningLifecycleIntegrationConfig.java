/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.ref.service.CapDentalVersioningService;
import com.eisgroup.genesis.factory.modeling.types.immutable.RootEntity;
import com.eisgroup.genesis.versioning.Version;
import com.eisgroup.genesis.versioning.VersioningReadRepository;
import com.eisgroup.genesis.versioning.VersioningService;
import com.eisgroup.genesis.versioning.VersioningWriteRepository;
import org.springframework.context.annotation.Bean;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class DentalVersioningLifecycleIntegrationConfig<P extends RootEntity, V extends Version> {

    @Bean
    @Primary
    public VersioningService<P, V> versioningService(VersioningReadRepository<V> versioningReadRepository,
                                                     VersioningWriteRepository<V> versioningWriteRepository) {
        return new CapDentalVersioningService<>(versioningReadRepository, versioningWriteRepository);
    }
}
