core.controllers.actions = [
  {
    name: "LoggingAction",
    routes: [
      "/.*"
    ]
  },
  {
    name: "SecurityAction",
    routes: [
      "/.*"
    ]
  },
  {
    name: "RequestForwardingAction",
    routes: [
      "/.*"
    ]
  },
  {
    name: "GenesisQueryParametersTransformingAction",
    routes: [
      "/.*"
    ]
  },
  {
    name: "FrameOptionsResponseAction",
    routes: [
      "/.*"
    ]
  },
  {
    name: "StrictTransportSecurityResponseAction",
    routes: [
      "/.*"
    ]
  }
]