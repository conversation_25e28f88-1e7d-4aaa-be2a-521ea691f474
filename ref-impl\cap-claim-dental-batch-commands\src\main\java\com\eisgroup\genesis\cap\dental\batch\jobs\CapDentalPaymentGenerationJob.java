/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.jobs;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.dental.batch.commands.CapJobCommands;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentCommands;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.jobs.lifecycle.api.commands.output.SubsequentCommand;
import com.eisgroup.genesis.jobs.lifecycle.api.handler.CommandCollectingHandler;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.Variation;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Payment Generation Job implementation
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapDentalPaymentGenerationJob extends CommandCollectingHandler {

    public static final String NAME = CapJobCommands.PAYMENT_GENERATION_JOB;
    private static final String CAP_DENTAL_PAYMENT_SCHEDULE = "CapDentalPaymentSchedule";
    private static final String CAP_PAYMENT_SCHEDULE = "CapPaymentSchedule";

    @Autowired
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;


    @Override
    protected Streamable<SubsequentCommand> execute() {

        return capDentalPaymentScheduleIndexRepository.load("Active")
                .self()
                .flatMap(capDentalPaymentScheduleIndexEntities -> Streamable.from(capDentalPaymentScheduleIndexEntities))
                .map(capPaymentSchedule -> {
                    FactoryLink factoryLink = new FactoryLink(new EntityLink<>(JsonEntity.class, capPaymentSchedule.getPaymentSchedule()));
                    RootEntityKey rootKey = new RootEntityKey(factoryLink.getRootId(), factoryLink.getRevisionNo());
                    IdentifierRequest request = new IdentifierRequest(rootKey);
                    return new SubsequentCommand(CapDentalPaymentCommands.GENERATE_PAYMENTS, request, Variation.INVARIANT, CAP_DENTAL_PAYMENT_SCHEDULE, "1", CAP_PAYMENT_SCHEDULE);
                });
    }

    @Override
    public String getName() {
        return NAME;
    }
}
