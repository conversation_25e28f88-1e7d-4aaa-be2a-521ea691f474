/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.settlement;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.impl.CapAccumulatorModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.service.ICapAccumulatorService;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl.CapDentalAccumulatorModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import com.exigen.istf.webdriver.controls.waiters.Waiters;
import org.javamoney.moneta.Money;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;
import java.util.List;
import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.OPEN;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.PENDING;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_SETTLEMENT;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorNetworkType.INN;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorProcedureCategory.BASIC;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorProcedureCategory.MAJOR;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorRenewalType.ANNUAL;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorType.INDIVIDUAL_DEDUCTIBLE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAccumulatorType.INDIVIDUAL_MAXIMUM;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementAccumulatorType.INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_BASIC;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementAccumulatorType.INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_MAJOR;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementAccumulatorType.INDIVIDUAL_MAXIMUM_INN_ANNUAL_BASIC;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementAccumulatorType.INDIVIDUAL_MAXIMUM_INN_ANNUAL_MAJOR;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0330;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.policybenefits.constant.PolicyConstant.Currency.USD;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalSettlementAccumulator extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private ICapAccumulatorService capAccumulator;
    @Autowired
    private IClaimSearchService claimSearchService;

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-176768", component = CAP_DENTAL_SETTLEMENT)
    public void testDentalSettlementIntegrationWithAccumulators() {
        // Preconditions
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoWrapperModel dentalPolicyModel = capPolicy.createDentalUnverifiedPolicyModel(
                capPolicy.getSpecificTestData("GENESIS-176768", "TestData_Accumulator"), individualCustomer);
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(dentalPolicyModel);

        //Step 1 Load Accumulators Info
        Waiters.SLEEP(10000).go();
        List<ICapAccumulatorContainerModel> verifyAccumulator1 = capAccumulator.loadAccumulator(individualCustomer, createdDentalPolicy);
        verifyAccumulators(verifyAccumulator1, INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_BASIC, 100, 100, 0, 0);
        verifyAccumulators(verifyAccumulator1, INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_MAJOR, 100, 100, 0, 0);
        verifyAccumulators(verifyAccumulator1, INDIVIDUAL_MAXIMUM_INN_ANNUAL_BASIC, 1000, 1000, 0, 0);
        verifyAccumulators(verifyAccumulator1, INDIVIDUAL_MAXIMUM_INN_ANNUAL_MAJOR, 1000, 1000, 0, 0);

        //Step 2 Create Dental Claim and submit
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(D0330);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setSubmittedFee(Money.of(180, USD));
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);

        //Step 3 Load Settlement and check results
        ICapDentalLossModel openDentalClaim = capDentalLoss.loadDentalLoss(submitDentalClaim, OPEN);
        ICapDentalSettlementModel initSettlement = claimSearchService.searchDentalSettlement(openDentalClaim, 1).get(0);
        ICapDentalSettlementModel approvedDentalSettlement = capDentalSettlement.loadDentalSettlement(initSettlement, APPROVED);

        verifySettlementResult("patientAccumulator", approvedDentalSettlement, INDIVIDUAL_DEDUCTIBLE, BASIC,100, null, ANNUAL, INN);
        verifySettlementResult("patientAccumulator", approvedDentalSettlement, INDIVIDUAL_DEDUCTIBLE, MAJOR,100, null, ANNUAL, INN);
        verifySettlementResult("patientAccumulator", approvedDentalSettlement, INDIVIDUAL_MAXIMUM, BASIC,1000, null, ANNUAL, INN);
        verifySettlementResult("patientAccumulator", approvedDentalSettlement, INDIVIDUAL_MAXIMUM, MAJOR,1000, null, ANNUAL, INN);

        verifySettlementResult("reservedAccumulator", approvedDentalSettlement, INDIVIDUAL_MAXIMUM, BASIC,null, 80, ANNUAL, INN);
        verifySettlementResult("reservedAccumulator", approvedDentalSettlement, INDIVIDUAL_MAXIMUM, MAJOR,null, 80, ANNUAL, INN);
        verifySettlementResult("reservedAccumulator", approvedDentalSettlement, INDIVIDUAL_DEDUCTIBLE, BASIC,null, 100, ANNUAL, INN);
        verifySettlementResult("reservedAccumulator", approvedDentalSettlement, INDIVIDUAL_DEDUCTIBLE, MAJOR,null, 100, ANNUAL, INN);

        //Step 4 Load Accumulator result after settlement
        List<ICapAccumulatorContainerModel> verifyAccumulator2 = capAccumulator.loadAccumulator(individualCustomer, createdDentalPolicy);
        verifyAccumulators(verifyAccumulator2, INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_BASIC, 100, 0, 100, 0);
        verifyAccumulators(verifyAccumulator2, INDIVIDUAL_DEDUCTIBLE_INN_ANNUAL_MAJOR, 100, 0, 100, 0);
        verifyAccumulators(verifyAccumulator2, INDIVIDUAL_MAXIMUM_INN_ANNUAL_BASIC, 1000, 920, 80, 0);
        verifyAccumulators(verifyAccumulator2, INDIVIDUAL_MAXIMUM_INN_ANNUAL_MAJOR, 1000, 920, 80, 0);

    }

    private void verifyAccumulators(List<ICapAccumulatorContainerModel> initLoad, String type, Integer limitAmount,
                                    Integer remainingAmount, Integer reservedAmount, Integer usedAmount) {
        CapAccumulatorModel getInfo = initLoad.get(0).getAccumulators().stream()
                .map(c -> (CapAccumulatorModel) c)
                .filter(r -> r.getType().equals(type)).findFirst().get();
        assertSoftly(softly -> {
            softly.assertThat(getInfo.getLimitAmount()).isEqualTo(limitAmount);
            softly.assertThat(getInfo.getRemainingAmount()).isEqualTo(remainingAmount);
            softly.assertThat(getInfo.getReservedAmount()).isEqualTo(reservedAmount);
            softly.assertThat(getInfo.getUsedAmount()).isEqualTo(usedAmount);
        });
    }

    private void verifySettlementResult(String type, ICapDentalSettlementModel settlementModel, String accumType, String procedureCategory,
                                        Integer remainingAmount, Integer reservedAmount, String renewalType, String networkType ) {
        if ("patientAccumulator".equals(type)) {
        CapDentalAccumulatorModel getPatientAccum = settlementModel.getSettlementLossInfo().getPatient().getAccumulators().stream()
                .map(p ->(CapDentalAccumulatorModel) p)
                .filter(r ->r.getAccumulatorType().equals(accumType) && r.getAppliesToProcedureCategory().equals(procedureCategory)).findAny().get();
        assertSoftly(softly -> {
            softly.assertThat(getPatientAccum.getRemainingAmount()).isEqualTo(Money.of(remainingAmount, USD));
            softly.assertThat(getPatientAccum.getRenewalType()).isEqualTo(renewalType);
            softly.assertThat(getPatientAccum.getNetworkType()).isEqualTo(networkType);
        });
        }
        if ("reservedAccumulator".equals(type)) {
            CapDentalAccumulatorModel getReservedAccum = settlementModel.getSettlementResult().getEntries().get(0).getReservedAccumulators().stream()
                    .map(c ->(CapDentalAccumulatorModel) c)
                    .filter(d ->d.getAccumulatorType().equals(accumType) && d.getAppliesToProcedureCategory().equals(procedureCategory)).findAny().get();
            assertSoftly(softly -> {
                softly.assertThat(getReservedAccum.getReservedAmount()).isEqualTo(Money.of(reservedAmount, USD));
                softly.assertThat(getReservedAccum.getRenewalType()).isEqualTo(renewalType);
                softly.assertThat(getReservedAccum.getNetworkType()).isEqualTo(networkType);
            });

        }
    }
}