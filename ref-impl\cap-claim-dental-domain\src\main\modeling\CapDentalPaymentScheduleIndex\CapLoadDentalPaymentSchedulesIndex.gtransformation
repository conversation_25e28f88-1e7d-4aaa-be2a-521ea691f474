Transformation CapLoadDentalPaymentSchedulesIndex {
    Input {
        ? as originSourceKeyFilter
    }
    Output {
        *CapDentalPaymentScheduleIndex.CapDentalPaymentScheduleIdxEntity as out
    }

    Mapping out is Load(originSourceKeyFilter, "CapDentalPaymentScheduleIndex", "CapDentalPaymentScheduleIdxEntity") {
        Attr originSource is dentalLoss
        Attr paymentSchedule is paymentSchedule
        Attr state is state
    }

}