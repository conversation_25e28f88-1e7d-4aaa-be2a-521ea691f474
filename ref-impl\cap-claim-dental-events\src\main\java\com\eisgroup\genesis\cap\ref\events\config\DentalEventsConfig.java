/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.config;
import com.eisgroup.genesis.cap.ref.events.CapDentalSearchEntityKeyProducer;
import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalLossReactiveCaseInstanceEventHandler;
import com.eisgroup.genesis.cap.ref.events.CapDentalCaseInstanceReactivation;
import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalOutboundPaymentEventHandler;
import com.eisgroup.genesis.cap.ref.events.resolver.CapDentalCaseInstanceResolver;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.events.EventConfiguration;
import com.eisgroup.genesis.http.factory.HttpClientFactory;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.search.events.SearchEntityKeyProducer;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * Events configuration for dental CAP subsystem.
 *
 * <AUTHOR>
 * @since 22.5
 */
@EventConfiguration
public class DentalEventsConfig {

    @Bean
    public CapDentalLossReactiveCaseInstanceEventHandler capDentalLossReactiveCaseInstanceEventHandler(EntityLinkBuilderRegistry entityLinkBuilderRegistry, CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation) {
        return new CapDentalLossReactiveCaseInstanceEventHandler(entityLinkBuilderRegistry, capDentalCaseInstanceReactivation);
    }

    @Bean
    @Primary
    public SearchEntityKeyProducer capDentalSearchKeyExtractor(EntityLinkBuilderRegistry linkBuilderRegistry) {
        return new CapDentalSearchEntityKeyProducer(linkBuilderRegistry);
    }

    @Bean
    public CapDentalCaseInstanceResolver capDentalCaseInstanceResolver(
            HttpClientFactory clientFactory,
            @Value("${genesis.workflow.app.url:null}") String workflowServerUrl,
            ServiceAccessRunner serviceAccessRunner) {
        return new CapDentalCaseInstanceResolver(clientFactory.createSecured(), workflowServerUrl, serviceAccessRunner);
    }

    @Bean
    public CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation(
            HttpClientFactory clientFactory,
            @Value("${genesis.workflow.app.url:null}") String workflowServerUrl,
            ServiceAccessRunner serviceAccessRunner, CapDentalCaseInstanceResolver capDentalCaseInstanceResolver) {
        return new CapDentalCaseInstanceReactivation(clientFactory.createSecured(), workflowServerUrl, serviceAccessRunner, capDentalCaseInstanceResolver);
    }

    @Bean
    public CapDentalOutboundPaymentEventHandler outboundPaymentEventHandler(CommandPublisher commandPublisher) {
        return new CapDentalOutboundPaymentEventHandler(commandPublisher);
    }
}
