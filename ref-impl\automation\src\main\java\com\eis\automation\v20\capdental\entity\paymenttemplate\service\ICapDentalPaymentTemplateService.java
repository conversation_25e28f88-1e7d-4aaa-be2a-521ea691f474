/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.ICapDentalPaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;

public interface ICapDentalPaymentTemplateService extends ITestDataService, IFacadeService<ICapDentalPaymentTemplate> {

    ICapDentalPaymentTemplateModel initDentalPaymentTemplate(ICapDentalPaymentTemplateModel paymentTemplateModel);

    ICapDentalPaymentTemplateModel createDentalPaymentTemplateModel(ICustomerModel customerModel, ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalPaymentTemplateModel createDentalPaymentTemplateModel(TestData td, ICustomerModel customerModel, ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalPaymentTemplateModel financialData(ICapDentalPaymentTemplateBuildModel dentalPaymentTemplateBuildModel);

    ICapDentalPaymentTemplateBuildModel createDentalPaymentTemplateBuildModel(ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel);

}
