/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.events.CapDentalCaseInstanceReactivation;
import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalLossReactiveCaseInstanceEventHandler;
import com.eisgroup.genesis.cap.ref.events.resolver.CapDentalCaseInstanceResolver;
import com.eisgroup.genesis.http.factory.HttpClientFactory;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.search.events.SearchEntityKeyProducer;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;

/**
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class DentalEventsConfigTest {

    @InjectMocks
    private DentalEventsConfig config;

    @Mock
    private EntityLinkBuilderRegistry entityLinkBuilderRegistry;

    @Mock
    private CapDentalCaseInstanceReactivation capDentalCaseInstanceReactivation;

    @Mock
    private EntityLinkBuilderRegistry linkBuilderRegistry;

    @Mock
    private HttpClientFactory clientFactory;

    @Mock
    private ServiceAccessRunner serviceAccessRunner;

    @Mock
    private CapDentalCaseInstanceResolver capDentalCaseInstanceResolver;

    @Test
    public void shouldReturnCapDentalLossReactiveCaseInstanceEventHandler() {
        //when
        CapDentalLossReactiveCaseInstanceEventHandler result = config.capDentalLossReactiveCaseInstanceEventHandler(entityLinkBuilderRegistry, capDentalCaseInstanceReactivation);

        //then
        assertThat(result, notNullValue());
    }

    @Test
    public void shouldReturnCapDentalSearchKeyExtractor() {
        //when
        SearchEntityKeyProducer result = config.capDentalSearchKeyExtractor(linkBuilderRegistry);

        //then
        assertThat(result, notNullValue());
    }

    @Test
    public void shouldReturnCapDentalCaseInstanceResolver() {
        //when
        CapDentalCaseInstanceResolver result = config.capDentalCaseInstanceResolver(clientFactory, null, serviceAccessRunner);

        //then
        assertThat(result, notNullValue());
    }

    @Test
    public void shouldReturnCapDentalCaseInstanceReactivation() {
        //when
        CapDentalCaseInstanceReactivation result = config.capDentalCaseInstanceReactivation(clientFactory, null, serviceAccessRunner, capDentalCaseInstanceResolver);

        //then
        assertThat(result, notNullValue());
    }


}
