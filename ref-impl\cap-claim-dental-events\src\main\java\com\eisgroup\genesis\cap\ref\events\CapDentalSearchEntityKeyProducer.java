/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.json.IdentifiableEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.json.link.LinkingParams;
import com.eisgroup.genesis.model.Variation;
import com.eisgroup.genesis.search.events.SearchEntityKeyProducer;
import com.eisgroup.genesis.search.schema.SearchSchema;
import com.eisgroup.genesis.util.GsonUtil;
import com.google.common.collect.Iterables;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Optional;

/**
 * Searchable entity key extractor (creates gentity link)
 * <AUTHOR>
 * @since 22.10
 */
public class CapDentalSearchEntityKeyProducer implements SearchEntityKeyProducer {

    private final EntityLinkBuilderRegistry linkBuilderRegistry;

    public CapDentalSearchEntityKeyProducer(EntityLinkBuilderRegistry linkBuilderRegistry) {
        this.linkBuilderRegistry = linkBuilderRegistry;
    }

    @Nonnull
    @Override
    public Streamable<String> create(@Nonnull IdentifiableEntity entity,
                                     @Nullable EntityKey modelRootKey,
                                     @Nonnull Variation variation,
                                     @Nonnull SearchSchema searchSchema) {
        var uniqueKeyPaths = searchSchema.getUniqueKey().getMappingPaths();
        var uniqueKey = uniqueKeyPaths.isEmpty()
                ? createLink(entity, createParams(modelRootKey, variation)).getURIString()
                : Optional.ofNullable(Iterables.getFirst(
                        GsonUtil.resolveJsonPath(entity.toJson(), Iterables.getLast(uniqueKeyPaths)), null))
                .orElseThrow(() -> new IllegalStateException("Missing unique key " + uniqueKeyPaths))
                .getAsString();
        return Streamable.of(new String[]{uniqueKey});
    }

    @Nonnull
    @Override
    public Streamable<String> create(@Nonnull IdentifiableEntity entity,
                                     @Nullable EntityKey modelRootKey,
                                     @Nonnull Variation variation,
                                     @Nonnull DomainModel model) {
        return Streamable.of(new String[] { this.createLink(entity,
            this.createParams(modelRootKey, variation).with(DomainModel.class, model)).getURIString() });
    }

    @Nonnull
    private LinkingParams createParams(@Nullable EntityKey modelRootKey,
                                       @Nonnull Variation variation) {
        return Optional.ofNullable(modelRootKey)
                .map(key -> LinkingParams.just(EntityKey.class, key))
                .orElse(LinkingParams.empty())
                .with(Variation.class, variation);
    }

    @Nonnull
    private EntityLink<Object> createLink(@Nonnull IdentifiableEntity entity, @Nonnull LinkingParams params) {
        return linkBuilderRegistry.getByType(entity.getClass())
                .createLink(entity, params);
    }
}
