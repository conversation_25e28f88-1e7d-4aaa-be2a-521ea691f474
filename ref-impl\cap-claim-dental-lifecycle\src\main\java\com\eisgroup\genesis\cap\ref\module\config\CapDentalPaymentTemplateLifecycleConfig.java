/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.config.CapPaymentBaseLifecycleConfig;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapFinancialDataResolver;
import com.eisgroup.genesis.cap.financial.command.template.validator.CapPaymentTemplateValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalBuildPaymentScheduleInput;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPreviewPaymentScheduleInput;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.CapDentalPaymentTemplateIndexResolver;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl.DefaultCapDentalFinancialDataPreviewResolver;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl.DefaultCapDentalFinancialDataResolver;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl.DefaultCapDentalPaymentTemplateIndexResolver;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.validator.CapDentalPaymentTemplateInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.validator.CapDentalPaymentTemplateUpdateInputValidator;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.generator.impl.SimpleNumberGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * Claim Dental Payment Template lifecycle services configuration.
 *
 * <AUTHOR>
 * @since 22.2
 */
@Import({CapBaseCommandConfig.class, CapTransformationCommandsConfig.class})
public class CapDentalPaymentTemplateLifecycleConfig {

    @Bean
    public EntityNumberGenerator paymentTemplateNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("cap_payment_template_number", "P%d", sequenceGenerator);
    }

    @Bean
    public CapValidatorRegistry capValidatorRegistry() {
        return new CapValidatorRegistry();
    }

    @Bean
    public CapPaymentTemplateValidator capPaymentTemplateValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapPaymentTemplateValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalPaymentTemplateIndexResolver capDentalPaymentTemplateIndexResolver(ModeledTransformationService modeledTransformationService) {
        return new DefaultCapDentalPaymentTemplateIndexResolver("CapLoadDentalPaymentTemplatesIndex",
                modeledTransformationService, ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }

    @Bean
    public CapFinancialDataResolver<CapDentalBuildPaymentScheduleInput, CapDentalPaymentTemplateEntity> capFinancialDataResolver(ModeledTransformationService modeledTransformationService) {
        return new DefaultCapDentalFinancialDataResolver(
                modeledTransformationService, ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }

    @Bean
    public CapFinancialDataResolver<CapDentalPreviewPaymentScheduleInput, CapDentalPaymentTemplateEntity> capFinancialDataPreviewResolver(ModeledTransformationService modeledTransformationService) {
        return new DefaultCapDentalFinancialDataPreviewResolver(
                modeledTransformationService, ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }


    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalPaymentTemplateInitInputValidator capDentalPaymentTemplateInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPaymentTemplateInitInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalPaymentTemplateUpdateInputValidator capDentalPaymentTemplateUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPaymentTemplateUpdateInputValidator(capDentalLinkValidator);
    }
}
