{"applicationName": "cap-loss-term-life-deployer", "timestamp": 1656422327852, "modelType": "com.eisgroup.genesis.factory.model.domain.DomainModel", "modelName": "CapDentalUnverifiedPolicy", "modelVersion": "1", "model": "{\"@class\":\"com.eisgroup.genesis.factory.model.domain.DomainModel\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.domain.DomainModelFeature\"},\"identityModel\":null,\"importedFeatures\":[],\"indexDeterminants\":[\"ModelType\"],\"moduleType\":\"UnverifiedPolicy\",\"name\":\"CapDentalUnverifiedPolicy\",\"namespaceIncludes\":[],\"root\":{\"@class\":\"com.eisgroup.genesis.factory.model.domain.RootEntityType\",\"type\":\"CapDentalUnverifiedPolicy\"},\"storeDeterminants\":[\"ModelName\"],\"types\":{\"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"preventWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Late entrant preventive Waiting Period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Late entrant preventive Waiting Period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"preventWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"NUMBER\"}},\"majorWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Late entrant major Waiting Period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Late entrant major Waiting Period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"majorWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"NUMBER\"}},\"basicWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Late entrant basic Waiting Period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Late entrant basic Waiting Period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"basicWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"NUMBER\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity\",\"references\":{}},\"CapDeductibleDetailEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"individualBasicINNAnnualDeductible\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Deductible limit for Basic services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Deductible limit for Basic services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualBasicINNAnnualDeductible\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}},\"individualMajorINNAnnualDeductible\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Deductible limit for Major services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Deductible limit for Major services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualMajorINNAnnualDeductible\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}},\"individualPreventiveINNAnnualDeductible\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Deductible limit for Preventive services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Deductible limit for Preventive services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualPreventiveINNAnnualDeductible\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDeductibleDetailEntity\",\"references\":{}},\"Term\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"expirationDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"expirationDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"effectiveDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"effectiveDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"Term\",\"references\":{}},\"CapDentalUnverifiedInfoEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"employerName\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Claim Without Policy Employer Name.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Claim Without Policy Employer Name.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"employerName\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"groupNumber\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Claim Without Policy Group Number.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Claim Without Policy Group Number.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"groupNumber\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalUnverifiedInfoEntity\",\"references\":{}},\"CapDentalTermEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"expirationDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"expirationDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"effectiveDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"effectiveDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalTermEntity\",\"references\":{}},\"CapDentalUnverifiedPolicy\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"orthoINNCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Ortho In Network Coinsurance Percentage.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Ortho In Network Coinsurance Percentage.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"orthoINNCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"pcdId\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Primary Care Dentist ID.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Primary Care Dentist ID.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"pcdId\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"basicWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Basic waiting period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Basic waiting period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"basicWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"standardChildAgeLimit\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"standardChildAgeLimit\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"NUMBER\"}},\"majorOONCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"majorOONCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"capPolicyVersionId\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Defines policy version stored on CAP side.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Defines policy version stored on CAP side.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"capPolicyVersionId\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"majorWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Major waiting period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Major waiting period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"majorWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"lateEntrantWaitingPeriodsDetails\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Late Entrant Waiting Period details.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Late Entrant Waiting Period details.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"lateEntrantWaitingPeriodsDetails\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity\"}},\"policyType\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Indicates whether policy type is master or certificate (individual).\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Indicates whether policy type is master or certificate (individual).\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyType\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"orthoWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Ortho waiting period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Ortho waiting period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"orthoWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"policyPaidToDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Date which the Policy is paid up to.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Date which the Policy is paid up to.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyPaidToDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATE\"}},\"dentalMaximums\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Dental Maximums details.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Dental Maximums details.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"dentalMaximums\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalMaximumEntity\"}},\"productCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"productCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"pcdTerm\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"PCD Assignment Effective/Termination Period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"PCD Assignment Effective/Termination Period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"pcdTerm\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalTermEntity\"}},\"basicINNCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"basicINNCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"preventiveINNCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"preventiveINNCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"insureds\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Insured details.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Insured details.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"insureds\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalPolicyInfoInsuredDetailsEntity\"}},\"riskStateCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Situs state\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Situs state\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"riskStateCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"nonStandardChildAgeLimit\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"nonStandardChildAgeLimit\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"NUMBER\"}},\"orthoOONCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Ortho Out of Network Coinsurance Percentage.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Ortho Out of Network Coinsurance Percentage.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"orthoOONCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"isWaitingPeriodWaived\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"isWaitingPeriodWaived\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"policyNumber\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Indicates policy number.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Indicates policy number.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyNumber\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"unverifiedInfo\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"unverifiedInfo\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalUnverifiedInfoEntity\"}},\"term\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Policy effective and expiration dates.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Policy effective and expiration dates.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"term\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"Term\"}},\"policyPaidToDateWithGracePeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Date which the Policy is paid up to with Grace period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Date which the Policy is paid up to with Grace period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyPaidToDateWithGracePeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATE\"}},\"majorINNCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"majorINNCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"applyLateEntrantBenefitWaitingPeriods\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Is late entrant benefit period waiting applied.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Is late entrant benefit period waiting applied.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"applyLateEntrantBenefitWaitingPeriods\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"patientTerm\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"patientTerm\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalTermEntity\"}},\"waiveOONInd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"waiveOONInd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"fullTimeStudentAgeCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Full time student Age limit.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Full time student Age limit.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"fullTimeStudentAgeCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"isVerified\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Indicates if policy is verified.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Indicates if policy is verified.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"isVerified\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"implantsWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Implants waiting period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Implants waiting period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"implantsWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"plan\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Plan type for the policy.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Plan type for the policy.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"plan\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"coinsurances\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Coinsurances percentages details.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Coinsurances percentages details.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"coinsurances\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDentalPolicyInfoCoinsuranceEntity\"}},\"deductibleDetails\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Dental Dedudictble details.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Dental Dedudictble details.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"deductibleDetails\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapDeductibleDetailEntity\"}},\"childMaxAgeCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Child Max Age limit.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Child Max Age limit.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"childMaxAgeCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"policyStatus\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Policy version status\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Policy version status\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyStatus\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"basicOONCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"basicOONCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"preventiveOONCoinsurancePercent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"preventiveOONCoinsurancePercent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"planName\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Plan Name.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Plan Name.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"planName\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"capPolicyId\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Identification number of the policy in CAP subsystem.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Identification number of the policy in CAP subsystem.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"capPolicyId\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"txEffectiveDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"txEffectiveDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"planCategory\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"PPO/DHMO product.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"PPO/DHMO product.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"planCategory\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"preventWaitingPeriod\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Preventetive waiting period.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Preventetive waiting period.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"preventWaitingPeriod\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"waiveINNInd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"NOT USED\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"NOT USED\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.factory.model.features.Deprecated\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.features.Deprecated\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"waiveINNInd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.RootType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.RootType\\\",\\\"modelTypes\\\":{\\\"@array\\\":\\\"java.lang.String\\\",\\\"values\\\":[]}}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapPolicyInfo\"},{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"RootEntity\"}],\"typeName\":\"UnverifiedPolicy\"}],\"baseTypes\":[\"UnverifiedPolicy\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalUnverifiedPolicy\",\"references\":{}},\"CapDentalPolicyInfoInsuredDetailsEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"insuredRoleNameCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Insured's role.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Insured's role.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"insuredRoleNameCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"isMain\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Indicates if a party is a primary insured.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Indicates if a party is a primary insured.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"isMain\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"registryTypeId\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"The unique registry ID that identifies the subject of the claim.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"The unique registry ID that identifies the subject of the claim.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"},\"com.eisgroup.genesis.factory.model.types.features.Searchable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.features.Searchable\\\",\\\"fieldName\\\":null}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"registryTypeId\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"relationshipToPrimaryInsuredCd\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Patient's relationship to primary insured.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Patient's relationship to primary insured.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"relationshipToPrimaryInsuredCd\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"isFullTimeStudent\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Is patient full time student?\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Is patient full time student?\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"isFullTimeStudent\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.\\\",\\\"messageBundle\\\":\\\"domain-messages/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalPolicyInfoInsuredDetailsEntity\",\"references\":{}},\"CapDentalMaximumEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"individualPreventiveINNAnnualMaximum\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Maximum limit for Preventive services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Maximum limit for Preventive services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualPreventiveINNAnnualMaximum\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}},\"individualBasicINNAnnualMaximum\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Maximum limit for Basic services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Maximum limit for Basic services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualBasicINNAnnualMaximum\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}},\"individualMajorINNAnnualMaximum\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Annual Maximum limit for Major services.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Annual Maximum limit for Major services.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"individualMajorINNAnnualMaximum\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"MONEY\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalMaximumEntity\",\"references\":{}},\"CapDentalPolicyInfoCoinsuranceEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"coinsuranceServiceType\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Coinsurance Service Type.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Coinsurance Service Type.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"coinsuranceServiceType\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"coinsuranceINPct\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Coinsurance percentage in network.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Coinsurance percentage in network.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"coinsuranceINPct\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"coinsuranceOONPct\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Coinsurance percentage outside network.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Coinsurance percentage outside network.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapDentalSettlement/description\\\"}\"},\"com.eisgroup.genesis.comparison.features.NonComparable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.comparison.features.NonComparable\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"coinsuranceOONPct\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapDentalPolicyInfoCoinsuranceEntity\",\"references\":{}}},\"variations\":[],\"version\":\"1\"}"}