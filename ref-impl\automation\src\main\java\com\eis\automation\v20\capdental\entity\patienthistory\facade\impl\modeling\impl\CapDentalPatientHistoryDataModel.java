/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryDataModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalProviderDataModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalServiceDataModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.AccessTrackInfoModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.IAccessTrackInfoModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPatientHistoryDataModel extends TypeModel implements ICapDentalPatientHistoryDataModel {

    private IAccessTrackInfoModel accessTrackInfo;
    private ICapDentalProviderDataModel providerData;
    private ICapDentalClaimDataModel claimData;
    private ICapDentalServiceDataModel serviceData;

    @JsonSerialize(as = AccessTrackInfoModel.class)
    public IAccessTrackInfoModel getAccessTrackInfo() {
        return accessTrackInfo;
    }

    @JsonDeserialize(as = AccessTrackInfoModel.class)
    public void setAccessTrackInfo(IAccessTrackInfoModel accessTrackInfo) {
        this.accessTrackInfo = accessTrackInfo;
    }

    @JsonSerialize(as = CapDentalProviderDataModel.class)
    public ICapDentalProviderDataModel getProviderData() {
        return providerData;
    }

    @JsonDeserialize(as = CapDentalProviderDataModel.class)
    public void setProviderData(ICapDentalProviderDataModel providerData) {
        this.providerData = providerData;
    }

    @JsonSerialize(as = CapDentalClaimDataModel.class)
    public ICapDentalClaimDataModel getClaimData() {
        return claimData;
    }

    @JsonDeserialize(as = CapDentalClaimDataModel.class)
    public void setClaimData(ICapDentalClaimDataModel claimData) {
        this.claimData = claimData;
    }

    @JsonSerialize(as = CapDentalServiceDataModel.class)
    public ICapDentalServiceDataModel getServiceData() {
        return serviceData;
    }

    @JsonDeserialize(as = CapDentalServiceDataModel.class)
    public void setServiceData(ICapDentalServiceDataModel serviceData) {
        this.serviceData = serviceData;
    }
}
