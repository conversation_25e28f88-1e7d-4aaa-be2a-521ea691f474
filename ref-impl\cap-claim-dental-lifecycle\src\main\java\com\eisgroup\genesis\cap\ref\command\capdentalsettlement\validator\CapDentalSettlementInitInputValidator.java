/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input.CapDentalSettlementInitInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.entity.Stateful;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.exception.JsonValueFormatError;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

import java.util.Optional;

import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator.CapDentalSettlementInitInputErrorDefinition.LOSS_IDENTIFICATION_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator.CapDentalSettlementInitInputErrorDefinition.LOSS_STATE_INCOMPLETE;
import static com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator.CapDentalSettlementInitInputErrorDefinition.SETTLEMENT_EXISTS;

/**
 * Validates {@link CapDentalSettlementInitInput} request
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalSettlementInitInputValidator extends CapInputValidator<CapDentalSettlementInitInput> {

    private static final String INCOMPLETE_STATE = "Incomplete";

    private final CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;
    private final EntityLinkResolverRegistry entityLinkResolverRegistry;

    public CapDentalSettlementInitInputValidator(CapDentalSettlementIndexResolver capDentalSettlementIndexResolver,
            EntityLinkResolverRegistry entityLinkResolverRegistry) {

        super(CapDentalSettlementInitInput.class);
        this.capDentalSettlementIndexResolver = capDentalSettlementIndexResolver;
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalSettlementInitInput input) {
        return Streamable.concat(validateSettlementNotExist(input.getClaimLossIdentification()),validateLossLinkAndStatus(input.getClaimLossIdentification()));
    }

    private Streamable<ErrorHolder> validateSettlementNotExist(EntityLink<RootEntity> lossUri) {
        return Optional.ofNullable(lossUri)
                .map(Streamable::of)
                .orElseGet(Streamable::empty)
                .flatMap(capDentalSettlementIndexResolver::resolveSettlementIndex)
                .count()
                .filter(count -> count > 0)
            .flatMapMany(count -> Streamable.of(SETTLEMENT_EXISTS.builder().build()));
    }

    private Streamable<ErrorHolder> validateLossLinkAndStatus(EntityLink<RootEntity> reference) {
        return Optional.ofNullable(reference)
                .map(Streamable::of)
                .orElseGet(Streamable::empty)
                .flatMap(lossUri -> {
                    if (!isModelTypeValid(lossUri)) {
                        return Streamable.of(LOSS_IDENTIFICATION_INCORRECT.builder().build());
                    }
                    return retrieveLossEntity(lossUri)
                            .filter(this::isLossIncomplete)
                            .map(incompleteLoss -> LOSS_STATE_INCOMPLETE.builder().build())
                            .onErrorResume(e->Lazy.of(LOSS_IDENTIFICATION_INCORRECT.builder().build()));
                });
    }

    private boolean isModelTypeValid(EntityLink<RootEntity> lossUri) {
        try {
            return new FactoryLink(lossUri).getTypeName().equals("CapLoss");
        } catch (JsonValueFormatError e) {
            return false;
        }
    }

    private boolean isLossIncomplete(RootEntity loss) {
        if (loss instanceof Stateful) {
            return INCOMPLETE_STATE.equals(((Stateful) loss).getState());
        }
        return false;
    }

    private Lazy<RootEntity> retrieveLossEntity(EntityLink<RootEntity> reference) {
        return entityLinkResolverRegistry.getByURIScheme(reference.getSchema())
                .resolve(reference, ReadContext.empty());
    }

    public static class CapDentalSettlementInitInputErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalSettlementInitInputErrorDefinition SETTLEMENT_EXISTS = new CapDentalSettlementInitInputErrorDefinition(
                "dsie-001", "This command cannot be performed because settlement already exists.");
        public static final CapDentalSettlementInitInputErrorDefinition LOSS_STATE_INCOMPLETE = new CapDentalSettlementInitInputErrorDefinition(
                "dsie-002", "This command cannot be performed when Loss is in Incomplete state.");
        public static final CapDentalSettlementInitInputErrorDefinition LOSS_IDENTIFICATION_INCORRECT = new CapDentalSettlementInitInputErrorDefinition(
                "dsie-003", "claimLossIdentification URI is not valid.");

        private CapDentalSettlementInitInputErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
