Transformation CapDentlaPaymentToBalancePayment {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        CapDentalBalance.CapDentalBalancePaymentEntity
    }

    Attr paymentDetails is payment.paymentDetails
    Attr paymentNetAmount is payment.paymentNetAmount
    Attr messages is payment.messages
    Attr creationDate is payment.creationDate
    Attr paymentNumber is payment.paymentNumber
    Attr direction is payment.direction
    Attr state is payment.state
    Attr _modelName is "CapDentalBalance"
}