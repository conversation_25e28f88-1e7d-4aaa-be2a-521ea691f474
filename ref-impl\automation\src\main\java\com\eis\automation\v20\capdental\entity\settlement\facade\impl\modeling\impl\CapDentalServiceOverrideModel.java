/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalBypassClaimLogicModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalOverrideServiceValueModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalServiceOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalWaiveClaimValueModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

public class CapDentalServiceOverrideModel extends TypeModel implements ICapDentalServiceOverrideModel {

    @JsonProperty("isAllowed")
    private Boolean isAllowed;
    @JsonProperty("isDenied")
    private Boolean isDenied;
    @JsonProperty("suppressMemberEob")
    private Boolean suppressMemberEob;
    @JsonProperty("suppressProviderEob")
    private Boolean suppressProviderEob;
    private String overrideRemark;
    private String serviceSource;
    private ICapDentalBypassClaimLogicModel bypassServiceLogic;
    private ICapDentalOverrideServiceValueModel overrideServiceValue;
    private ICapDentalWaiveClaimValueModel waiveServiceValue;

    public Boolean getIsAllowed() {
        return isAllowed;
    }

    public void setIsAllowed(Boolean isAllowed) {
        this.isAllowed = isAllowed;
    }

    public Boolean getIsDenied() {
        return isDenied;
    }

    public void setIsDenied(Boolean isDenied) {
        this.isDenied = isDenied;
    }

    public Boolean getSuppressMemberEob() {
        return suppressMemberEob;
    }

    public void setSuppressMemberEob(Boolean suppressMemberEob) {
        this.suppressMemberEob = suppressMemberEob;
    }

    public Boolean getSuppressProviderEob() {
        return suppressProviderEob;
    }

    public void setSuppressProviderEob(Boolean suppressProviderEob) {
        this.suppressProviderEob = suppressProviderEob;
    }

    public String getOverrideRemark() {
        return overrideRemark;
    }

    public void setOverrideRemark(String overrideRemark) {
        this.overrideRemark = overrideRemark;
    }

    public String getServiceSource() {
        return serviceSource;
    }

    public void setServiceSource(String serviceSource) {
        this.serviceSource = serviceSource;
    }

    @JsonSerialize(as = CapDentalBypassClaimLogicModel.class)
    public ICapDentalBypassClaimLogicModel getBypassServiceLogic() {
        return bypassServiceLogic;
    }

    @JsonDeserialize(as = CapDentalBypassClaimLogicModel.class)
    public void setBypassServiceLogic(ICapDentalBypassClaimLogicModel bypassServiceLogic) {
        this.bypassServiceLogic = bypassServiceLogic;
    }

    @JsonSerialize(as = CapDentalOverrideServiceValueModel.class)
    public ICapDentalOverrideServiceValueModel getOverrideServiceValue() {
        return overrideServiceValue;
    }

    @JsonDeserialize(as = CapDentalOverrideServiceValueModel.class)
    public void setOverrideServiceValue(ICapDentalOverrideServiceValueModel overrideServiceValue) {
        this.overrideServiceValue = overrideServiceValue;
    }

    @JsonSerialize(as = CapDentalWaiveClaimValueModel.class)
    public ICapDentalWaiveClaimValueModel getWaiveServiceValue() {
        return waiveServiceValue;
    }

    @JsonDeserialize(as = CapDentalWaiveClaimValueModel.class)
    public void setWaiveServiceValue(ICapDentalWaiveClaimValueModel waiveServiceValue) {
        this.waiveServiceValue = waiveServiceValue;
    }
}