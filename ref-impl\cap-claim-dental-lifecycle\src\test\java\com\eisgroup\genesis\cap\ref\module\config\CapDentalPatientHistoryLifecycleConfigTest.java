package com.eisgroup.genesis.cap.ref.module.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPatientHistoryLifecycleConfigTest {

    @InjectMocks
    private CapDentalPatientHistoryLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Mock
    private CapDentalLossValidator capDentalLossValidator;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;


    @Test
    public void testReturnCapValidatorRegistry() {
        var result = config.capValidatorRegistry();
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPatientHistoryUpdateValidator() {
        var result = config.capDentalPatientHistoryUpdateValidator();
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.capDentalLinkValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPatientHistoryInitInputValidator() {
        var result = config.capDentalPatientHistoryInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }


    @Test
    public void testReturnCapDentalPatientHistoryUpdateInputValidator() {
        var result = config.capDentalPatientHistoryUpdateInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }
}





