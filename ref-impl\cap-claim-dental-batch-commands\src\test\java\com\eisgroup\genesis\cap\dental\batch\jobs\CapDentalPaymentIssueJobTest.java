/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.jobs;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentIndexRepository;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentindex.CapDentalPaymentIdxEntity;
import com.eisgroup.genesis.jobs.lifecycle.api.commands.output.SubsequentCommand;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static java.util.List.of;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentIssueJobTest {

    private static final String PAYMENT_UUID = "4de19a14-d626-452c-863c-c56eb998102a";

    @Mock
    private CapDentalPaymentIndexRepository capDentalPaymentIndexRepository;

    @InjectMocks
    private CapDentalPaymentIssueJob capDentalPaymentIssueJob;

    @Test
    public void shouldReturnSubsequentCommands() {
        //given
        List<CapDentalPaymentIdxEntity> paymentIndexes = of(
                createPaymentIndex("o1", createPaymentUri(PAYMENT_UUID, 1)),
                createPaymentIndex("o1", createPaymentUri(PAYMENT_UUID, 2)),
                createPaymentIndex("o1", createPaymentUri(PAYMENT_UUID, 3))
        );

        when(capDentalPaymentIndexRepository.loadPaymentIndexesByState(anyString())).thenReturn(Streamable.from(Collections.singleton(paymentIndexes)));

        //when
        List<SubsequentCommand> actualResult = TestStreamable.create(capDentalPaymentIssueJob.execute())
                .assertNoErrors()
                .values();

        //then
        assertThat(actualResult, Matchers.hasSize(3));
        assertThat(getRootId(actualResult, 0), equalTo(PAYMENT_UUID));
        assertThat(getRevNo(actualResult, 0), equalTo("1"));

        assertThat(getRootId(actualResult, 1), equalTo(PAYMENT_UUID));
        assertThat(getRevNo(actualResult, 1), equalTo("2"));

        assertThat(getRootId(actualResult, 2), equalTo(PAYMENT_UUID));
        assertThat(getRevNo(actualResult, 2), equalTo("3"));
    }

    @Test
    public void shouldReturnOnlyPaymentSubsequentCommands() {
        //given
        List<CapDentalPaymentIdxEntity> paymentIndexes = of(
                createPaymentIndex("o1", createPaymentUri(PAYMENT_UUID, 1)),
                createPaymentIndex("o1", createPaymentUri(PAYMENT_UUID, 2))
        );

        when(capDentalPaymentIndexRepository.loadPaymentIndexesByState(anyString())).thenReturn(Streamable.from(Collections.singleton(paymentIndexes)));

        //when
        List<SubsequentCommand> actualResult = TestStreamable.create(capDentalPaymentIssueJob.execute())
                .assertNoErrors()
                .values();

        //then
        assertThat(actualResult, Matchers.hasSize(2));
        assertThat(getRootId(actualResult, 0), equalTo(PAYMENT_UUID));
        assertThat(getRevNo(actualResult, 0), equalTo("1"));

        assertThat(getRootId(actualResult, 1), equalTo(PAYMENT_UUID));
        assertThat(getRevNo(actualResult, 1), equalTo("2"));
    }

    @Test
    public void shouldReturnEmptySubsequentCommands() {
        //given
        when(capDentalPaymentIndexRepository.loadPaymentIndexesByState(anyString())).thenReturn(Streamable.from(Collections.EMPTY_LIST));

        //when
        TestStreamable.create(capDentalPaymentIssueJob.execute())
                //then
                .assertNoErrors()
                .assertNoValues();
    }

    @Test
    public void shouldReturnJobCommandName() {
        assertThat(capDentalPaymentIssueJob.getName(), equalTo("paymentIssueJob"));
    }

    private String getRootId(List<SubsequentCommand> actualResult, int idx) {
        return actualResult.get(idx).getCommand().getData().getAsJsonObject().get("_key").getAsJsonObject().get("rootId").getAsString();
    }

    private String getRevNo(List<SubsequentCommand> actualResult, int idx) {
        return actualResult.get(idx).getCommand().getData().getAsJsonObject().get("_key").getAsJsonObject().get("revisionNo").getAsString();
    }

    private CapDentalPaymentIdxEntity createPaymentIndex(String originSource, String paymentId) {
        CapDentalPaymentIdxEntity paymentIndex = (CapDentalPaymentIdxEntity) ModelInstanceFactory.createInstance("CapDentalPaymentIndex", "1", "CapDentalPaymentIdxEntity");
        paymentIndex.setOriginSource(originSource);
        paymentIndex.setPaymentId(paymentId);
        return paymentIndex;
    }

    private String createPaymentUri(String uuid, Integer revNo) {
        return "gentity://CapPayment/CapDentalPaymentDefinition/payment/%s/%s".formatted(uuid, revNo);
    }
}
