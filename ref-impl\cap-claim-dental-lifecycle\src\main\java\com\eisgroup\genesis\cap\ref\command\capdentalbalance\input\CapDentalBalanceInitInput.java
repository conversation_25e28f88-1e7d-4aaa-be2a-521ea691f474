/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.input;

import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceItemEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import javax.money.MonetaryAmount;
import java.util.Collection;
import java.util.Optional;

/**
 * Dental balance initiation request.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalBalanceInitInput extends AbstractJsonEntity {

    private static final String ORIGIN_SOURCE = "originSource";
    private static final String PAYEE = "payee";
    private static final String TOTAL_BALANCE_AMOUNT = "totalBalanceAmount";
    private static final String BALANCE_ITEMS = "balanceItems";

    public CapDentalBalanceInitInput(JsonObject original) {
        super(original);
    }

    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(originSource -> new EntityLink<>(RootEntity.class, originSource))
                .orElse(null);
    }

    public EntityLink<RootEntity> getPayee() {
        return Optional.ofNullable(getRawChild(PAYEE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(payee -> new EntityLink<>(RootEntity.class, payee))
                .orElse(null);
    }

    public MonetaryAmount getTotalBalanceAmount() {
        return getMonetaryAmount(TOTAL_BALANCE_AMOUNT);
    }

    public Collection<CapDentalBalanceItemEntity> getBalanceItems() {
        return getCollection(CapDentalBalanceItemEntity.class, BALANCE_ITEMS);
    }
}
