{"TestData_Accumulator": {"entity": {"isImplantsMaximumAppliedTowardPlanMaximum": false, "term": {"effectiveDate": "$<today-1M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "expirationDate": "$<today+11M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "Term"}, "policyPaidToDate": "$<today+50d:yyyy-MM-dd>", "productCd": "DNIndividual", "policyPaidToDateWithGracePeriod": "$<today+50d:yyyy-MM-dd>", "plan": "High", "planCategory": "PPO", "coinsurances": [{"coinsuranceServiceType": "Major", "coinsuranceINPct": 0, "coinsuranceOONPct": 0, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}, {"coinsuranceServiceType": "Basic", "coinsuranceINPct": 0, "coinsuranceOONPct": 0, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}], "deductibleDetails": [{"individualBasicINNAnnualDeductible": {"currency": "USD", "amount": 100}, "individualMajorINNAnnualDeductible": {"currency": "USD", "amount": 100}, "_type": "CapDeductibleDetailEntity"}], "dentalMaximums": [{"individualBasicINNAnnualMaximum": {"currency": "USD", "amount": 1000}, "individualMajorINNAnnualMaximum": {"currency": "USD", "amount": 1000}, "_type": "CapDentalMaximumEntity"}], "insureds": [{"isFullTimeStudent": false, "isMain": true, "relationshipToPrimaryInsuredCd": "Self", "registryTypeId": "{{partyRegistryId}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity"}], "_modelName": "CapDentalUnverifiedPolicy", "_modelVersion": "1", "_modelType": "UnverifiedPolicy", "_archived": false, "_type": "CapDentalUnverifiedPolicy"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl.CapDentalPolicyInfoWrapperModel"}}