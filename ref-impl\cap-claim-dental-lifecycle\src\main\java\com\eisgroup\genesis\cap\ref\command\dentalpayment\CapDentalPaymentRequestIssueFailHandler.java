/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.common.repository.api.CapCommonRepository;
import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.model.Variation;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command that marks {@link CapDentalPaymentEntity} issue request failed.
 *
 * <AUTHOR>
 * @since 25.10
 */
@Modifying
public class CapDentalPaymentRequestIssueFailHandler implements ProductCommandHandler<IdentifierRequest, CapDentalPaymentEntity> {

    @Autowired
    private CapCommonRepository<CapDentalPaymentEntity> capPaymentRepository;

    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapDentalPaymentEntity load(@Nonnull IdentifierRequest input) {
        return capPaymentRepository.load(input.getKey(), modelResolver.getModelName(), getVariation());
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull IdentifierRequest input, @Nonnull CapDentalPaymentEntity entity) {
        return Lazy.of(entity).get();
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity save(@Nonnull IdentifierRequest input, @Nonnull CapDentalPaymentEntity entity) {
        return capPaymentRepository.save(entity);
    }

    @Override
    public String getName() {
        return CapDentalPaymentCommands.REQUEST_ISSUE_FAIL_PAYMENT;
    }

    @Nonnull
    @Override
    public Variation getVariation() {
        return PaymentVariations.PAYMENT;
    }

}
