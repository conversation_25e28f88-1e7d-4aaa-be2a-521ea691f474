Transformation CapTemplateFinancialData {
    Input {
        DentalInternal.CapDentalFinancialDataInput as input
    }
    Output {
        CapDentalPaymentTemplate.CapDentalPaymentTemplateEntity
    }

    Attr originSource is input.originSource
    Attr paymentDetailsTemplate is createPaymentDetailsTemplate(input)

    Producer createPaymentDetailsTemplate(input) {
        Attr paymentAllocationTemplates is transformTemplateDetails(input.settlements).transformed
    }

    Producer transformTemplateDetails(settlementUri) {
        Var settlement is ExtLink(settlementUri)
        Var transformationName is settlement._modelName + "ToFinancialData"

        Attr transformed is SafeInvoke(transformationName, Transform(transformationName, settlement))
    }
}