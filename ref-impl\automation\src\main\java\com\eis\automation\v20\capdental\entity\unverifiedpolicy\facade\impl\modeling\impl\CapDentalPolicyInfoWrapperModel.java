/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.impl.CapPolicyInfoWrapperModel;
import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.interf.ICapPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoWrapperModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPolicyInfoWrapperModel extends CapPolicyInfoWrapperModel implements ICapDentalPolicyInfoWrapperModel {

    @JsonSerialize(as = CapDentalPolicyInfoModel.class)
    public ICapPolicyInfoModel getEntity() {
        return super.getEntity();
    }

    @JsonDeserialize(as = CapDentalPolicyInfoModel.class)
    public CapDentalPolicyInfoWrapperModel setEntity(ICapPolicyInfoModel entity) {
        super.setEntity(entity);
        return this;
    }
}
