package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.loss.repository.ClaimLossRepository;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.model.ModelResolver;
import com.google.gson.JsonObject;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossPendHandlerTest {

    @InjectMocks
    private CapDentalLossPendHandler handler;

    @Mock
    private ClaimLossRepository<CapLoss> claimLossRepository;

    @Mock
    private ModelResolver modelResolver;

    private IdentifierRequest input = new IdentifierRequest(new JsonObject());
    private CapDentalLossEntity entity = (CapDentalLossEntity) ModelInstanceFactory.createRootInstance("CapDentalLoss", "1");

    @Before
    public void setUp() {
        when(modelResolver.getModelName()).thenReturn("CapDentalLoss");
    }

    @Test
    public void shouldLoadEntity() {
        //given
        when(claimLossRepository.load(any(),anyString())).thenReturn(Lazy.of(entity));

        //when
        CapLoss result = handler.load(input);

        //then
        assertThat(result.getModelFactory().getModelName(), equalTo("CapDentalLoss"));
    }

    @Test
    public void testHandlerExecute() {
        //when
        CapLoss result = handler.execute(input, entity);

        //then
        assertThat(result.getModelFactory().getModelName(), equalTo("CapDentalLoss"));
    }

    @Test
    public void shouldSaveEntity() {
        //given
        when(claimLossRepository.save(entity)).thenReturn(Lazy.of(entity));

        //when
        handler.save(input, entity);

        //then
        verify(claimLossRepository).save(entity);
    }

    @Test
    public void testReturnHandlerName() {
        assertThat(handler.getName(), equalTo("pendLoss"));
    }
}
