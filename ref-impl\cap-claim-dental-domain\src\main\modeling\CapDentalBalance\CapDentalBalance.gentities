//EISDEVTS-49202
@Description("The Root Entity of Balance Details Domain.")
Entity CapDentalBalanceEntity is CapDentalBalance {

    //EISDEVTS-49202
    @Description("Details of how the balance was calculated.")
    Attr balanceItems: *CapDentalBalanceItemEntity

    //EISDEVTS-42666
    @Description("The date when the balance entity was created.")
    @Searchable
    Attr creationDate: Datetime

    //EISDEVTS-49832
    @Description("Link to the related dental claim.")
    @Searchable
    ExtLink originSource: RootEntity

    //EISDEVTS-49832
    @Description("Link to the CEM.")
    @Searchable
    ExtLink payee: RootEntity

    //EISDEVTS-49832
    @Description("Total calculated balance.")
    Attr totalBalanceAmount: Money
}
//EISDEVTS-49202
@Description("Details of how the balance was calculated.")
Entity CapDentalBalanceItemEntity {

    //EISDEVTS-49202
    @Description("List of allocations in the payments.")
    Attr actualAllocations: *CapDentalBalanceItemActualAllocationEntity

    //EISDEVTS-49202
    @Description("Amount that was paid.")
    Attr actualNetAmount: Money

    //EISDEVTS-49202
    @Description("Amount that was paid including applied adjustments.")
    Attr balancedNetAmount: Money

    //EISDEVTS-49202
    @Description("Payment post date.")
    Attr paymentDate: Datetime

    //EISDEVTS-49202
    @Description("Payment number where payable item appeared.")
    Attr paymentNumber: String

    //EISDEVTS-49202
    @Description("Amount that had to be paid.")
    Attr scheduledNetAmount: Money
}
//EISDEVTS-49202
@Description("Actual allocation details.")
Entity CapDentalBalanceItemActualAllocationEntity is CapDentalBaseBalanceItemAllocation {

    //EISDEVTS-49202
    @Description("List of allocations related to the payment from the balance adjustment payment variations.")
    Attr adjustmentAllocations: *CapDentalBalanceItemAdjustmentAllocationEntity

    //EISDEVTS-49202
    @Description("List of additions applied to the allocation.")
    Attr allocationAdditions: *CapDentalBalanceItemActualAllocationAdditionEntity

    //EISDEVTS-49202
    @Description("Actual gross amount after applying adjustment allocations gross amounts.")
    Attr allocationBalancedGrossAmount: Money

    //EISDEVTS-49202
    @Description("Allocation loss details.")
    Attr allocationLossInfo: CapDentalBalanceItemAllocationLossInfoEntity

    //EISDEVTS-49202
    @Description("List of reductions applied to the allocation.")
    Attr allocationReductions: *CapDentalBalanceItemActualAllocationReductionEntity

    //EISDEVTS-49202
    @Description("List of taxes applied to the allocation.")
    Attr allocationTaxes: *CapDentalBalanceItemActualAllocationTaxEntity

    //EISDEVTS-49202
    @Description("List of allocations related to the payment from the schedule.")
    Attr scheduledAllocations: *CapDentalBalanceItemScheduledAllocationEntity
}
//EISDEVTS-49202
@Description("Actual allocation loss details.")
Entity CapDentalBalanceItemAllocationLossInfoEntity {

    //EISDEVTS-49202
    @Description("Link to the claim.")
    ExtLink lossSource: RootEntity

    //EISDEVTS-49202
    @Description("Claim loss type.")
    Attr lossType: String
}
//EISDEVTS-49202
@Description("Addition details applied to the actual allocation.")
Entity CapDentalBalanceItemActualAllocationAdditionEntity is CapDentalBaseBalanceItemAllocationAddition {

    //EISDEVTS-49202
    @Description("Actual addition amount after applying adjustment allocations addition amounts.")
    Attr balancedAppliedAmount: Money
}
//EISDEVTS-49202
@Description("Reduction details applied to the actual allocation.")
Entity CapDentalBalanceItemActualAllocationReductionEntity is CapDentalBaseBalanceItemAllocationReduction {

    //EISDEVTS-49202
    @Description("Actual reduction amount after applying adjustment allocations reduction amounts.")
    Attr balancedAppliedAmount: Money
}
//EISDEVTS-49202
@Description("Tax details applied to the actual allocation.")
Entity CapDentalBalanceItemActualAllocationTaxEntity is CapDentalBaseBalanceItemAllocationTax {

    //EISDEVTS-49202
    @Description("Actual tax amount after applying adjustment allocations tax amounts.")
    Attr balancedAppliedAmount: Money
}
//EISDEVTS-49202
@Description("Scheduled allocation details.")
Entity CapDentalBalanceItemScheduledAllocationEntity is CapDentalBaseBalanceItemAllocation {

}
//EISDEVTS-49202
@Description("Adjustment allocation details.")
Entity CapDentalBalanceItemAdjustmentAllocationEntity is CapDentalBaseBalanceItemAllocation {

}
//EISDEVTS-49202
@Description("Defines payment transaction information.")
Entity CapDentalBalancePaymentEntity is CapPaymentInfo {

    //EISDEVTS-49202
    @Description("Payment transaction details.")
    Attr paymentDetails: CapDentalPaymentDetailsEntity

    //EISDEVTS-49202
    @Description("Payment transaction state.")
    Attr state: String
}
//EISDEVTS-49202
@Description("Input for balance calculation rules.")
Entity CapDentalBalanceCalculationRulesInput {

    //EISDEVTS-49202
    @Description("Balance calculation rules engine input attribute. List of the payment transactions in the databse.")
    Attr actualPayments: *CapDentalBalancePaymentEntity

    //EISDEVTS-49202
    @Description("Balance calculation rules engine input attribute. List of the balance adjustment payment variations.")
    Attr adjustmentPayments: *CapDentalBalancePaymentEntity

    //EISDEVTS-49202
    @Description("Balance calculation rules engine input attribute. List of the scheduled payments.")
    Attr scheduledPayments: *CapDentalBalancePaymentEntity
}
//EISDEVTS-49202
@Description("Output of balance calculation rules.")
Entity CapDentalBalanceCalculationRulesOutput {

    //EISDEVTS-49202
    @Description("Balance calculation rules engine output attribute. List of generated balances.")
    Attr balances: *CapDentalBalanceEntity
}
//EISDEVTS-49202
@Description("Input for balance amount allocation rules.")
Entity CapDentalBalanceAmountAllocationRulesInput {

    //EISDEVTS-49202
    @Description("Balance amount allocation rules engine input attribute. List of the balances.")
    Attr balances: *CapDentalBalanceEntity

    //EISDEVTS-49202
    @Description("Balance amount allocation rules engine input attribute. Balance transcation with amount is allocated.")
    Attr balanceTransaction: CapDentalBalancePaymentEntity
}
//EISDEVTS-49202
@Description("Output of balance amount allocation rules.")
Entity CapDentalBalanceAmountAllocationRulesOutput {

    //EISDEVTS-49202
    @Description("Balance amount allocation rules engine output attribute. List of the generated allocations.")
    Attr balanceTransaction: CapDentalBalancePaymentEntity
}