<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ms-claim-dental-applications-pom</artifactId>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-events-app</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.eisgroup.genesis.events</groupId>
            <artifactId>events-bundle</artifactId>
            <classifier>property</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.apps</groupId>
            <artifactId>spring-app-bundle</artifactId>
            <classifier>app</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.events</groupId>
            <artifactId>events-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.events</groupId>
            <artifactId>events-bundle</artifactId>
            <classifier>producer</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-claim-dental-events</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
            <artifactId>cap-dental-transformation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.confidential-data</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.search</artifactId>
            <classifier>events</classifier>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>column-store-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.data</groupId>
            <artifactId>search-index-bundle</artifactId>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.stream</groupId>
            <artifactId>streams-bundle</artifactId>
            <type>tile</type>
            <classifier>consumer</classifier>
        </dependency>

        <!-- DLQ Runtime -->
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>lifecycle-bundle.dead-letter-queue</artifactId>
            <type>tile</type>
        </dependency>

        <!-- Transformations -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-transformation-bundle</artifactId>
            <classifier>events</classifier>
            <type>tile</type>
        </dependency>

        <!-- BAM -->
        <dependency>
            <groupId>com.eisgroup.genesis.bam</groupId>
            <artifactId>bam-bundle</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>

        <!-- Logging -->
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>runtime</scope>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.loss</groupId>
            <artifactId>cap-loss-events</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.adjudication</groupId>
            <artifactId>cap-adjudication-events</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-events</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.party</groupId>
            <artifactId>cap-party-role-events</artifactId>
            <version>${cap.core.version}</version>
        </dependency>
    </dependencies>
</project>
