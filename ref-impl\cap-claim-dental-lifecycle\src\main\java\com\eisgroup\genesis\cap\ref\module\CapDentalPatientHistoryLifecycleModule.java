/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import java.util.Collection;
import java.util.List;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryCancelHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPatientHistoryLifecycleConfig;
import com.eisgroup.genesis.cap.ref.repository.config.CapDentalPatientHistoryRepositoryConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;

import javax.annotation.Nonnull;

public class CapDentalPatientHistoryLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        return List.of(
            new CapDentalPatientHistoryInitHandler(),
            new CapDentalPatientHistoryUpdateHandler(),
            new CapDentalPatientHistoryCancelHandler()
        );
    }

    @Override
    public String getModelType() {
        return "CapDentalPatientHistoryModelType";
    }

    @Override
    public String getModelName() {
        return "CapDentalPatientHistory";
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[]{
                CapBaseCommandConfig.class,
                CapDentalPatientHistoryLifecycleConfig.class,
                CapDentalPatientHistoryRepositoryConfig.class

        };
    }

    @Nonnull
    @Override
    public String getStateMachineName() {
        return "CapDentalPatientHistory";
    }
}
