@Passthrough
@KeyStrategy("PASSTHROUGH")
@PayloadMutator
@CommandListener("Execute", "submitLoss")
Transformation CapDentalLossEnrichment {
    Input {
        CapDentalLoss.CapDentalLossEntity as in
    }
    Output {
        CapDentalLoss.CapDentalLossEntity
    }

    Attr policy is transformPolicy(in.policyId, in).transformed
    Attr lossDetail is createLossDetail(in.lossDetail)

    Producer transformPolicy(policyId, loss) {
        Var policy is ExtLink(SafeInvoke(policyId, AsExtLink(policyId)))
        Var transformationName is policy._modelName + "To" + loss._modelName

        Attr transformed is SafeInvoke(transformationName, Transform(transformationName, input(policy)))

        // due to Transform not supporting external model inputs
        @Passthrough
        Producer input(policy) {
            Attr _type is "CapPolicyEntity"
            Attr _modelName is "CapPolicyHolder"
        }
    }

    @Passthrough("lossDetail")
    Producer createLossDetail(lossDetail) {
        Attr claimData is createClaimData(lossDetail.claimData)
    }

    @Passthrough("claimData")
    @PassthroughExclude("initialDateOfService")
    Producer createClaimData(claimData) {
        Attr initialDateOfService is AsTime(First(Root().in.lossDetail.submittedProcedures<dateOfServiceAsc>).dateOfService)
    }

    Sort dateOfServiceAsc {
        "dateOfService" -> "ASC"
    }
}