/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalMaximumModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalMaximumModel extends TypeModel implements ICapDentalMaximumModel {

    private Money individualBasicINNAnnualMaximum;
    private Money individualMajorINNAnnualMaximum;
    private Money implantsINNAnnualMaximum;
    private Money orthoINNAnnualMaximum;
    private Money individualPreventiveINNAnnualMaximum;


    public Money getIndividualBasicINNAnnualMaximum() {
        return individualBasicINNAnnualMaximum;
    }

    public void setIndividualBasicINNAnnualMaximum(Money individualBasicINNAnnualMaximum) {
        this.individualBasicINNAnnualMaximum = individualBasicINNAnnualMaximum;
    }

    public Money getIndividualMajorINNAnnualMaximum() {
        return individualMajorINNAnnualMaximum;
    }

    public void setIndividualMajorINNAnnualMaximum(Money individualMajorINNAnnualMaximum) {
        this.individualMajorINNAnnualMaximum = individualMajorINNAnnualMaximum;
    }

    public Money getImplantsINNAnnualMaximum() {
        return implantsINNAnnualMaximum;
    }

    public void setImplantsINNAnnualMaximum(Money implantsINNAnnualMaximum) {
        this.implantsINNAnnualMaximum = implantsINNAnnualMaximum;
    }

    public Money getOrthoINNAnnualMaximum() {
        return orthoINNAnnualMaximum;
    }

    public void setOrthoINNAnnualMaximum(Money orthoINNAnnualMaximum) {
        this.orthoINNAnnualMaximum = orthoINNAnnualMaximum;
    }

    public Money getIndividualPreventiveINNAnnualMaximum() {
        return individualPreventiveINNAnnualMaximum;
    }

    public void setIndividualPreventiveINNAnnualMaximum(Money individualPreventiveINNAnnualMaximum) {
        this.individualPreventiveINNAnnualMaximum = individualPreventiveINNAnnualMaximum;
    }
}
