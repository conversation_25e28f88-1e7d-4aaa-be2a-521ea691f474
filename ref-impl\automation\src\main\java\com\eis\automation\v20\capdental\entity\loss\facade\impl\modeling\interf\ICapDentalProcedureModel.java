/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf;

import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;
import org.javamoney.moneta.Money;

import java.time.LocalDate;
import java.util.List;

public interface ICapDentalProcedureModel extends ITypeModel {

    String getToothSystem();

    String getProcedureType();

    Integer getQuantity();

    String getProcedureCode();

    void setProcedureCode(String procedureCode);

    void setDateOfService(LocalDate dateOfService);

    void setSubmittedFee(Money submittedFee);

    String getDescription();

    String getPreauthorizationNumber();

    String getToothArea();

    void setToothArea(String toothArea);

    List<String> getSurfaces();

    void setSurfaces(List<String> surfaces);

    List<String> getToothCodes();

    void setToothCodes(List<String> toothCodes);

    Money getSubmittedFee();

    Boolean getPredetInd();

    void setPredetInd(Boolean predetInd);

    LocalDate getDateOfService();

    LocalDate getPriorProsthesisPlacementDate();

    ICapDentalPreauthorizationModel getPreauthorization();

    ICapDentalOrthodonticModel getOrtho();

    void setOrtho(ICapDentalOrthodonticModel ortho);

    ICapDentalTreatmentReasonModel getTreatmentReason();

    List<ICapDentalDiagnosisCodeModel> getDiagnosisCodes();

    ICapDentalProcedureCoordinationOfBenefitsModel getCob();
}
