StateMachine CapDentalSettlement {
    EntryState uninitialized {
        Using initSettlement command transit to Adjudicating
    }

    State Adjudicating {
        @CapCreateVersion
        Using readjudicateSettlement command transit to Adjudicating
        Using adjudicateSettlement command transit to Pending
        @CapCreateVersion
        Using refreshSettlement command transit to Adjudicating
    }

    State Pending {
        Using approveSettlement command transit to Approved
        @CapCreateVersion
        Using readjudicateSettlement command transit to Adjudicating
        Using disapproveSettlement command transit to Disapproved
        @CapCreateVersion
        Using refreshSettlement command transit to Adjudicating
    }

    State Approved {
        @CapCreateVersion
        Using readjudicateSettlement command transit to Adjudicating
        Using closeSettlement command transit to Closed
        @CapCreateVersion
        Using refreshSettlement command transit to Adjudicating
    }

    State Disapproved {
        @CapCreateVersion
        Using readjudicateSettlement command transit to Adjudicating
        @CapCreateVersion
        Using refreshSettlement command transit to Adjudicating
    }

    State Closed {
        @CapCreateVersion
        Using readjudicateSettlement command transit to Adjudicating
        @CapCreateVersion
        Using refreshSettlement command transit to Adjudicating
    }
}