/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.loss.command.ClaimLossSubmitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;

/**
 * The command that performs validation of the provided claim loss
 * <AUTHOR>
 * @since 22.6
 */
@Modifying
public class CapDentalLossSubmitHandler extends ClaimLossSubmitHandler<IdentifierRequest, CapLoss> {

    @Autowired
    private CapDentalLossValidator capDentalLossValidator;

    @Override
    public Streamable<ErrorHolder> validateAsync(IdentifierRequest request, CapLoss loadedEntity) {
        return Streamable.concat(super.validateAsync(request, loadedEntity),capDentalLossValidator.validateLinks(loadedEntity.getLossDetail()));
    }
}
