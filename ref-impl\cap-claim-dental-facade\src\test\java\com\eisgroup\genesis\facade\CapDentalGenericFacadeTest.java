/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;


import com.eisgroup.genesis.cap.transformation.endpoint.CommonTransformationEndpoint;
import org.junit.Before;
import org.junit.Test;

import static com.eisgroup.genesis.facade.module.FacadeModule.GENERIC_MODEL_TYPE;
import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

/**
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalGenericFacadeTest {

    private CapDentalGenericFacade facade;

    @Before
    public void setup() {
        facade = new CapDentalGenericFacade();
    }

    @Test
    public void shouldReturnCorrectEndpoints() {
        // when
        var result = facade.getEndpoints();
        // then
        assertThat(result, notNullValue());
        assertThat(result.stream().anyMatch(endoint -> CommonTransformationEndpoint.class.isAssignableFrom(endoint.getClass())), equalTo(true));
    }

    @Test
    public void shouldReturnModelName() {
        assertThat(facade.getModelName(), equalTo(GENERIC_MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(facade.getModelType(), equalTo(GENERIC_MODEL_TYPE));
    }

    @Test
    public void shouldReturnVersion() {
        assertThat(facade.getFacadeVersion(), equalTo(1));
    }

}
