<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="openClaim" name="Open Claim" isExecutable="true">
    <startEvent id="startEvent1" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="approveSettlement" name="call approveSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${settlementKey}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelName}" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="approveSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="approvedSettlement"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_uri" sourceType="string" target="settlementURI"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <serviceTask id="openLoss" name="call openLoss" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${lossKey}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalLoss" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="openLoss" target="commandName"></flowable:eventInParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <endEvent id="sid-BC2FD42E-CC26-43AE-BB6D-CAD0F627660E"></endEvent>
    <sequenceFlow id="sid-13505FBB-6742-4EFB-BB94-C95BE8724E74" sourceRef="openLoss" targetRef="sid-BC2FD42E-CC26-43AE-BB6D-CAD0F627660E"></sequenceFlow>
    <sequenceFlow id="sid-BB7B728E-3B9B-48D4-B4AB-8520409DA193" sourceRef="startEvent1" targetRef="approveSettlement"></sequenceFlow>
    <serviceTask id="constructAccumTxPayload" name="Construct Accumulator Tx Payload" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('approveSettlementAccumulatorTxInput').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{"body":{"_key" : ${execution.getVariable('settlementKey')}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[txInput]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-6CEE7636-C0E2-4E29-9C6B-681D39B58650" sourceRef="approveSettlement" targetRef="constructAccumTxPayload"></sequenceFlow>
    <serviceTask id="writeAccumTransaction" name="Write Accumulator Transaction" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'entity': ${txInput.body.success.output}}" target="payload"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapAccumulatorTransaction" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="writeTransaction" target="commandName"></flowable:eventInParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-D3CEED58-BBF9-4D42-8A19-2CD7E3E86A0B" sourceRef="writeAccumTransaction" targetRef="openLoss"></sequenceFlow>
    <exclusiveGateway id="sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20"></exclusiveGateway>
    <sequenceFlow id="sid-0745BB62-CB41-4C08-A022-7D923ACB35EE" sourceRef="constructAccumTxPayload" targetRef="sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20"></sequenceFlow>
    <sequenceFlow id="sid-FE87F8D4-9466-477B-910F-408F93C39B74" name="Transaction has data" sourceRef="sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20" targetRef="writeAccumTransaction">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${txInput.body.success.output.data != null && txInput.body.success.output.data.size() > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-2D6EF8F3-5A08-4637-B347-3E99DB29F625" sourceRef="sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20" targetRef="openLoss">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${txInput.body.success.output.data == null || txInput.body.success.output.data.size() == 0}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_openClaim">
    <bpmndi:BPMNPlane bpmnElement="openClaim" id="BPMNPlane_openClaim">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="60.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="approveSettlement" id="BPMNShape_approveSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="120.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="openLoss" id="BPMNShape_openLoss">
        <omgdc:Bounds height="80.0" width="100.0" x="720.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-BC2FD42E-CC26-43AE-BB6D-CAD0F627660E" id="BPMNShape_sid-BC2FD42E-CC26-43AE-BB6D-CAD0F627660E">
        <omgdc:Bounds height="28.0" width="28.0" x="855.0" y="164.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="constructAccumTxPayload" id="BPMNShape_constructAccumTxPayload">
        <omgdc:Bounds height="80.00000000000003" width="100.0" x="255.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="writeAccumTransaction" id="BPMNShape_writeAccumTransaction">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="138.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20" id="BPMNShape_sid-F622B705-B8E0-49CB-8723-6FD5D7A3DF20">
        <omgdc:Bounds height="40.0" width="40.0" x="390.0" y="158.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-D3CEED58-BBF9-4D42-8A19-2CD7E3E86A0B" id="BPMNEdge_sid-D3CEED58-BBF9-4D42-8A19-2CD7E3E86A0B" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="684.9499999999999" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="720.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-13505FBB-6742-4EFB-BB94-C95BE8724E74" id="BPMNEdge_sid-13505FBB-6742-4EFB-BB94-C95BE8724E74" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="819.9499999999999" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="855.0" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6CEE7636-C0E2-4E29-9C6B-681D39B58650" id="BPMNEdge_sid-6CEE7636-C0E2-4E29-9C6B-681D39B58650" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.000000000000014">
        <omgdi:waypoint x="219.95" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="254.99999999989527" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FE87F8D4-9466-477B-910F-408F93C39B74" id="BPMNEdge_sid-FE87F8D4-9466-477B-910F-408F93C39B74" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="429.48805220883537" y="178.45758928571425"></omgdi:waypoint>
        <omgdi:waypoint x="584.9999999999982" y="178.11124721603562"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-BB7B728E-3B9B-48D4-B4AB-8520409DA193" id="BPMNEdge_sid-BB7B728E-3B9B-48D4-B4AB-8520409DA193" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="89.94999797575196" y="178.0"></omgdi:waypoint>
        <omgdi:waypoint x="119.99999999993875" y="178.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-0745BB62-CB41-4C08-A022-7D923ACB35EE" id="BPMNEdge_sid-0745BB62-CB41-4C08-A022-7D923ACB35EE" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.000000000000014" flowable:targetDockerX="20.5" flowable:targetDockerY="20.5">
        <omgdi:waypoint x="354.95000000000005" y="178.2367298578199"></omgdi:waypoint>
        <omgdi:waypoint x="390.4047619047619" y="178.40476190476195"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-2D6EF8F3-5A08-4637-B347-3E99DB29F625" id="BPMNEdge_sid-2D6EF8F3-5A08-4637-B347-3E99DB29F625" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="410.49999999999994" y="197.44076510721248"></omgdi:waypoint>
        <omgdi:waypoint x="410.5" y="281.0"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="281.0"></omgdi:waypoint>
        <omgdi:waypoint x="770.0" y="217.95000000000002"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>