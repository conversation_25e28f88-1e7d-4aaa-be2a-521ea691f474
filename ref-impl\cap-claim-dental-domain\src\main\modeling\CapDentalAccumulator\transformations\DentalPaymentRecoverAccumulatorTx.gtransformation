@EventListener("declinePayment", "voidPayment", "stopPayment")
@TargetEntityCommand("writeTransaction")
Transformation DentalPaymentRecoverAccumulatorTx {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity
    }

    Var loss is ExtLink(payment.originSource)
    Var systemTime is Now()

    Attr transactionTimestamp is systemTime
    Attr policyURI is loss.policyId
    //GENESIS-366362
    Attr customerURI is loss.lossDetail.claimData.policyholderRole.registryId
    Attr sourceURI is ToExtLink(payment)._uri
    Attr data is SafeInvoke(payment.paymentDetails.paymentAllocations, accumulatorDataForAllocation(payment.paymentDetails.paymentAllocations).data)

    Producer accumulatorDataForAllocation(allocation) {
        Attr data is SafeInvoke(allocation.allocationDentalDetails.accumulatorDetails, createTxData(allocation.allocationDentalDetails.accumulatorDetails, allocation))
    }

    Producer createTxData(accumulatorDetails, allocation) {
        Attr amount is accumulatorDetails.accumulatorAmount.amount
        Attr party is allocation.allocationDentalDetails.patient
        Attr type is "DentalPaymentRecover" + "_" + accumulatorDetails.accumulatorType + "_" + accumulatorDetails.networkType + "_" + accumulatorDetails.renewalType + "_" + accumulatorDetails.appliesToProcedureCategory
        Attr transactionDate is systemTime
    }

}
