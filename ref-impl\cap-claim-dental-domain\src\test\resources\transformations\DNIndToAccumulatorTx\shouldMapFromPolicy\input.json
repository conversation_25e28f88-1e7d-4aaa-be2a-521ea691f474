{"_key": {"variation": "policy", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2}, "_type": "DNIndividualPolicySummary", "_modelName": "DNIndividual", "_modelVersion": "1", "_modelType": "Policy", "_timestamp": "2025-07-02T01:29:57.885Z", "_variation": "policy", "createdFromQuoteRev": 2, "transactionDetails": {"txCreateDate": "2025-07-02T01:29:35.572Z", "_type": "DNTransactionDetails", "txType": "AMENDMENT", "txEffectiveDate": "2024-08-01T00:00:00Z", "_key": {"id": "38e0b2b8-bc4e-30da-b1f2-eb2c09ba15c0", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "txReasonCd": "Cascaded from Master Policy", "txOtherReason": "Cascaded from Master Policy"}, "currencyCd": "USD", "accessTrackInfo": {"updatedBy": "qa", "createdBy": "qa", "_type": "DNPolicyAccessTrackInfo", "updatedOn": "2025-07-02T01:29:57.885Z", "createdOn": "2024-06-05T03:10:02.235Z", "_key": {"id": "7583356b-03d5-3f43-a4aa-d80050fd9356", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, "policyDetail": {"_type": "DNPolicyDetail", "_key": {"id": "f0468b2c-bc65-3584-aa2e-7b1f6cc4a97c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, "countryCd": "US", "policyNumber": "DN1748471639", "issueCountryCd": "US", "masterLink": {"_key": {"id": "814670a5-4418-3c6d-bba7-27f601670da6", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "_type": "DNMasterLink", "source": {"_uri": "gentity://PolicySummary/DNMaster/policy/17ca5922-3e7f-362a-bf8c-149cca6dfcbf/2"}}, "inceptionDate": "2024-08-01T00:00:00Z", "state": "issued", "workStateCd": "AK", "insureds": [{"relationshipDetails": {"originalHireDate": "2023-07-31", "classGroupExtLink": {"_uri": "geroot://CensusClassBase/CensusClass//549b4424-a81b-31d4-8c00-c4425bd43af3"}, "employmentTypeCd": "FullTime", "payrollFrequencyCd": "Monthly", "_type": "DNInsuredRelationshipInfo", "payTypeCd": "Salary", "_key": {"id": "054f0dbe-8b71-378e-85bb-f9f282787dc4", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "f68ecf6d-6696-356a-93e1-2d7295b59dbd", "variation": "policy"}}, "_type": "DNInsured", "insuredRoleNameCd": "PrimaryInsured", "relationshipToPrimaryInsuredCd": "Self", "insuredInfo": {"_ref": "0d410431-2f3a-318b-8343-018a208a7744"}, "_key": {"id": "f68ecf6d-6696-356a-93e1-2d7295b59dbd", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, {"_type": "DNInsured", "insuredRoleNameCd": "SpouseInsured", "relationshipToPrimaryInsuredCd": "SpouseDomestic<PERSON><PERSON><PERSON>", "_key": {"id": "57cfde65-903e-30bb-bebe-b49a6fde59df", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "insuredInfo": {"_ref": "d08f3f8e-3a8e-3eaf-a760-8c9bd3ab9fe5"}}, {"_type": "DNInsured", "insuredRoleNameCd": "ChildInsured", "relationshipToPrimaryInsuredCd": "Dependent<PERSON><PERSON><PERSON>", "_key": {"id": "20ae74fa-25a3-351d-a414-82c8eb62c5b1", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "offerStatus": "offered", "insuredInfo": {"_ref": "36d439d6-850c-3481-927e-69756dcf6fee"}}], "residenceStateCd": "AZ", "individualPackagingDetail": {"_key": {"id": "12357aaf-4a3a-39d9-9fea-f4621ae98d3f", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "plan": {"planCd": "Low", "planName": "Low", "covDef": {"code": "DENTAL", "cosmeticServicesCoverage": {"cosmeticAnnualDeductible": {"_type": "DNCosmeticAnnualDeductible", "_key": {"id": "e5117504-2652-38ff-9e9f-2818343d5c1c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b1526330-ba39-3bc0-83ba-305dbd8758e3", "variation": "policy"}}, "cosmeticDeductibleType": "None", "cosmeticLifetimeDeductible": {"_type": "DNCosmeticLifetimeDeductible", "_key": {"id": "77ce3933-90ee-3fa4-a71c-ef50d2fe9125", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b1526330-ba39-3bc0-83ba-305dbd8758e3", "variation": "policy"}}, "cosmeticMaximumType": "Lifetime", "cosmeticAnnualMaximum": {"_type": "DNCosmeticAnnualMaximum", "_key": {"id": "373b55be-8752-3976-b887-0e446b80dc36", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b1526330-ba39-3bc0-83ba-305dbd8758e3", "variation": "policy"}}, "cosmeticLifetimeMaximum": {"lifetimeMaximumAppliesTo": "CosmeticServices", "lifetimeMaximumInNetworkAmount": {"amount": 1000, "currency": "USD"}, "lifetimeMaximumOutOfNetworkAmount": {"amount": 1000, "currency": "USD"}, "_type": "DNCosmeticLifetimeMaximum", "_key": {"id": "824bb3c3-9260-3944-b03e-a3acf2deb889", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b1526330-ba39-3bc0-83ba-305dbd8758e3", "variation": "policy"}}, "cosmeticWaitingPeriod": 0, "_type": "DNCosmeticServicesCoverage", "_key": {"id": "b1526330-ba39-3bc0-83ba-305dbd8758e3", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "tmjCoverage": {"tmjLifetimeMaximum": {"lifetimeMaximumAppliesTo": "TMJ", "lifetimeMaximumInNetworkAmount": {"amount": 1000, "currency": "USD"}, "lifetimeMaximumOutOfNetworkAmount": {"amount": 1000, "currency": "USD"}, "_type": "DNTmjLifetimeMaximum", "_key": {"id": "c3c9a40f-eab0-392a-a19e-58c17fbfc69f", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "47841d8a-df92-3e69-b639-6e4a229d3c6f", "variation": "policy"}}, "tmjLifetimeDeductible": {"_type": "DNTmjLifetimeDeductible", "_key": {"id": "5eccc780-b2f7-36e0-b4c1-d22f047b9aa7", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "47841d8a-df92-3e69-b639-6e4a229d3c6f", "variation": "policy"}}, "tmjAnnualDeductible": {"_type": "DNTmjAnnualDeductible", "_key": {"id": "9ddb0552-9be4-322f-bafa-75126e72ea81", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "47841d8a-df92-3e69-b639-6e4a229d3c6f", "variation": "policy"}}, "tmjMaximumType": "Lifetime", "tmjAnnualMaximum": {"_type": "DNTmjAnnualMaximum", "_key": {"id": "da21536a-4db0-3eb7-a89e-696bf139e15a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "47841d8a-df92-3e69-b639-6e4a229d3c6f", "variation": "policy"}}, "tmjDeductibleType": "None", "_type": "DNTmjCoverage", "_key": {"id": "47841d8a-df92-3e69-b639-6e4a229d3c6f", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "enrollmentDate": "2024-06-01", "exclusions": {"isMissingToothCovered": true, "_type": "DNExclusions", "_key": {"id": "2228b19c-f302-3c41-b2f3-1d067de0e0ea", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "_key": {"id": "1f065e62-e7ab-3960-b1f2-6346f2902654", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "3f4ef348-bb59-3bd1-a77b-c59feafedd6f", "variation": "policy"}, "effDate": "2024-08-01T00:00:00Z", "eligibilities": {"waitingPeriodAmount": 30, "waitingPeriodModeCd": "Days", "minHourlyReq": 20, "eligibilityTypeCd": "ExistingEmployees", "waitingPeriodDefCd": "1stMonthFollowingAmountAndMode", "_type": "DNEligibility", "_key": {"id": "44ef3fa2-462d-399a-a329-af260cfff32e", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "whoCovered": {"tierCd": "Family", "_type": "D<PERSON>ier", "_key": {"id": "e32cf9f7-49ec-3571-9ed8-7981a1a50155", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "coinsuranceDetails": {"reimbursementOONOptions": "80th", "coinsurances": [{"coinsuranceINAmount": 0.8, "_type": "DNCoinsurance", "coinsuranceOONAmount": 0.8, "coinsuranceServiceType": "Preventive", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "c608830e-9cad-44e1-b3b0-b770145f2cd5", "parentId": "8f90daa8-1c95-3756-9638-b92c52a61e4c", "variation": "policy"}}, {"coinsuranceINAmount": 0.5, "_type": "DNCoinsurance", "coinsuranceOONAmount": 0.5, "coinsuranceServiceType": "Basic", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "a3ace00d-8663-4574-8e36-566c5a6cecca", "parentId": "8f90daa8-1c95-3756-9638-b92c52a61e4c", "variation": "policy"}}, {"coinsuranceINAmount": 0.5, "_type": "DNCoinsurance", "coinsuranceOONAmount": 0.5, "coinsuranceServiceType": "Major", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "67ecbc8b-010c-44a9-9c3b-862d9c4827b5", "parentId": "8f90daa8-1c95-3756-9638-b92c52a61e4c", "variation": "policy"}}], "_type": "DNCoinsuranceDefinition", "_key": {"id": "8f90daa8-1c95-3756-9638-b92c52a61e4c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "ratingDetails": {"rateBasisCd": "PerCoverageTier", "_type": "DNCoverageRating", "_key": {"id": "a2386e29-590c-39e0-8299-6d3c69368605", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "implantsCoverage": {"implantsLifetimeMaximum": {"_type": "DNImplantsLifetimeMaximum", "_key": {"id": "34afda34-c9eb-372e-a3da-f37cf9aac64d", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "27cc6043-6205-3d93-a7aa-8b9871f44618", "variation": "policy"}}, "implantsAnnualMaximum": {"annualMaximumAppliesTo": "Implants", "annualMaximumOutOfNetworkAmount": {"amount": 750, "currency": "USD"}, "annualMaximumInNetworkAmount": {"amount": 750, "currency": "USD"}, "_type": "DNImplantsAnnualMaximum", "_key": {"id": "6bd03bb1-1a8f-3abc-85ca-237c915db450", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "27cc6043-6205-3d93-a7aa-8b9871f44618", "variation": "policy"}}, "isImplantsMaximumAppliedTowardPlanMaximum": true, "implantsMaximumType": "Annual", "_type": "DNImplantsCoverage", "_key": {"id": "27cc6043-6205-3d93-a7aa-8b9871f44618", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "limitations": [{"_type": "DNPreventiveDiagnostic", "preventiveBitewingRadiographs": "1in12Months", "preventiveBrushBiopsy": "1in6Months", "preventiveFluorideTreatment": "1in6Months", "preventiveFluorideTreatmentAgeLimit": "19", "preventiveFullMouthRadiographs": "1in60Months", "preventiveHarmfulHabitAppliance": "1perLifetime", "preventiveHarmfulHabitApplianceAgeLimit": "14", "preventiveOralEvaluations": "1in6Months", "preventiveProphylaxis": "1in6Months", "preventiveSealants": "1in60Months", "preventiveSealantsAgeLimit": "14", "preventiveSpaceMaintainers": "1perLifetime", "preventiveSpaceMaintainersAgeLimit": "14", "_key": {"id": "acb50e3a-97b3-4906-a1e5-2f50ce85de08", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, {"_type": "DNBasic", "basicCompositeFillings": "AnteriorTeethOnly", "basicFillings": "1in36Months", "basicFillingsUnderNineteen": "1in12Months", "basicFullMouthDebridement": "1perLifetime", "basicGeneralAnesthesia": "Covered", "basicPeriodontalMaintenance": "CombinedWithProphylaxisFrequency", "basicPeriodontalSurgery": "1in36Months", "basicRootCanalTreatment": "1perLifetime", "basicScalingRootPlaning": "1in36Months", "basicStainlessSteelCrowns": "1in36Months", "basicStainlessSteelCrownsAgeLimit": "16", "_key": {"id": "69c86f56-d944-442a-b822-7b269054b1bc", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, {"_type": "DNMajor", "majorBleaching": "1in36Months", "majorCrownBuildups": "1in120Months", "majorCrowns": "1in120Months", "majorDentureAdjustments": "1in12Months", "majorDentureRebasesRelines": "1in36Months", "majorDentures": "1in120Months", "majorFixedBridgework": "1in120Months", "majorImplants": "1in120Months", "majorInlaysOnlays": "1in120Months", "majorOcclusalAdjustments": "1perLifetime", "majorOcclusalGuard": "1perLifetime", "majorPostCore": "1in120Months", "majorTissueConditioning": "1in36Months", "majorVeneers": "1in120Months", "_key": {"id": "8d019000-c3a9-4c51-a0aa-aabe1c1d27ae", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}], "typeOfServicesCovered": [{"serviceTypeCd": "Basic", "_type": "DNServiceType", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "f63fcee3-346c-33e1-a6d4-733289d4928e", "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, {"serviceTypeCd": "Major", "_type": "DNServiceType", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "6df6b6e7-8386-33a7-98c4-66db071e30b4", "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, {"serviceTypeCd": "Preventive", "_type": "DNServiceType", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "00d4416b-58ed-3ddd-93a2-8228855f64f2", "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}], "dentalMaximumAmount": {"dentalMaximums": [{"maximumINAmount": {"amount": 1000, "currency": "USD"}, "maximumType": "AnnualMaximum", "maximumOONAmount": {"amount": 1000, "currency": "USD"}, "_type": "DNMaximum", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "4179eb66-89d9-4c3e-b8aa-8f93b8d719ba", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}], "isMaximumCredit": false, "maximumServicesInNetwork": [{"maximumAppliesToServicesIN": "Major", "_type": "DNMaximumServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "272973de-575b-3324-bee0-ff98ca47bf30", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}, {"maximumAppliesToServicesIN": "Basic", "_type": "DNMaximumServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "e44a08ac-4e98-3a26-b15b-dd37daa0ff21", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}, {"maximumAppliesToServicesIN": "Preventive", "_type": "DNMaximumServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "5b98841e-3211-387f-8a97-ad76d0cf794a", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}], "maximumServicesOutOfNetwork": [{"_type": "DNMaximumServicesOutOfNetwork", "maximumAppliesToServicesOON": "Basic", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "c7dd760e-6b1a-3853-809e-eb73ff376be1", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}, {"_type": "DNMaximumServicesOutOfNetwork", "maximumAppliesToServicesOON": "Major", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "dd7ea2a6-cbbc-38bf-82c3-017ca0a2e62f", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}, {"_type": "DNMaximumServicesOutOfNetwork", "maximumAppliesToServicesOON": "Preventive", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "409f2be8-7ebb-33f7-81d1-1ec03806178c", "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}], "isMaximumRollover": false, "maximumAccumulationPeriod": "BenefitYear", "maxRolloverDetails": {"maximumRolloverThresholdAmount": {"amount": 500, "currency": "USD"}, "accumulatedRolloverMaximumAmount": {"amount": 500, "currency": "USD"}, "maximumRolloverAmount": {"amount": 125, "currency": "USD"}, "maximumBonusRolloverAmount": {"amount": 50, "currency": "USD"}, "_type": "DNMaximumRollover", "_key": {"id": "2871fcd2-1cdb-377b-8de2-746f64c18447", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "variation": "policy"}}, "_type": "DNDentalMaximumAmount", "_key": {"id": "2bce4cb8-9d4e-3c65-a0a0-38083d0b05ad", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "waitingPeriods": {"applyLateEntrantBenefitWaitingPeriods": true, "basicWaitingPeriod": 0, "lateEntrantWaitingPeriodsDetails": {"basicWaitingPeriod": 0, "majorWaitingPeriod": 0, "preventiveWaitingPeriod": 0, "_type": "DNLateEntrantBenefitWaitingPeriods", "_key": {"id": "25298be1-3cb8-3ecb-8dd1-984bb668627a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "d2b88a69-8d83-3aac-8edd-576962712d81", "variation": "policy"}}, "majorWaitingPeriod": 0, "waitingPeriodApplyTo": "NewAndExistingEmployees", "preventiveWaitingPeriod": 0, "_type": "DNWaitingPeriods", "_key": {"id": "d2b88a69-8d83-3aac-8edd-576962712d81", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "dependentEligibilityDetails": {"childMaxAgeCd": "26", "fullTimeStudentAgeCd": "26", "includeDisabledDependentsInd": true, "_type": "DNDependentEligibility", "_key": {"id": "22a49849-9ed0-3163-86c1-928d42a82866", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "fundingStructure": {"participantContributionPct": 1, "contributionBasisCd": "Percent", "contributionTypeCd": "Voluntary", "_type": "DNFundingStructure", "_key": {"id": "7ddd4914-cc61-343f-9b3b-d9a1833577d7", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "deductibleDetails": {"deductibleAccumulationPeriod": "BenefitYear", "deductibleServicesOutOfNetwork": [{"_type": "DNDeductibleServicesOutNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "f6b064d7-16ce-3b8d-8ebf-16f02066ff83", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}, "deductibleAppliesToServicesOutOfNetwork": "Major"}, {"_type": "DNDeductibleServicesOutNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "4764f6e3-270f-341b-a004-a6b6b94562f8", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}, "deductibleAppliesToServicesOutOfNetwork": "Basic"}, {"_type": "DNDeductibleServicesOutNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "44c01281-e1db-3b9e-8848-254ab21dbe13", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}, "deductibleAppliesToServicesOutOfNetwork": "Preventive"}], "deductibles": [{"deductibleType": "IndividualAnnual", "_type": "DNDeductible", "deductibleInNetworkAmount": {"amount": 50, "currency": "USD"}, "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "f79b0452-aa0a-4ffb-a6af-6124a5019ad7", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}, "deductibleOutOfNetworkAmount": {"amount": 50, "currency": "USD"}}, {"familyDeductibleInNetwork": "3X", "deductibleType": "FamilyAnnual", "_type": "DNDeductible", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "dd70c55c-d6a6-4fa6-ac61-0b1f0e012fe0", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}, "familyDeductibleOutOfNetwork": "3X"}], "deductibleCredit": false, "deductibleCarryover": false, "deductibleServicesInNetwork": [{"deductibleAppliesToServicesInNetwork": "Basic", "_type": "DNDeductibleServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "7f555785-2fb1-3f04-9fbe-3fff31c20d01", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}}, {"deductibleAppliesToServicesInNetwork": "Major", "_type": "DNDeductibleServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "2dbf8342-2119-309c-95ad-197c3d14828b", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}}, {"deductibleAppliesToServicesInNetwork": "Preventive", "_type": "DNDeductibleServicesInNetwork", "_key": {"rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "id": "5422cb21-3514-3d20-bfa7-270ff0ef0e19", "parentId": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "variation": "policy"}}], "_type": "DNDeductibleDefinition", "_key": {"id": "6b6e7a29-4db0-32c2-bfd7-27b17de1637a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "orthodonticCoverage": {"orthoLifetimeDeductible": {"_type": "DNOrthoLifetimeDeductible", "_key": {"id": "b2db83e0-3788-393d-9574-9d391dfe3bc0", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "0e312a2d-7c77-383f-aa86-69e7f90277ca", "variation": "policy"}}, "orthoWaitingPeriod": 0, "orthoDeductibleType": "None", "isOrthoMaximumCreditApplied": false, "orthoLifetimeMaximum": {"lifetimeMaximumAppliesTo": "Orthodontics", "lifetimeMaximumInNetworkAmount": {"amount": 1000, "currency": "USD"}, "lifetimeMaximumOutOfNetworkAmount": {"amount": 1000, "currency": "USD"}, "_type": "DNOrthoLifetimeMaximum", "_key": {"id": "0e876deb-37d1-3927-b696-48ed75a268e3", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "0e312a2d-7c77-383f-aa86-69e7f90277ca", "variation": "policy"}}, "orthoLateEntrantWaitingPeriod": 0, "orthoCoinsuranceOON": 0.5, "orthoChildAgeLimit": 19, "orthoAnnualDeductible": {"_type": "DNOrthoAnnualDeductible", "_key": {"id": "cc5027ee-5f9f-3912-9890-bca581a58a7b", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "0e312a2d-7c77-383f-aa86-69e7f90277ca", "variation": "policy"}}, "orthoCoinsuranceIN": 0.5, "orthoAnnualMaximum": {"_type": "DNOrthoAnnualMaximum", "_key": {"id": "a9583a99-acde-3d41-a00f-6fde70931414", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "0e312a2d-7c77-383f-aa86-69e7f90277ca", "variation": "policy"}}, "orthoMaximumType": "Lifetime", "orthoAvailability": "<PERSON><PERSON><PERSON><PERSON>", "_type": "DNOrthodonticsCoverage", "_key": {"id": "0e312a2d-7c77-383f-aa86-69e7f90277ca", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "serviceCategories": {"scOtherPreventiveServices": "Preventive", "scOtherOralSurgery": "Basic", "scStainlessSteelCrowns": "Basic", "scRootCanals": "Basic", "scAllOtherRadiographs": "Preventive", "scOtherEndodonticServices": "Basic", "scImplantServices": "Major", "scSurgicalPeriodontics": "Basic", "scOtherAdjunctiveServices": "Major", "scOtherOrthodonticServices": "Orthodontics", "scFullMouthRadiographs": "Preventive", "scSpaceMaintainers": "Preventive", "scFluorides": "Preventive", "scTreatmenttoControlHarmfulHabits": "Preventive", "scGeneralAnesthesia": "Basic", "scOtherProsthodonticServices": "Major", "scOtherPeriodonticServices": "Basic", "scOtherDiagnosticServices": "Preventive", "scOralEvaluations": "Preventive", "scFillings": "Basic", "scNonSurgicalExtractions": "Basic", "scTMJ": "Major", "scProphylaxis": "Preventive", "scOtherRestorativeServices": "Major", "scSealants": "Preventive", "scBitewingRadiographs": "Preventive", "scDentures": "Major", "scCrowns": "Major", "scDentureRepairs": "Major", "scNonSurgicalPeriodontics": "Basic", "scInlaysOnlays": "Major", "scFixedBridgework": "Major", "scSurgicalExtractions": "Basic", "_type": "DNServiceCategories", "_key": {"id": "e80d9f62-cb09-3bb7-b0d5-364b4c65e29a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "1f065e62-e7ab-3960-b1f2-6346f2902654", "variation": "policy"}}, "insuredLink": [{"_ref": "f68ecf6d-6696-356a-93e1-2d7295b59dbd"}], "_type": "DNCoverageDefinition", "offerStatus": "offered"}, "_key": {"id": "3f4ef348-bb59-3bd1-a77b-c59feafedd6f", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "12357aaf-4a3a-39d9-9fea-f4621ae98d3f", "variation": "policy"}, "_type": "DNBenefitPlan"}, "packageCd": "DentalPackage", "_type": "DNPolicyPackage"}, "offerStatus": "offered", "termDetails": {"termEffectiveDate": "2024-08-01T00:00:00Z", "termNo": 1, "termCd": "Perpetual", "_type": "DNTermDetails", "_key": {"id": "6136aa9d-9fd7-3040-8916-5391798b530d", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, "rateEffectiveDate": "2024-06-01T00:00:00Z", "riskStateCd": "NY", "commonProvisionDetails": {"isDomesticPartnersCovered": false, "classChangesCd": "OnAnniversary", "rehireProvisionCd": "1Month", "terminationProvisionCd": "1stMonthFollowingTerminationAge", "_key": {"id": "db521784-5dce-31c1-a0f4-3ce716c3e45c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}, "agingFrequencyCd": "OnAnniversary", "_type": "DNCommonProvision"}, "createdFromPolicyRev": 1, "parties": [{"partyInfo": [{"communicationInfo": {"emails": [{"_type": "DNEmailInfo", "type": "Work", "value": "<EMAIL>", "_key": {"id": "a7f5e815-7864-352a-b3f0-6526cb06fc19", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "328628b9-9b46-38b1-a884-5f0b4e40e5f3", "variation": "policy"}}], "_type": "DNCommunicationInfo", "phones": [{"_type": "DNPhoneInfo", "type": "Home", "value": "2678208132", "_key": {"id": "8f6e0d7f-797b-3c22-8a4d-5ce2a7220af8", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "328628b9-9b46-38b1-a884-5f0b4e40e5f3", "variation": "policy"}}], "_key": {"id": "328628b9-9b46-38b1-a884-5f0b4e40e5f3", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "ec57481c-57d7-357c-b204-f06cc26efc1a", "variation": "policy"}}, "personBaseDetails": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "genderCd": "Male", "taxId": "*********", "_type": "<PERSON><PERSON><PERSON>", "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164", "birthDate": "1974-10-11", "maritalStatus": "Married", "_key": {"id": "4d74814e-d0f4-3141-a39d-04059c6a4f07", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "ec57481c-57d7-357c-b204-f06cc26efc1a", "variation": "policy"}, "age": 49, "registryEntityNumber": null}, "addressInfos": [{"city": "South Coltonview", "stateProvinceCd": "NY", "addressType": "Mailing", "postalCode": "30344", "_type": "DNLocationParty", "countryCd": "US", "addressLine1": "4303 Considine Union", "registryTypeId": "registry://Location/1125e637-a91d-3225-8949-c64d7eea317d", "_key": {"id": "a0cc1bd0-f59e-33e3-8a65-beea7586e446", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "ec57481c-57d7-357c-b204-f06cc26efc1a", "variation": "policy"}, "registryEntityNumber": "LO0000059995"}], "_type": "DNIndividualPolicyPerson", "_key": {"id": "ec57481c-57d7-357c-b204-f06cc26efc1a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "0d410431-2f3a-318b-8343-018a208a7744", "variation": "policy"}, "tobaccoCd": "No"}], "_type": "DNIndividualPolicyParty", "_key": {"id": "0d410431-2f3a-318b-8343-018a208a7744", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, {"partyInfo": [{"communicationInfo": {"emails": [{"_type": "DNEmailInfo", "type": "Work", "value": "<EMAIL>", "_key": {"id": "24e69524-b657-305a-ac6c-8d6e9c94833e", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "ca1f4a3b-e291-3cd0-aba1-d3a92560bfe4", "variation": "policy"}}], "_type": "DNCommunicationInfo", "phones": [{"_type": "DNPhoneInfo", "type": "Home", "value": "************", "_key": {"id": "fa1df278-883e-31cf-af30-24c5c1f0422d", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "ca1f4a3b-e291-3cd0-aba1-d3a92560bfe4", "variation": "policy"}}], "_key": {"id": "ca1f4a3b-e291-3cd0-aba1-d3a92560bfe4", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "8c340f84-e301-379b-afb2-6a6c66ae0377", "variation": "policy"}}, "personBaseDetails": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "<PERSON>strom", "taxId": "*********", "genderCd": "Male", "_type": "<PERSON><PERSON><PERSON>", "birthDate": "1972-10-19", "maritalStatus": "Married", "_key": {"id": "91f68ed4-90d2-33d6-8553-d46fb0632a0e", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "8c340f84-e301-379b-afb2-6a6c66ae0377", "variation": "policy"}, "age": 51, "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47", "registryEntityNumber": null}, "addressInfos": [{"city": "Estevanbury", "stateProvinceCd": "NY", "addressType": "Mailing", "postalCode": "85083", "_type": "DNLocationParty", "countryCd": "US", "addressLine1": "3800 Elise Crossing", "_key": {"id": "37edaa34-94b8-3363-b3b1-d7b1df8fa4f0", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "8c340f84-e301-379b-afb2-6a6c66ae0377", "variation": "policy"}, "registryTypeId": "registry://Location/89f6f564-9a83-32ba-8a52-c6500d525074", "registryEntityNumber": "LO0000059997"}], "_type": "DNIndividualPolicyPerson", "_key": {"id": "8c340f84-e301-379b-afb2-6a6c66ae0377", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "d08f3f8e-3a8e-3eaf-a760-8c9bd3ab9fe5", "variation": "policy"}}], "_type": "DNIndividualPolicyParty", "_key": {"id": "d08f3f8e-3a8e-3eaf-a760-8c9bd3ab9fe5", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}, {"partyInfo": [{"communicationInfo": {"emails": [{"_type": "DNEmailInfo", "type": "Work", "value": "<EMAIL>", "_key": {"id": "7929c060-ac0e-3a25-9d2d-bb6a8913772c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b88eda55-1d68-3204-98c8-5784da54bf58", "variation": "policy"}}], "_type": "DNCommunicationInfo", "phones": [{"_type": "DNPhoneInfo", "type": "Home", "value": "************", "_key": {"id": "6d13d3e9-5a29-373b-b22d-22ece37af944", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "b88eda55-1d68-3204-98c8-5784da54bf58", "variation": "policy"}}], "_key": {"id": "b88eda55-1d68-3204-98c8-5784da54bf58", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "41b2af09-10a1-3d5f-83a2-32284e0fba8c", "variation": "policy"}}, "personBaseDetails": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "taxId": "*********", "_type": "<PERSON><PERSON><PERSON>", "genderCd": "Male", "isFullTimeStudent": false, "birthDate": "2016-06-12", "age": 8, "_key": {"id": "9ccf7aed-6648-3b0b-852f-47dcce701a4a", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "41b2af09-10a1-3d5f-83a2-32284e0fba8c", "variation": "policy"}, "registryTypeId": "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f", "registryEntityNumber": null}, "addressInfos": [{"city": "Estevanbury", "stateProvinceCd": "NY", "addressType": "Mailing", "postalCode": "85083", "_type": "DNLocationParty", "countryCd": "US", "addressLine1": "28420 <PERSON><PERSON><PERSON>", "_key": {"id": "ee7b9a07-a8b1-3f38-a76f-feff4622bd8d", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "41b2af09-10a1-3d5f-83a2-32284e0fba8c", "variation": "policy"}, "registryTypeId": "registry://Location/7e72ed69-7774-327b-8a7d-ce3e5dc834bd", "registryEntityNumber": "LO0000059996"}], "_type": "DNIndividualPolicyPerson", "_key": {"id": "41b2af09-10a1-3d5f-83a2-32284e0fba8c", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "36d439d6-850c-3481-927e-69756dcf6fee", "variation": "policy"}}], "_type": "DNIndividualPolicyParty", "_key": {"id": "36d439d6-850c-3481-927e-69756dcf6fee", "rootId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "revisionNo": 2, "parentId": "c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc", "variation": "policy"}}], "enrollmentTypeCd": "AnnualEnrollment", "productCd": "DNIndividual", "recordTypeCd": "Member", "premiumSplit": {"_uri": "gentity://BasePremiumSplitInfo/PremiumSplit//d0426ba0-91a4-3148-9432-adfe72ff2724/1"}, "customer": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"}}