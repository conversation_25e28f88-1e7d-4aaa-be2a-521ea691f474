package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalBuildPaymentScheduleHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalCancelActivePaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentGenerationHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleActivateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleCompleteHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleSuspendHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleUnsuspendHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPreviewPaymentScheduleHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalSuspendLossPaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalUnsuspendLossPaymentSchedulesHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentScheduleLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalPaymentScheduleLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalPaymentSchedule";

    private static final String MODEL_TYPE = "CapPaymentSchedule";

    private CapDentalPaymentScheduleLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalPaymentScheduleLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalPaymentScheduleInitHandler.class,
                CapDentalPaymentScheduleUpdateHandler.class,
                CapDentalPreviewPaymentScheduleHandler.class,
                CapDentalPaymentScheduleActivateHandler.class,
                CapDentalPaymentScheduleCancelHandler.class,
                CapDentalPaymentScheduleCompleteHandler.class,
                CapDentalPaymentScheduleSuspendHandler.class,
                CapDentalPaymentScheduleUnsuspendHandler.class,
                CapDentalPaymentGenerationHandler.class,
                CapDentalBuildPaymentScheduleHandler.class,
                CapDentalCancelActivePaymentSchedulesHandler.class,
                CapDentalSuspendLossPaymentSchedulesHandler.class,
                CapDentalUnsuspendLossPaymentSchedulesHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(module.getModelVersion(), equalTo("1"));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalPaymentScheduleLifecycleConfig.class), equalTo(true));
    }
}
