/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalrecovery.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalrecovery.validator.CapDentalRecoveryInitInputValidator.CapDentalRecoveryInitInputValidatorErrorDefinition.ORIGIN_SOURCE_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.dentalrecovery.validator.CapDentalRecoveryInitInputValidator.CapDentalRecoveryInitInputValidatorErrorDefinition.PAYEE_INCORRECT;

import java.util.List;
import java.util.Optional;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.input.CapDentalRecoveryInitInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentPayeeDetailsEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

/**
 * Class for validating {@link CapDentalRecoveryInitInput} data.
 *
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalRecoveryInitInputValidator extends CapInputValidator<CapDentalRecoveryInitInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalRecoveryInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalRecoveryInitInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalRecoveryInitInput input) {
        return Streamable.concat(validateOriginSource(input.getOriginSource())
                ,validatePayee(input.getEntity()));
    }

    private Streamable<ErrorHolder> validateOriginSource(EntityLink<RootEntity> originSource) {
        return Optional.ofNullable(originSource)
                .map(origin -> capDentalLinkValidator.validateLink(origin, "CapLoss", ORIGIN_SOURCE_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePayee(CapPaymentDetails paymentDetails) {
        return Optional.ofNullable(paymentDetails)
                .filter(CapDentalPaymentDetailsEntity.class::isInstance)
                .map(CapDentalPaymentDetailsEntity.class::cast)
                .map(CapDentalPaymentDetailsEntity::getPayeeDetails)
                .map(CapDentalPaymentPayeeDetailsEntity::getPayee)
                .map(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    public static class CapDentalRecoveryInitInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static CapDentalRecoveryInitInputValidatorErrorDefinition ORIGIN_SOURCE_INCORRECT =
                new CapDentalRecoveryInitInputValidatorErrorDefinition("criv001", "originSource URI is not valid");
        public static CapDentalRecoveryInitInputValidatorErrorDefinition PAYEE_INCORRECT =
                new CapDentalRecoveryInitInputValidatorErrorDefinition("criv002", "payeeDetails.payee URI is not valid");

        protected CapDentalRecoveryInitInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
