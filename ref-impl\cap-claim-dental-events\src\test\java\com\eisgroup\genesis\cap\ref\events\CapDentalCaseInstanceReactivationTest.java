/* Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.events.resolver.CapDentalCaseInstanceResolver;
import com.eisgroup.genesis.http.response.JsonResponseHandler;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.security.service2service.ServiceAccessRunner;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.google.gson.JsonObject;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CapDentalCaseInstanceReactivationTest {

    private final static JsonObject EVENT_CASE_REACTIVATION_RESPONSE_NOT_SUCCESSFUL = JsonUtils.loadJson("json/notSuccessfulReactivationResponse.json").getAsJsonObject();
    private final static JsonObject EVENT_CASE_REACTIVATION_RESPONSE_SUCCESSFUL = JsonUtils.loadJson("json/reactivationResponse.json").getAsJsonObject();
    private CapDentalCaseInstanceReactivation reactivation;

    @Mock
    private CapDentalCaseInstanceResolver capDentalCaseInstanceResolver;

    @Mock
    private HttpClient client;

    @Mock
    private ServiceAccessRunner serviceAccessRunner;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        reactivation = new CapDentalCaseInstanceReactivation(client, "URI", serviceAccessRunner, capDentalCaseInstanceResolver);
    }

    @Test
    public void testDentalCaseReactivationError() throws IOException {
        //given
        when(capDentalCaseInstanceResolver.resolveCaseInstanceId(any(EntityLink.class))).thenReturn(Lazy.of("ce84b5a5-9e41-11ed-b960-0242ac120011"));
        when(client.execute(any(HttpPost.class), any(JsonResponseHandler.class))).thenReturn(EVENT_CASE_REACTIVATION_RESPONSE_NOT_SUCCESSFUL);

        //when
//        reactivation.executeReactivation("ce84b5a5-9e41-11ed-b960-0242ac120011").test().assertErrorMessage("Case reactivation was not successful");
    }

    @Test
    public void testDentalCaseReactivation() throws IOException {
        //given
        when(capDentalCaseInstanceResolver.resolveCaseInstanceId(any(EntityLink.class))).thenReturn(Lazy.of("ce84b5a5-9e41-11ed-b960-0242ac120011"));
        when(client.execute(any(HttpPost.class), any(JsonResponseHandler.class))).thenReturn(EVENT_CASE_REACTIVATION_RESPONSE_SUCCESSFUL);

        //when
//        reactivation.executeReactivation("ce84b5a5-9e41-11ed-b960-0242ac120011").test().assertNoErrors();
    }
}