/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.cap.loss.repository.ClaimLossRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.model.ModelResolver;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Pend action handler
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossPendHandler  implements ProductCommandHandler<IdentifierRequest, CapLoss> {

    @Autowired
    private ClaimLossRepository claimLossRepository;

    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapLoss load(@Nonnull IdentifierRequest request) {
        return (CapLoss) this.claimLossRepository.load(request.getKey(), this.modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public CapLoss execute(@Nonnull IdentifierRequest request, @Nonnull CapLoss entity) {
        return entity;
    }

    @Nonnull
    @Override
    public CapLoss save(@Nonnull IdentifierRequest request, @Nonnull CapLoss entity) {
        return (CapLoss) claimLossRepository.save(entity).get();
    }



    @Override
    public String getName() {
        return CapDentalLossCommands.PEND_LOSS;
    }

}
