/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.ICapDentalPatientHistory;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.platform.common.action.CommonLoadAction;
import com.eis.automation.v20.platform.common.action.PostModelAction;

import javax.ws.rs.HttpMethod;

public class CapDentalPatientHistory implements ICapDentalPatientHistory {

    private final PostModelAction<ICapDentalPatientHistoryWrapperModel, ICapDentalPatientHistoryModel> initAction;
    private final CommonLoadAction<ICapDentalPatientHistoryModel> entitiesAction;

    public CapDentalPatientHistory(RestActionConfiguration configuration) {
        initAction = new PostModelAction<>(configuration);
        entitiesAction = new CommonLoadAction<>(configuration);
    }

    @RestActionGetter("initAction")
    @RestActionContext(target = "/api/capdentalpatienthistorymodeltype/{product}/{version}/command/initPatientHistory")
    public PostModelAction<ICapDentalPatientHistoryWrapperModel, ICapDentalPatientHistoryModel> init() {
        return initAction;
    }

    @RestActionGetter("entitiesAction")
    @RestActionContext(
            target = "/api/capdentalpatienthistorymodeltype/{product}/{version}/entities/{rootId}/{revisionNo}",
            method = HttpMethod.GET)
    public CommonLoadAction<ICapDentalPatientHistoryModel> entities() {
        return entitiesAction;
    }

}
