// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CORRELATIONIDHOLDER } from "./kraken_model_tree_CorrelationIdHolder"

let name = "CorrelationIdHolder"

let namespace = "CorrelationIdHolder"

export type CorrelationIdHolderEntryPointName = never

let entryPointNames = [
] as CorrelationIdHolderEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CORRELATIONIDHOLDER as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CorrelationIdHolder = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree}, 'rules-model')));
