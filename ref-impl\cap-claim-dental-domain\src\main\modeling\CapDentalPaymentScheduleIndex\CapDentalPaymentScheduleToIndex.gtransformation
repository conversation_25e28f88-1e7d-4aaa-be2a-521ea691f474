@Persistable
@CommandListener("Save","initPaymentSchedule,activatePaymentSchedule,completePaymentSchedule,suspendPaymentSchedule,cancelPaymentSchedule,unsuspendPaymentSchedule")
Transformation CapDentalPaymentScheduleToStateIndex {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as paymentSchedule
    }
    Output {
        CapDentalPaymentScheduleIndex.CapDentalPaymentScheduleIdxEntity
    }

    Attr originSource is paymentSchedule.originSource._uri
    Attr paymentSchedule is ToExtLink(paymentSchedule)._uri
    Attr state is paymentSchedule.state

}