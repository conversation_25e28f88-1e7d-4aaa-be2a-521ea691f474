import com.typesafe.sbt.packager.archetypes.JavaAppPackaging.autoImport.scriptClasspath
import sbt.Keys._
import sbt._

object Common {

  lazy val dxpCoreVersion = "24.16" // DXP Core version
  lazy val capDentalVersion = "23.11.0-SNAPSHOT.202309260014"

  // Common settings
  def projectSettings = {

    Seq(
      organization := "com.eisgroup.dxp",
      scalaVersion := "3.3.3",

      // Setting sbt log level from environment variable (otherwise set "info" level)
      logLevel := Level.withName(setLogLevel(sys.env.getOrElse("LOG_LEVEL", "info").toLowerCase)),

      // Nexus links with artifacts
      resolvers += "DXP Group Sonatype Nexus Repository Manager" at sys.env.getOrElse("DXP_NEXUS_DEPENDENCIES_MVN_REPO_URL", "https://sfoeisgennexus01.exigengroup.com/repository/GENESIS_MVN"),
      credentials += Credentials(Path.userHome / ".ivy2" / ".credentials.genesis"),

      // Enable super shell to display the current tasks in progress
      ThisBuild / useSuperShell := true,

      // Removes Scala version from artifact names
      crossPaths := false,

      // Speed-up dependency resolution for multi-module products
      updateOptions := updateOptions.value.withCachedResolution(true),

      // Turn off "Resolving" log messages that clutter build logs
      ThisBuild / ivyLoggingLevel := UpdateLogging.Quiet,

      // Suppress dependency eviction warnings
      update / evictionWarningOptions := EvictionWarningOptions.default.withWarnTransitiveEvictions(false),
      Global / excludeLintKeys += update / evictionWarningOptions,
      Global / excludeLintKeys += update / ivyLoggingLevel,

      // Filter routes files during "sbt test"
      Test / managedSources := {
        val files: Seq[File] = (Test / managedSources).value
        val filter: FileFilter = "*.routes"
        files.filterNot(file => filter.accept(file))
      },

      // Automatically using "application.local.conf" file when running application locally
      initialize ~= { _ =>
        System.setProperty("config.file", file(".") / "envs" / "application.local.conf" absolutePath)
      },

      // Settings for SBT development options for compilation performance
      javaOptions ++= Seq(
        "-Xmx8G", "-XX:+UseG1GC", "-Dlog.level=" + sys.env.getOrElse("LOG_LEVEL", "TRACE")
      ),

      // Settings for javac options
      Compile / compile / javacOptions ++= Seq(
        "-encoding", "UTF-8",
        "-source", "21",
        "-target", "21",
        "-parameters",
        "-Xlint:unchecked",
        "-Xlint:deprecation"
      ),

      // Fix symbol count problem (<256) for the generated "gateway.bat" (need for Windows script only)
      scriptClasspath := Seq("../conf", "*"),

      // Dependency on core module (with basic functionality)
      libraryDependencies += "com.eisgroup.dxp" % "genesiscore" % Common.dxpCoreVersion
    ) ++
      PlaySettings.playSettings ++
      JacocoPluginSettings.jacocoPluginReportSettings ++
      BuildPluginsSettings.javadocSettings ++
      Release.publishSettings
  }

  def setLogLevel(logLevel: String): String = {
    if (logLevel.equals("off")) {
      return "error"
    } else if (logLevel.equals("trace")) {
      return "debug"
    }
    logLevel;
  }

  def extractSwagger(jarName: String, originalSwagger: String, destinationSwagger: String) : sbt.Def.Initialize[Task[Seq[File]]] = Def.task {
    val jar = (update in Compile).value
             .select(configurationFilter("compile"))
             .filter(_.name.contains(jarName))
             .head
    if (!jar.exists) {
      throw new RuntimeException("Jar [" + jarName + "] is not part of compile time dependencies")
    }   
 
    val extractTo = (target in Compile).value / "extracted-jar/"
    IO.unzip(jar, extractTo)

    val moveFrom = extractTo / originalSwagger
    //val moveTo = (target in Compile).value / "classes/codegen/integration.genesis" / destinationSwagger 
    val moveTo = (baseDirectory in Compile).value / "conf/codegen/integration.genesis" / destinationSwagger

    try {
      if (!moveFrom.exists()) {
        throw new RuntimeException("File [" + moveFrom.getPath() + "] Cannot be moved, as it does not exist")
      }
      println("Moving file from " + moveFrom.exists())
      println("Moving file to " + moveTo.getParentFile().exists())

      java.nio.file.Files.createDirectories(moveTo.getParentFile().toPath())
      java.nio.file.Files.move(moveFrom.toPath(), moveTo.toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING)
    } catch {
      case e: java.nio.file.FileSystemException => println("File is in-use")
    }

    Seq.empty[File]
  }
}
