/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input;

import com.eisgroup.genesis.cap.financial.command.template.input.CapPaymentTemplateUnsuspendInput;
import com.google.gson.JsonObject;

/**
 * Input for Dental Payment template unsuspend action command
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalPaymentTemplateUnsuspendInput extends CapPaymentTemplateUnsuspendInput {

    public CapDentalPaymentTemplateUnsuspendInput(JsonObject original) {
        super(original);
    }
}
