{"TestData_Individual": {"providerNpi": "$<rx:\\d{10}>", "suspend": false, "networks": [], "directFeeSchedules": [], "businessName": "$<rx:[a-zA-Z]{8}>", "contractEffectiveDate": "$<today:yyyy-MM-dd>", "contractExpirationDate": "$<today+1y:yyyy-MM-dd>", "credentialsVerificationEffectiveDate": "$<today:yyyy-MM-dd>", "credentialsVerificationExpirationDate": "$<today+1y:yyyy-MM-dd>", "paymentInfo": "@TestData_PaymentInfo", "serviceCd": ["IME", "SSA"], "state": "active", "_modelName": "IndividualProvider", "_modelType": "Provider", "_type": "GenesisIndividualProvider", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.common.modeling.core.impl.IndividualProviderModel"}, "TestData_Organization": {"providerNpi": "$<rx:\\d{10}>", "suspend": false, "networks": [], "directFeeSchedules": [], "businessName": "$<rx:[a-zA-Z]{8}>", "contractEffectiveDate": "$<today:yyyy-MM-dd>", "contractExpirationDate": "$<today+1y:yyyy-MM-dd>", "credentialsVerificationEffectiveDate": "$<today:yyyy-MM-dd>", "credentialsVerificationExpirationDate": "$<today+1y:yyyy-MM-dd>", "paymentInfo": "@TestData_PaymentInfo", "serviceCd": ["IME", "SSA"], "state": "active", "_modelName": "OrganizationProvider", "_modelType": "Provider", "_type": "GenesisOrganizationProvider", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.common.modeling.core.impl.OrganizationProviderModel"}, "TestData_PaymentInfo": {"acceptCheck": true, "bulkPayment": true, "providerPaymentFrequencyCd": "Weekly", "_type": "GenesisPaymentInfo", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.common.modeling.core.impl.PaymentInfoModel"}}