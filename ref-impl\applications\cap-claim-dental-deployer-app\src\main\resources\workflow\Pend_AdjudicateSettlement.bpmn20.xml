<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="pendAdjudicateSettlementProcess" name="Pend AdjudicateSettlement" isExecutable="true">
    <startEvent id="startAdjudicateClaim" flowable:formFieldValidation="true"></startEvent>
    <serviceTask id="pendAdjudicateSettlement" name="adjudicateSettlement" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalSettlement" target="_modelName"></flowable:eventInParameter>
        <flowable:eventInParameter source="adjudicateSettlement" target="commandName"></flowable:eventInParameter>
        <flowable:eventOutParameter source="_uri" target="_uri"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="adjudicationResult"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_uri" target="settlementURI"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_key" target="settlementKey"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelName" target="settlementModelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="_modelVersion" target="settlementVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter source="rootId" sourceType="string" target="rootId"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <endEvent id="end"></endEvent>
    <sequenceFlow id="toEnd" sourceRef="pendAdjudicateSettlement" targetRef="end"></sequenceFlow>
    <sequenceFlow id="sid-36366CFC-9099-45E4-A21A-EBA9408F03DA" sourceRef="startAdjudicateClaim" targetRef="pendAdjudicateSettlement"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_pendAdjudicateSettlementProcess">
    <bpmndi:BPMNPlane bpmnElement="pendAdjudicateSettlementProcess" id="BPMNPlane_pendAdjudicateSettlementProcess">
      <bpmndi:BPMNShape bpmnElement="startAdjudicateClaim" id="BPMNShape_startAdjudicateClaim">
        <omgdc:Bounds height="30.0" width="30.0" x="330.0" y="163.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="pendAdjudicateSettlement" id="BPMNShape_pendAdjudicateSettlement">
        <omgdc:Bounds height="80.0" width="100.0" x="570.0" y="139.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="end" id="BPMNShape_end">
        <omgdc:Bounds height="28.0" width="28.0" x="958.5" y="165.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-36366CFC-9099-45E4-A21A-EBA9408F03DA" id="BPMNEdge_sid-36366CFC-9099-45E4-A21A-EBA9408F03DA" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="359.94989337206505" y="178.05436351390534"></omgdi:waypoint>
        <omgdi:waypoint x="569.9999999999964" y="178.8181818181818"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toEnd" id="BPMNEdge_toEnd" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="669.949999999926" y="179.0"></omgdi:waypoint>
        <omgdi:waypoint x="958.5" y="179.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>