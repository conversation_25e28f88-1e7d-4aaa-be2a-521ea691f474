/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations.config;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.transformations.executors.MockTestAccumulatorTermCalculationOperationExecutor;
import com.eisgroup.genesis.cap.transformations.executors.MockTestLoadOperationExecutor;
import com.eisgroup.genesis.cap.transformations.executors.MockTestSearchPolicyVersionOperationExecutor;
import com.eisgroup.genesis.queryfield.repository.QueryableFieldRepository;
import org.springframework.context.annotation.Bean;

import com.eisgroup.genesis.queryfield.repository.QueryCriteria;
import com.eisgroup.genesis.cap.transformations.executors.MockTestGenericOperationExecutor;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.google.gson.JsonElement;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * Config class for dental specific tests.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class DentalTestConfig {

    @Bean
    public OperationExecutor<GenericOperation, JsonElement> mockCapDentalLoadProviderOperationExecutor() {
        return new MockTestGenericOperationExecutor("LoadProvider");
    }

    @Bean
    public OperationExecutor<GenericOperation, JsonElement> mockCapDentalResolveAccumulatorsOperationExecutor() {
        return new MockTestGenericOperationExecutor("ResolveAccumulators");
    }

    @Bean
    public QueryableFieldRepository queryableFieldRepository() {
        QueryableFieldRepository queryableFieldRepository = mock(QueryableFieldRepository.class);
        when(queryableFieldRepository.load(any(QueryCriteria.class))).thenReturn(Streamable.empty());
        return queryableFieldRepository;
    }

    @Bean
    public OperationExecutor<GenericOperation, JsonElement> mockCapDentalSearchPolicyVersionOperationExecutor() {
        return new MockTestSearchPolicyVersionOperationExecutor();
    }

    @Bean
    public OperationExecutor<GenericOperation, JsonElement> mockQueryOperationExecutor() {
        return new MockTestGenericOperationExecutor("Query");
    }

    @Bean
    public OperationExecutor<GenericOperation, JsonElement> mockMockTestAccumulatorTermCalculationOperationExecutor() {
        return new MockTestAccumulatorTermCalculationOperationExecutor();
    }

    @Bean
    public MockTestLoadOperationExecutor capLoadOperationExecutor() {
        return new MockTestLoadOperationExecutor("mockLoadJson");
    }
}
