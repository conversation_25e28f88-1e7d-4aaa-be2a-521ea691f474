/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;

import java.util.Arrays;
import java.util.Collection;

import com.eisgroup.genesis.cap.transformation.endpoint.CapEntityEndpoint;
import com.eisgroup.genesis.cap.transformation.endpoint.ModelAwareTransformationEndpoint;
import com.eisgroup.genesis.facade.endpoint.command.CommandEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.LoadEntityRestEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.LoadHistoryEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.link.EntityLinkEndpoint;
import com.eisgroup.genesis.facade.endpoint.model.ModelFacade;
import com.eisgroup.genesis.facade.module.EndpointPackage;
import com.eisgroup.genesis.facade.module.FacadeModule;
import com.eisgroup.genesis.kraken.facade.KrakenBundleRepositoryEndpoint;

/**
 * Facade for endpoints related to CapDentalLoss.
 *
 *  <AUTHOR>
 * @since 21.15
 */
public class CapDentalLossFacade implements FacadeModule {

    @Override
    public Collection<EndpointPackage> getEndpoints() {
        return Arrays.asList(new ModelFacade(),
            new CommandEndpoint(),
            new LoadEntityRestEndpoint<>(),
            new LoadHistoryEndpoint<>(),
            new EntityLinkEndpoint(),
            new KrakenBundleRepositoryEndpoint(),
            new ModelAwareTransformationEndpoint(),
            new CapEntityEndpoint());
    }

    public String getModelName() {
        return "CapDentalLoss";
    }

    public String getModelVersion() {
        return "1";
    }

    public String getModelType() {
        return "CapLoss";
    }

    public int getFacadeVersion() {
        return 1;
    }

}
