/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.databinding.serializer.annotation.JsonMonetarySerializeFormat;
import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.impl.MessageTypeModel;
import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.interf.IMessageTypeModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalCalculationStatusModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalCalculationStatusModel extends TypeModel implements ICapDentalCalculationStatusModel {

    private String flag;
    private String statusReason;
    private String code;
    private Integer percentage;
    private Money fee;
    private List<String> questions;
    private Boolean predetInd;
    private String reasonCode;
    private String procedureID;
    private String submittedCode;
    private String coveredCode;
    private IMessageTypeModel message;

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getStatusReason() {
        return statusReason;
    }

    public void setStatusReason(String statusReason) {
        this.statusReason = statusReason;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Integer getPercentage() {
        return percentage;
    }

    public void setPercentage(Integer percentage) {
        this.percentage = percentage;
    }

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getFee() {
        return fee;
    }

    public void setFee(Money fee) {
        this.fee = fee;
    }

    public List<String> getQuestions() {
        return questions;
    }

    public void setQuestions(List<String> questions) {
        this.questions = questions;
    }

    public Boolean getPredetInd() {
        return predetInd;
    }

    public void setPredetInd(Boolean predetInd) {
        this.predetInd = predetInd;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getProcedureID() {
        return procedureID;
    }

    public void setProcedureID(String procedureID) {
        this.procedureID = procedureID;
    }

    public String getSubmittedCode() {
        return submittedCode;
    }

    public void setSubmittedCode(String submittedCode) {
        this.submittedCode = submittedCode;
    }

    public String getCoveredCode() {
        return coveredCode;
    }

    public void setCoveredCode(String coveredCode) {
        this.coveredCode = coveredCode;
    }

    @JsonSerialize(as = MessageTypeModel.class)
    public IMessageTypeModel getMessage() {
        return message;
    }

    @JsonDeserialize(as = MessageTypeModel.class)
    public void setMessage(IMessageTypeModel message) {
        this.message = message;
    }
}
