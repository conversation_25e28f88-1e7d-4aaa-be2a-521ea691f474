/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.versioning.facade.CapChangeHistoryRequest;
import com.eisgroup.genesis.cap.versioning.facade.validator.CapChangeHistoryEndpointValidator;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.facade.FacadeFailureException;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.JsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.json.wrapper.JsonWrapper;
import com.eisgroup.genesis.repository.ReadContext;
import com.eisgroup.genesis.repository.error.RepositoryException;
import com.google.gson.JsonElement;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.facade.validator.CapDentalChangeHistoryEndpointValidator.CapDentalChangeHistoryDefinition.INVALID_FILTER_ATTRIBUTE;
import static com.eisgroup.genesis.facade.validator.CapDentalChangeHistoryEndpointValidator.CapDentalChangeHistoryDefinition.INVALID_REQUEST;
import static com.eisgroup.genesis.facade.validator.CapDentalChangeHistoryEndpointValidator.CapDentalChangeHistoryDefinition.NON_EXISTING_URI;
import static com.eisgroup.genesis.facade.validator.CapDentalChangeHistoryEndpointValidator.CapDentalChangeHistoryDefinition.URI_REQUIRED;


/**
 * Dental Validator responsible for validating {@link CapChangeHistoryRequest} provided to changeHistory endpoint
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalChangeHistoryEndpointValidator extends CapChangeHistoryEndpointValidator {

    private static final List<String> SUPPORTED_FILTER_VALUES = List.of("rootId", "revisionNo", "modelName");
    private static final String DENTAL_MODEL_NAME = "CapDentalLoss";

    private final EntityLinkResolverRegistry entityLinkResolverRegistry;

    public CapDentalChangeHistoryEndpointValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        super(entityLinkResolverRegistry);
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
    }

    @Override
    public Lazy validateRequest(CapChangeHistoryRequest request) {
        return Lazy.from(() -> Streamable.concat(validateDentalClaimExists(request.getUri()), validateFilterAttributes(request.getFilter()))
                    .collect(Collectors.toList()))
            .flatMap(errorHolders -> {
                    if (!errorHolders.isEmpty()) {
                        return Lazy.error(() ->new FacadeFailureException(INVALID_REQUEST.builder().addChildren(errorHolders).build()));
                    }
                return Lazy.empty();
                });
    }


    private Streamable<ErrorHolder> validateFilterAttributes(Map<String, String> requestFilterAttr) {
        List<ErrorHolder> errors = Optional.ofNullable(requestFilterAttr)
                .map(Map::keySet)
                .orElse(Collections.emptySet())
                .stream()
                .filter(keyValue -> !SUPPORTED_FILTER_VALUES.contains(keyValue))
                .map(notSupportedKey -> INVALID_FILTER_ATTRIBUTE.builder().params(notSupportedKey, SUPPORTED_FILTER_VALUES).build())
                .collect(Collectors.toList());
        return Streamable.from(errors);
    }

    private Streamable<ErrorHolder> validateDentalClaimExists(String lossUri) {
        return Optional.ofNullable(lossUri)
                .map(uri -> new EntityLink<>(JsonEntity.class, uri))
                .map(link -> {
                    if (link.getSchema() == null) {
                        return Streamable.of(NON_EXISTING_URI.builder().build());
                    }
                    EntityLinkResolver<JsonEntity> scheme;
                    try {
                        scheme = entityLinkResolverRegistry.getByURIScheme(link.getSchema());
                    } catch (RepositoryException e) {
                        return Streamable.of(NON_EXISTING_URI.builder().build());
                    }
                    return scheme.resolve(link, ReadContext.empty())
                            .filter(entity -> !DENTAL_MODEL_NAME.equals(resolveModelName(entity)))
                            .flatMapMany(notDentalEntity -> Streamable.of(NON_EXISTING_URI.builder().build()))
                            .onErrorResume(e->Streamable.of(NON_EXISTING_URI.builder().build()));
                })
                .orElse(Streamable.of(URI_REQUIRED.builder().build()));
    }

    private String resolveModelName(JsonEntity entity) {
        return Optional.of(entity)
                .map(JsonWrapper::toJson)
                .map(json -> json.get(RootEntity.MODEL_NAME_ATTR))
                .map(JsonElement::getAsString)
                .orElse(null);
    }

    public static class CapDentalChangeHistoryDefinition extends BaseErrorDefinition {

        public static final CapDentalChangeHistoryDefinition INVALID_REQUEST = new CapDentalChangeHistoryDefinition("cdche001",
                "Invalid endpoint parameters.");

        public static final CapDentalChangeHistoryDefinition URI_REQUIRED = new CapDentalChangeHistoryDefinition("cdche002",
                "uri is required.");

        public static final CapDentalChangeHistoryDefinition INVALID_FILTER_ATTRIBUTE = new CapDentalChangeHistoryDefinition("cdche003",
                "Invalid filter attribute passed `{0}` available attributes: {1}");

        public static final CapDentalChangeHistoryDefinition NON_EXISTING_URI = new CapDentalChangeHistoryDefinition("cdche004",
                "Only Dental uri is accepted.");

        private CapDentalChangeHistoryDefinition(String code, String message) {
            super(code, message);
        }
    }

}
