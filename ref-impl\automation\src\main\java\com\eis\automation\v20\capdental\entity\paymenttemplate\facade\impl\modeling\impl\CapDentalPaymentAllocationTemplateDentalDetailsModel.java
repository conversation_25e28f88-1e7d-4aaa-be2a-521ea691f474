/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.databinding.serializer.annotation.JsonMonetarySerializeFormat;
import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentAllocationTemplateDentalDetailsModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.javamoney.moneta.Money;

import java.time.LocalDate;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentAllocationTemplateDentalDetailsModel extends TypeModel implements ICapDentalPaymentAllocationTemplateDentalDetailsModel {

    private String transactionTypeCd;
    private String orthoFrequencyCd;
    private Integer orthoMonthQuantity;
    private Money paymentAmount;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate dateOfService;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate receivedDate;

    public String getTransactionTypeCd() {
        return transactionTypeCd;
    }

    public void setTransactionTypeCd(String transactionTypeCd) {
        this.transactionTypeCd = transactionTypeCd;
    }

    public String getOrthoFrequencyCd() {
        return orthoFrequencyCd;
    }

    public void setOrthoFrequencyCd(String orthoFrequencyCd) {
        this.orthoFrequencyCd = orthoFrequencyCd;
    }

    public Integer getOrthoMonthQuantity() {
        return orthoMonthQuantity;
    }

    public void setOrthoMonthQuantity(Integer orthoMonthQuantity) {
        this.orthoMonthQuantity = orthoMonthQuantity;
    }

    @JsonMonetarySerializeFormat(scale = 2)
    public Money getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Money paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public LocalDate getDateOfService() {
        return dateOfService;
    }

    public void setDateOfService(LocalDate dateOfService) {
        this.dateOfService = dateOfService;
    }

    public LocalDate getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDate receivedDate) {
        this.receivedDate = receivedDate;
    }
}
