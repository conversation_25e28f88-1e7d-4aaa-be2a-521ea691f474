/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalProcessPaymentLifecycleInput;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalProcessPaymentLifecycleOutput;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.model.Variation;

import javax.annotation.Nonnull;
import java.util.UUID;

/**
 * <PERSON><PERSON> that is used to start process lifecycle BPMN flow
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalProcessPaymentLifecycleHandler implements ProductCommandHandler<CapDentalProcessPaymentLifecycleInput, CapDentalProcessPaymentLifecycleOutput> {

    private static final String MODEL_NAME = "CapDentalPaymentDefinition";
    @Nonnull
    @Override
    public CapDentalProcessPaymentLifecycleOutput load(@Nonnull CapDentalProcessPaymentLifecycleInput input) {
        return Lazy.of(ModelInstanceFactory.createInstance(MODEL_NAME, "1", CapDentalProcessPaymentLifecycleOutput.class.getSimpleName()))
            .cast(CapDentalProcessPaymentLifecycleOutput.class).get();
    }

    @Nonnull
    @Override
    public CapDentalProcessPaymentLifecycleOutput execute(@Nonnull CapDentalProcessPaymentLifecycleInput input, @Nonnull CapDentalProcessPaymentLifecycleOutput entity) {
        return Lazy.of(entity)
            .map(output -> {
                output.setPaymentUri(input.getPaymentUri());
                output.setPaymentMethodType(input.getPaymentMethodType());
                output.setPaymentEvent(input.getPaymentEvent());
                output.setKey(new EntityKey(UUID.randomUUID(), 1, UUID.randomUUID(), UUID.randomUUID()));

                output.toJson().addProperty("_variation", PaymentVariations.PAYMENT.getName());
                output.toJson().addProperty("_modelName", MODEL_NAME);
                return output;
            }).get();
    }

    @Nonnull
    @Override
    public CapDentalProcessPaymentLifecycleOutput save(@Nonnull CapDentalProcessPaymentLifecycleInput input, @Nonnull CapDentalProcessPaymentLifecycleOutput entity) {
        return Lazy.of(entity).get();
    }

    @Nonnull
    @Override
    public Variation getVariation() {
        return PaymentVariations.PAYMENT;
    }

    @Override
    public String getName() {
        return CapDentalPaymentCommands.PROCESS_PAYMENT_LIFECYCLE;
    }
}
