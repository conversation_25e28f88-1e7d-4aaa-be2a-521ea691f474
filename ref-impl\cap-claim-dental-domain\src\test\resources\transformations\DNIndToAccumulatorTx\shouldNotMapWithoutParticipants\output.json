{
  "data" : [ ],
  "sourceURI" : "gentity://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc/2",
  "policyURI" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc",
  "transactionTimestamp" : ignore,
  "customerURI" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164",
  "_modelName" : "CapAccumulatorTransaction",
  "_modelVersion" : "1",
  "_modelType" : "CapAccumulatorTransactionEntry",
  "_type" : "CapAccumulatorTransactionEntity"
}