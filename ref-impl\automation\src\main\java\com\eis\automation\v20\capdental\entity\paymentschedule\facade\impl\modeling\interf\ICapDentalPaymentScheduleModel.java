/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf;

import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;

import java.time.LocalDateTime;
import java.util.List;

public interface ICapDentalPaymentScheduleModel extends ITypeModel {
    String getState();

    String getScheduleNumber();

    UriModel getOriginSource();

    UriModel getPaymentTemplate();

    List<ICapDentalPaymentScheduleMessageModel> getScheduleMessages();

    List<ICapDentalPaymentModel> getPayments();

    String getModelName();

    String entityName();

    String endpointName();

    String getModelVersion();

    String getModelType();

    LocalDateTime getTimestamp();

    Boolean getArchived();
}
