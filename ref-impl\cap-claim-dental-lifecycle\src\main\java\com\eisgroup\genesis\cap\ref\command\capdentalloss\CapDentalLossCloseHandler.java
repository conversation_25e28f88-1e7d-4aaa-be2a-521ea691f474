/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.loss.command.ClaimLossCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossCloseInput;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalClaimLossCloseValidator;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * Command handler for Dental Loss close to extend core functionality
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossCloseHandler extends ClaimLossCloseHandler<CapDentalLossCloseInput, CapLoss> {

    @Autowired
    private CapValidatorRegistry validators;

    @Autowired
    private EntityLinkBuilderRegistry linkBuilderRegistry;

    @Autowired
    private CapDentalClaimLossCloseValidator capDentalClaimLossCloseValidator;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalLossCloseInput input, @Nonnull CapLoss loadedEntity) {
        EntityLink<RootEntity> capLossUri =  linkBuilderRegistry.<RootEntity>getByType(loadedEntity.getClass()).createLink(loadedEntity);
        return Streamable.concat(validators.validateRequest(input),
                capDentalClaimLossCloseValidator.validateSettlementNotApproved(capLossUri));
    }




}
