@Description("Dental allocation payable item details.")
BaseType CapDentalPaymentAllocationPayableItemEntity extends CapPaymentAllocationPayableItem {

    //EISDEVTS-45186
    @Description("Link to dental claim.")
    ExtLink claimSource: RootEntity

    //EISDEVTS-45186
    @Description("Month number for which allocation is paid.")
    Attr orthoMonth: Integer

    //EISDEVTS-40055
    @Description("Related Settlement Result entry's procedure ID.")
    Attr procedureID: String
}