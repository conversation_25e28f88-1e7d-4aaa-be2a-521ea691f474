Transformation CalculatePaymentSchedule {
    Input {
        CapDentalPaymentTemplate.CapDentalPaymentTemplateEntity as template
    }
    Output {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity
    }

    Var request is createOpenlRequest(template)
    Var response is ExecuteRules("claim-dental-financial", "_api_payment_scheduler", request)

    Attr payments is response.payments
    Attr paymentTemplate is SafeInvoke(template._key.id, ToExtLink(template))
    Attr originSource is template.originSource
    Attr scheduleMessages is response.scheduleMessages

    Producer createOpenlRequest(template) {
        Var paymentIndexes is Load(createPaymentIndexKey(template.originSource), "CapDentalPaymentIndex", "CapDentalPaymentIdxEntity")

        Attr template is template
        Attr actualPayments is resolveActualPayments().actualPayments
    }

    Producer resolveActualPayments() {
         Var resolvedPayments is resolvePayments(paymentIndexes).resolvedPayment
         Attr actualPayments is SafeInvoke(resolvedPayments, mapPayment(resolvedPayments))
    }

    Producer resolvePayments(indexedPayment) {
        Attr resolvedPayment is ExtLink(AsExtLink(indexedPayment.paymentId))
    }

    Producer createPaymentIndexKey(originSource) {
        Attr originSource is originSource._uri
    }

    Producer mapPayment(payment) {
        Attr state is payment.state
        Attr paymentNumber is payment.paymentNumber
        Attr paymentDetails is payment.paymentDetails
        Attr creationDate is payment.creationDate
        Attr paymentUri is ToExtLink(payment)
    }
}
