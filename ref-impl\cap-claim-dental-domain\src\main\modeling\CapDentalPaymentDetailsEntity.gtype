@Description("An object which extends payment details.")
BaseType CapDentalPaymentDetailsEntity extends CapPaymentDetails {

    //EISDEVTS-45186
    @Description("Payment payee details.")
    @KrakenChildContext
    @KrakenField
    Attr payeeDetails: CapDentalPaymentPayeeDetailsEntity

    //EISDEVTS-45186
    @Description("Payment additions")
    @Exclude
    Attr paymentAdditions: *CapPaymentAddition

    //EISDEVTS-45186
    @Description("Payment allocations details.")
    Attr paymentAllocations: *CapDentalPaymentAllocationEntity

    //EISDEVTS-45186
    @Description("Payment reductions")
    @Exclude
    Attr paymentReductions: *CapPaymentReduction

    //EISDEVTS-45186
    @Description("Payment taxes")
    @Exclude
    Attr paymentTaxes: *CapPaymentTax
}