<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2">
  <process id="closeLoss" name="Close Loss" isExecutable="true">
    <startEvent id="requestCloseLossEventReceived" name="request close loss">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalLoss_requestCloseLoss]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="payload" sourceType="string" target="lossPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>        
    </startEvent>
    <serviceTask id="closeLossSendEvent" name="call Close Loss" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType>V20_command</flowable:eventType>
        <flowable:triggerEventType>V20_response</flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"/>
        <flowable:eventInParameter source="closeLoss" target="commandName" targetType="string"/>
        <flowable:eventInParameter source="CapDentalLoss" target="_modelName" targetType="string"/>
        <flowable:eventInParameter sourceExpression="${_key}" target="_key" targetType="string"/>
        <flowable:eventInParameter sourceExpression="${_modelVersion}" target="_modelVersion" targetType="string"/>
        <flowable:eventInParameter sourceExpression="{'reasonCd': '${lossPayload.reasonCd}', '_key': ${_key}}" target="payload" targetType="string"/>
        <flowable:channelKey>V20_outbound_kafka_command</flowable:channelKey>
        <flowable:triggerEventCorrelationParameter name="requestId" type="null" value="${requestId}"/>
      </extensionElements>
    </serviceTask>
    <endEvent id="sid-E4B74577-DB4C-45B9-A19F-B2B04CA55E51"/>
    <sequenceFlow id="sid-871B6B39-915B-45A6-8DF2-7F687CC4BE0E" sourceRef="closeLossSendEvent" targetRef="sid-E4B74577-DB4C-45B9-A19F-B2B04CA55E51"/>
    <serviceTask id="callCancelSchedule" name="call cancelLossPaymentSchedules" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType>V20_command</flowable:eventType>
        <flowable:triggerEventType>V20_response</flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"/>
        <flowable:eventInParameter source="_key" target="_key" targetType="string"/>
        <flowable:eventInParameter source="_modelVersion" target="_modelVersion" targetType="string"/>
        <flowable:eventInParameter source="CapDentalPaymentSchedule" target="_modelName" targetType="string"/>
        <flowable:eventInParameter source="cancelLossPaymentSchedules" target="commandName" targetType="string"/>
        <flowable:eventInParameter sourceExpression="{'originSource':{'_uri': '${_uri}'}}" target="payload" targetType="string"/>
        <flowable:channelKey>V20_outbound_kafka_command</flowable:channelKey>
        <flowable:triggerEventCorrelationParameter name="requestId" type="null" value="${requestId}"/>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="sid-82DC01AF-77E8-4D1D-83E7-09ABAB918C5B" sourceRef="callCancelSchedule" targetRef="closeLossSendEvent"/>
    <sequenceFlow id="sid-CF759322-65B8-4F69-928B-210D161B9656" sourceRef="requestCloseLossEventReceived" targetRef="callCancelSchedule"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_closeLoss">
    <bpmndi:BPMNPlane bpmnElement="closeLoss" id="BPMNPlane_closeLoss">
      <bpmndi:BPMNShape bpmnElement="requestCloseLossEventReceived" id="BPMNShape_requestCloseLossEventReceived">
        <omgdc:Bounds height="30.0" width="30.0" x="100.0" y="163.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="closeLossSendEvent" id="BPMNShape_closeLossSendEvent">
        <omgdc:Bounds height="86.0" width="208.0" x="705.0" y="138.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-E4B74577-DB4C-45B9-A19F-B2B04CA55E51" id="BPMNShape_sid-E4B74577-DB4C-45B9-A19F-B2B04CA55E51">
        <omgdc:Bounds height="28.0" width="28.0" x="1035.0" y="167.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="callCancelSchedule" id="BPMNShape_callCancelSchedule">
        <omgdc:Bounds height="89.0" width="174.0" x="285.0" y="136.5"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-82DC01AF-77E8-4D1D-83E7-09ABAB918C5B" id="BPMNEdge_sid-82DC01AF-77E8-4D1D-83E7-09ABAB918C5B" flowable:sourceDockerX="87.0" flowable:sourceDockerY="44.5" flowable:targetDockerX="104.0" flowable:targetDockerY="43.0">
        <omgdi:waypoint x="458.95000000000005" y="181.0"/>
        <omgdi:waypoint x="704.9999999998386" y="181.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-871B6B39-915B-45A6-8DF2-7F687CC4BE0E" id="BPMNEdge_sid-871B6B39-915B-45A6-8DF2-7F687CC4BE0E" flowable:sourceDockerX="104.0" flowable:sourceDockerY="43.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="912.9499999999944" y="181.0"/>
        <omgdi:waypoint x="1035.0" y="181.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-CF759322-65B8-4F69-928B-210D161B9656" id="BPMNEdge_sid-CF759322-65B8-4F69-928B-210D161B9656" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="87.0" flowable:targetDockerY="44.5">
        <omgdi:waypoint x="129.94896939246908" y="178.17450217134655"/>
        <omgdi:waypoint x="284.99999999999875" y="179.98443579766536"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>