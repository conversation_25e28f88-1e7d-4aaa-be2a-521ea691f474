/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.CapPaymentScheduleActivateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;


/**
 * Command for activating Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.9
 */
@Modifying
public class CapDentalPaymentScheduleActivateHandler extends CapPaymentScheduleActivateHandler<IdentifierRequest, CapDentalPaymentScheduleEntity> {

    @Autowired
    private CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull IdentifierRequest request, @Nonnull CapDentalPaymentScheduleEntity loadedEntity) {
        return capDentalPaymentScheduleValidator.validatePaymentScheduleActivation(loadedEntity);
    }

}
