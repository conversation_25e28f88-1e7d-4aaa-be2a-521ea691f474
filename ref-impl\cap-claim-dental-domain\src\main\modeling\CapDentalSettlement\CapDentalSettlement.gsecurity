Secure using privilege ("Adjudication: Initialize Settlement") {
    Model CapDentalSettlement {
        Command initSettlement
    }
}

Secure using privilege ("Adjudication: Adjudicate Settlement") {
    Model CapDentalSettlement {
        Command adjudicateSettlement
    }
}

Secure using privilege ("Adjudication: Approve Settlement") {
    Model CapDentalSettlement {
        Command approveSettlement
    }
}

Secure using privilege ("Adjudication: Close Settlement") {
    Model CapDentalSettlement {
        Command closeSettlement
    }
}