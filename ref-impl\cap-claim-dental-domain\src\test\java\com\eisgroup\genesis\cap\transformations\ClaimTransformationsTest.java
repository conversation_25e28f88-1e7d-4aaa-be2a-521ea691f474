/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations;

import com.eisgroup.genesis.cap.transformation.executors.config.DemandingOperatorsConfig;
import com.eisgroup.genesis.cap.transformation.testing.GenericTransformationsTestV2;
import com.eisgroup.genesis.transformation.config.TransformationListenerConfig;
import com.eisgroup.genesis.transformation.listeners.config.CompositeTransformationListenerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.test.context.ContextConfiguration;

import com.eisgroup.genesis.cap.transformation.testing.GenericTransformationsTest;
import com.eisgroup.genesis.cap.transformation.testing.TestConfig;
import com.eisgroup.genesis.cap.transformations.config.DentalTestConfig;

import java.util.TimeZone;

/**
 * Tests transformations.
 *
 * @see GenericTransformationsTest
 */
@ContextConfiguration(classes = {
        TestConfig.class,
        DentalTestConfig.class,
        DemandingOperatorsConfig.class,
        CompositeTransformationListenerConfig.class,
        TransformationListenerConfig.class
})
public class ClaimTransformationsTest extends GenericTransformationsTestV2 {

    @BeforeEach
    void setup() {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
    }
}
