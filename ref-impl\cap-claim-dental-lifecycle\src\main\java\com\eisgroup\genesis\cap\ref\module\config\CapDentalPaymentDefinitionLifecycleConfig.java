/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.validator.ClaimPaymentValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.validator.CapDentalPaymentInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.validator.CapDentalPaymentUpdateInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.validator.CapDentalRecoveryInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.validator.CapDentalRecoveryUpdateInputValidator;
import com.eisgroup.genesis.cap.ref.command.underpayment.validator.CapDentalUnderpaymentInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.underpayment.validator.CapDentalUnderpaymentUpdateInputValidator;
import com.eisgroup.genesis.cap.ref.command.waive.validator.CapDentalOverpaymentWaiveInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.waive.validator.CapDentalOverpaymentWaiveUpdateValidator;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.generator.impl.SimpleNumberGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import org.springframework.context.annotation.Bean;

public class CapDentalPaymentDefinitionLifecycleConfig {

    @Bean
    public EntityNumberGenerator paymentNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("claim_payment_number", "P%d", sequenceGenerator);
    }

    @Bean
    public EntityNumberGenerator recoveryNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("claim_recovery_number", "R%d", sequenceGenerator);
    }

    @Bean
    public ClaimPaymentValidator claimSettlementInputValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new ClaimPaymentValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapValidatorRegistry capValidatorRegistry() {
        return new CapValidatorRegistry();
    }

    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalPaymentInitInputValidator capDentalPaymentInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPaymentInitInputValidator(capDentalLinkValidator);
    }
    
    @Bean
    public CapDentalPaymentUpdateInputValidator capDentalPaymentUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalPaymentUpdateInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalRecoveryInitInputValidator capDentalRecoveryInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalRecoveryInitInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalRecoveryUpdateInputValidator capDentalRecoveryUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalRecoveryUpdateInputValidator(capDentalLinkValidator);
    }

    @Bean
    public EntityNumberGenerator underpaymentNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("claim_underpayment_number", "U%d", sequenceGenerator);
    }

    @Bean
    public EntityNumberGenerator overpaymentWaiveNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("claim_overpayment_waive_number", "W%d", sequenceGenerator);
    }

    @Bean
    public CapDentalOverpaymentWaiveInitInputValidator capDentalOverpaymentWaiveInitInputValidator(CapDentalLinkValidator capDentalLinkValidator){
        return new CapDentalOverpaymentWaiveInitInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalOverpaymentWaiveUpdateValidator capDentalOverpaymentWaiveUpdateValidator(CapDentalLinkValidator capDentalLinkValidator){
        return new CapDentalOverpaymentWaiveUpdateValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalUnderpaymentInitInputValidator capDentalUnderpaymentInitInputValidator(CapDentalLinkValidator capDentalLinkValidator){
        return new CapDentalUnderpaymentInitInputValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalUnderpaymentUpdateInputValidator capDentalUnderpaymentUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator){
        return new CapDentalUnderpaymentUpdateInputValidator(capDentalLinkValidator);
    }
}
