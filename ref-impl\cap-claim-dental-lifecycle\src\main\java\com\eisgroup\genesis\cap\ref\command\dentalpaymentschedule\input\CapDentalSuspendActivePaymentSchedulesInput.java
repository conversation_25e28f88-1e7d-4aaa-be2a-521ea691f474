/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input;

import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalSuspendLossPaymentSchedulesHandler;
import com.eisgroup.genesis.common.Required;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import java.util.Optional;

/**
 * {@link CapDentalSuspendLossPaymentSchedulesHandler} command input.
 *
 * <AUTHOR>
 * @since 22.10
 */
public class CapDentalSuspendActivePaymentSchedulesInput extends AbstractJsonEntity {

    private static final String ORIGIN_SOURCE = "originSource";

    public CapDentalSuspendActivePaymentSchedulesInput(JsonObject original) {
        super(original);
    }

    /**
     * @return {@link com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity} link
     */
    @Required
    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(dentalLossUri -> new EntityLink<>(RootEntity.class, dentalLossUri))
                .orElse(null);
    }
}
