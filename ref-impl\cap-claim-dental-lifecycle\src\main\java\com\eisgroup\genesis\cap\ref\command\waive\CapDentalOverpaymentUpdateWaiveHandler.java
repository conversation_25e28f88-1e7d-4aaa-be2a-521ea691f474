/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.waive;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.waive.CapOverpaymentWaiveUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.waive.input.CapDentalOverpaymentWaiveUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.json.key.BaseKey;

import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for initialization of update overpayment waive with provided input data.
 *
 * <AUTHOR>
 * @since 22.15
 */
@Modifying
public class CapDentalOverpaymentUpdateWaiveHandler extends CapOverpaymentWaiveUpdateHandler<CapDentalOverpaymentWaiveUpdateInput, CapDentalPaymentEntity> {

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalOverpaymentWaiveUpdateInput request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

    @Override
    protected Lazy<CapDentalPaymentEntity> populateAttributes(CapDentalOverpaymentWaiveUpdateInput input, CapDentalPaymentEntity entity) {
        return Lazy.deferUsing(Streamable::currentContext, contextView -> {
            CapPaymentDetails paymentDetails = input.getEntity();
            paymentDetails.toJson().add(BaseKey.ATTRIBUTE_NAME, entity.getPaymentDetails().getKey().toJson());
            entity.setPaymentDetails(paymentDetails);
            entity.setPaymentNetAmount(input.getPaymentNetAmount());
            KeyTraversalUtil.traverseRoot(contextView, entity.toJson(), modelResolver.resolveModel(DomainModel.class));
            return Lazy.of(entity);
        });
    }
}
