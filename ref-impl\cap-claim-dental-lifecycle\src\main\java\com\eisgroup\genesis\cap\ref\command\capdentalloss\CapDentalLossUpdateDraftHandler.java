/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.cap.loss.command.ClaimLossUpdateDraftHandler;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossUpdateInput;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;

/**
 * The command that updates claim loss with provided input data
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossUpdateDraftHandler extends <PERSON>lai<PERSON><PERSON><PERSON>UpdateDraftHandler<ClaimLossUpdateInput, CapLoss> {


}
