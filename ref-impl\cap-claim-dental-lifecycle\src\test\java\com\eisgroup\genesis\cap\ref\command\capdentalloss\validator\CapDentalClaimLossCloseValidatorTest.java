/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.factory.model.capdentalsettlementindex.CapDentalSettlementIdx;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.google.gson.JsonObject;
import org.hamcrest.Matchers;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.List;

import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalClaimLossCloseValidator.CapDentalClaimLossCloseErrorDefinition.SETTLEMENT_APPROVED;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalClaimLossCloseValidatorTest {

    @InjectMocks
    private CapDentalClaimLossCloseValidator validator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Mock
    private EntityLinkResolver<RootEntity> entityLinkResolver;

    @Before
    public void setUp() {
        when(entityLinkResolverRegistry.getByURIScheme(any())).thenReturn(entityLinkResolver);
    }

    @Test
    public void shouldValidateSettlementNotApproved() {
        //given
        EntityLink<RootEntity> capLossUri = new EntityLink<>(RootEntity.class, "LossUri");
        JsonObject settlementJsonObj = JsonUtils.load("json/capDentalClaimLossCloseValidator/settlementApprovedState.json");
        CapDentalSettlementEntity settlementEntity = (CapDentalSettlementEntity) ModelInstanceFactory.createInstance(settlementJsonObj);
        when(capDentalSettlementIndexResolver.resolveSettlementIndex(capLossUri)).thenReturn(Streamable.of(createCapDentalSettlementIdx()));
        when(entityLinkResolver.resolve(eq(new EntityLink<>(RootEntity.class, "SettlementId")), any()))
                .thenReturn(Lazy.of(settlementEntity));

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(validator.validateSettlementNotApproved(capLossUri))
                .assertNoErrors()
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(1));
        assertThat(errorHolders.get(0).getCode(), Matchers.equalTo(SETTLEMENT_APPROVED.getCode()));
    }

    private CapDentalSettlementIdx createCapDentalSettlementIdx() {
        CapDentalSettlementIdx settlementIdx = (CapDentalSettlementIdx) ModelInstanceFactory.createRootInstance("CapDentalSettlementIndex", "1");
        settlementIdx.setSettlementId("SettlementId");
        LocalDateTime now = LocalDateTime.of(2025, 1, 1, 12, 30);
        settlementIdx.setCreatedOn(now);
        return settlementIdx;
    }

}
