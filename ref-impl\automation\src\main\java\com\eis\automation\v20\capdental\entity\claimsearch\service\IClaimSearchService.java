/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.claimsearch.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.claimsearch.facade.IClaimSearch;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;

import java.util.List;

public interface IClaimSearchService extends ITestDataService, IFacadeService<IClaimSearch> {

    List<ICapDentalSettlementModel> searchDentalSettlement(ICapDentalLossModel capDentalLossModel, int responseCount);

    List<ICapDentalPaymentTemplateModel> searchDentalPaymentTemplate(ICapDentalLossModel capDentalLossModel, int responseCount);

    List<ICapDentalPaymentScheduleModel> searchDentalPaymentSchedule(ICapDentalLossModel capDentalLossModel, int responseCount);
}
