// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"CapDentalSettlementIndex",
  "name":"CapDentalSettlementIndex",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalSettlementIdx"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapDentalSettlementIdx":{
      "attributes":{
        "createdOn":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"createdOn",
          "type":{
            "type":"DATETIME"
          }
        },
        "lossId":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.PartitionedKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"lossId",
          "type":{
            "type":"STRING"
          }
        },
        "settlementId":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
              "order":0
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.factory.features.ClusteredKey":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      {
                        "@class":"java.lang.Long",
                        "value":"0"
                      }
                    ]
                  },
                  "inherited":false
                }
              }
            }
          },
          "name":"settlementId",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.cap.factory.features.Persistable":{
          "keyspace":"CapSettlement"
        },
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.cap.factory.features.Persistable":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "CapSettlement"
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalSettlementIdx",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalSettlementIndex {
    export type Variations = never


    export class CapDentalSettlementIdx extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalSettlementIdx.name) }
        readonly _modelName: string = 'CapDentalSettlementIndex'
        readonly _modelType: string = 'CapDentalSettlementIndex'
        readonly _modelVersion?: string = '1'
        readonly createdOn?: Date
        readonly lossId?: string
        readonly settlementId?: string
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalSettlementIdx, ()=> new CapDentalSettlementIdx)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}