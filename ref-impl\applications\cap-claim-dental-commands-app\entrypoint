#!/usr/bin/env bash
JAVA_EXEC="${JAVA_HOME:-java}${JAVA_HOME:+/bin/java} ${JAVA_OPTS}"

# Set NM_HOST
export NM_HOST=${EXTERNAL_IP:-$( hostname -I | cut -d ' ' -f 1)}

# Set APP_NAME
APP_NAME=${GENESIS_APP_NAME:-`echo $NM_HOST`}

# Change to /usr/lib/genesis/
cd /usr/lib/genesis/

# Execute
exec ${JAVA_EXEC} \
        -Dgenesis.app.name="$APP_NAME" \
                    -Dspring.autoconfigure.exclude=com.eisgroup.genesis.security.config.AuthorizationClientConfig \
                               org.springframework.boot.loader.launch.PropertiesLauncher ${1}
