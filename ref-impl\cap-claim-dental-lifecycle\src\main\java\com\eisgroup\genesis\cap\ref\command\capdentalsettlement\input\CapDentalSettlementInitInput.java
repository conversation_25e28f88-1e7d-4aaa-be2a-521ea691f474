/* Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.input;

import com.eisgroup.genesis.cap.adjudication.command.input.CapRequestSettlementAdjudicationInput;
import com.google.gson.JsonObject;

/**
 * LifeIntakeSettlement init input.
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalSettlementInitInput extends CapRequestSettlementAdjudicationInput {

    public CapDentalSettlementInitInput(JsonObject original) {
        super(original);
    }
}
