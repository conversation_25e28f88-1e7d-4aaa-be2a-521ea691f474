@EventListener("adjudicateSettlement")
Transformation InitSettlementProcedureIndex {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        CapDentalSettlement.CapDentalSettlementEntity
    }

    // output of transformation unused
    Attr settlementLossInfo is indexProcedures(settlement).settlementLossInfo

    Producer indexProcedures(settlement) {
        Attr settlementLossInfo is settlement.settlementLossInfo
        Var procedures is settlement.settlementLossInfo.submittedProcedures
        // index for every procedure
        Attr indexed is indexingTransformation(settlement, procedures)
    }

    Producer indexingTransformation(settlement, procedure) {
        Attr projection is Transform("SubmittedProcedureToProcedureProjection", settlement, addModelName(procedure))
    }

    @Passthrough
    Producer addModelName(procedure) {
        Attr _modelName is "CapDentalSettlement"
    }
}