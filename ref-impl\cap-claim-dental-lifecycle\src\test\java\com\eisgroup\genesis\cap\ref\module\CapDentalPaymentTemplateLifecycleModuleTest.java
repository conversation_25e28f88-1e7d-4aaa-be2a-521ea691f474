package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateCloseHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentTemplateLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalPaymentTemplateLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalPaymentTemplate";

    private static final String MODEL_TYPE = "CapPaymentTemplate";

    private CapDentalPaymentTemplateLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalPaymentTemplateLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalPaymentTemplateInitHandler.class,
                CapDentalPaymentTemplateUpdateHandler.class,
                CapDentalPaymentTemplateCloseHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(module.getModelVersion(), equalTo("1"));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalPaymentTemplateLifecycleConfig.class), equalTo(true));
    }
}
