/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;

import java.util.Collection;
import java.util.List;

import com.eisgroup.genesis.cap.transformation.endpoint.CommonTransformationEndpoint;
import com.eisgroup.genesis.facade.module.EndpointPackage;
import com.eisgroup.genesis.facade.module.FacadeModule;

/**
 * Dental Generic Facade
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalGenericFacade implements FacadeModule {

    @Override
    public Collection<EndpointPackage> getEndpoints() {
        return List.of(new CommonTransformationEndpoint());
    }

    @Override
    public String getModelType() {
        return GENERIC_MODEL_TYPE;
    }

    @Override
    public String getModelName() {
        return GENERIC_MODEL_TYPE;
    }

    @Override
    public int getFacadeVersion() {
        return 1;
    }

}