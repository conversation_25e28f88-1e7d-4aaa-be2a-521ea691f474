/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.impl.modeling.impl.CapBasePaymentAllocationTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentAllocationTemplateDentalDetailsModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentAllocationTemplateModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentAllocationTemplateModel extends CapBasePaymentAllocationTemplateModel implements ICapDentalPaymentAllocationTemplateModel {

    private ICapDentalPaymentAllocationTemplateDentalDetailsModel allocationDentalDetails;

    @JsonSerialize(as = CapDentalPaymentAllocationTemplateDentalDetailsModel.class)
    public ICapDentalPaymentAllocationTemplateDentalDetailsModel getAllocationDentalDetails() {
        return allocationDentalDetails;
    }

    @JsonDeserialize(as = CapDentalPaymentAllocationTemplateDentalDetailsModel.class)
    public void setAllocationDentalDetails(ICapDentalPaymentAllocationTemplateDentalDetailsModel allocationDentalDetails) {
        this.allocationDentalDetails = allocationDentalDetails;
    }
}