AssertClaimOverideAllowed=Claim override isAllowed cannot be used and the same time with isDenied override.
AssertClaimOverideDenied=Claim isDenied cannot be used at the same time with isAllowed override.
AssertServiceOverideAllowed=Service override isAllowed cannot be used and the same time with isDenied override.
AssertServiceOverideDenied=Service override isDenied cannot be used at the same time with isAllowed override.
ClaimOverrideBasicWaitingPeriodHigherThanZero=overrideBasicWaitingPeriod has to be greater than 0.
ClaimOverrideGracePeriodEqualOrHigherThanZero=Claim overrideGracePeriod cannot be negative.
ClaimOverrideLateEntrantWaitingPeriodHigherThanZero=Claim overrideLateEntrantWaitingPeriod has to be greater than 0.
ClaimOverrideMajorWaitingPeriodHigherThanZero=overrideMajorWaitingPeriod has to be greater than 0.
ClaimOverrideOrthoWaitingPeriodHigherThanZero=overrideOrthoWaitingPeriod has to be greater than 0.
ClaimOverridePaymentInterestAmountEqualOrHigherThanZero=Claim overridePaymentInterestAmount cannot be negative.
ClaimOverridePaymentInterestDaysEqualOrHigherThanZero=Claim overridePaymentInterestDays cannot be negative.
ClaimOverridePreventiveWaitingPeriodHigherThanZero=overridePreventiveWaitingPeriod has to be greater than 0.
MandatoryClaimURI=A claimLossIdentification link to Claim is mandatory.
ServiceOverrideCoinsurancePctEqualOrHigherThanZero=overrideCoinsurancePct cannot be negative.
ServiceOverrideConsideredAmountEqualOrHigherThanZero=overrideConsideredAmount cannot be negative.
ServiceOverrideCopayAmountEqualOrHigherThanZero=overrideCopayAmount cannot be negative.
ServiceOverrideCoveredAmountEqualOrHigherThanZero=overrideCoveredAmount cannot be negative.
ServiceOverrideDeductibleEqualOrHigherThanZero=overrideDeductible cannot be negative.
ServiceOverrideGracePeriodEqualOrHigherThanZero=Service overrideGracePeriod cannot be negative.
ServiceOverrideLateEntrantWaitingPeriodHigherThanZero=Service overrideLateEntrantWaitingPeriod has to be greater than 0.
ServiceOverrideMaximumAmountEqualOrHigherThanZero=overrideMaximumAmount cannot be negative.
ServiceOverridePatientResponsibilityEqualOrHigherThanZero=overridePatientResponsibility cannot be negative.
ServiceOverridePaymentInterestAmountEqualOrHigherThanZero=Service overridePaymentInterestAmount cannot be negative.
ServiceOverridePaymentInterestDaysEqualOrHigherThanZero=Service overridePaymentInterestDays cannot be negative.
ServiceOverrideReplacementLimitHigherThanZero=overrideReplacementLimit has to be greater than 0.
ServiceOverrideServiceFrequencyLimitHigherThanZero=overrideServiceFrequencyLimit has to be greater than 0.
ServiceOverrideServiceWaitingPeriodHigherThanZero=overrideServiceWaitingPeriod has be to greater than 0.