@CapEndpoint("capDentalFindSettlementForSchedule")
Transformation CapDentalFindSettlementForSchedule {
  Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        ?  //as out
    }
    Var loadedLoss is ExtLink(schedule.originSource)
    Var settlementIndex is Load(createLossId(schedule), "CapDentalSettlementIndex", "CapDentalSettlementIdx")
    Var loadedSettlements is loadSettlement(settlementIndex.settlementId)
    Var settlements is FlatMap(loadedSettlements.settlement)
    Var currentSettlement is First(settlements[filterApproved]<revisionNoDesc>)

    Attr loss is loadedLoss
    Attr lossUri is ToExtLink(loadedLoss)
    Attr settlement is currentSettlement

    Producer createLossId(schedule) {
        Attr lossId is schedule.originSource._uri
    }

    Producer loadSettlement(settlementId){
        Attr settlement is ExtLink(AsExtLink(settlementId))
    }

    Filter filterApproved {
        state == "Approved"
    }

    Sort revisionNoDesc {
        "revisionNo" -> "DESC"
    }
}