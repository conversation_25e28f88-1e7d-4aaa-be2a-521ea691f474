/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapLookupValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossSubStatusInitInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.google.gson.JsonObject;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossSubStatusHandlerTest {

    @InjectMocks
    private CapDentalLossSubStatusHandler handler;

    @Mock(lenient=true)
    private CapLookupValidator lookupValidator;

    @Test
    public void shouldValidateAsync() {
        //given
        CapDentalLossSubStatusInitInput input = new CapDentalLossSubStatusInitInput(new JsonObject());
        CapDentalLossEntity entity = (CapDentalLossEntity) ModelInstanceFactory.createRootInstance("CapDentalLoss", "1");
        when(lookupValidator.validateAdditionalLookupField(anyString(), anyString(), anyString(), anyString())).thenReturn(Streamable.empty());

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(handler.validateAsync(input, entity))
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(0));
    }
}
