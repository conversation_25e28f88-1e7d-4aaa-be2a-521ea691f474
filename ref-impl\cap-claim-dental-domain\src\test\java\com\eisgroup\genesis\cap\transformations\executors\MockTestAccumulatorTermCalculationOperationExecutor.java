/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations.executors;

import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.executors.OperationExecutorValidator;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.eisgroup.genesis.util.DateUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;

/**
 * Operation for accumulator term calculation based on benefit period and limit level type.
 *
 * Expected argument values:
 * <ul>
 *     <li>first argument: policy term effective date</li>
 *     <li>second argument: JsonObject of benefit period. e.g. {"startDate":"2023-04-20T02:52:41.103Z","endDate":"2023-05-25T02:52:41.103Z"}. Only endDate can be empty.</li>
 *     <li>third argument: Limit Level type, only "BenefitYear" and "CalendarYear" are supported.</li>
 * </ul>
 *
 * Benefit Year Example:
 *   Input:
 *     {
 *         "policyTermEffectiveDate": "2023-06-20T020:52:41.103Z"
 *         "period": {
 *              "startDate":"2023-04-20T02:52:41.103Z",
 *              "endDate":"2023-10-25T02:52:41.103Z"
 *         }
 *         "limitLevelType": "BenefitYear"
 *     }
 *   Output:
 *   [{
 *       "effectiveDate": "2022-06-20T00:00:00.000Z"
 *       "expirationDate": "2023-06-19T23:59:59.999Z"
 *   }]
 *
 * Calendar Year Example:
 *   Input:
 *     {
 *         "policyTermEffectiveDate": "2023-06-20T020:52:41.103Z"
 *         "period": {
 *              "startDate":"2023-04-20T02:52:41.103Z",
 *              "endDate":"2023-10-25T02:52:41.103Z"
 *         }
 *         "limitLevelType": "CalendarYear"
 *     }
 *   Output:
 *   [{
 *       "effectiveDate": "2023-01-01T00:00:00.000Z"
 *       "expirationDate": "2023-12-31T23:59:59.999Z"
 *   }]
 *
 * <AUTHOR>
 * @since 25.8
 */
public class MockTestAccumulatorTermCalculationOperationExecutor implements OperationExecutor<GenericOperation, JsonElement> {

    private static final String BENEFIT_YEAR_LIMIT_LEVEL_TYPE = "BenefitYear";
    private static final String CALENDAR_YEAR_LIMIT_LEVEL_TYPE = "CalendarYear";
    private static final String[] APPLICABLE_LIMIT_LEVEL_TYPES = new String[]{BENEFIT_YEAR_LIMIT_LEVEL_TYPE, CALENDAR_YEAR_LIMIT_LEVEL_TYPE};
    public static final String END_DATE = "endDate";

    @Override
    public JsonElement execute(GenericOperation operation,
                                       TransformationContext<JsonElement> context) {
        var policyEffectiveDate = OperationExecutorValidator.validateStringOrNull(context.execute(operation.getInput(0)),
            "CalculateAccumulatorTerm operation 1st argument should resolve to a string.");
        var period = OperationExecutorValidator.validateJsonObject(context.execute(operation.getInput(1)),
            "CalculateAccumulatorTerm operation 2nd argument should resolve to a jsonObject.");
        var limitLevelType = validateLimitLevelType(context.execute(operation.getInput(2)));
        return calculateAccumulatorTerm(policyEffectiveDate, period, limitLevelType);
    }

    /**
     * Calculate accumulator team based on benefit perid and limit level type
     *
     * @param policyEffectiveDate
     * @param period
     * @param limitLevelType
     * @return
     */
    private JsonArray calculateAccumulatorTerm(JsonElement policyEffectiveDate, JsonObject period, String limitLevelType) {
        LocalDateTime periodStartDate = DateUtil.toDateTime(period.get("startDate").getAsString());
        LocalDateTime periodEndDate = null;
        if (period.has(END_DATE) && !period.get(END_DATE).isJsonNull()) {
            periodEndDate = DateUtil.toDateTime(period.get(END_DATE).getAsString());
        }
        if (StringUtils.equals(limitLevelType, CALENDAR_YEAR_LIMIT_LEVEL_TYPE)) {
            return calculateAccumulatorTermForCalendarYear(periodStartDate, periodEndDate);
        } else if (StringUtils.equals(limitLevelType, BENEFIT_YEAR_LIMIT_LEVEL_TYPE)) {
            LocalDateTime effectiveDate = DateUtil.toDateTime(policyEffectiveDate.getAsString());
            return calculateAccumulatorTermForBenefitYear(effectiveDate, periodStartDate, periodEndDate);
        }
        return new JsonArray();
    }

    /**
     * Calculate accumulator team for calendar year
     *
     * @param periodStartDate
     * @param periodEndDate
     * @return
     */
    private JsonArray calculateAccumulatorTermForCalendarYear(LocalDateTime periodStartDate, LocalDateTime periodEndDate) {
        JsonArray termArray = new JsonArray();
        LocalDateTime termEffectiveDate = LocalDateTime.of(LocalDate.from(periodStartDate.with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
        LocalDateTime termExpirationDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
        termArray.add(constructTerm(termEffectiveDate, termExpirationDate));
        if (periodEndDate != null) {
            while(termExpirationDate.compareTo(periodEndDate) < 0) {
                termEffectiveDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.plusYears(1).with(TemporalAdjusters.firstDayOfYear())), LocalTime.MIN);
                termExpirationDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.with(TemporalAdjusters.lastDayOfYear())), LocalTime.MAX);
                termArray.add(constructTerm(termEffectiveDate, termExpirationDate));
            }
        }
        return termArray;
    }

    /**
     * Calculate accumulator term for benefit year
     *
     * @param policyEffectiveDate
     * @param periodStartDate
     * @param periodEndDate
     * @return
     */
    private JsonArray calculateAccumulatorTermForBenefitYear(LocalDateTime policyEffectiveDate, LocalDateTime periodStartDate, LocalDateTime periodEndDate) {
        JsonArray termArray = new JsonArray();
        LocalDateTime termEffectiveDate = resolveEffectiveDateForBenefitYear(policyEffectiveDate, periodStartDate);
        LocalDateTime termExpirationDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.plusYears(1).plusDays(-1)), LocalTime.MAX);
        termArray.add(constructTerm(termEffectiveDate, termExpirationDate));
        if (periodEndDate != null) {
            while(termExpirationDate.compareTo(periodEndDate) < 0) {
                termEffectiveDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.plusYears(1)), LocalTime.MIN);
                termExpirationDate = LocalDateTime.of(LocalDate.from(termEffectiveDate.plusYears(1).plusDays(-1)), LocalTime.MAX);
                termArray.add(constructTerm(termEffectiveDate, termExpirationDate));
            }
        }
        return termArray;
    }

    /**
     * Resove effective date for benefit year
     *
     * @param policyEffectiveDate
     * @param startDate
     * @return
     */
    private LocalDateTime resolveEffectiveDateForBenefitYear(LocalDateTime policyEffectiveDate, LocalDateTime startDate) {
        LocalDateTime effectiveDate = LocalDateTime.of(LocalDate.from(policyEffectiveDate), LocalTime.MIN);
        LocalDateTime startDateWithoutTime = LocalDateTime.of(LocalDate.from(startDate), LocalTime.MIN);
        if (effectiveDate.compareTo(startDateWithoutTime) > 0) {
            while(effectiveDate.compareTo(startDateWithoutTime) > 0) {
                effectiveDate = LocalDateTime.of(LocalDate.from(effectiveDate.plusYears(-1)), LocalTime.MIN);
            }
        } else {
            while(effectiveDate.compareTo(startDateWithoutTime) < 0 && ChronoUnit.YEARS.between(effectiveDate, startDateWithoutTime) >= 1) {
                effectiveDate = LocalDateTime.of(LocalDate.from(effectiveDate.plusYears(1)), LocalTime.MIN);
            }
        }
        return effectiveDate;
    }

    /**
     * Validate if limit level type is applicable
     *
     * @param parameter
     * @return
     */
    private String validateLimitLevelType(JsonElement parameter) {
        if (parameter == null || !parameter.isJsonPrimitive()) {
            throw new IllegalArgumentException("CalculateAccumulatorTerm operation 3rd argument should be BenefitYear or CalendarYear.");
        }
        String limitLevelType = parameter.getAsString();
        if (!Arrays.asList(APPLICABLE_LIMIT_LEVEL_TYPES).contains(limitLevelType)) {
            throw new IllegalArgumentException("CalculateAccumulatorTerm operation 3rd argument should be BenefitYear or CalendarYear.");
        }
        return limitLevelType;
    }

    /**
     * Construct accumulator term
     *
     * @param effectiveDate
     * @param expirationDate
     * @return
     */
    private JsonObject constructTerm(LocalDateTime effectiveDate, LocalDateTime expirationDate) {
        JsonObject term = new JsonObject();
        term.add("effectiveDate", new JsonPrimitive(DateUtil.toString(effectiveDate)));
        term.add("expirationDate", new JsonPrimitive(DateUtil.toString(expirationDate)));
        return term;
    }

    @Override
    public String getName() {
        return "CalculateAccumulatorTerm";
    }
}
