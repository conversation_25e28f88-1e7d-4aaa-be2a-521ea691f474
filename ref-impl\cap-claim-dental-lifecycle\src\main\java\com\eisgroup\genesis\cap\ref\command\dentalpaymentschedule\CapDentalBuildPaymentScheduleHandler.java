/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.CapPaymentScheduleCommands;
import com.eisgroup.genesis.cap.financial.command.CapPaymentTemplateCommands;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.CapPaymentScheduleBaseCommandHandler;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapFinancialDataResolver;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapPaymentScheduleService;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalBuildPaymentScheduleInput;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPaymentScheduleInitInput;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input.CapDentalPaymentTemplateInitInput;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.command.result.CommandSuccess;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetailsTemplate;
import com.eisgroup.genesis.model.Variation;
import com.google.gson.JsonObject;

import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import static com.eisgroup.genesis.cap.financial.command.CapPaymentTemplateCommands.CLOSE_PAYMENT_TEMPLATE;
import static com.eisgroup.genesis.cap.financial.command.CapPaymentScheduleCommands.BUILD_PAYMENT_SCHEDULE;

/**
 * Collects settlements financial data and creates {@link CapDentalPaymentScheduleEntity}
 *
 * <AUTHOR>
 * @since 22.10
 */
@Modifying
public class CapDentalBuildPaymentScheduleHandler
    extends CapPaymentScheduleBaseCommandHandler<CapDentalBuildPaymentScheduleInput, CapDentalPaymentScheduleEntity> {

    @Autowired
    private CapFinancialDataResolver<CapDentalBuildPaymentScheduleInput, CapDentalPaymentTemplateEntity> capFinancialDataResolver;

    @Autowired
    private CapPaymentScheduleService<CapDentalPaymentScheduleEntity> capPaymentScheduleService;

    @Autowired
    private CommandPublisher commandPublisher;

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public CapDentalPaymentScheduleEntity execute(@Nonnull CapDentalBuildPaymentScheduleInput request, @Nonnull CapDentalPaymentScheduleEntity entity) {
        return resolveFinancialData(request)
            .flatMap(this::initPaymentTemplate)
            .flatMap(createdTemplate -> createPaymentSchedule(createdTemplate)
                .flatMap(this::initPaymentSchedule)
                .flatMap(savedSchedule -> postProcessPaymentTemplate(createdTemplate)
                    .map(capPaymentTemplate -> savedSchedule)).flatMap(template -> Lazy.of(super.execute(request, template)))).get();
    }


    @Nonnull
    public CapDentalPaymentScheduleEntity save(@Nonnull CapDentalBuildPaymentScheduleInput request, @Nonnull CapDentalPaymentScheduleEntity entity) {
        return Lazy.of(entity).get();
    }

    public String getName() {
        return BUILD_PAYMENT_SCHEDULE;
    }

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalBuildPaymentScheduleInput input,
                                                 @Nonnull CapDentalPaymentScheduleEntity entity) {
        return capValidatorRegistry.validateRequest(input);
    }

    protected Lazy<CapDentalPaymentTemplateEntity> initPaymentTemplate(CapDentalPaymentTemplateEntity template) {
        JsonObject requestJson = createInitPaymentTemplateRequest(template).toJson();
        Command command = new Command(Variation.INVARIANT.getName(), CapPaymentTemplateCommands.INIT_PAYMENT_TEMPLATE,
            requestJson);
        return commandPublisher.publishLocally(command, template.getModelName())
            .cast(CommandSuccess.class)
            .map(result -> (CapDentalPaymentTemplateEntity) ModelInstanceFactory.createInstance(
                result.getData().getAsJsonObject()));
    }

    protected Lazy<CapDentalPaymentScheduleEntity> initPaymentSchedule(CapDentalPaymentScheduleEntity schedule) {
        JsonObject requestJson = createInitPaymentScheduleRequest(schedule).toJson();
        Command command = new Command(Variation.INVARIANT.getName(), CapPaymentScheduleCommands.INIT_PAYMENT_SCHEDULE,
            requestJson);
        return commandPublisher.publishLocally(command, schedule.getModelName())
            .cast(CommandSuccess.class)
            .map(result -> (CapDentalPaymentScheduleEntity) ModelInstanceFactory.createInstance(
                result.getData().getAsJsonObject()));
    }

    protected Lazy<CapDentalPaymentTemplateEntity> postProcessPaymentTemplate(CapDentalPaymentTemplateEntity template) {
        JsonObject requestJson = new IdentifierRequest(template.getKey()).toJson();
        Command command = new Command(Variation.INVARIANT.getName(), CLOSE_PAYMENT_TEMPLATE, requestJson);
        return commandPublisher.publishLocally(command, template.getModelName())
            .cast(CommandSuccess.class)
            .map(result -> (CapDentalPaymentTemplateEntity) ModelInstanceFactory.createInstance(
                result.getData().getAsJsonObject()));
    }

    private CapDentalPaymentTemplateInitInput createInitPaymentTemplateRequest(
        CapDentalPaymentTemplateEntity template) {
        return new CapDentalPaymentTemplateInitInput((CapPaymentDetailsTemplate) template.getPaymentDetailsTemplate(),
            template.getOriginSource());
    }

    private CapDentalPaymentScheduleInitInput createInitPaymentScheduleRequest(
        CapDentalPaymentScheduleEntity schedule) {
        return new CapDentalPaymentScheduleInitInput(schedule);
    }

    protected Lazy<CapDentalPaymentTemplateEntity> resolveFinancialData(CapDentalBuildPaymentScheduleInput request) {
        return capFinancialDataResolver.resolveTemplateFinancialData(request);

    }

    protected Lazy<CapDentalPaymentScheduleEntity> createPaymentSchedule(CapDentalPaymentTemplateEntity template) {
        return capPaymentScheduleService.createSchedule(template);
    }


}
