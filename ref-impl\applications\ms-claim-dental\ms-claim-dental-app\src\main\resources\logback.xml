<configuration>
  <jmxConfigurator />
  <property scope="context" name="appName" value="ms-claim-dental-app"/>

  <!-- includes -->
  <include optional="true" resource="zookeeper-logback.xml"/>
  <include optional="true" resource="stream-consumer-logback.xml"/>
  <include optional="true" resource="stream-producer-logback.xml"/>
  <include resource="spring-app-logback.xml"/>

  <logger name="com.eisgroup.cap.common.listener.CapCommonLoggingListener" level="DEBUG"/>
</configuration>