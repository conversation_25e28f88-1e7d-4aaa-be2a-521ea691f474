{"TestData_Preventive": {"entity": {"term": {"effectiveDate": "$<today-1M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "expirationDate": "$<today+11M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "Term"}, "policyPaidToDate": "$<today+50d:yyyy-MM-dd>", "productCd": "DNIndividual", "policyPaidToDateWithGracePeriod": "$<today+50d:yyyy-MM-dd>", "plan": "High", "planCategory": "PPO", "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 0, "coinsuranceOONPct": 0, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}], "insureds": [{"isFullTimeStudent": false, "isMain": true, "relationshipToPrimaryInsuredCd": "Self", "registryTypeId": "{{partyRegistryId}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity"}], "_modelName": "CapDentalUnverifiedPolicy", "_modelVersion": "1", "_modelType": "UnverifiedPolicy", "_archived": false, "_type": "CapDentalUnverifiedPolicy"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl.CapDentalPolicyInfoWrapperModel"}, "TestData_WO_Preventive": {"entity": {"term": {"effectiveDate": "$<today-1M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "expirationDate": "$<today+11M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "Term"}, "policyPaidToDate": "$<today+50d:yyyy-MM-dd>", "productCd": "DNIndividual", "policyPaidToDateWithGracePeriod": "$<today+50d:yyyy-MM-dd>", "plan": "High", "planCategory": "PPO", "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 0, "coinsuranceOONPct": 0, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}], "insureds": [{"isFullTimeStudent": false, "isMain": true, "relationshipToPrimaryInsuredCd": "Self", "registryTypeId": "{{partyRegistryId}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity"}], "_modelName": "CapDentalUnverifiedPolicy", "_modelVersion": "1", "_modelType": "UnverifiedPolicy", "_archived": false, "_type": "CapDentalUnverifiedPolicy"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl.CapDentalPolicyInfoWrapperModel"}}