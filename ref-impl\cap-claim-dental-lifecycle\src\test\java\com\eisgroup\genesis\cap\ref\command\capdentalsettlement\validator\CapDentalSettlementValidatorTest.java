/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementValidator.DentalSettlementErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;
import com.eisgroup.genesis.repository.TargetEntityNotFoundException;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class CapDentalSettlementValidatorTest {

    private static final EntityLink<RootEntity> LOSS_LINK = new EntityLink<>(RootEntity.class, "gentity://CapLoss/DentalLoss//d0e11578-8732-4610-b4cf-0d2db9c5c285/1");

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private EntityLinkResolver entityLinkResolver;

    @InjectMocks
    private CapDentalSettlementValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        when(entityLinkResolverRegistry.getByURIScheme(anyString())).thenReturn(entityLinkResolver);
    }

    @Test
    public void testWhenLossUriNotProvided() {
        //when
        TestStreamable.create(validator.validateLossNotClosed(null))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testErrorWhenLossClosed() {
        //given
        RootEntity loss = (RootEntity) ModelInstanceFactory.createInstance(JsonUtils.loadJson(
                "json/capDentalSettlementValidator/lossClosedState.json"));
        when(entityLinkResolver.resolve(eq(LOSS_LINK), any(ReadContext.class)))
                .thenReturn(Lazy.of(loss));
        //when
        TestStreamable.create(validator.validateLossNotClosed(LOSS_LINK))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(CapDentalSettlementValidator.DentalSettlementErrorDefinition.LOSS_CLOSED.getCode()));

        //then
    }

    @Test
    public void testWhenLossOpen() {
        //given
        RootEntity loss = (RootEntity) ModelInstanceFactory.createInstance(JsonUtils.loadJson(
                "json/capDentalSettlementValidator/lossOpenState.json"));
        when(entityLinkResolver.resolve(eq(LOSS_LINK), any(ReadContext.class)))
                .thenReturn(Lazy.of(loss));
        //when
        TestStreamable.create(validator.validateLossNotClosed(LOSS_LINK))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWhenLossNotFound() {
        //given
        when(entityLinkResolver.resolve(eq(LOSS_LINK), any(ReadContext.class)))
            .thenReturn(Lazy.error(() -> new TargetEntityNotFoundException()));
        //when
        TestStreamable.create(validator.validateLossNotClosed(LOSS_LINK))
            .assertValueCount(1)
            .assertValue(
                errorHolder -> errorHolder.getCode().equals(DentalSettlementErrorDefinition.LOSS_CLOSED.getCode()));
    }
}