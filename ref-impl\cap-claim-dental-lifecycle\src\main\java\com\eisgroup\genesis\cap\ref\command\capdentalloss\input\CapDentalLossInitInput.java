/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.input;

import com.eisgroup.genesis.cap.loss.command.input.ClaimLossInitInput;
import com.eisgroup.genesis.domain.SinglePolicyAware;
import com.eisgroup.genesis.factory.modeling.types.LossDetail;
import com.google.gson.JsonObject;

/**
 * Dental Loss initiation input working as request with single policy support
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalLossInitInput extends ClaimL<PERSON>InitInput implements SinglePolicyAware {

    public CapDentalLossInitInput(JsonObject original) {
        super(original);
    }

    public CapDentalLossInitInput(LossDetail lossDetail, String policyId) {
        super(lossDetail);
        setString(POLICY_ID, policyId);
    }

    @Override
    public String getPolicyId() {
        return getString(POLICY_ID);
    }
}
