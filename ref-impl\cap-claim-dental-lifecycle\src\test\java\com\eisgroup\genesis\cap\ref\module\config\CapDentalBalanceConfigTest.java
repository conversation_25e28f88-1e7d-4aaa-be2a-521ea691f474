package com.eisgroup.genesis.cap.ref.module.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.CapDentalBalanceCalculator;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalBalanceConfigTest {

    @InjectMocks
    private CapDentalBalanceConfig config;

    @Mock
    private ModeledTransformationService modeledTransformationService;


    @Test
    public void testReturnCapDentalBalanceCalculator() {
        CapDentalBalanceCalculator result = config.capDentalBalanceCalculator(modeledTransformationService);
        assertThat(result, notNullValue());
    }

}
