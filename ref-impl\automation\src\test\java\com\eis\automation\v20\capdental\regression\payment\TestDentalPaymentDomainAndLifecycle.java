/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.payment;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentAllocationModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.payment.service.ICapDentalPaymentService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.eis.automation.v20.platform.entity.activity.facade.IActivity;
import com.eis.automation.v20.platform.entity.activity.facade.impl.modeling.ActivityModel;
import com.eis.automation.v20.platform.utils.assertion.bam.BamModel;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.getCurrentDateTime;
import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.BamConstant.BamMessage.*;
import static com.eis.automation.v20.cap.constant.BamConstant.BamStatus.FINISHED;
import static com.eis.automation.v20.cap.constant.CapConstant.CapPaymentStates.*;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_PAYMENT;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.PlatformPredicates.bam;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalPaymentDomainAndLifecycle extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private ICapDentalPaymentService capDentalPayment;
    @Autowired
    private IClaimSearchService claimSearchService;
    @Autowired
    private IActivity activity;

    private IndividualCustomerModel individualCustomer;
    private ICapDentalLossModel submitDentalClaim;
    private ICapDentalSettlementModel initSettlement;

    @BeforeClass(groups = {REGRESSION}, alwaysRun = true)
    public void createPreconditions() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));
        // Dental Claim
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        // Dental Settlement
        initSettlement = claimSearchService.searchDentalSettlement(submitDentalClaim, 1).get(0);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156332", component = CAP_DENTAL_PAYMENT)
    public void testDentalPaymentDomain() {
        ICapDentalPaymentModel capDentalPaymentModel = capDentalPayment.createDentalPaymentModel(individualCustomer, submitDentalClaim, initSettlement);
        ICapDentalPaymentModel initDentalPayment = capDentalPayment.initDentalPayment(capDentalPaymentModel);
        //ICapDentalPaymentModel loadDentalPayment = capDentalPayment.loadDentalPayment(initDentalPayment);
        assertSoftly(softly -> {
            softly.assertThat(initDentalPayment.getState()).isEqualTo(APPROVED);
            softly.assertThat(initDentalPayment.getPaymentNumber()).startsWith("P");
            softly.assertThat(Integer.valueOf(initDentalPayment.getPaymentNumber().substring(1))).isGreaterThanOrEqualTo(1);
            softly.assertThat(initDentalPayment.getCreationDate()).isEqualToIgnoringHours(getCurrentDateTime());
            softly.assertThat(initDentalPayment.getOriginSource()).isEqualTo(capDentalPaymentModel.getOriginSource());
            softly.assertThat(initDentalPayment.getPaymentDetails().getPayeeDetails().getPayee())
                    .isEqualTo(capDentalPaymentModel.getEntity().getPayeeDetails().getPayee());
            ICapDentalPaymentAllocationModel initDentalPaymentAllocation = (ICapDentalPaymentAllocationModel)
                    initDentalPayment.getPaymentDetails().getPaymentAllocations().get(0);
            ICapDentalPaymentAllocationModel dentalPaymentAllocationModel = (ICapDentalPaymentAllocationModel)
                    capDentalPaymentModel.getEntity().getPaymentAllocations().get(0);
            softly.assertThat(initDentalPaymentAllocation.getAllocationSource()).isEqualTo(dentalPaymentAllocationModel.getAllocationSource());
            softly.assertThat(initDentalPaymentAllocation.getAllocationNetAmount())
                    .isEqualTo(dentalPaymentAllocationModel.getAllocationNetAmount());
            softly.assertThat(initDentalPaymentAllocation.getAllocationPayableItem().getClaimSource())
                    .isEqualTo(dentalPaymentAllocationModel.getAllocationPayableItem().getClaimSource());
            softly.assertThat(initDentalPaymentAllocation.getAllocationDentalDetails().getTransactionTypeCd())
                    .isEqualTo(dentalPaymentAllocationModel.getAllocationDentalDetails().getTransactionTypeCd());
        });
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156400", component = CAP_DENTAL_PAYMENT)
    public void testDentalPaymentLifecycle() {
        // Payment lifecycle changes GENESIS-129704
        // Test steps updated according to TC GENESIS-192969
        ICapDentalPaymentModel initDentalPayment = capDentalPayment.initDentalPayment(individualCustomer, submitDentalClaim, initSettlement);
        assertThat(initDentalPayment.getState()).isEqualTo(APPROVED);
        // Step 2 - Command Approved was removed due to GENESIS-129704
        // Step 3
        ICapDentalPaymentModel requestIssueDentalPayment = capDentalPayment.requestIssueDentalPayment(initDentalPayment);
        assertThat(requestIssueDentalPayment.getState()).isEqualTo(ISSUE_REQUESTED);
        ICapDentalPaymentModel issueDentalPayment = capDentalPayment.issueDentalPayment(requestIssueDentalPayment);
        assertThat(issueDentalPayment.getState()).isEqualTo(ISSUED);
        // Step 4
        ICapDentalPaymentModel clearDentalPayment = capDentalPayment.clearDentalPayment(issueDentalPayment);
        assertThat(clearDentalPayment.getState()).isEqualTo(CLEARED);
        // BAM verification
        List<ActivityModel> paymentActivityList = activity.fetch().performNoSuccessAnalysis(b -> b
                .setModel(clearDentalPayment), bam(CAP_PAYMENT_CLEAR)).safeGetResponseBody();
        verifyBamAttributes(paymentActivityList, CAP_PAYMENT_INIT, FINISHED);
        verifyBamAttributes(paymentActivityList, CAP_PAYMENT_REQUEST_ISSUE, FINISHED);
        verifyBamAttributes(paymentActivityList, CAP_PAYMENT_ISSUE, FINISHED);
        verifyBamAttributes(paymentActivityList, CAP_PAYMENT_CLEAR, FINISHED);
    }

    private void verifyBamAttributes(List<ActivityModel> activityList, String bamMessage, String bamStatus) {
        assertThat(BamModel.of(activityList)).hasMessage(bamMessage).withStatus(bamStatus);
    }
}