// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_DENTALLOSSROOTIDENTIFIERTRANSFORMATIONMODEL } from "./kraken_model_tree_DentalLossRootIdentifierTransformationModel"

let name = "DentalLossRootIdentifierTransformationModel"

let namespace = "DentalLossRootIdentifierTransformationModel"

let currencyCd = "USD"

export type DentalLossRootIdentifierTransformationModelEntryPointName = never

let entryPointNames = [
] as DentalLossRootIdentifierTransformationModelEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_DENTALLOSSROOTIDENTIFIERTRANSFORMATIONMODEL as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_DentalLossRootIdentifierTransformationModel = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
