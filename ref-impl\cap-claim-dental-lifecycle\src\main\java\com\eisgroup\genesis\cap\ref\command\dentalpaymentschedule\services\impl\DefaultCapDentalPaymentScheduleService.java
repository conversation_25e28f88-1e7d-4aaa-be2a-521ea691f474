/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.services.impl;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.services.CapPaymentScheduleService;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentTemplate;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

/**
 * Default implementation of {@link CapPaymentScheduleService} which use OpenL rules to generate payment schedule
 *
 * <AUTHOR>
 * @since 22.5
 */
public class DefaultCapDentalPaymentScheduleService implements CapPaymentScheduleService {

    private final ModeledTransformationService transformationService;
    private final ModelRepository<TransformationModel> transformationRepository;
    private final CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator;
    private final String scheduleTransformationName;

    public DefaultCapDentalPaymentScheduleService(ModeledTransformationService transformationService,
                                                  ModelRepository<TransformationModel> transformationRepository,
                                                  CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator,
                                                  String scheduleTransformationName) {
        this.transformationService = transformationService;
        this.transformationRepository = transformationRepository;
        this.capDentalPaymentScheduleValidator = capDentalPaymentScheduleValidator;
        this.scheduleTransformationName = scheduleTransformationName;
    }


    @Override
    public Lazy<CapDentalPaymentScheduleEntity> createSchedule(CapPaymentTemplate template) {

        var schedule = (CapDentalPaymentScheduleEntity) transformationService.transform(transformationRepository.getActiveModel(scheduleTransformationName), template);
        return capDentalPaymentScheduleValidator.validateCalculatedSchedule(schedule);
    }

}
