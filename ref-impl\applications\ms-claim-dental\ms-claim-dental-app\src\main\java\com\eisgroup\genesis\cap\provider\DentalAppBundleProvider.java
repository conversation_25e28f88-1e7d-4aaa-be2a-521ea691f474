/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.provider;

import java.util.ResourceBundle;


public class DentalAppBundleProvider {

    public static ResourceBundle getErrorMessagesBundle() {
        return ResourceBundle.getBundle("dental.error-messages.messages");
    }
}