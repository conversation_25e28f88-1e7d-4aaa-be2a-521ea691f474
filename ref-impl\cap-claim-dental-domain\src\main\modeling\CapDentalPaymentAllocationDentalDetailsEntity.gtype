@Description("Dental allocation details.")
BaseType CapDentalPaymentAllocationDentalDetailsEntity {

    //EISDEVTS-40079
    @Description("Allocation acumulator details.")
    Attr accumulatorDetails: *CapDentalPaymentAllocationAccumulatorDetailsEntity

    //EISDEVTS-40079
    @Description("URI to the patient.")
    ExtLink patient: RootEntity

    //EISDEVTS-45186
    @Description("Dental claim transaction type.")
    Attr transactionTypeCd: String
}