/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment.input;

import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalProcessPaymentLifecycleHandler;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.google.gson.JsonObject;

/**
 * {@link CapDentalProcessPaymentLifecycleHandler} input
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalProcessPaymentLifecycleInput extends AbstractJsonEntity {

    private static final String PAYMENT_URI = "paymentUri";
    private static final String PAYMENT_METHOD_TYPE = "paymentMethodType";
    private static final String PAYMENT_EVENT = "paymentEvent";

    public CapDentalProcessPaymentLifecycleInput(JsonObject original) {
        super(original);
    }

    public CapDentalProcessPaymentLifecycleInput(String paymentUri, String paymentMethodType, String paymentEvent) {
        super(new JsonObject());
        setString(PAYMENT_URI, paymentUri);
        setString(PAYMENT_METHOD_TYPE, paymentMethodType);
        setString(PAYMENT_EVENT, paymentEvent);
    }

    public String getPaymentUri() {
        return getString(PAYMENT_URI);
    }

    public String getPaymentMethodType() {
        return getString(PAYMENT_METHOD_TYPE);
    }

    public String getPaymentEvent() {
        return getString(PAYMENT_EVENT);
    }
}
