@Description("An object which extends payment allocations details.")
BaseType CapDentalPaymentAllocationEntity extends CapPaymentAllocation {

    //EISDEVTS-45186
    @Description("Payment allocation addition split results")
    @Exclude
    Attr additionSplitResults: *CapPaymentAllocationAdditionSplitResult

    //EISDEVTS-45186
    @Description("Dental allocation details.")
    Attr allocationDentalDetails: CapDentalPaymentAllocationDentalDetailsEntity

    //EISDEVTS-45186
    @Description("Allocation claim details.")
    Attr allocationLossInfo: CapDentalPaymentAllocationLossInfoEntity

    //EISDEVTS-45186
    @Description("Details of what is paid for.")
    Attr allocationPayableItem: CapDentalPaymentAllocationPayableItemEntity

    //EISDEVTS-45186
    @Description("Payment allocation reduction split results")
    @Exclude
    Attr reductionSplitResults: *CapPaymentAllocationReductionSplitResult

    //EISDEVTS-45186
    @Description("Payment allocation tax split results")
    @Exclude
    Attr taxSplitResults: *CapPaymentAllocationTaxSplitResult
}