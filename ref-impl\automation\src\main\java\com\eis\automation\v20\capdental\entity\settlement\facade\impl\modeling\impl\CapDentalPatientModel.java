/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalAccumulatorModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalPatientModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementInfoProcedureModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPatientModel extends TypeModel implements ICapDentalPatientModel {

    @JsonProperty("isDisabledChild")
    private Boolean isDisabledChild;
    private List<String> disabilities;
    @JsonProperty("isDependentChild")
    private Boolean isDependentChild;
    private String patientID;
    @JsonProperty("isFullTimeStudent")
    private Boolean isFullTimeStudent;
    private List<ICapDentalSettlementInfoProcedureModel> historyProcedures;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate dateOfBirth;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate birthDate;
    private List<ICapDentalAccumulatorModel> accumulators;

    public Boolean getIsDisabledChild() {
        return isDisabledChild;
    }

    public void setIsDisabledChild(Boolean isDisabledChild) {
        this.isDisabledChild = isDisabledChild;
    }

    public List<String> getDisabilities() {
        return disabilities;
    }

    public void setDisabilities(List<String> disabilities) {
        this.disabilities = disabilities;
    }

    public Boolean getIsDependentChild() {
        return isDependentChild;
    }

    public void setDependentChild(Boolean isDependentChild) {
        this.isDependentChild = isDependentChild;
    }

    public String getPatientID() {
        return patientID;
    }

    public void setPatientID(String patientID) {
        this.patientID = patientID;
    }

    public Boolean getIsFullTimeStudent() {
        return isFullTimeStudent;
    }

    public void setIsFullTimeStudent(Boolean isFullTimeStudent) {
        this.isFullTimeStudent = isFullTimeStudent;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalSettlementInfoProcedureModel.class)
    public List<ICapDentalSettlementInfoProcedureModel> getHistoryProcedures() {
        return historyProcedures;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalSettlementInfoProcedureModel.class)
    public void setHistoryProcedures(List<ICapDentalSettlementInfoProcedureModel> historyProcedures) {
        this.historyProcedures = historyProcedures;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalAccumulatorModel.class)
    public List<ICapDentalAccumulatorModel> getAccumulators() {
        return accumulators;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalAccumulatorModel.class)
    public void setAccumulators(List<ICapDentalAccumulatorModel> accumulators) {
        this.accumulators = accumulators;
    }
}
