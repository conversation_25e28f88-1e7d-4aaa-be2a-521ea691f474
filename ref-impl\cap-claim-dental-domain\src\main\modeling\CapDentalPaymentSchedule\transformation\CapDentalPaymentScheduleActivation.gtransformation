@Passthrough
@KeyStrategy("PASSTHROUGH")
Transformation CapDentalPaymentScheduleActivation {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity
    }

    Attr paymentScheduleActivationResult is mapResults(schedule.paymentScheduleActivationResult, schedule)

    @Passthrough("paymentScheduleActivationResult")
    Producer mapResults(paymentScheduleActivationResult, schedule) {
        Var request is Transform("CapDentalPaymentScheduleActivationInput", schedule)
        Var response is ExecuteRules("claim-dental-financial", "_api_paymentSchedule_activation", request)

        Attr activationStatus is response.activationStatus
    }

}