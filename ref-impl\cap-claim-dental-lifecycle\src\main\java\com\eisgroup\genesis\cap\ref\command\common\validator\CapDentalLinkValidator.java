/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.common.validator;

import java.util.Collection;
import java.util.Collections;

import org.apache.commons.collections4.CollectionUtils;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.exception.JsonValueFormatError;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

/**
 * Class for validating {@link EntityLink}s.
 *
 * <AUTHOR>
 * @since 22.11
 */
public class CapDentalLinkValidator {

    private final EntityLinkResolverRegistry entityLinkResolverRegistry;

    public CapDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
    }

    public Streamable<ErrorHolder> validateLink(EntityLink<RootEntity> link, String modelType, ErrorHolder error) {
        return validateLink(link, Collections.singletonList(modelType), error);
    }

    public Streamable<ErrorHolder> validateLink(EntityLink<RootEntity> link, Collection<String> allowedModelTypes, ErrorHolder error) {
        if (link == null) {
            return Streamable.empty();
        }

        try {
            if (CollectionUtils.isNotEmpty(allowedModelTypes) && !allowedModelTypes.contains(new FactoryLink(link).getTypeName())) {
                return Streamable.of(error);
            }
        } catch (JsonValueFormatError e) {
            return Streamable.of(error);
        }

        return resolveEntity(link)
            .flatMap(entity -> Streamable.<ErrorHolder>empty())
            .onErrorResume(e -> Streamable.of(error));
    }

    protected Streamable<RootEntity> resolveEntity(EntityLink<RootEntity> link) {
        return Streamable.of(link)
            .map(entityLink -> entityLinkResolverRegistry.getByURIScheme(entityLink.getSchema()))
            .map(resolver -> (EntityLinkResolver<RootEntity>) resolver)
            .flatMap(resolver -> resolver.resolve(link, ReadContext.empty()));
    }
}
