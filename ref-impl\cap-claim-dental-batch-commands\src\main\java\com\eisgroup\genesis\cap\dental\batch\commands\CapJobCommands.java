/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.dental.batch.commands;

/**
 * Contains all cap dental job command names
 *
 * <AUTHOR>
 * @since 22.9
 */
public class CapJobCommands {

    public static final String PAYMENT_GENERATION_JOB = "paymentGenerationJob";
    public static final String PAYMENT_ISSUE_JOB = "paymentIssueJob";

    private CapJobCommands() {
        //only constants
    }
}
