/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S.
 * copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or
 * incorporated into any other media without EIS Group prior written consent.
 */

import {describe, expect, it, vi} from 'vitest'

import {CapDentalPaymentDefinition} from '@eisgroup/dental-models'

import {validateIssuedPayment} from '../../../src/utils/common/ValidationUtils'

import CapDentalPaymentEntity = CapDentalPaymentDefinition.CapDentalPaymentEntity

vi.mock('@eisgroup/i18n', () => ({
    LocalizationUtils: {
        translate: vi.fn((key: string) => {
            if (key === 'dental-product:months-of-treatment-validate') {
                return 'Month of Treatments cannot be less than the number of payment(s) already issued.'
            }
            return key
        })
    }
}))

describe('aaa validation function', () => {
    const createMockPayment = (state: string): CapDentalPaymentEntity =>
        ({
            state,
            paymentNumber: 'TEST-001',
            paymentNetAmount: {amount: 100, currency: 'USD'}
        } as CapDentalPaymentEntity)

    it('should return undefined when no payments have Issued state', () => {
        const paymentsData = [
            createMockPayment('Approved'),
            createMockPayment('Pending'),
            createMockPayment('Canceled')
        ]

        const validator = validateIssuedPayment(paymentsData)
        const result = validator(5)
        expect(result).toBeUndefined()
    })

    it('should return undefined when value is greater than or equal to issued payments count', () => {
        const paymentsData = [
            createMockPayment('Issued'),
            createMockPayment('Issued'),
            createMockPayment('Approved'),
            createMockPayment('Pending')
        ]

        const validator = validateIssuedPayment(paymentsData)

        const result1 = validator(3)
        expect(result1).toBeUndefined()

        const result2 = validator(2)
        expect(result2).toBeUndefined()
    })

    it('should return error message when value is less than issued payments count', () => {
        const paymentsData = [
            createMockPayment('Issued'),
            createMockPayment('Issued'),
            createMockPayment('Issued'),
            createMockPayment('Approved')
        ]

        const validator = validateIssuedPayment(paymentsData)

        const result = validator(2)
        expect(result).toBe('Month of Treatments cannot be less than the number of payment(s) already issued.')
    })

    it('should handle empty payments array', () => {
        const validator = validateIssuedPayment([])
        const result = validator(5)
        expect(result).toBeUndefined()
    })

    it('should handle mixed payment states correctly', () => {
        const paymentsData = [
            createMockPayment('Issued'),
            createMockPayment('Approved'),
            createMockPayment('Issued'),
            createMockPayment('Canceled'),
            createMockPayment('Pending'),
            createMockPayment('Issued')
        ]

        const validator = validateIssuedPayment(paymentsData)
        const result1 = validator(2)
        expect(result1).toBe('Month of Treatments cannot be less than the number of payment(s) already issued.')

        const result2 = validator(3)
        expect(result2).toBeUndefined()

        const result3 = validator(5)
        expect(result3).toBeUndefined()
    })
})
