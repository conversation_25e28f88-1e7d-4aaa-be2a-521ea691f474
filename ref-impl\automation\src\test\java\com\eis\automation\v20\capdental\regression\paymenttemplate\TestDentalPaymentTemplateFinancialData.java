/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.paymenttemplate;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentAllocationTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.service.ICapDentalPaymentTemplateService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapPaymentTemplateState.OPEN;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_PAYMENT_TEMPLATE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalAllocationLobCd.DENTAL;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSettlementProposal.PAY;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalPaymentTemplateFinancialData extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private ICapDentalPaymentTemplateService capDentalPaymentTemplate;
    @Autowired
    private IClaimSearchService claimSearch;
    private IndividualCustomerModel individualCustomer;
    private ICapDentalLossModel submitDentalClaim;
    private ICapDentalSettlementModel approvedSettlement;

    @BeforeClass(groups = {REGRESSION}, alwaysRun = true)
    public void createPreconditions() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));
        // Dental Claim
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        // Dental Settlement
        approvedSettlement = claimSearch.searchDentalSettlement(submitDentalClaim, 1).get(0);
        assertThat(approvedSettlement.getState()).isEqualTo(APPROVED);
        assertThat(approvedSettlement.getSettlementResult().getProposal()).isEqualTo(PAY);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-150669", component = CAP_DENTAL_PAYMENT_TEMPLATE)
    public void testDentalPaymentTemplateFinancialData() {
        // Step 1
        ICapDentalPaymentTemplateBuildModel buildModel = capDentalPaymentTemplate.createDentalPaymentTemplateBuildModel(
                submitDentalClaim, approvedSettlement);
        ICapDentalPaymentTemplateModel dentalPaymentTemplateModel = capDentalPaymentTemplate.financialData(buildModel);

        ICapDentalPaymentAllocationTemplateModel allocationTemplateModel = (ICapDentalPaymentAllocationTemplateModel)
                dentalPaymentTemplateModel.getPaymentDetailsTemplate().getPaymentAllocationTemplates().get(0);
        assertSoftly(softly -> {
            softly.assertThat(dentalPaymentTemplateModel.getOriginSource()).isEqualTo(submitDentalClaim.getGentityUri().getUriModel());
            softly.assertThat(allocationTemplateModel.getAllocationSource()).isEqualTo(approvedSettlement.getGentityUri().getUriModel());
            softly.assertThat(allocationTemplateModel.getAllocationLobCd()).isEqualTo(DENTAL);
            softly.assertThat(allocationTemplateModel.getAllocationPayeeDetails().getPayee())
                    .isEqualTo(submitDentalClaim.getLossDetail().getClaimData().getProviderRole().getProviderLink());
            softly.assertThat(allocationTemplateModel.getAllocationPayeeDetails().getPayeeTypeCd())
                    .isEqualTo(submitDentalClaim.getLossDetail().getClaimData().getPayeeType());
            softly.assertThat(allocationTemplateModel.getAllocationDentalDetails().getTransactionTypeCd())
                    .isEqualTo(approvedSettlement.getSettlementLossInfo().getClaimData().getTransactionType());
        });
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-150747", component = CAP_DENTAL_PAYMENT_TEMPLATE)
    public void testDentalPaymentTemplateInit() {
        ICapDentalPaymentTemplateModel paymentTemplateModel = capDentalPaymentTemplate
                .createDentalPaymentTemplateModel(individualCustomer, submitDentalClaim, approvedSettlement);
        ICapDentalPaymentTemplateModel initDentalPaymentTemplate = capDentalPaymentTemplate.initDentalPaymentTemplate(paymentTemplateModel);
        assertThat(initDentalPaymentTemplate.getState()).isEqualTo(OPEN);
    }
}