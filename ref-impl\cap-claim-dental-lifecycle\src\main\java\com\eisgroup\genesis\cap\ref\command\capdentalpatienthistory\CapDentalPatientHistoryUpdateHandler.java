/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory;

import java.util.Optional;
import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.input.CapDentalPatientHistoryUpdateInput;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator.CapDentalPatientHistoryUpdateValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryDataEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.services.AccessTrackInfoService;
import com.eisgroup.genesis.model.ModelResolver;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

import static com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryCommands.UPDATE_PATIENT_HISTORY;

/**
 * Handler for dental patient history update.
 *
 * <AUTHOR>
 * @since 22.6
 */
@Modifying
public class CapDentalPatientHistoryUpdateHandler implements ProductCommandHandler<CapDentalPatientHistoryUpdateInput, CapDentalPatientHistoryEntity> {

    @Autowired
    private AccessTrackInfoService accessTrackInfoService;

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private CapDentalPatientHistoryRepository capDentalPatientHistoryRepository;

    @Autowired
    private CapDentalPatientHistoryUpdateValidator capDentalPatientHistoryUpdateValidator;

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity load(@Nonnull CapDentalPatientHistoryUpdateInput capDentalPatientHistoryEntity) {
        return capDentalPatientHistoryRepository.load(capDentalPatientHistoryEntity.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPatientHistoryUpdateInput request,
                                                 @Nonnull CapDentalPatientHistoryEntity loadedEntity) {
        return Streamable.concat(capDentalPatientHistoryUpdateValidator.validatePatientUri(request, loadedEntity),
            capValidatorRegistry.validateRequest(request));
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity execute(@Nonnull CapDentalPatientHistoryUpdateInput inputEntity, @Nonnull CapDentalPatientHistoryEntity entity) {
        entity.setPatientHistoryData(inputEntity.getEntity().getPatientHistoryData());
//        return Lazy.from(entity)
//                .map(historyEntity -> {
//                    Optional.ofNullable(historyEntity.getPatientHistoryData())
//                            .map(CapDentalPatientHistoryDataEntity::getAccessTrackInfo)
//                            .ifPresent(accessTrackInfo -> accessTrackInfoService.update(accessTrackInfo));
//                    return historyEntity;
//                });
        if(entity.getPatientHistoryData() !=null){
            accessTrackInfoService.update(entity.getPatientHistoryData().getAccessTrackInfo());
        }
        return entity;
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity save(@Nonnull CapDentalPatientHistoryUpdateInput capDentalPatientHistoryEntity, @Nonnull CapDentalPatientHistoryEntity entity) {
        return capDentalPatientHistoryRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return UPDATE_PATIENT_HISTORY;
    }
}
