/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.cap.loss.command.input.ClaimLossCloseInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;

import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;

import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.ClaimLossCloseInputValidator.ClaimLossCloseInputErrorDefinition.REASON_DESCRIPTION_PROVIDED;

public class ClaimLossCloseInputValidatorTest {

    @InjectMocks
    private ClaimLossCloseInputValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testWithReasonCd() {
        //given
        ClaimLossCloseInput withReasonCd = new ClaimLossCloseInput(JsonUtils.load(
                "requestSamples/closeReopenWithReasonCd.json"));

        //when
        TestStreamable.create(validator.validate(withReasonCd))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWithReasonAttributes() {
        //given
        ClaimLossCloseInput withReasonsRequest = new ClaimLossCloseInput(JsonUtils.load(
                "requestSamples/closeReopenWithReasonCdAndDescription.json"));

        //when
        TestStreamable.create(validator.validate(withReasonsRequest))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(REASON_DESCRIPTION_PROVIDED.getCode()));
    }

}