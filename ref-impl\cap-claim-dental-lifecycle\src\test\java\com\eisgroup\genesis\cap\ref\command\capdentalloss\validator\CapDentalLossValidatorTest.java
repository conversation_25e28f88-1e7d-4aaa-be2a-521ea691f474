/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossInitInput;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PATIENT_REGISTRY_ID_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PROVIDER_LINK_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator.CapDentalLossValidationErrorDefinition.PROVIDER_REGISTRY_ID_LINK_INCORRECT;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossValidatorTest {

    @InjectMocks
    private CapDentalLossValidator validator;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;

    private CapDentalLossInitInput input = new CapDentalLossInitInput(JsonUtils.load(
            "requestSamples/lossInitInput.json"));

    @Test
    public void shouldValidateProvider() {
        //given
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.of(PROVIDER_REGISTRY_ID_LINK_INCORRECT.builder().build()));
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerLink")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "patientRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "policyholderRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(validator.validateLinks(input.getEntity()))
                .assertNoErrors()
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(1));
        assertThat(errorHolders.get(0).getCode(), Matchers.equalTo(PROVIDER_REGISTRY_ID_LINK_INCORRECT.getCode()));
    }

    @Test
    public void shouldValidateProviderLink() {
        //given
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerLink")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.of(PROVIDER_LINK_INCORRECT.builder().build()));
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "patientRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "policyholderRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(validator.validateLinks(input.getEntity()))
                .assertNoErrors()
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(1));
        assertThat(errorHolders.get(0).getCode(), Matchers.equalTo(PROVIDER_LINK_INCORRECT.getCode()));
    }

    @Test
    public void shouldValidatePatient() {
        //given
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerLink")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "patientRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.of(PATIENT_REGISTRY_ID_LINK_INCORRECT.builder().build()));
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "policyholderRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(validator.validateLinks(input.getEntity()))
                .assertNoErrors()
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(1));
        assertThat(errorHolders.get(0).getCode(), Matchers.equalTo(PATIENT_REGISTRY_ID_LINK_INCORRECT.getCode()));
    }

    @Test
    public void shouldValidatePolicyholder() {
        //given
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "providerLink")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "patientRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.empty());
        when(capDentalLinkValidator.validateLink(eq(new EntityLink<>(RootEntity.class, "policyholderRegistryId")), anyString(), any(ErrorHolder.class)))
                .thenReturn(Streamable.of(POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT.builder().build()));

        //when
        List<ErrorHolder> errorHolders = TestStreamable.create(validator.validateLinks(input.getEntity()))
                .assertNoErrors()
                .values();

        //then
        assertThat(errorHolders, Matchers.hasSize(1));
        assertThat(errorHolders.get(0).getCode(), Matchers.equalTo(POLICY_HOLDER_REGISTRY_ID_LINK_INCORRECT.getCode()));
    }
}
