/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalPaymentCancelInput;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentMessageEntity;
import com.eisgroup.genesis.factory.modeling.types.builder.CapPaymentBuilder;
import com.eisgroup.genesis.time.TimeUtils;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.List;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.Mockito.when;

class CapDentalPaymentCancelHandlerTest {

    @InjectMocks
    private CapDentalPaymentCancelHandler handler;

    private MockedStatic<TimeUtils> mockedTimeUtils;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mockedTimeUtils = Mockito.mockStatic(TimeUtils.class);
    }

    @AfterEach
    public void teardownMocks() {
        mockedTimeUtils.closeOnDemand();
    }

    @Test
    void testHandlerExecute() {
        //given
        LocalDateTime now = LocalDateTime.of(2023, 1, 1, 12, 30);
        CapDentalPaymentMessageEntity message = (CapDentalPaymentMessageEntity) ModelInstanceFactory.createInstance(
                "CapDentalPaymentDefinition", "1", "CapDentalPaymentMessageEntity");
        message.setCode("CD1");
        CapDentalPaymentCancelInput request = createRequest(List.of(message));
        CapDentalPaymentEntity payment = (CapDentalPaymentEntity) CapPaymentBuilder.createRoot("CapDentalPaymentDefinition", "1", PaymentVariations.PAYMENT).build();
        when(TimeUtils.currentTime()).thenAnswer(ans -> now);

        //when
        CapDentalPaymentEntity afterExecutePayment = handler.execute(request, payment);

        assertThat(afterExecutePayment.getCancelationDate(), equalTo(now));
        assertThat(afterExecutePayment.getMessages().size(), equalTo(1));
        assertThat(afterExecutePayment.getMessages().iterator().next().getCode(), equalTo("CD1"));
    }

    private CapDentalPaymentCancelInput createRequest(List<CapDentalPaymentMessageEntity> messages) {
        JsonObject request = new JsonObject();
        JsonArray messageArray = messages.stream().reduce(new JsonArray(), (a, b) -> {
            a.add(b.toJson());
            return a;
        }, (a, b) -> a);
        request.add("messages", messageArray);
        return new CapDentalPaymentCancelInput(request);
    }
}
