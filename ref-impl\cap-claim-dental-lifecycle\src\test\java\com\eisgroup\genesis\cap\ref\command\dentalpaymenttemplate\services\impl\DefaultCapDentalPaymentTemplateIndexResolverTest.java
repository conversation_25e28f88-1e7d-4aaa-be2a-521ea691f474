package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.services.impl;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplateindex.CapDentalPaymentTemplateIdxEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.parameter.TransformationInput;
import com.eisgroup.genesis.transformation.parameter.TransformationOutput;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class DefaultCapDentalPaymentTemplateIndexResolverTest {

    private static final EntityLink<RootEntity> ORIGIN_SOURCE = new EntityLink<>(RootEntity.class,
            "gentity://CapLoss/DentalLoss//d0e11578-8732-4610-b4cf-0d2db9c5c285/1");
    private static final String ACTIVE_TEMPLATE_LINK =
            "gentity://CapPaymentTemplate/CapDentalPaymentTemplate/cf836f14-ba9e-4e7d-aa9f-f81b321a17f1/1";

    @Mock
    private ModeledTransformationService modeledTransformationService;

    private DefaultCapDentalPaymentTemplateIndexResolver resolver;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        resolver = new DefaultCapDentalPaymentTemplateIndexResolver("CapLoadDentalPaymentTemplatesIndex",
                modeledTransformationService, ModelRepositoryFactory.getRepositoryFor(TransformationModel.class));
    }

    @Test
    public void testResolveActiveIndex() {
        //given
        TransformationOutput<CapDentalPaymentTemplateIdxEntity> transformationOutput = mock(TransformationOutput.class);
        when(transformationOutput.asCollection()).thenReturn(loadIndexEntities());
        when(modeledTransformationService.<CapDentalPaymentTemplateIdxEntity>transformAll(any(), any(TransformationInput.class)))
                .thenReturn(Streamable.of(transformationOutput));

        //when

        TestStreamable.create(resolver.resolvePaymentTemplateIndex(ORIGIN_SOURCE, List.of("Active")))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(val -> val.getPaymentTemplate().equals(ACTIVE_TEMPLATE_LINK));
    }

    private Collection<CapDentalPaymentTemplateIdxEntity> loadIndexEntities() {
        JsonArray indexJsonArray = JsonUtils.loadJson(
                "json/index/dentalPaymentTemplateIndexEntities.json");
        return StreamSupport.stream(indexJsonArray.spliterator(), false)
                .map(JsonElement::getAsJsonObject)
                .map(json -> (CapDentalPaymentTemplateIdxEntity) ModelInstanceFactory.createInstance(json))
                .collect(Collectors.toList());
    }
}