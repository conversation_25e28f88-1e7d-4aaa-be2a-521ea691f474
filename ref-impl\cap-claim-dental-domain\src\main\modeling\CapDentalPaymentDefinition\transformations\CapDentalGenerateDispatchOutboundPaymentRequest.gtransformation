@CapEndpoint("generateDispatchOutboundPaymentRequest")
@CapVariationFilter("payment")
Transformation CapDentalGenerateDispatchOutboundPaymentRequest {
    Input {
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        ?
    }

    Var currentTime is Now()
    Var currentDate is AsDate(currentTime)
    Var dentalLoss is ExtLink(payment.originSource)

    Var payeeType is dentalLoss.lossDetail.claimData.payeeType
    Var payee is Ternary(Equals(payeeType, "AlternatePayee"), ExtLink(AsExtLink(dentalLoss.lossDetail.claimData.alternatePayeeRole,registryId)),
                    Ternary(Equals(payeeType, "Provider"), ExtLink(AsExtLink(dentalLoss.lossDetail.claimData.providerRole.registryId)),
                        Ternary(Equals(payeeType, "PrimaryInsured"), ExtLink(AsExtLink(dentalLoss.lossDetail.claimData.policyholderRole.registryId)), Null())))

    Var foundPaymentMethods is payee.paymentMethods
    Var foundPaymentMethodsCount is Ternary(Equals(foundPaymentMethods, Null()),0, Count(foundPaymentMethods))
    Var paymentMethodNotFound is Equals(foundPaymentMethodsCount, 0)
    Var activePaymentMethod is Ternary(paymentMethodNotFound == false, First(foundPaymentMethods[filterActiveEFTCHECK]), Null())
    Var paymentMethodExpired is Equals(activePaymentMethod, Null()) && Equals(paymentMethodNotFound, false)

    Var foundCheckAddresses is payee.communicationInfo.addresses
    Var foundCheckAddressesCount is Ternary(Equals(foundCheckAddresses, Null()),0, Count(foundCheckAddresses))
    Var checkAddressNotFound is Equals(foundCheckAddressesCount, 0)
    Var activeCheckAddress is Ternary(checkAddressNotFound == false, First(foundCheckAddresses[filterActiveAddresses]), Null())
    Var checkAddressExpired is Equals(activeCheckAddress, Null()) && Equals(checkAddressNotFound, false)

    Var latestPayee is Ternary(payee._type == "IndividualCustomer" || payee._type == "OrganizationCustomer",
                               ExtLink(ToExtLink(payee, "geroot")),
                               Null())

    Var provider is dentalLoss.lossDetail.claimData.providerRole.providerLink
    Var loadedProvider is SafeInvoke(provider, DoOnError(ExtLink(AsExtLink(provider)), Null()))

    Var paymentMethod is constructCustomerPaymentMethod(latestPayee).selectedMethod
    Var requestVar is constructRequest(payment, latestPayee, paymentMethod, activeCheckAddress)
    Var messagesVar is FlatMap(
                               Ternary(Equals(latestPayee, Null()), createNoPayeeMessage(payee), Null()),
                               Ternary(Equals(requestVar.paymentMethodType, Null()), createInvalidPaymentMethodTypeMessage(), Null()),
                               Ternary(paymentMethodNotFound == true, createPaymentMethodNotFoundMessage(), Null()),
                               Ternary(paymentMethodExpired == true,  createExpiredPaymentMethodMessage(),Null()),
                               Ternary(Equals(requestVar.paymentMethodType, "CHECK") && checkAddressNotFound == true, createCheckAddressNotFoundMessage(), Null()),
                               Ternary(Equals(requestVar.paymentMethodType, "CHECK") && checkAddressExpired == true,  createExpiredCheckAddressMessage(),Null()),
                               Ternary(Equals(provider, Null()), Null(),
                                   Ternary(Equals(loadedProvider, Null()), createInvalidProviderLinkMessage(provider._uri),
                                       Ternary(loadedProvider.state == "active", Null(), createInvalidProviderStateMessage()))))

    Attr messages is messagesVar
    Attr request is requestVar
    Attr validationResult is Ternary(Count(messagesVar) > 0, "Invalid", "Valid")

    Producer constructCustomerPaymentMethod(latestPayee) {
        Var paymentMethods is latestPayee.paymentMethods[filterActiveEFTCHECK]<effectiveDateDesc>
        Var preferredMethods is paymentMethods[filterPreferred]
        Attr selectedMethod is Ternary(Count(preferredMethods) == 0, First(paymentMethods), First(preferredMethods))
    }

    Producer constructRequest(payment, latestPayee, selectedMethod, selectedCheckAddress) {
        Var methodType is Ternary(selectedMethod._type == "EftAchEntity",
                                  "EFT",
                                  Ternary(selectedMethod._type == "CheckEntity",
                                          "CHECK",
                                          Null())
                                  )
        Attr amount is payment.paymentNetAmount.amount
        Attr currencyCode is payment.paymentNetAmount.currency
        Attr domainCorrelationId is ToExtLink(payment)._uri
        Attr domainName is "CLAIM"
        Attr transactionDate is currentTime
        Attr recipient is Ternary(latestPayee._type == "IndividualCustomer",
                                  latestPayee.details.person.firstName + " " + latestPayee.details.person.lastName,
                                  latestPayee.details.legalEntity.legalName)

        Attr paymentMethodType is methodType
        Attr paymentMethodDetails is Ternary(methodType == "EFT",
                                             constructEFTDetails(selectedMethod.profileId),
                                             Ternary(methodType == "CHECK",
                                                     constructCheckDetails(latestPayee, selectedCheckAddress),
                                                     Null()))
    }

    Producer constructEFTDetails(profileId) {
        Attr _type is "PaymentMethodDetailsEftEntity"
        Attr paymentMethodTokenId is profileId
    }

    Producer constructCheckDetails(latestPayee, selectedCheckAddress) {
        Var addresses is latestPayee.communicationInfo.addresses[filterActiveAddresses]
        Var checkAddresses is extractCommunicationPreferencesAsObj(addresses).wrappedCommunicationPreferences[filterForCheckAddresses].address
        Var applicableAddresses is Ternary(Count(checkAddresses) > 0, checkAddresses, addresses)
        Var applicableAddressForSort is SafeInvoke(applicableAddresses, applicableAddressForSort(applicableAddresses))
        Var preferredAddresses is applicableAddresses[filterPreferred]
        Var checkAddress is Ternary(Count(preferredAddresses) > 0, First(preferredAddresses), First(applicableAddressForSort<effectiveToDesc>).applicableAddress)

        Attr mailingAddressDetails is Ternary(selectedCheckAddress == Null(), constructMailingAddressDetails(checkAddress), constructMailingAddressDetails(selectedCheckAddress))
        Attr _type is "PaymentMethodDetailsCheckEntity"
    }

    Producer applicableAddressForSort(applicableAddress) {
        Attr effectiveTo is applicableAddress.schedulingContactInfo.effectiveTo
        Attr applicableAddress is applicableAddress
    }

    Producer createNoPayeeMessage(payee) {
        Attr code is "GetPreferredPaymentMethod-InvalidPayee"
        Attr severity is "critical"
        Attr message is "Operation could not be performed as target entity could not be loaded with provided key " + payee._key.rootId
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createInvalidPaymentMethodTypeMessage() {
        Attr code is "GetPreferredPaymentMethod-InvalidPaymentMethodType"
        Attr severity is "critical"
        Attr message is "Invalid payment method type. Must be one of the values: [CHECK, EFT]"
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createExpiredPaymentMethodMessage() {
        Attr code is "GenerateDispatchRequest-CAPPaymentMethodExpired"
        Attr severity is "critical"
        Attr message is "Payment method set for the payee in this case has expired."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createPaymentMethodNotFoundMessage() {
        Attr code is "GenerateDispatchRequest-CAPPaymentMethodNotFound"
        Attr severity is "critical"
        Attr message is "Payment method set for the payee in this case cannot be found."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createExpiredCheckAddressMessage() {
        Attr code is "GenerateDispatchRequest-CAPCheckAddressExpired"
        Attr severity is "critical"
        Attr message is "Check address set for the payee in this case has expired."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createCheckAddressNotFoundMessage() {
        Attr code is "GenerateDispatchRequest-CAPCheckAddressNotFound"
        Attr severity is "critical"
        Attr message is "Check address set for the payee in this case cannot be found."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createInvalidProviderLinkMessage(providerUri) {
        Attr code is "GenerateDispatchRequest-InvalidProvider"
        Attr severity is "critical"
        Attr message is "Operation could not be performed as target entity could not be loaded with provided key " + providerUri + "."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer createInvalidProviderStateMessage() {
        Attr code is "GenerateDispatchRequest-InvalidProviderState"
        Attr severity is "critical"
        Attr message is "Invalid provider state. Provider state must be 'Active'."
        Attr _type is "CapDentalPaymentMessageEntity"
    }

    Producer extractCommunicationPreferencesAsObj(address) {
        Attr wrappedCommunicationPreferences is wrapAddressCommunicationPreferences(address.communicationPreferences, address)
    }

    Producer wrapAddressCommunicationPreferences(communicationPreferences, address) {
        Attr communicationPreferences is communicationPreferences
        Attr address is address
    }

    Producer constructMailingAddressDetails(mailingAddress) {
        Attr addressLine1 is mailingAddress.location.addressLine1
        Attr addressLine2 is mailingAddress.location.addressLine2
        Attr addressLine3 is mailingAddress.location.addressLine3
        Attr city is mailingAddress.location.city
        Attr stateProvinceCd is mailingAddress.location.stateProvinceCd
        Attr postalCode is mailingAddress.location.postalCode
        Attr countryCd is mailingAddress.location.countryCd
        Attr addressType is mailingAddress.location.addressType
        Attr _type is "MailingAddressDetailsEntity"
    }

    Filter filterActiveEFTCHECK {
        CompareDates(effectiveDate, currentDate) <= 0 && (Equals(expirationDate, Null()) || CompareDates(expirationDate, currentDate) >= 0) && (_type == "EftAchEntity" || _type == "CheckEntity")
    }

    Filter filterActiveAddresses {
        (schedulingContactInfo.temporary == true && CompareDates(schedulingContactInfo.effectiveFrom, currentDate) <= 0 && (Equals(schedulingContactInfo.effectiveTo, Null()) || CompareDates(schedulingContactInfo.effectiveTo, currentDate) >= 0)) || (schedulingContactInfo.temporary == false || schedulingContactInfo.temporary == Null())
    }

    Filter filterForCheckAddresses {
        communicationPreferences == "MailingAddressForCheck"
    }

    Filter filterPreferred {
        preferred == true
    }

    Sort effectiveDateDesc {
        "effectiveDate" -> "DESC"
    }

    Sort effectiveToDesc {
        "effectiveTo" -> "DESC"
    }
}
