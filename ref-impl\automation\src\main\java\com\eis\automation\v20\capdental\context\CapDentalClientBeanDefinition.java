/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.context;

import com.eis.automation.tzappa.rest.client.impl.RestClient;
import com.eis.automation.tzappa.rest.client.impl.uri.DefaultUriResolver;
import com.eis.automation.tzappa.rest.client.impl.uri.IUriResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.Configuration;

import static com.eis.automation.v20.cap.constant.CapConstant.CapEnvironementConfigConstants.CAPGENERIC;
import static com.eis.automation.v20.cap.constant.CapConstant.CapEnvironementConfigConstants.CAP_LIFE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.CapEnvironementConfigConstants.CLAIM_DENTAL;

@Lazy
@Component
public class CapDentalClientBeanDefinition {

    @Autowired
    @Qualifier("platform.jerseyConfig")
    private Configuration config;

    @Lazy
    @Bean("cap.claimdental.uriResolver")
    private IUriResolver uriResolverClaimDental() {
        return new DefaultUriResolver("http", null, 8104);
    }

    @Lazy
    @Bean("cap.claim.uriResolver")
    private IUriResolver uriResolverClaim() {
        return new DefaultUriResolver("http", null, 8104);
    }

    @Lazy
    @Bean("cap.capDentalUnverifiedPolicy.restClient")
    private RestClient clientCapDentalUnverifiedPolicy() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capDentalLoss.restClient")
    private RestClient clientCapDentalLoss() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capDentalSettlement.restClient")
    private RestClient clientCapDentalSettlement() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capDentalPayment.restClient")
    private RestClient clientCapDentalPayment() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capDentalPaymentTemplate.restClient")
    private RestClient clientCapDentalPaymentTemplate() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capDentalPatientHistory.restClient")
    private RestClient clientCapDentalPatientHistory() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.claimSearch.restClient")
    private RestClient clientClaimSearch() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaimDental());
    }

    @Lazy
    @Bean("cap.capAccumulatorLoad.restClient")
    private RestClient clientCapAccumulatorLoad() {
        return new RestClient(CAPGENERIC, config, uriResolverClaim());
    }

    @Lazy
    @Bean("cap.capDentalPaymentSchedulePreview.restClient")
    private RestClient clientCapDentalPaymentSchedulePreview() {
        return new RestClient(CLAIM_DENTAL, config, uriResolverClaim());
    }
}