/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.ICapDentalSettlement;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;

public interface ICapDentalSettlementService extends ITestDataService, IFacadeService<ICapDentalSettlement> {

    ICapDentalSettlementModel initDentalSettlement(ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalSettlementModel initDentalSettlement(ICapDentalLossModel dentalClaimModel);

    ICapDentalSettlementModel createDentalSettlementModel(ICapDentalLossModel dentalClaimModel);

    ICapDentalSettlementModel loadDentalSettlement(ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalSettlementModel loadDentalSettlement(ICapDentalSettlementModel dentalSettlementModel, String state);

    ICapDentalSettlementModel loadApprovedDentalSettlement(ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalSettlementModel adjudicateDentalSettlement(ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalSettlementModel createReadjudicateSettlementModel(ICapDentalSettlementModel settlementModel);

    ICapDentalSettlementModel readjudicateDentalSettlement(ICapDentalSettlementModel readjudicateModel);

}
