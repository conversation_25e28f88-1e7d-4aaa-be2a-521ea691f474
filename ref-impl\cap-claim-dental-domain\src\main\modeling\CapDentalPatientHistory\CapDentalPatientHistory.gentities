@StateMachine
Entity CapDentalPatientHistoryEntity is RootEntity {

    //EISDEVTS-40482
    Attr patientHistoryData: CapDentalPatientHistoryDataEntity

    //EISDEVTS-40072
    Attr state: String
}

Entity CapDentalPatientHistoryDataEntity {

    //EISDEVTS-40482
    Attr accessTrackInfo: CapDentalAccessTrackInfoEntity

    //EISDEVTS-40482
    Attr claimData: CapDentalClaimDataEntity

    //EISDEVTS-40482
    Attr providerData: CapDentalProviderDataEntity

    //EISDEVTS-40482
    Attr serviceData: CapDentalServiceDataEntity
}

Entity CapDentalServiceDataEntity {

    //EISDEVTS-40482
    @Description("Indicates the period for which procedure is preauthorized.")
    Attr authorizationPeriod: Period

    //EISDEVTS-40482
    @Description("Calculated payable amount after adjudication.")
    Attr benefitAmount: Money

    //EISDEVTS-40482
    @Description("Procedure code received after adjudication.")
    Attr cdtCoveredCd: String

    //EISDEVTS-40482
    @Description("Procedure code stored during intake.")
    Attr cdtSubmittedCd: String

    //EISDEVTS-40482
    @Description("Amount to be paid by member, calculated after adjudication (based on %).")
    Attr coinsuranceAmount: Money

    //EISDEVTS-40482
    @Description("Maximum amount that will be considered for the service.")
    Attr consideredAmount: Money

    //EISDEVTS-40482
    @Description("Amount to be paid by member, calculated after adjudication (flat fee).")
    Attr copayAmount: Money

    //EISDEVTS-40482
    @Description("The amount that will be applied to the service.")
    Attr coveredAmount: Money

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("Calculated resolution if procedure is covered.")
    @Lookup("CapDNHistoryDecision")
    Attr decision: String

    //EISDEVTS-40482
    @Description("Calculated deductible money amount after adjudication.")
    Attr deductibleAmount: Money

    //EISDEVTS-40482
    @Description("Date of Service.")
    Attr DOSDate: Datetime

    //EISDEVTS-40482
    @Description("Indicates if procedure is part of predetermination or actual service.")
    Attr isPredet: Boolean

    //EISDEVTS-40482
    @Description("Indicates if predet procedure was preauthorized.")
    Attr isProcedureAuthorized: Boolean

    //EISDEVTS-40482
    @Description("Date payment was made, if applicable.")
    Attr paymentDate: Datetime

    //EISDEVTS-40482
    @Description("Covered Procedure area calculated after adjudication")
    Attr procedureType: String

    //EISDEVTS-40482
    @Description("Submitted quantity of procedures.")
    Attr quantity: Integer

    //EISDEVTS-40482
    @Description("Additional multiple codes that explains how decision achieved.")
    Attr remarkCodes: *String

    //EISDEVTS-40482
    @Description("Link to Service.")
    @Exists
    ExtLink service: RootEntity

    //EISDEVTS-40482
    @Description("Submitted amount what was requested to be paid.")
    Attr submittedAmount: Money

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("Submitted Tooth Surfaces.")
    @Lookup("CapDNSurfaces")
    Attr surfaces: *String

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("Submitted Area of Oral Cavity.")
    @Lookup("CapDNToothArea")
    Attr toothArea: String

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("Submitted Tooth Numbers/Letters.")
    @Lookup("CapDNToothCodes")
    Attr toothCodes: *String
}

Entity CapDentalClaimDataEntity {

    //EISDEVTS-40482
    @Description("Link to Claim.")
    @Exists
    ExtLink claim: RootEntity

    //EISDEVTS-40482
    @Description("Claim Number.")
    Attr claimNumber: String

    //EISDEVTS-40482
    @Description("Numbers of related attached documents.")
    Attr digitalImageNumbers: *String

    //EISDEVTS-40482
    @Description("Link to Customer.")
    @Queryable("claim_dental_history_patient")
    ExtLink patient: RootEntity

    //EISDEVTS-40482
    @Description("Patient Number.")
    Attr patientNumber: String

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("PPO/DHMO product.")
    @Lookup("CapDNCoverageType")
    Attr planCategory: String

    //EISDEVTS-40482
    @Description("Link to Policy.")
    @Exists
    ExtLink policy: RootEntity

    //EISDEVTS-40482
    @Description("Policy Number.")
    Attr policyNumber: String

    //EISDEVTS-40482
    @Description("Remark added to Claim by user.")
    Attr remark: String
}

Entity CapDentalProviderDataEntity {

    //EISDEVTS-40511
    @Description("Provider ID.")
    Attr dentistID: String

    //EISDEVTS-40482, EISDEVTS-50584
    @Description("Type of provider fee schedule that was applied during adjudication: INN or ONN.")
    @Lookup("CapDNInnOnn")
    Attr inOutNetwork: String

    //EISDEVTS-40482
    @Description("Link to Provider.")
    @Exists
    ExtLink provider: RootEntity

    //EISDEVTS-40482
    @Description("Provider business name.")
    Attr providerBusinessName: String

    //EISDEVTS-40482
    @Description("Provider tax identification number.")
    Attr providerTIN: String
}

Entity CapDentalAccessTrackInfoEntity is AccessTrackInfo {

}