/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.paymentschedule.CapPaymentScheduleBaseCommandHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPreviewPaymentScheduleInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;

import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPaymentScheduleCommands.PREVIEW_PAYMENT_SCHEDULE;

/**
 * Collects settlements financial data and creates {@link CapDentalPaymentScheduleEntity}
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPreviewPaymentScheduleHandler extends
    CapPaymentScheduleBaseCommandHandler<CapDentalPreviewPaymentScheduleInput, CapDentalPaymentScheduleEntity> {


    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPreviewPaymentScheduleInput input, @Nonnull CapDentalPaymentScheduleEntity entity) {
        return capValidatorRegistry.validateRequest(input);
    }

//    @Nonnull
//    @Override
//    public ValidationResult validate(@Nonnull CapDentalPreviewPaymentScheduleInput request,
//                                     @Nonnull CapDentalPaymentScheduleEntity loadedEntity) {
//        return super.validate(request, loadedEntity);
//    }

//    @Override
//    public void cleanUp(@Nonnull CapDentalPreviewPaymentScheduleInput request,
//                        @Nonnull CapDentalPaymentScheduleEntity entity) {
//        super.cleanUp(request, entity);
//    }

//    @Nonnull
//    @Override
//    public Variation getVariation() {
//        return super.getVariation();
//    }

    @Override
    public String getName() {
        return PREVIEW_PAYMENT_SCHEDULE;
    }

//    @Override
//    public Class<?> getInputType() {
//        return super.getInputType();
//    }
//
//    @Override
//    public Class<?> getOutputType() {
//        return super.getOutputType();
//    }
}
