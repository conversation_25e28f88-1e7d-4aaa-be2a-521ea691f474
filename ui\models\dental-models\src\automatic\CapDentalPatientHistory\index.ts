// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "ModelType"
  ],
  "moduleType":"CapDentalPatientHistoryModelType",
  "name":"CapDentalPatientHistory",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalPatientHistoryEntity"
  },
  "storeDeterminants":[
    "ModelName"
  ],
  "types":{
    "CapDentalAccessTrackInfoEntity":{
      "attributes":{
        "createdBy":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"createdBy",
          "type":{
            "type":"STRING"
          }
        },
        "createdOn":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"createdOn",
          "type":{
            "type":"DATETIME"
          }
        },
        "updatedBy":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"updatedBy",
          "type":{
            "type":"STRING"
          }
        },
        "updatedOn":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"updatedOn",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"AccessTrackInfo"
        }
      ],
      "baseTypes":[
        "AccessTrackInfo"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalAccessTrackInfoEntity",
      "references":{
      }
    },
    "CapDentalClaimDataEntity":{
      "attributes":{
        "claimNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Claim Number."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Claim Number.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"claimNumber",
          "type":{
            "type":"STRING"
          }
        },
        "digitalImageNumbers":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Numbers of related attached documents."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Numbers of related attached documents.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"digitalImageNumbers",
          "type":{
            "type":"STRING"
          }
        },
        "patientNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Patient Number."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Patient Number.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"patientNumber",
          "type":{
            "type":"STRING"
          }
        },
        "planCategory":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "PPO/DHMO product."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNCoverageType"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"PPO/DHMO product.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNCoverageType"
            }
          },
          "name":"planCategory",
          "type":{
            "type":"STRING"
          }
        },
        "policyNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Policy Number."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Policy Number.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"policyNumber",
          "type":{
            "type":"STRING"
          }
        },
        "remark":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Remark added to Claim by user."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Remark added to Claim by user.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"remark",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "claim":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.domain.feature.Exists":{
              "modelNames":[
              ],
              "modelTypes":[
              ]
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.domain.feature.Exists":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to Claim."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to Claim.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"claim",
          "targetType":"RootEntity"
        },
        "patient":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to Customer."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.queryfield.Queryable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "claim_dental_history_patient"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to Customer.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.queryfield.Queryable":{
              "groups":[
                {
                  "key":"claim_dental_history_patient"
                }
              ]
            }
          },
          "name":"patient",
          "targetType":"RootEntity"
        },
        "policy":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.domain.feature.Exists":{
              "modelNames":[
              ],
              "modelTypes":[
              ]
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.domain.feature.Exists":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to Policy."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to Policy.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"policy",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalClaimDataEntity",
      "references":{
      }
    },
    "CapDentalPatientHistoryDataEntity":{
      "attributes":{
        "accessTrackInfo":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"accessTrackInfo",
          "type":{
            "type":"CapDentalAccessTrackInfoEntity"
          }
        },
        "claimData":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"claimData",
          "type":{
            "type":"CapDentalClaimDataEntity"
          }
        },
        "providerData":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"providerData",
          "type":{
            "type":"CapDentalProviderDataEntity"
          }
        },
        "serviceData":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"serviceData",
          "type":{
            "type":"CapDentalServiceDataEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalPatientHistoryDataEntity",
      "references":{
      }
    },
    "CapDentalPatientHistoryEntity":{
      "attributes":{
        "patientHistoryData":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"patientHistoryData",
          "type":{
            "type":"CapDentalPatientHistoryDataEntity"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.lifecycle.statemachine.features.StateMachine":{
        }
      },
      "links":{
      },
      "name":"CapDentalPatientHistoryEntity",
      "references":{
      }
    },
    "CapDentalProviderDataEntity":{
      "attributes":{
        "dentistID":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Provider ID."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Provider ID.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"dentistID",
          "type":{
            "type":"STRING"
          }
        },
        "inOutNetwork":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Type of provider fee schedule that was applied during adjudication: INN or ONN."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNInnOnn"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Type of provider fee schedule that was applied during adjudication: INN or ONN.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNInnOnn"
            }
          },
          "name":"inOutNetwork",
          "type":{
            "type":"STRING"
          }
        },
        "providerBusinessName":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Provider business name."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Provider business name.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"providerBusinessName",
          "type":{
            "type":"STRING"
          }
        },
        "providerTIN":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Provider tax identification number."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Provider tax identification number.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"providerTIN",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "provider":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.domain.feature.Exists":{
              "modelNames":[
              ],
              "modelTypes":[
              ]
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.domain.feature.Exists":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to Provider."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to Provider.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"provider",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalProviderDataEntity",
      "references":{
      }
    },
    "CapDentalServiceDataEntity":{
      "attributes":{
        "DOSDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Date of Service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Date of Service.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"DOSDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "authorizationPeriod":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Indicates the period for which procedure is preauthorized."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Indicates the period for which procedure is preauthorized.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"authorizationPeriod",
          "type":{
            "type":"Period"
          }
        },
        "benefitAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated payable amount after adjudication."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated payable amount after adjudication.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"benefitAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "cdtCoveredCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Procedure code received after adjudication."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Procedure code received after adjudication.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"cdtCoveredCd",
          "type":{
            "type":"STRING"
          }
        },
        "cdtSubmittedCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Procedure code stored during intake."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Procedure code stored during intake.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"cdtSubmittedCd",
          "type":{
            "type":"STRING"
          }
        },
        "coinsuranceAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Amount to be paid by member, calculated after adjudication (based on %)."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Amount to be paid by member, calculated after adjudication (based on %).",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"coinsuranceAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "consideredAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Maximum amount that will be considered for the service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Maximum amount that will be considered for the service.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"consideredAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "copayAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Amount to be paid by member, calculated after adjudication (flat fee)."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Amount to be paid by member, calculated after adjudication (flat fee).",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"copayAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "coveredAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The amount that will be applied to the service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The amount that will be applied to the service.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"coveredAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "decision":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated resolution if procedure is covered."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNHistoryDecision"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated resolution if procedure is covered.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNHistoryDecision"
            }
          },
          "name":"decision",
          "type":{
            "type":"STRING"
          }
        },
        "deductibleAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated deductible money amount after adjudication."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated deductible money amount after adjudication.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"deductibleAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "isPredet":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Indicates if procedure is part of predetermination or actual service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Indicates if procedure is part of predetermination or actual service.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"isPredet",
          "type":{
            "type":"BOOLEAN"
          }
        },
        "isProcedureAuthorized":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Indicates if predet procedure was preauthorized."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Indicates if predet procedure was preauthorized.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"isProcedureAuthorized",
          "type":{
            "type":"BOOLEAN"
          }
        },
        "paymentDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Date payment was made, if applicable."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Date payment was made, if applicable.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"paymentDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "procedureType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Covered Procedure area calculated after adjudication"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Covered Procedure area calculated after adjudication",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"procedureType",
          "type":{
            "type":"STRING"
          }
        },
        "quantity":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Submitted quantity of procedures."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Submitted quantity of procedures.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"quantity",
          "type":{
            "type":"NUMBER"
          }
        },
        "remarkCodes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Additional multiple codes that explains how decision achieved."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Additional multiple codes that explains how decision achieved.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"remarkCodes",
          "type":{
            "type":"STRING"
          }
        },
        "submittedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Submitted amount what was requested to be paid."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Submitted amount what was requested to be paid.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"submittedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "surfaces":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Submitted Tooth Surfaces."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNSurfaces"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Submitted Tooth Surfaces.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNSurfaces"
            }
          },
          "name":"surfaces",
          "type":{
            "type":"STRING"
          }
        },
        "toothArea":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Submitted Area of Oral Cavity."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNToothArea"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Submitted Area of Oral Cavity.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNToothArea"
            }
          },
          "name":"toothArea",
          "type":{
            "type":"STRING"
          }
        },
        "toothCodes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Submitted Tooth Numbers/Letters."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "CapDNToothCodes"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Submitted Tooth Numbers/Letters.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"CapDNToothCodes"
            }
          },
          "name":"toothCodes",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "service":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.cap.domain.feature.Exists":{
              "modelNames":[
              ],
              "modelTypes":[
              ]
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.cap.domain.feature.Exists":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to Service."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to Service.",
              "messageBundle":"domain-messages/CapDentalPatientHistory/description"
            }
          },
          "name":"service",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalServiceDataEntity",
      "references":{
      }
    },
    "Period":{
      "attributes":{
        "endDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"endDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "startDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"startDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"Period"
        }
      ],
      "baseTypes":[
        "Period"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"Period",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalPatientHistory {
    export type Variations = never


    export class CapDentalAccessTrackInfoEntity extends MAPI.BusinessEntity implements BusinessTypes.AccessTrackInfo {
    constructor() { super(CapDentalAccessTrackInfoEntity.name) }
        readonly createdBy?: string
        readonly createdOn?: Date
        readonly updatedBy?: string
        readonly updatedOn?: Date
    }

    export class CapDentalClaimDataEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalClaimDataEntity.name) }
        readonly claim?: MAPI.ExternalLink
        readonly claimNumber?: string
        readonly digitalImageNumbers: string[] = []
        readonly patient?: MAPI.ExternalLink
        readonly patientNumber?: string
        readonly planCategory?: string
        readonly policy?: MAPI.ExternalLink
        readonly policyNumber?: string
        readonly remark?: string
    }

    export class CapDentalPatientHistoryDataEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalPatientHistoryDataEntity.name) }
        readonly accessTrackInfo?: CapDentalAccessTrackInfoEntity
        readonly claimData?: CapDentalClaimDataEntity
        readonly providerData?: CapDentalProviderDataEntity
        readonly serviceData?: CapDentalServiceDataEntity
    }

    export class CapDentalPatientHistoryEntity extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalPatientHistoryEntity.name) }
        readonly _modelName: string = 'CapDentalPatientHistory'
        readonly _modelType: string = 'CapDentalPatientHistoryModelType'
        readonly _modelVersion?: string = '1'
        readonly patientHistoryData?: CapDentalPatientHistoryDataEntity
        readonly state?: string
    }

    export class CapDentalProviderDataEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalProviderDataEntity.name) }
        readonly dentistID?: string
        readonly inOutNetwork?: string
        readonly provider?: MAPI.ExternalLink
        readonly providerBusinessName?: string
        readonly providerTIN?: string
    }

    export class CapDentalServiceDataEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalServiceDataEntity.name) }
        readonly DOSDate?: Date
        readonly authorizationPeriod?: Period
        readonly benefitAmount?: Money
        readonly cdtCoveredCd?: string
        readonly cdtSubmittedCd?: string
        readonly coinsuranceAmount?: Money
        readonly consideredAmount?: Money
        readonly copayAmount?: Money
        readonly coveredAmount?: Money
        readonly decision?: string
        readonly deductibleAmount?: Money
        readonly isPredet?: boolean
        readonly isProcedureAuthorized?: boolean
        readonly paymentDate?: Date
        readonly procedureType?: string
        readonly quantity?: number
        readonly remarkCodes: string[] = []
        readonly service?: MAPI.ExternalLink
        readonly submittedAmount?: Money
        readonly surfaces: string[] = []
        readonly toothArea?: string
        readonly toothCodes: string[] = []
    }

    export class Period extends MAPI.BusinessEntity implements BusinessTypes.Period {
    constructor() { super(Period.name) }
        readonly endDate?: Date
        readonly startDate?: Date
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalAccessTrackInfoEntity, ()=> new CapDentalAccessTrackInfoEntity)
    factory.registerEntity(CapDentalClaimDataEntity, ()=> new CapDentalClaimDataEntity)
    factory.registerEntity(CapDentalPatientHistoryDataEntity, ()=> new CapDentalPatientHistoryDataEntity)
    factory.registerEntity(CapDentalPatientHistoryEntity, ()=> new CapDentalPatientHistoryEntity)
    factory.registerEntity(CapDentalProviderDataEntity, ()=> new CapDentalProviderDataEntity)
    factory.registerEntity(CapDentalServiceDataEntity, ()=> new CapDentalServiceDataEntity)
    factory.registerEntity(Period, ()=> new Period)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}