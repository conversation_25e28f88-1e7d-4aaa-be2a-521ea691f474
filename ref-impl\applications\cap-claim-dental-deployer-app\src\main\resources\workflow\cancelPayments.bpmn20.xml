<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="cancelPayments" name="cancelPayments" isExecutable="true">
    <startEvent id="cancelLossPaymentSchedulesEntryPoint">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentSchedule_cancelLossPaymentSchedules]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="payload" sourceType="string" target="cancelPayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <serviceTask id="findNotIssuedPayments" name="Find Not Issued Payments" flowable:async="true" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('capLoadNotIssuedPayments').serviceGroup('CapDentalLoss').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{body:{ "_key" : ${cancelPayload._key}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[payments]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <exclusiveGateway id="sid-1827B710-58B1-48D1-AEA2-8262252380A5"></exclusiveGateway>
    <sequenceFlow id="sid-8750A8B5-A2DF-4B26-8DEF-D0084FBE9E3C" sourceRef="cancelLossPaymentSchedulesEntryPoint" targetRef="findNotIssuedPayments"></sequenceFlow>
    <subProcess id="cancelPaymentSubProcess" name="Cancel Payment">
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${payments.body.success.output.payments}" flowable:elementVariable="payload">
        <extensionElements></extensionElements>
      </multiInstanceLoopCharacteristics>
      <startEvent id="sid-597C7882-5010-4E80-9BFE-93127F536337" flowable:formFieldValidation="true"></startEvent>
      <serviceTask id="cancelPayment" name="Cancel Payment" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
        <extensionElements>
          <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
          <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
          <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter sourceExpression="{'_key': ${payload._key}}" target="payload" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="cancelPayment" target="commandName" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="payment" target="_variation" targetType="string"></flowable:eventInParameter>
          <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
          <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
        </extensionElements>
      </serviceTask>
      <endEvent id="sid-69F017B3-8920-43F2-BFAE-67C78C0A33A1"></endEvent>
      <sequenceFlow id="sid-C9A00D13-B24C-492C-8A85-B6A609585078" sourceRef="sid-597C7882-5010-4E80-9BFE-93127F536337" targetRef="cancelPayment"></sequenceFlow>
      <sequenceFlow id="sid-5E2270D0-1BBB-4391-8B32-D212E9CF6C43" sourceRef="cancelPayment" targetRef="sid-69F017B3-8920-43F2-BFAE-67C78C0A33A1"></sequenceFlow>
    </subProcess>
    <endEvent id="sid-BD411E38-8B56-4DA8-A527-3C1CE5F27E05"></endEvent>
    <sequenceFlow id="sid-47590185-02A6-490D-9F22-807EBD12E14E" sourceRef="cancelPaymentSubProcess" targetRef="sid-BD411E38-8B56-4DA8-A527-3C1CE5F27E05"></sequenceFlow>
    <sequenceFlow id="sid-8F9248C1-D7CE-459E-AFA0-766EF0ACB834" sourceRef="findNotIssuedPayments" targetRef="sid-1827B710-58B1-48D1-AEA2-8262252380A5"></sequenceFlow>
    <sequenceFlow id="sid-8C9C13AB-EF88-405F-A62D-0BC63414E4F8" sourceRef="sid-1827B710-58B1-48D1-AEA2-8262252380A5" targetRef="cancelPaymentSubProcess">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${payments.body.success.output.payments != null && payments.body.success.output.payments.size() > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-13391D7A-9487-49F5-B21E-4DDDD6F170F6" sourceRef="sid-1827B710-58B1-48D1-AEA2-8262252380A5" targetRef="sid-BD411E38-8B56-4DA8-A527-3C1CE5F27E05">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${payments.body.success.output.payments == null || payments.body.success.output.payments.size() == 0}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_cancelPayments">
    <bpmndi:BPMNPlane bpmnElement="cancelPayments" id="BPMNPlane_cancelPayments">
      <bpmndi:BPMNShape bpmnElement="cancelLossPaymentSchedulesEntryPoint" id="BPMNShape_cancelLossPaymentSchedulesEntryPoint">
        <omgdc:Bounds height="30.0" width="30.5" x="30.0" y="285.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="findNotIssuedPayments" id="BPMNShape_findNotIssuedPayments">
        <omgdc:Bounds height="80.0" width="100.0" x="195.0" y="260.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-1827B710-58B1-48D1-AEA2-8262252380A5" id="BPMNShape_sid-1827B710-58B1-48D1-AEA2-8262252380A5">
        <omgdc:Bounds height="40.0" width="40.0" x="382.5" y="280.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="cancelPaymentSubProcess" id="BPMNShape_cancelPaymentSubProcess">
        <omgdc:Bounds height="171.0" width="350.0" x="495.0" y="214.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-597C7882-5010-4E80-9BFE-93127F536337" id="BPMNShape_sid-597C7882-5010-4E80-9BFE-93127F536337">
        <omgdc:Bounds height="30.0" width="30.0" x="525.0" y="285.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="cancelPayment" id="BPMNShape_cancelPayment">
        <omgdc:Bounds height="80.0" width="100.0" x="620.0" y="260.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-69F017B3-8920-43F2-BFAE-67C78C0A33A1" id="BPMNShape_sid-69F017B3-8920-43F2-BFAE-67C78C0A33A1">
        <omgdc:Bounds height="28.0" width="28.0" x="780.0" y="286.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-BD411E38-8B56-4DA8-A527-3C1CE5F27E05" id="BPMNShape_sid-BD411E38-8B56-4DA8-A527-3C1CE5F27E05">
        <omgdc:Bounds height="28.0" width="28.0" x="945.0" y="286.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-C9A00D13-B24C-492C-8A85-B6A609585078" id="BPMNEdge_sid-C9A00D13-B24C-492C-8A85-B6A609585078" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="554.9499989186911" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="619.9999999999902" y="300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8C9C13AB-EF88-405F-A62D-0BC63414E4F8" id="BPMNEdge_sid-8C9C13AB-EF88-405F-A62D-0BC63414E4F8" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="175.0" flowable:targetDockerY="85.5">
        <omgdi:waypoint x="421.9819861215304" y="300.4643527204503"></omgdi:waypoint>
        <omgdi:waypoint x="494.99999999999477" y="300.3276217228464"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8750A8B5-A2DF-4B26-8DEF-D0084FBE9E3C" id="BPMNEdge_sid-8750A8B5-A2DF-4B26-8DEF-D0084FBE9E3C" flowable:sourceDockerX="15.5" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="60.94999949308973" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="195.0" y="300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-47590185-02A6-490D-9F22-807EBD12E14E" id="BPMNEdge_sid-47590185-02A6-490D-9F22-807EBD12E14E" flowable:sourceDockerX="175.0" flowable:sourceDockerY="85.5" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="844.94999999992" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="945.0" y="300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-13391D7A-9487-49F5-B21E-4DDDD6F170F6" id="BPMNEdge_sid-13391D7A-9487-49F5-B21E-4DDDD6F170F6" flowable:sourceDockerX="20.5" flowable:sourceDockerY="20.5" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="403.0" y="319.44420843520777"></omgdi:waypoint>
        <omgdi:waypoint x="403.0" y="464.0"></omgdi:waypoint>
        <omgdi:waypoint x="959.0" y="464.0"></omgdi:waypoint>
        <omgdi:waypoint x="959.0" y="313.94992653596495"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-5E2270D0-1BBB-4391-8B32-D212E9CF6C43" id="BPMNEdge_sid-5E2270D0-1BBB-4391-8B32-D212E9CF6C43" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="719.9499999998671" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="780.0" y="300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-8F9248C1-D7CE-459E-AFA0-766EF0ACB834" id="BPMNEdge_sid-8F9248C1-D7CE-459E-AFA0-766EF0ACB834" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="294.94999999981223" y="300.0"></omgdi:waypoint>
        <omgdi:waypoint x="382.5" y="300.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>