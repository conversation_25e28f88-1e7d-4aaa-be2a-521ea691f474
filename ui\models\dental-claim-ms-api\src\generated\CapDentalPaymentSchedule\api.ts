// tslint:disable
/**
 * CapDentalPaymentSchedule model API facade
 * API for CapDentalPaymentSchedule
 *
 * OpenAPI spec version: 1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


import * as url from "url";
import * as portableFetch from "portable-fetch";
import { Configuration } from "./configuration";

const BASE_PATH = "https://localhost".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface FetchAPI
 */
export interface FetchAPI {
    (url: string, init?: any): Promise<Response>;
}

/**
 *  
 * @export
 * @interface FetchArgs
 */
export interface FetchArgs {
    url: string;
    options: any;
}

/**
 * 
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected fetch: FetchAPI = portableFetch) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath || this.basePath;
        }
    }
};

/**
 * 
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    name: "RequiredError"
    constructor(public field: string, msg?: string) {
        super(msg);
    }
}

/**
 * 
 * @export
 * @interface CapDentalBuildPaymentScheduleInput
 */
export interface CapDentalBuildPaymentScheduleInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalBuildPaymentScheduleInput
     */
    originSource: EntityLink;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof CapDentalBuildPaymentScheduleInput
     */
    settlements?: Array<EntityLink>;
}

/**
 * 
 * @export
 * @interface CapDentalBuildPaymentScheduleInputBody
 */
export interface CapDentalBuildPaymentScheduleInputBody {
    /**
     * 
     * @type {CapDentalBuildPaymentScheduleInput}
     * @memberof CapDentalBuildPaymentScheduleInputBody
     */
    body: CapDentalBuildPaymentScheduleInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalBuildPaymentScheduleInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalBuildPaymentScheduleInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface CapDentalCancelActivePaymentSchedulesInput
 */
export interface CapDentalCancelActivePaymentSchedulesInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalCancelActivePaymentSchedulesInput
     */
    originSource: EntityLink;
}

/**
 * 
 * @export
 * @interface CapDentalCancelActivePaymentSchedulesInputBody
 */
export interface CapDentalCancelActivePaymentSchedulesInputBody {
    /**
     * 
     * @type {CapDentalCancelActivePaymentSchedulesInput}
     * @memberof CapDentalCancelActivePaymentSchedulesInputBody
     */
    body: CapDentalCancelActivePaymentSchedulesInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalCancelActivePaymentSchedulesInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalCancelActivePaymentSchedulesInputBody
     */
    requestId?: string;
}

/**
 * Allocation acumulator details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorAmount?: Money;
    /**
     * Accumulator type.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorType?: string;
    /**
     * Defines to which procedure category accumulator applies to.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    appliesToProcedureCategory?: string;
    /**
     * Network type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    networkType?: string;
    /**
     * Renewal type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    renewalType?: string;
}

/**
 * Dental allocation details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Array<CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity>}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
     */
    accumulatorDetails?: Array<CapDentalPaymentDefinitionCapDentalPaymentAllocationAccumulatorDetailsEntity>;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
     */
    patient?: EntityLink;
    /**
     * Dental claim transaction type.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity
     */
    transactionTypeCd?: string;
}

/**
 * An object which extends payment allocations details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    _type: string;
    /**
     * 
     * @type {CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationDentalDetails?: CapDentalPaymentDefinitionCapDentalPaymentAllocationDentalDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationGrossAmount?: Money;
    /**
     * Allocation Line Of Business code.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationLobCd?: string;
    /**
     * 
     * @type {CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationLossInfo?: CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationNetAmount?: Money;
    /**
     * 
     * @type {CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationPayableItem?: CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    allocationSource?: EntityLink;
    /**
     * Allocation reserve type.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity
     */
    reserveType?: string;
}

/**
 * Allocation claim details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationLossInfoEntity
     */
    lossSource?: EntityLink;
}

/**
 * Dental allocation payable item details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
     */
    claimSource?: EntityLink;
    /**
     * Month number for which allocation is paid.
     * @type {number}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
     */
    orthoMonth?: number;
    /**
     * Related Settlement Result entry's procedure ID.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentAllocationPayableItemEntity
     */
    procedureID?: string;
}

/**
 * An object which extends payment details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    _version?: string;
    /**
     * 
     * @type {CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    payeeDetails?: CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity;
    /**
     * 
     * @type {Array<CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity>}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    paymentAllocations?: Array<CapDentalPaymentDefinitionCapDentalPaymentAllocationEntity>;
    /**
     * The payment post date.
     * @type {Date}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity
     */
    paymentDate?: Date;
}

/**
 * Payment transaction information. The Root Entity of CAP Payment Domain.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _key?: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _variation: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    _version?: string;
    /**
     * A date when the payment was created.
     * @type {Date}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    creationDate?: Date;
    /**
     * Defines if payment is incoming or outgoing.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    direction?: string;
    /**
     * 
     * @type {Array<CapDentalPaymentDefinitionMessageType>}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    messages?: Array<CapDentalPaymentDefinitionMessageType>;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    paymentDetails?: CapDentalPaymentDefinitionCapDentalPaymentDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    paymentNetAmount?: Money;
    /**
     * Unique payment number.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    paymentNumber?: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    paymentSchedule?: EntityLink;
    /**
     * Payment state in the lifecycle.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentEntity
     */
    state?: string;
}

/**
 * An object which extends payment payee details.
 * @export
 * @interface CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity
 */
export interface CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentDefinitionCapDentalPaymentPayeeDetailsEntity
     */
    payee?: EntityLink;
}

/**
 * Defines a message that can be forwarded to the user on some exceptions.
 * @export
 * @interface CapDentalPaymentDefinitionMessageType
 */
export interface CapDentalPaymentDefinitionMessageType {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapDentalPaymentDefinitionMessageType
     */
    source?: string;
}

/**
 * Allocation acumulator details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorAmount?: Money;
    /**
     * Accumulator type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    accumulatorType?: string;
    /**
     * Defines to which procedure category accumulator applies to.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    appliesToProcedureCategory?: string;
    /**
     * Network type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    networkType?: string;
    /**
     * Renewal type for accumulator.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity
     */
    renewalType?: string;
}

/**
 * Dental allocation details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    accumulatorDetails?: Array<CapDentalPaymentScheduleCapDentalPaymentAllocationAccumulatorDetailsEntity>;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    patient?: EntityLink;
    /**
     * Dental claim transaction type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity
     */
    transactionTypeCd?: string;
}

/**
 * An object which extends payment allocations details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    _type: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationDentalDetails?: CapDentalPaymentScheduleCapDentalPaymentAllocationDentalDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationGrossAmount?: Money;
    /**
     * Allocation Line Of Business code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationLobCd?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationLossInfo?: CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationNetAmount?: Money;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationPayableItem?: CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    allocationSource?: EntityLink;
    /**
     * Allocation reserve type.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationEntity
     */
    reserveType?: string;
}

/**
 * Allocation claim details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationLossInfoEntity
     */
    lossSource?: EntityLink;
}

/**
 * Dental allocation payable item details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    claimSource?: EntityLink;
    /**
     * Month number for which allocation is paid.
     * @type {number}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    orthoMonth?: number;
    /**
     * Related Settlement Result entry's procedure ID.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentAllocationPayableItemEntity
     */
    procedureID?: string;
}

/**
 * An object which extends payment details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    payeeDetails?: CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentAllocationEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    paymentAllocations?: Array<CapDentalPaymentScheduleCapDentalPaymentAllocationEntity>;
    /**
     * The payment post date.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentDetailsEntity
     */
    paymentDate?: Date;
}

/**
 * An object which extends payment payee details.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentPayeeDetailsEntity
     */
    payee?: EntityLink;
}

/**
 * The Root Entity of CAP Payment Schedule Domain.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _key?: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    _version?: string;
    /**
     * A date when the schedule was created.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    creationDate?: Date;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    paymentTemplate?: EntityLink;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    payments?: Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    scheduleMessages?: Array<CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity>;
    /**
     * A unique ID, that is assigned to a new payment schedule.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    scheduleNumber?: string;
    /**
     * State of a payment schedule lifecycle.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntity
     */
    state?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleEntity}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess
     */
    success?: CapDentalPaymentScheduleCapDentalPaymentScheduleEntity;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody {
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    body?: CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccess;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody
     */
    requestId?: string;
}

/**
 * Stores Payment Schedule messages
 * @export
 * @interface CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
 */
export interface CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalPaymentScheduleMessageEntity
     */
    source?: string;
}

/**
 * Defines payment transaction information.
 * @export
 * @interface CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
 */
export interface CapDentalPaymentScheduleCapDentalScheduledPaymentEntity {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    _type: string;
    /**
     * A date when the payment was created.
     * @type {Date}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    creationDate?: Date;
    /**
     * Defines if payment is incoming or outgoing.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    direction?: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleMessageType>}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    messages?: Array<CapDentalPaymentScheduleMessageType>;
    /**
     * 
     * @type {CapDentalPaymentScheduleCapDentalPaymentDetailsEntity}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentDetails?: CapDentalPaymentScheduleCapDentalPaymentDetailsEntity;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentNetAmount?: Money;
    /**
     * A unique ID, that is assigned to a new payment.
     * @type {string}
     * @memberof CapDentalPaymentScheduleCapDentalScheduledPaymentEntity
     */
    paymentNumber?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleInitInput
 */
export interface CapDentalPaymentScheduleInitInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleInitInput
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPaymentScheduleInitInput
     */
    paymentTemplate?: EntityLink;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>}
     * @memberof CapDentalPaymentScheduleInitInput
     */
    payments?: Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleInitInputBody
 */
export interface CapDentalPaymentScheduleInitInputBody {
    /**
     * 
     * @type {CapDentalPaymentScheduleInitInput}
     * @memberof CapDentalPaymentScheduleInitInputBody
     */
    body: CapDentalPaymentScheduleInitInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleInitInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleInitInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleLoadHistoryResult
 */
export interface CapDentalPaymentScheduleLoadHistoryResult {
    /**
     * 
     * @type {number}
     * @memberof CapDentalPaymentScheduleLoadHistoryResult
     */
    count?: number;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalPaymentScheduleEntity>}
     * @memberof CapDentalPaymentScheduleLoadHistoryResult
     */
    result?: Array<CapDentalPaymentScheduleCapDentalPaymentScheduleEntity>;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleLoadHistoryResultSuccess
 */
export interface CapDentalPaymentScheduleLoadHistoryResultSuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleLoadHistoryResultSuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalPaymentScheduleLoadHistoryResult}
     * @memberof CapDentalPaymentScheduleLoadHistoryResultSuccess
     */
    success?: CapDentalPaymentScheduleLoadHistoryResult;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleLoadHistoryResultSuccessBody
 */
export interface CapDentalPaymentScheduleLoadHistoryResultSuccessBody {
    /**
     * 
     * @type {CapDentalPaymentScheduleLoadHistoryResultSuccess}
     * @memberof CapDentalPaymentScheduleLoadHistoryResultSuccessBody
     */
    body?: CapDentalPaymentScheduleLoadHistoryResultSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleLoadHistoryResultSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleLoadHistoryResultSuccessBody
     */
    requestId?: string;
}

/**
 * Defines a message that can be forwarded to the user on some exceptions.
 * @export
 * @interface CapDentalPaymentScheduleMessageType
 */
export interface CapDentalPaymentScheduleMessageType {
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapDentalPaymentScheduleMessageType
     */
    source?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleUpdateInput
 */
export interface CapDentalPaymentScheduleUpdateInput {
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalPaymentScheduleUpdateInput
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleUpdateInput
     */
    _version?: string;
    /**
     * 
     * @type {Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>}
     * @memberof CapDentalPaymentScheduleUpdateInput
     */
    payments?: Array<CapDentalPaymentScheduleCapDentalScheduledPaymentEntity>;
}

/**
 * 
 * @export
 * @interface CapDentalPaymentScheduleUpdateInputBody
 */
export interface CapDentalPaymentScheduleUpdateInputBody {
    /**
     * 
     * @type {CapDentalPaymentScheduleUpdateInput}
     * @memberof CapDentalPaymentScheduleUpdateInputBody
     */
    body: CapDentalPaymentScheduleUpdateInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPaymentScheduleUpdateInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPaymentScheduleUpdateInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface CapDentalPreviewPaymentScheduleInput
 */
export interface CapDentalPreviewPaymentScheduleInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalPreviewPaymentScheduleInput
     */
    originSource: EntityLink;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof CapDentalPreviewPaymentScheduleInput
     */
    settlements?: Array<EntityLink>;
}

/**
 * 
 * @export
 * @interface CapDentalPreviewPaymentScheduleInputBody
 */
export interface CapDentalPreviewPaymentScheduleInputBody {
    /**
     * 
     * @type {CapDentalPreviewPaymentScheduleInput}
     * @memberof CapDentalPreviewPaymentScheduleInputBody
     */
    body: CapDentalPreviewPaymentScheduleInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalPreviewPaymentScheduleInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalPreviewPaymentScheduleInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface CapDentalSuspendActivePaymentSchedulesInput
 */
export interface CapDentalSuspendActivePaymentSchedulesInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalSuspendActivePaymentSchedulesInput
     */
    originSource: EntityLink;
}

/**
 * 
 * @export
 * @interface CapDentalSuspendActivePaymentSchedulesInputBody
 */
export interface CapDentalSuspendActivePaymentSchedulesInputBody {
    /**
     * 
     * @type {CapDentalSuspendActivePaymentSchedulesInput}
     * @memberof CapDentalSuspendActivePaymentSchedulesInputBody
     */
    body: CapDentalSuspendActivePaymentSchedulesInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalSuspendActivePaymentSchedulesInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalSuspendActivePaymentSchedulesInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface CapDentalUnsuspendLossPaymentSchedulesInput
 */
export interface CapDentalUnsuspendLossPaymentSchedulesInput {
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalUnsuspendLossPaymentSchedulesInput
     */
    originSource: EntityLink;
}

/**
 * 
 * @export
 * @interface CapDentalUnsuspendLossPaymentSchedulesInputBody
 */
export interface CapDentalUnsuspendLossPaymentSchedulesInputBody {
    /**
     * 
     * @type {CapDentalUnsuspendLossPaymentSchedulesInput}
     * @memberof CapDentalUnsuspendLossPaymentSchedulesInputBody
     */
    body: CapDentalUnsuspendLossPaymentSchedulesInput;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalUnsuspendLossPaymentSchedulesInputBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalUnsuspendLossPaymentSchedulesInputBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface DentalInternalCancelActivePaymentSchedulesOutput
 */
export interface DentalInternalCancelActivePaymentSchedulesOutput {
    /**
     * 
     * @type {EntityKey}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutput
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutput
     */
    _type: string;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutput
     */
    cancelledSchedules?: Array<EntityLink>;
    /**
     * 
     * @type {EntityLink}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutput
     */
    originSource?: EntityLink;
}

/**
 * 
 * @export
 * @interface DentalInternalCancelActivePaymentSchedulesOutputSuccess
 */
export interface DentalInternalCancelActivePaymentSchedulesOutputSuccess {
    /**
     * 
     * @type {string}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutputSuccess
     */
    response?: string;
    /**
     * 
     * @type {DentalInternalCancelActivePaymentSchedulesOutput}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutputSuccess
     */
    success?: DentalInternalCancelActivePaymentSchedulesOutput;
}

/**
 * 
 * @export
 * @interface DentalInternalCancelActivePaymentSchedulesOutputSuccessBody
 */
export interface DentalInternalCancelActivePaymentSchedulesOutputSuccessBody {
    /**
     * 
     * @type {DentalInternalCancelActivePaymentSchedulesOutputSuccess}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutputSuccessBody
     */
    body?: DentalInternalCancelActivePaymentSchedulesOutputSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutputSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalCancelActivePaymentSchedulesOutputSuccessBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface DentalInternalSuspendActivePaymentSchedulesOutput
 */
export interface DentalInternalSuspendActivePaymentSchedulesOutput {
    /**
     * 
     * @type {EntityKey}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutput
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutput
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutput
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutput
     */
    suspendedSchedules?: Array<EntityLink>;
}

/**
 * 
 * @export
 * @interface DentalInternalSuspendActivePaymentSchedulesOutputSuccess
 */
export interface DentalInternalSuspendActivePaymentSchedulesOutputSuccess {
    /**
     * 
     * @type {string}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutputSuccess
     */
    response?: string;
    /**
     * 
     * @type {DentalInternalSuspendActivePaymentSchedulesOutput}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutputSuccess
     */
    success?: DentalInternalSuspendActivePaymentSchedulesOutput;
}

/**
 * 
 * @export
 * @interface DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody
 */
export interface DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody {
    /**
     * 
     * @type {DentalInternalSuspendActivePaymentSchedulesOutputSuccess}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody
     */
    body?: DentalInternalSuspendActivePaymentSchedulesOutputSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface DentalInternalUnsuspendPaymentSchedulesOutput
 */
export interface DentalInternalUnsuspendPaymentSchedulesOutput {
    /**
     * 
     * @type {EntityKey}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutput
     */
    _key?: EntityKey;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutput
     */
    _type: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutput
     */
    originSource?: EntityLink;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutput
     */
    unsuspendedSchedules?: Array<EntityLink>;
}

/**
 * 
 * @export
 * @interface DentalInternalUnsuspendPaymentSchedulesOutputSuccess
 */
export interface DentalInternalUnsuspendPaymentSchedulesOutputSuccess {
    /**
     * 
     * @type {string}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutputSuccess
     */
    response?: string;
    /**
     * 
     * @type {DentalInternalUnsuspendPaymentSchedulesOutput}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutputSuccess
     */
    success?: DentalInternalUnsuspendPaymentSchedulesOutput;
}

/**
 * 
 * @export
 * @interface DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody
 */
export interface DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody {
    /**
     * 
     * @type {DentalInternalUnsuspendPaymentSchedulesOutputSuccess}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody
     */
    body?: DentalInternalUnsuspendPaymentSchedulesOutputSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface EndpointFailure
 */
export interface EndpointFailure {
    /**
     * 
     * @type {ErrorHolder}
     * @memberof EndpointFailure
     */
    data?: ErrorHolder;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailure
     */
    failure?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EndpointFailure
     */
    httpCode?: number;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailure
 */
export interface EndpointFailureFailure {
    /**
     * 
     * @type {EndpointFailure}
     * @memberof EndpointFailureFailure
     */
    failure?: EndpointFailure;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailure
     */
    response?: string;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailureBody
 */
export interface EndpointFailureFailureBody {
    /**
     * 
     * @type {EndpointFailureFailure}
     * @memberof EndpointFailureFailureBody
     */
    body?: EndpointFailureFailure;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailureFailureBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailureBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface EntityKey
 */
export interface EntityKey {
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    parentId?: string;
    /**
     * 
     * @type {number}
     * @memberof EntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    rootId?: string;
}

/**
 * 
 * @export
 * @interface EntityLink
 */
export interface EntityLink {
    /**
     * 
     * @type {string}
     * @memberof EntityLink
     */
    _uri?: string;
}

/**
 * 
 * @export
 * @interface EntityLinkRequest
 */
export interface EntityLinkRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    fields?: Array<string>;
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    limit?: number;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof EntityLinkRequest
     */
    links?: Array<EntityLink>;
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    offset?: number;
}

/**
 * 
 * @export
 * @interface EntityLinkRequestBody
 */
export interface EntityLinkRequestBody {
    /**
     * 
     * @type {EntityLinkRequest}
     * @memberof EntityLinkRequestBody
     */
    body: EntityLinkRequest;
    /**
     * 
     * @type {boolean}
     * @memberof EntityLinkRequestBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof EntityLinkRequestBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface ErrorHolder
 */
export interface ErrorHolder {
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    code?: string;
    /**
     * 
     * @type {any}
     * @memberof ErrorHolder
     */
    details?: any;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    errorCode?: string;
    /**
     * 
     * @type {Array<ErrorHolder>}
     * @memberof ErrorHolder
     */
    errors?: Array<ErrorHolder>;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    field?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    logReference?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    message?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    path?: string;
}

/**
 * 
 * @export
 * @interface GeneratePaymentOutputs
 */
export interface GeneratePaymentOutputs {
    /**
     * 
     * @type {Array<CapDentalPaymentDefinitionCapDentalPaymentEntity>}
     * @memberof GeneratePaymentOutputs
     */
    payments?: Array<CapDentalPaymentDefinitionCapDentalPaymentEntity>;
}

/**
 * 
 * @export
 * @interface GeneratePaymentOutputsSuccess
 */
export interface GeneratePaymentOutputsSuccess {
    /**
     * 
     * @type {string}
     * @memberof GeneratePaymentOutputsSuccess
     */
    response?: string;
    /**
     * 
     * @type {GeneratePaymentOutputs}
     * @memberof GeneratePaymentOutputsSuccess
     */
    success?: GeneratePaymentOutputs;
}

/**
 * 
 * @export
 * @interface GeneratePaymentOutputsSuccessBody
 */
export interface GeneratePaymentOutputsSuccessBody {
    /**
     * 
     * @type {GeneratePaymentOutputsSuccess}
     * @memberof GeneratePaymentOutputsSuccessBody
     */
    body?: GeneratePaymentOutputsSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof GeneratePaymentOutputsSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof GeneratePaymentOutputsSuccessBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface IdentifierRequest
 */
export interface IdentifierRequest {
    /**
     * 
     * @type {RootEntityKey}
     * @memberof IdentifierRequest
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof IdentifierRequest
     */
    _version?: string;
}

/**
 * 
 * @export
 * @interface IdentifierRequestBody
 */
export interface IdentifierRequestBody {
    /**
     * 
     * @type {IdentifierRequest}
     * @memberof IdentifierRequestBody
     */
    body: IdentifierRequest;
    /**
     * 
     * @type {boolean}
     * @memberof IdentifierRequestBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof IdentifierRequestBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequest
 */
export interface LoadEntityByBusinessKeyRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequestBody
 */
export interface LoadEntityByBusinessKeyRequestBody {
    /**
     * 
     * @type {LoadEntityByBusinessKeyRequest}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    body: LoadEntityByBusinessKeyRequest;
    /**
     * 
     * @type {boolean}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface LoadEntityRootRequest
 */
export interface LoadEntityRootRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityRootRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityRootRequest
     */
    fields?: Array<string>;
    /**
     * 
     * @type {number}
     * @memberof LoadEntityRootRequest
     */
    limit?: number;
    /**
     * 
     * @type {number}
     * @memberof LoadEntityRootRequest
     */
    offset?: number;
}

/**
 * 
 * @export
 * @interface LoadEntityRootRequestBody
 */
export interface LoadEntityRootRequestBody {
    /**
     * 
     * @type {LoadEntityRootRequest}
     * @memberof LoadEntityRootRequestBody
     */
    body: LoadEntityRootRequest;
    /**
     * 
     * @type {boolean}
     * @memberof LoadEntityRootRequestBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof LoadEntityRootRequestBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequest
 */
export interface LoadSingleEntityRootRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequestBody
 */
export interface LoadSingleEntityRootRequestBody {
    /**
     * 
     * @type {LoadSingleEntityRootRequest}
     * @memberof LoadSingleEntityRootRequestBody
     */
    body: LoadSingleEntityRootRequest;
    /**
     * 
     * @type {boolean}
     * @memberof LoadSingleEntityRootRequestBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof LoadSingleEntityRootRequestBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface Money
 */
export interface Money {
    /**
     * 
     * @type {number}
     * @memberof Money
     */
    amount: number;
    /**
     * 
     * @type {string}
     * @memberof Money
     */
    currency: string;
}

/**
 * 
 * @export
 * @interface ObjectSuccess
 */
export interface ObjectSuccess {
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccess
     */
    response?: string;
    /**
     * 
     * @type {any}
     * @memberof ObjectSuccess
     */
    success?: any;
}

/**
 * 
 * @export
 * @interface ObjectSuccessBody
 */
export interface ObjectSuccessBody {
    /**
     * 
     * @type {ObjectSuccess}
     * @memberof ObjectSuccessBody
     */
    body?: ObjectSuccess;
    /**
     * 
     * @type {boolean}
     * @memberof ObjectSuccessBody
     */
    finalResponse?: boolean;
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccessBody
     */
    requestId?: string;
}

/**
 * 
 * @export
 * @interface RootEntityKey
 */
export interface RootEntityKey {
    /**
     * 
     * @type {number}
     * @memberof RootEntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof RootEntityKey
     */
    rootId?: string;
}


/**
 * DefaultApi - fetch parameter creator
 * @export
 */
export const DefaultApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/activatePaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalBuildPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params: { body?: CapDentalBuildPaymentScheduleInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/buildPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalBuildPaymentScheduleInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalCancelActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params: { body?: CapDentalCancelActivePaymentSchedulesInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/cancelLossPaymentSchedules`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalCancelActivePaymentSchedulesInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/cancelPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/completePaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/generatePayments`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params: { body?: CapDentalPaymentScheduleInitInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/initPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalPaymentScheduleInitInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPreviewPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params: { body?: CapDentalPreviewPaymentScheduleInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/previewPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalPreviewPaymentScheduleInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalSuspendActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params: { body?: CapDentalSuspendActivePaymentSchedulesInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/suspendLossPaymentSchedules`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalSuspendActivePaymentSchedulesInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/suspendPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalUnsuspendLossPaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params: { body?: CapDentalUnsuspendLossPaymentSchedulesInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/unsuspendLossPaymentSchedules`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalUnsuspendLossPaymentSchedulesInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/unsuspendPaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params: { body?: CapDentalPaymentScheduleUpdateInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/command/updatePaymentSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalPaymentScheduleUpdateInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.businessKey' is not null or undefined
            if (params.businessKey === null || params.businessKey === undefined) {
                throw new RequiredError('params.businessKey','Required parameter params.businessKey was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet.');
            }
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/entities/{businessKey}/{revisionNo}`
                .replace(`{${"businessKey"}}`, encodeURIComponent(String(params.businessKey)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet.');
            }
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/entities/{rootId}/{revisionNo}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.businessKey' is not null or undefined
            if (params.businessKey === null || params.businessKey === undefined) {
                throw new RequiredError('params.businessKey','Required parameter params.businessKey was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost.');
            }
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/history/{businessKey}`
                .replace(`{${"businessKey"}}`, encodeURIComponent(String(params.businessKey)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"LoadEntityByBusinessKeyRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost.');
            }
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/history/{rootId}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"LoadEntityRootRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/link/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"EntityLinkRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params: { modelType?: string,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/model/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.modelType !== undefined) {
                localVarQueryParameter['modelType'] = params.modelType;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/transformation/capDentalFindSettlementForSchedule`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/cappaymentschedule/CapDentalPaymentSchedule/v1/transformation/generatePayment`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalBuildPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params: { body?: CapDentalBuildPaymentScheduleInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalCancelActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params: { body?: CapDentalCancelActivePaymentSchedulesInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<DentalInternalCancelActivePaymentSchedulesOutputSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params: { body?: CapDentalPaymentScheduleInitInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPreviewPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params: { body?: CapDentalPreviewPaymentScheduleInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalSuspendActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params: { body?: CapDentalSuspendActivePaymentSchedulesInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<DentalInternalSuspendActivePaymentSchedulesOutputSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalUnsuspendLossPaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params: { body?: CapDentalUnsuspendLossPaymentSchedulesInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<DentalInternalUnsuspendPaymentSchedulesOutputSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params: { body?: CapDentalPaymentScheduleUpdateInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleCapDentalPaymentScheduleEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleLoadHistoryResultSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalPaymentScheduleLoadHistoryResultSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params: { modelType?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<GeneratePaymentOutputsSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalBuildPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params: { body?: CapDentalBuildPaymentScheduleInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalCancelActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params: { body?: CapDentalCancelActivePaymentSchedulesInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params: { body?: CapDentalPaymentScheduleInitInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPreviewPaymentScheduleInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params: { body?: CapDentalPreviewPaymentScheduleInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalSuspendActivePaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params: { body?: CapDentalSuspendActivePaymentSchedulesInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalUnsuspendLossPaymentSchedulesInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params: { body?: CapDentalUnsuspendLossPaymentSchedulesInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalPaymentScheduleUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params: { body?: CapDentalPaymentScheduleUpdateInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params, options)(fetch, basePath);
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params: { modelType?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params, options)(fetch, basePath);
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params, options)(fetch, basePath);
        },
        /**
         * No description provided
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params, options)(fetch, basePath);
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandActivatePaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalBuildPaymentScheduleInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params: { body?: CapDentalBuildPaymentScheduleInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandBuildPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalCancelActivePaymentSchedulesInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params: { body?: CapDentalCancelActivePaymentSchedulesInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelLossPaymentSchedulesPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCancelPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandCompletePaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandGeneratePaymentsPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalPaymentScheduleInitInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params: { body?: CapDentalPaymentScheduleInitInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandInitPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalPreviewPaymentScheduleInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params: { body?: CapDentalPreviewPaymentScheduleInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandPreviewPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalSuspendActivePaymentSchedulesInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params: { body?: CapDentalSuspendActivePaymentSchedulesInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendLossPaymentSchedulesPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandSuspendPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalUnsuspendLossPaymentSchedulesInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params: { body?: CapDentalUnsuspendLossPaymentSchedulesInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendLossPaymentSchedulesPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUnsuspendPaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalPaymentScheduleUpdateInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params: { body?: CapDentalPaymentScheduleUpdateInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1CommandUpdatePaymentSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} businessKey 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesBusinessKeyRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1EntitiesRootIdRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory entity root records for a given path parameters.
     * @param {string} businessKey 
     * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryBusinessKeyPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory entity root records for a given path parameters.
     * @param {string} rootId 
     * @param {LoadEntityRootRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1HistoryRootIdPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory model root entity records for given links.
     * @param {EntityLinkRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1LinkPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
     * @param {string} [modelType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params: { modelType?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1ModelGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * No description provided
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationCapDentalFindSettlementForSchedulePost(params, options)(this.fetch, this.basePath);
    }

    /**
     * No description provided
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCappaymentscheduleCapDentalPaymentScheduleV1TransformationGeneratePaymentPost(params, options)(this.fetch, this.basePath);
    }

}

