<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
        <artifactId>ms-claim-dental-ref-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cap-claim-dental-domain</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-domain</artifactId>
        </dependency>

        <!-- Configures fcompiler, fgenerator plugins -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle</artifactId>
            <classifier>domain</classifier>
            <type>tile</type>
        </dependency>

        <!-- Security (Configures dimension filtering fcompiler plugin)-->
        <dependency>
            <groupId>com.eisgroup.genesis.security</groupId>
            <artifactId>security-bundle</artifactId>
            <classifier>domain</classifier>
            <type>tile</type>
        </dependency>

        <!-- Configures Lookups DSL fcompiler -->
        <dependency>
            <groupId>com.eisgroup.genesis.lookups</groupId>
            <artifactId>lookups-bundle</artifactId>
            <classifier>domain</classifier>
            <type>tile</type>
        </dependency>
        <!-- @Lookup feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.lookups</groupId>
            <artifactId>lookups-api</artifactId>
        </dependency>

        <!-- Configures relationship DSL fcompiler -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entities-bundle.relationships</artifactId>
            <classifier>domain</classifier>
            <type>tile</type>
        </dependency>

        <!-- feature @StateMachine -->
        <dependency>
            <groupId>com.eisgroup.genesis.lifecycle</groupId>
            <artifactId>state-machine-api</artifactId>
        </dependency>

        <!-- Constraints features -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entity-dsl-constraints</artifactId>
        </dependency>

        <!-- @Overridable feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>data-override-api</artifactId>
        </dependency>

        <!-- @Confidential feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>confidential-data-api</artifactId>
        </dependency>

        <!-- @Unique feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.uniquefield</groupId>
            <artifactId>unique-field-api</artifactId>
        </dependency>

        <!-- @SearchGroup feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.search</groupId>
            <artifactId>search-api</artifactId>
        </dependency>

        <!-- @Queryable feature -->
        <dependency>
             <groupId>com.eisgroup.genesis.queryfield</groupId>
             <artifactId>query-field-api</artifactId>
        </dependency>

        <!-- @Indexing feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.search</groupId>
            <artifactId>search-integration</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-search-index-ms-bundle</artifactId>
            <classifier>model</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-search-index-events</artifactId>
            <scope>test</scope>
        </dependency>


        <!-- @CorrelationIdResolver feature -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-stream-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>transformation-bundle</artifactId>
            <classifier>compiler</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>transformation-bundle</artifactId>
            <classifier>model</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-transformation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-transformation-persistence-executors</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.kraken.command.integration</groupId>
            <artifactId>rules.integration-bundle</artifactId>
            <classifier>domain</classifier>
            <type>tile</type>
        </dependency>

        <!-- Financial -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.financial</groupId>
            <artifactId>cap-financial-base-domain</artifactId>
        </dependency>
        <!-- Loss -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.loss</groupId>
            <artifactId>cap-loss-base-domain</artifactId>
        </dependency>
        <!-- Adjudication -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.adjudication</groupId>
            <artifactId>cap-adjudication-base-domain</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>transformation-lifecycle-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.party</groupId>
            <artifactId>cap-party-role-base-domain</artifactId>
        </dependency>

        <!-- Testing -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-transformation-testing</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

</project>
