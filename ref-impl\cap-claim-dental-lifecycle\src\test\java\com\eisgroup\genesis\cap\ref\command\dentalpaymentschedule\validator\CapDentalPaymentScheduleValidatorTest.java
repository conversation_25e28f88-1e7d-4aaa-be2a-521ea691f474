package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.commands.validator.errors.ValidationErrorException;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleActivationResult;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleMessageEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator.CapDentalPaymentScheduleErrorDefinition.ACTIVE_SCHEDULE_EXISTS;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator.CapDentalPaymentScheduleErrorDefinition.PAYMENT_SCHEDULE_CANNOT_BE_ACTIVATED;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

public class CapDentalPaymentScheduleValidatorTest {

    private static final String LOSS_URI = "gentity://CapLoss/DentalLoss//d0e11578-8732-4610-b4cf-0d2db9c5c285/1";

    @Mock
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;
    @Mock
    private ModeledTransformationService modeledTransformationService;
    @Mock
    private ModelRepository<TransformationModel> transformationRepository;

    @InjectMocks
    private CapDentalPaymentScheduleValidator validator;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testWhenOriginSourceNotProvided() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        when(schedule.getOriginSource()).thenReturn(null);
        
        // Mock the transformation service for validateActivationResult
        TransformationModel transformationModel = mock(TransformationModel.class);
        when(transformationRepository.getActiveModel("CapDentalPaymentScheduleActivation")).thenReturn(transformationModel);
        
        CapDentalPaymentScheduleEntity transformedSchedule = mock(CapDentalPaymentScheduleEntity.class);
        CapDentalPaymentScheduleActivationResult activationResult = mock(CapDentalPaymentScheduleActivationResult.class);
        when(transformedSchedule.getPaymentScheduleActivationResult()).thenReturn(activationResult);
        when(activationResult.getActivationStatus()).thenReturn("Activate");
        
        when(modeledTransformationService.transform(any(TransformationModel.class), any(CapDentalPaymentScheduleEntity.class)))
            .thenReturn(transformedSchedule);

        //when
        TestStreamable.create(validator.validatePaymentScheduleActivation(schedule))
            .assertNoErrors()
            .assertNoValues();
    }

    @Test
    public void testWhenActiveSchedulesResolved() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        CapDentalPaymentScheduleIdxEntity index = mock(CapDentalPaymentScheduleIdxEntity.class);
        EntityLink<RootEntity> originSource = new EntityLink<>(RootEntity.class, LOSS_URI);
        when(schedule.getOriginSource()).thenReturn(originSource);
        when(capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(eq(originSource), anyList()))
            .thenReturn(Streamable.of(index));
        
        // Mock the transformation service for validateActivationResult
        TransformationModel transformationModel = mock(TransformationModel.class);
        when(transformationRepository.getActiveModel("CapDentalPaymentScheduleActivation")).thenReturn(transformationModel);
        
        CapDentalPaymentScheduleEntity transformedSchedule = mock(CapDentalPaymentScheduleEntity.class);
        CapDentalPaymentScheduleActivationResult activationResult = mock(CapDentalPaymentScheduleActivationResult.class);
        when(transformedSchedule.getPaymentScheduleActivationResult()).thenReturn(activationResult);
        when(activationResult.getActivationStatus()).thenReturn("Activate");
        
        when(modeledTransformationService.transform(any(TransformationModel.class), any(CapDentalPaymentScheduleEntity.class)))
            .thenReturn(transformedSchedule);

        //when
        TestStreamable.create(validator.validatePaymentScheduleActivation(schedule))
            .assertValueCount(1)
            .assertNoErrors()
            .assertValue(holder -> holder.getCode().equals(ACTIVE_SCHEDULE_EXISTS.getCode()));
    }

    @Test
    public void testWhenActiveTemplateNotResolved() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        EntityLink<RootEntity> originSource = new EntityLink<>(RootEntity.class, LOSS_URI);
        when(schedule.getOriginSource()).thenReturn(originSource);
        when(capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(eq(originSource), anyList()))
                .thenReturn(Streamable.empty());
        
        // Mock the transformation service for validateActivationResult
        TransformationModel transformationModel = mock(TransformationModel.class);
        when(transformationRepository.getActiveModel("CapDentalPaymentScheduleActivation")).thenReturn(transformationModel);
        
        CapDentalPaymentScheduleEntity transformedSchedule = mock(CapDentalPaymentScheduleEntity.class);
        CapDentalPaymentScheduleActivationResult activationResult = mock(CapDentalPaymentScheduleActivationResult.class);
        when(transformedSchedule.getPaymentScheduleActivationResult()).thenReturn(activationResult);
        when(activationResult.getActivationStatus()).thenReturn("Activate");
        
        when(modeledTransformationService.transform(any(TransformationModel.class), any(CapDentalPaymentScheduleEntity.class)))
            .thenReturn(transformedSchedule);

        //when
        TestStreamable.create(validator.validatePaymentScheduleActivation(schedule))
                .assertNoErrors()
                .assertNoValues();
    }

    @Test
    public void testWhenActivationResultCannotBeActivated() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        EntityLink<RootEntity> originSource = new EntityLink<>(RootEntity.class, LOSS_URI);
        when(schedule.getOriginSource()).thenReturn(originSource);
        when(capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(eq(originSource), anyList()))
                .thenReturn(Streamable.empty());
        
        // Mock the transformation service for validateActivationResult with non-Activate status
        TransformationModel transformationModel = mock(TransformationModel.class);
        when(transformationRepository.getActiveModel("CapDentalPaymentScheduleActivation")).thenReturn(transformationModel);
        
        CapDentalPaymentScheduleEntity transformedSchedule = mock(CapDentalPaymentScheduleEntity.class);
        CapDentalPaymentScheduleActivationResult activationResult = mock(CapDentalPaymentScheduleActivationResult.class);
        when(transformedSchedule.getPaymentScheduleActivationResult()).thenReturn(activationResult);
        when(activationResult.getActivationStatus()).thenReturn("Failed");
        
        when(modeledTransformationService.transform(any(TransformationModel.class), any(CapDentalPaymentScheduleEntity.class)))
            .thenReturn(transformedSchedule);

        //when
        TestStreamable.create(validator.validatePaymentScheduleActivation(schedule))
                .assertValueCount(1)
                .assertNoErrors()
                .assertValue(holder -> holder.getCode().equals(PAYMENT_SCHEDULE_CANNOT_BE_ACTIVATED.getCode()));
    }

    @Test
    public void testValidateCalculatedScheduleWithCriticalMessages() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        
        CapDentalPaymentScheduleMessageEntity criticalMessage = mock(CapDentalPaymentScheduleMessageEntity.class);
        when(criticalMessage.getSeverity()).thenReturn("critical");
        when(criticalMessage.getCode()).thenReturn("TEST_ERROR");
        when(criticalMessage.getMessage()).thenReturn("Test error message");
        
        when(schedule.getScheduleMessages()).thenReturn(List.of(criticalMessage));

        //when
        Lazy<CapDentalPaymentScheduleEntity> result = validator.validateCalculatedSchedule(schedule);
        
        //then
        try {
            result.get();
            fail("Expected ValidationErrorException to be thrown");
        } catch (Exception e) {
            assertTrue("Expected ValidationErrorException", e instanceof ValidationErrorException);
            assertTrue("Error message should contain expected text", 
                      e.getMessage().contains("Dental Payment Schedule failed due to validation errors"));
        }
    }

    @Test
    public void testValidateCalculatedScheduleWithNoCriticalMessages() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        
        CapDentalPaymentScheduleMessageEntity infoMessage = mock(CapDentalPaymentScheduleMessageEntity.class);
        when(infoMessage.getSeverity()).thenReturn("info");
        when(infoMessage.getCode()).thenReturn("INFO_MESSAGE");
        when(infoMessage.getMessage()).thenReturn("Info message");
        
        when(schedule.getScheduleMessages()).thenReturn(List.of(infoMessage));

        //when
        Lazy<CapDentalPaymentScheduleEntity> result = validator.validateCalculatedSchedule(schedule);
        
        //then
        try {
            CapDentalPaymentScheduleEntity validatedSchedule = result.get();
            assertEquals("Expected the same schedule entity", schedule, validatedSchedule);
        } catch (Exception e) {
            fail("Expected validation to succeed but it failed: " + e.getMessage());
        }
    }

    @Test
    public void testValidateCalculatedScheduleWithNoMessages() {
        //given
        CapDentalPaymentScheduleEntity schedule = mock(CapDentalPaymentScheduleEntity.class);
        when(schedule.getScheduleMessages()).thenReturn(Collections.emptyList());

        //when
        Lazy<CapDentalPaymentScheduleEntity> result = validator.validateCalculatedSchedule(schedule);
        
        //then
        try {
            CapDentalPaymentScheduleEntity validatedSchedule = result.get();
            assertEquals("Expected the same schedule entity", schedule, validatedSchedule);
        } catch (Exception e) {
            fail("Expected validation to succeed but it failed: " + e.getMessage());
        }
    }
}