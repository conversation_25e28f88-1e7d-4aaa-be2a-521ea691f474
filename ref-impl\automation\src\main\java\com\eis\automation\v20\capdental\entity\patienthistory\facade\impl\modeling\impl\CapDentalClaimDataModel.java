/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl;

import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;

import java.util.List;

public class CapDentalClaimDataModel extends TypeModel implements ICapDentalClaimDataModel {

    private List<String> digitalImageNumbers;
    private String policyNumber;
    private String patientNumber;
    private String remark;
    private String planCategory;
    private String claimNumber;
    private UriModel patient;
    private UriModel claim;
    private UriModel policy;

    public List<String> getDigitalImageNumbers() {
        return digitalImageNumbers;
    }

    public void setDigitalImageNumbers(List<String> digitalImageNumbers) {
        this.digitalImageNumbers = digitalImageNumbers;
    }

    public String getPolicyNumber() {
        return policyNumber;
    }

    public void setPolicyNumber(String policyNumber) {
        this.policyNumber = policyNumber;
    }

    public String getPatientNumber() {
        return patientNumber;
    }

    public void setPatientNumber(String patientNumber) {
        this.patientNumber = patientNumber;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPlanCategory() {
        return planCategory;
    }

    public void setPlanCategory(String planCategory) {
        this.planCategory = planCategory;
    }

    public String getClaimNumber() {
        return claimNumber;
    }

    public void setClaimNumber(String claimNumber) {
        this.claimNumber = claimNumber;
    }

    public UriModel getPatient() {
        return patient;
    }

    public void setPatient(UriModel patient) {
        this.patient = patient;
    }

    public UriModel getClaim() {
        return claim;
    }

    public void setClaim(UriModel claim) {
        this.claim = claim;
    }

    public UriModel getPolicy() {
        return policy;
    }

    public void setPolicy(UriModel policy) {
        this.policy = policy;
    }
}
