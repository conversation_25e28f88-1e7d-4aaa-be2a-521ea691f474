@KeyStrategy("FRESH_KEYS") // bug in platform, output entity must have key for now
@Indexing
@EventListener("initPatientHistory", "cancelPatientHistory")
Transformation PatientHistoryToProcedureProjection {
    Input {
        CapDentalPatientHistory.CapDentalPatientHistoryEntity as patientHistory
    }
    Output {
        CapDentalProcedure.CapDentalProcedureProjection
    }

    Var patientHistoryData is patientHistory.patientHistoryData

    Attr uri is ToExtLink(patientHistory)
    Attr claimNumber is patientHistoryData.claimData.claimNumber
    Attr policyNumber is patientHistoryData.claimData.policyNumber
    Attr patientNumber is patientHistoryData.claimData.patientNumber
    Attr planCategory is patientHistoryData.claimData.planCategory

    Attr DOSDate is patientHistoryData.serviceData.DOSDate
    Attr toothCodes is patientHistoryData.serviceData.toothCodes

    Attr providerTIN is patientHistoryData.providerData.providerTIN
    Attr state is patientHistory.state

}