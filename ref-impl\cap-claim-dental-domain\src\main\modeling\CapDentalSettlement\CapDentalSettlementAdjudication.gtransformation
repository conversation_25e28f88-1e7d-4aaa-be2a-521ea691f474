@Passthrough
@KeyStrategy("PASSTHROUGH")
@Handler
@CommandListener("Execute", "adjudicateSettlement")
Transformation CapDentalSettlementAdjudication {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
    }
    Output {
        CapDentalSettlement.CapDentalSettlementEntity
    }

    Var response is ExecuteRules("claim-dental-adjudication", "_api_adjudication", Transform("CapDentalSettlementAdjudicationInput", settlement))
    Var openlError is First(response.messages[isOpenlError])
    Attr settlementResult is Ternary(openlError == Null(), response, produceOpenlErrorResults(openlError))

    Producer produceOpenlErrorResults(openlError){
        Attr messages is FlatMap(produceMessage(openlError), Null())
        Attr proposal is "DENY - ERR"
    }

    Producer produceMessage(openlError){
        Attr severity is "Critical"
        Attr code is openlError.code
        Attr message is openlError.message
    }

    Filter isOpenlError{
        code == "CapOpenlError"
    }
}
