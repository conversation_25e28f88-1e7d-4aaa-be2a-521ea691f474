{"TestData": {"originSource": {"_uri": "gentity://CapLoss/CapDentalLoss//{{Dental_rootId}}/1"}, "paymentNetAmount": {"amount": 0, "currency": "USD"}, "entity": {"paymentAllocations": [{"allocationSource": {"_uri": "gentity://CapSettlement/CapDentalSettlement//{{settlementID}}/1"}, "allocationNetAmount": {"amount": 100, "currency": "USD"}, "allocationPayableItem": {"claimSource": {"_uri": "gentity://CapLoss/CapDentalLoss//{{Dental_rootId}}/1"}, "_type": "CapDentalPaymentAllocationPayableItemEntity"}, "allocationDentalDetails": {"transactionTypeCd": "string", "_type": "CapDentalPaymentAllocationDentalDetailsEntity"}, "_type": "CapDentalPaymentAllocationEntity"}], "payeeDetails": {"payee": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{IndividualCustomer}}"}, "_type": "CapDentalPaymentPayeeDetailsEntity"}, "_type": "CapDentalPaymentDetailsEntity", "_modelName": "CapDentalPaymentDefinition"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentModel"}}