// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_DENTALSETTLEMENTROOTIDENTIFIERTRANSFORMATIONMODEL } from "./kraken_model_tree_DentalSettlementRootIdentifierTransformationModel"

let name = "DentalSettlementRootIdentifierTransformationModel"

let namespace = "DentalSettlementRootIdentifierTransformationModel"

let currencyCd = "USD"

export type DentalSettlementRootIdentifierTransformationModelEntryPointName = never

let entryPointNames = [
] as DentalSettlementRootIdentifierTransformationModelEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_DENTALSETTLEMENTROOTIDENTIFIERTRANSFORMATIONMODEL as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_DentalSettlementRootIdentifierTransformationModel = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
