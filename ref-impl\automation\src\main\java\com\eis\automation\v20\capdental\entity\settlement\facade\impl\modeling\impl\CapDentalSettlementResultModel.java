/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.impl.MessageTypeModel;
import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.interf.IMessageTypeModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalAccumulatorModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalResultEntryModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementResultModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalSettlementResultModel extends TypeModel implements ICapDentalSettlementResultModel {

    private String proposal;
    private String payeeRef;
    private Money paymentAmount;
    private List<ICapDentalResultEntryModel> entries;
    private List<IMessageTypeModel> messages;
    private Integer reserve;
    private List<ICapDentalAccumulatorModel> reservedAccumulators;

    public String getProposal() {
        return proposal;
    }

    public void setProposal(String proposal) {
        this.proposal = proposal;
    }

    public String getPayeeRef() {
        return payeeRef;
    }

    public void setPayeeRef(String payeeRef) {
        this.payeeRef = payeeRef;
    }

    public Money getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(Money paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalResultEntryModel.class)
    public List<ICapDentalResultEntryModel> getEntries() {
        return entries;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalResultEntryModel.class)
    public void setEntries(List<ICapDentalResultEntryModel> entries) {
        this.entries = entries;
    }

    @JsonSerialize(as = List.class, contentAs = MessageTypeModel.class)
    public List<IMessageTypeModel> getMessages() {
        return messages;
    }

    @JsonDeserialize(as = List.class, contentAs = MessageTypeModel.class)
    public void setMessages(List<IMessageTypeModel> messages) {
        this.messages = messages;
    }

    public Integer getReserve() {
        return reserve;
    }

    public void setReserve(Integer reserve) {
        this.reserve = reserve;
    }
}
