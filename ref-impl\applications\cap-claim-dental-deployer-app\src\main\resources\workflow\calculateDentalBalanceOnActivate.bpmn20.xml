<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="calculateDentalBalanceOnActivate" name="calculateDentalBalanceOnActivate" isExecutable="true">
    <startEvent id="activateDentalScheduleEntryPoint" name="Activate Dental Schedule">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentSchedule_activatePaymentSchedule]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="payload" sourceType="string" target="activatePayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <serviceTask id="calculateDentalLossBalance" name="Calculate Loss Balance" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
      <extensionElements>
        <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
        <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
        <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter sourceExpression="{'originSource': ${activatePayload.originSource}}" target="payload" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="CapDentalBalance" target="_modelName" targetType="string"></flowable:eventInParameter>
        <flowable:eventInParameter source="calculateLossBalance" target="commandName" targetType="string"></flowable:eventInParameter>
        <flowable:eventOutParameter source="payload" sourceType="string" target="calculatePayload"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
        <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="string" value="${requestId}"></flowable:triggerEventCorrelationParameter>
      </extensionElements>
    </serviceTask>
    <sequenceFlow id="toCalculateLossBalance" sourceRef="activateDentalScheduleEntryPoint" targetRef="calculateDentalLossBalance"></sequenceFlow>
    <exclusiveGateway id="gateWayAfterCalculate" name="Balance calculated"></exclusiveGateway>
    <sequenceFlow id="toGateway" sourceRef="calculateDentalLossBalance" targetRef="gateWayAfterCalculate"></sequenceFlow>
    <subProcess id="initDentalBalance" name="Init Balance">
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${calculatePayload.balances}" flowable:elementVariable="payload">
        <extensionElements></extensionElements>
      </multiInstanceLoopCharacteristics>
      <startEvent id="startInitBalance" flowable:formFieldValidation="true"></startEvent>
      <serviceTask id="initDentalBalanceServiceTask" name="Init Balance" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
        <extensionElements>
          <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
          <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
          <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter sourceExpression="{'payee': ${payload.payee}, 'originSource': ${activatePayload.originSource}, 'balanceItems': ${payload.balanceItems}, 'totalBalanceAmount': ${payload.totalBalanceAmount}}" target="payload" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="CapDentalBalance" target="_modelName" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="initBalance" target="commandName" targetType="string"></flowable:eventInParameter>
          <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
          <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
        </extensionElements>
      </serviceTask>
      <endEvent id="initBalanceEnd"></endEvent>
      <sequenceFlow id="toInitBalanceEvent" sourceRef="startInitBalance" targetRef="initDentalBalanceServiceTask"></sequenceFlow>
      <sequenceFlow id="toEndInitBalance" sourceRef="initDentalBalanceServiceTask" targetRef="initBalanceEnd"></sequenceFlow>
    </subProcess>
    <endEvent id="endOfCalculate" name="Exit"></endEvent>
    <sequenceFlow id="sid-190CC2CC-B5FC-4387-8D4F-88B2DC7A8D4F" sourceRef="initDentalBalance" targetRef="endOfCalculate"></sequenceFlow>
    <sequenceFlow id="sid-4A76AB56-ABA9-42DF-BBF6-2EA480671BAB" sourceRef="gateWayAfterCalculate" targetRef="initDentalBalance">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${calculatePayload.balances != null && calculatePayload.balances.size() > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-34EC3C56-65DE-45C5-AC81-49CDF069BC3E" sourceRef="gateWayAfterCalculate" targetRef="endOfCalculate">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${calculatePayload.balances == null || calculatePayload.balances.size() == 0}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_calculateDentalBalanceOnActivate">
    <bpmndi:BPMNPlane bpmnElement="calculateDentalBalanceOnActivate" id="BPMNPlane_calculateDentalBalanceOnActivate">
      <bpmndi:BPMNShape bpmnElement="activateDentalScheduleEntryPoint" id="BPMNShape_activateDentalScheduleEntryPoint">
        <omgdc:Bounds height="30.0" width="30.5" x="143.25" y="202.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="calculateDentalLossBalance" id="BPMNShape_calculateDentalLossBalance">
        <omgdc:Bounds height="80.0" width="100.0" x="330.5" y="177.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="gateWayAfterCalculate" id="BPMNShape_gateWayAfterCalculate">
        <omgdc:Bounds height="40.0" width="40.0" x="510.0" y="197.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initDentalBalance" id="BPMNShape_initDentalBalance">
        <omgdc:Bounds height="239.0" width="327.0" x="645.0" y="97.5"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="startInitBalance" id="BPMNShape_startInitBalance">
        <omgdc:Bounds height="30.0" width="30.0" x="675.0" y="202.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initDentalBalanceServiceTask" id="BPMNShape_initDentalBalanceServiceTask">
        <omgdc:Bounds height="80.0" width="100.0" x="758.5" y="177.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initBalanceEnd" id="BPMNShape_initBalanceEnd">
        <omgdc:Bounds height="28.0" width="28.0" x="900.0" y="203.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="endOfCalculate" id="BPMNShape_endOfCalculate">
        <omgdc:Bounds height="28.0" width="28.0" x="1110.0" y="203.0"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="toCalculateLossBalance" id="BPMNEdge_toCalculateLossBalance" flowable:sourceDockerX="15.25" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="173.6999996101668" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="330.5" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toInitBalanceEvent" id="BPMNEdge_toInitBalanceEvent" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.0" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="704.9499986987321" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="758.5" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toEndInitBalance" id="BPMNEdge_toEndInitBalance" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="858.4499999999999" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="900.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-34EC3C56-65DE-45C5-AC81-49CDF069BC3E" id="BPMNEdge_sid-34EC3C56-65DE-45C5-AC81-49CDF069BC3E" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="530.0" y="236.9461942006868"></omgdi:waypoint>
        <omgdi:waypoint x="530.0" y="479.0"></omgdi:waypoint>
        <omgdi:waypoint x="1124.0" y="479.0"></omgdi:waypoint>
        <omgdi:waypoint x="1124.0" y="230.94992135509167"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-190CC2CC-B5FC-4387-8D4F-88B2DC7A8D4F" id="BPMNEdge_sid-190CC2CC-B5FC-4387-8D4F-88B2DC7A8D4F" flowable:sourceDockerX="163.5" flowable:sourceDockerY="119.5" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="971.949999999967" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="1110.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="toGateway" id="BPMNEdge_toGateway" flowable:sourceDockerX="50.0" flowable:sourceDockerY="40.0" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="430.45000000000005" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="510.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-4A76AB56-ABA9-42DF-BBF6-2EA480671BAB" id="BPMNEdge_sid-4A76AB56-ABA9-42DF-BBF6-2EA480671BAB" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="163.5" flowable:targetDockerY="119.5">
        <omgdi:waypoint x="549.9464195977938" y="217.0"></omgdi:waypoint>
        <omgdi:waypoint x="645.0" y="217.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>