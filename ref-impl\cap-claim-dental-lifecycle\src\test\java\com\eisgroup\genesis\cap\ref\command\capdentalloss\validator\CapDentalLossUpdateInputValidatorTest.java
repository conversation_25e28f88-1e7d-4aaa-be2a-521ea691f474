/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossUpdateInput;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.google.gson.JsonObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossUpdateInputValidatorTest {

    @InjectMocks
    private CapDentalLossUpdateInputValidator validator;

    @Mock
    private CapDentalLossValidator capDentalLossValidator;

    @Test
    public void shouldValidateLinks() {
        //given
        ClaimLossUpdateInput input = new ClaimLossUpdateInput(new JsonObject());
        when(capDentalLossValidator.validateLinks(any())).thenReturn(Streamable.empty());

        //when
        TestStreamable.create(validator.validate(input))
                .assertNoErrors();

        //then
        verify(capDentalLossValidator).validateLinks(any());
    }
}
