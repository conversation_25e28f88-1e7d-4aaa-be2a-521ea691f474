/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalrecovery.input;

import java.util.Optional;

import com.eisgroup.genesis.cap.financial.command.recovery.input.CapRecoveryInitInput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

/**
 * Recovery initiation input.
 *
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalRecoveryInitInput extends CapRecoveryInitInput {

    private static final String ORIGIN_SOURCE = "originSource";

    public CapDentalRecoveryInitInput(JsonObject original) {
        super(original);
    }

    /**
     * Overridden to remove @Required annotation, field is made required through Kraken rules.
     */
    @Override
    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(originSource -> new EntityLink<>(RootEntity.class, originSource))
                .orElse(null);
    }
}
