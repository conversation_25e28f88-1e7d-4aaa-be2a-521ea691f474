/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.facade;

import com.eis.automation.v20.cap.entity.paymenttemplate.common.facade.ICapBasePaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public interface ICapDentalPaymentTemplate extends ICapBasePaymentTemplate {

    PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> build();

    PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> startBuild();

    PostModelAction<ICapDentalPaymentTemplateBuildModel, ICapDentalPaymentTemplateModel> financialData();
}
