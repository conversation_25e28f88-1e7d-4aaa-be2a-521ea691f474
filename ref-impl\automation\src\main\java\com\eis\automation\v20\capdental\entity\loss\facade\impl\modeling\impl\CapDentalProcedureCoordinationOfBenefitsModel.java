/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureCoordinationOfBenefitsModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalProcedureCoordinationOfBenefitsModel extends TypeModel implements ICapDentalProcedureCoordinationOfBenefitsModel {

    private String coverageType;
    private String primaryCoverageStatus;
    private String innOnn;
    private Money allowed;
    private Money considered;
    private Money paid;

    public String getCoverageType() {
        return coverageType;
    }

    public void setCoverageType(String coverageType) {
        this.coverageType = coverageType;
    }

    public String getPrimaryCoverageStatus() {
        return primaryCoverageStatus;
    }

    public void setPrimaryCoverageStatus(String primaryCoverageStatus) {
        this.primaryCoverageStatus = primaryCoverageStatus;
    }

    public String getInnOnn() {
        return innOnn;
    }

    public void setInnOnn(String innOnn) {
        this.innOnn = innOnn;
    }

    public Money getAllowed() {
        return allowed;
    }

    public void setAllowed(Money allowed) {
        this.allowed = allowed;
    }

    public Money getConsidered() {
        return considered;
    }

    public void setConsidered(Money considered) {
        this.considered = considered;
    }

    public Money getPaid() {
        return paid;
    }

    public void setPaid(Money paid) {
        this.paid = paid;
    }
}
