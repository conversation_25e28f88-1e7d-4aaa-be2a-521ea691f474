/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.underpayment.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.underpayment.input.CapDentalUnderPaymentInitInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentPayeeDetailsEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;

import java.util.List;
import java.util.Optional;

import static com.eisgroup.genesis.cap.ref.command.underpayment.validator.CapDentalUnderpaymentInitInputValidator.CapDentalUnderpaymentInitInputValidatorErrorDefinition.ORIGIN_SOURCE_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.underpayment.validator.CapDentalUnderpaymentInitInputValidator.CapDentalUnderpaymentInitInputValidatorErrorDefinition.PAYEE_INCORRECT;


/**
 * Class for validating {@link CapDentalUnderPaymentInitInput} data.
 *
 * <AUTHOR>
 * @since 22.15
 */
public class CapDentalUnderpaymentInitInputValidator extends CapInputValidator<CapDentalUnderPaymentInitInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalUnderpaymentInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalUnderPaymentInitInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalUnderPaymentInitInput input) {
        return Streamable.concat(validateOriginSource(input.getOriginSource())
                ,validatePayee(input.getEntity()));
    }

    private Streamable<ErrorHolder> validateOriginSource(EntityLink<RootEntity> originSource) {
        return Optional.ofNullable(originSource)
                .map(origin -> capDentalLinkValidator.validateLink(origin, "CapLoss", ORIGIN_SOURCE_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    private Streamable<ErrorHolder> validatePayee(CapPaymentDetails paymentDetails) {
        return Optional.ofNullable(paymentDetails)
                .filter(CapDentalPaymentDetailsEntity.class::isInstance)
                .map(CapDentalPaymentDetailsEntity.class::cast)
                .map(CapDentalPaymentDetailsEntity::getPayeeDetails)
                .map(CapDentalPaymentPayeeDetailsEntity::getPayee)
                .map(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    public static class CapDentalUnderpaymentInitInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static CapDentalUnderpaymentInitInputValidatorErrorDefinition ORIGIN_SOURCE_INCORRECT =
                new CapDentalUnderpaymentInitInputValidatorErrorDefinition("cdui001", "originSource URI is not valid");
        public static CapDentalUnderpaymentInitInputValidatorErrorDefinition PAYEE_INCORRECT =
                new CapDentalUnderpaymentInitInputValidatorErrorDefinition("cdui002", "payeeDetails.payee URI is not valid");

        protected CapDentalUnderpaymentInitInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
