/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.impl;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.CapDentalBalanceCalculator;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceCalculationRulesOutput;
import com.eisgroup.genesis.factory.model.dentalinternal.CapDentalFinancialCalculateLossBalanceDataInput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

/**
 * Default implementation of {@link CapDentalBalanceCalculator} which will use OpenL rules to calculate balance
 *
 * <AUTHOR>
 * @since 22.14
 */
public class DefaultCapDentalBalanceCalculator implements CapDentalBalanceCalculator {

    private static final String CAP_BALANCE_CALCULATION_TRANSFORMATION= "CapDentalBalanceCalculation";

    private final ModeledTransformationService transformationService;
    private final ModelRepository<TransformationModel> transformationRepository;

    public DefaultCapDentalBalanceCalculator(ModeledTransformationService transformationService, ModelRepository<TransformationModel> transformationRepository) {
        this.transformationService = transformationService;
        this.transformationRepository = transformationRepository;
    }

    @Override
    public Lazy<CapDentalBalanceCalculationRulesOutput> calculateLossBalance(EntityLink<RootEntity> originSource) {
        return Lazy.of(transformationService.transform(
                transformationRepository.getActiveModel(CAP_BALANCE_CALCULATION_TRANSFORMATION), constructInput(originSource)));
    }

    private CapDentalFinancialCalculateLossBalanceDataInput constructInput(EntityLink<RootEntity> originSource) {
        CapDentalFinancialCalculateLossBalanceDataInput balanceInput = (CapDentalFinancialCalculateLossBalanceDataInput) ModelInstanceFactory.createInstance("DentalInternal",
                "1", "CapDentalFinancialCalculateLossBalanceDataInput");
        balanceInput.setOriginSource(originSource);
        return balanceInput;
    }
}
