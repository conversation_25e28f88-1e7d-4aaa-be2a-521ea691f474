{
  "data" : [ {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "INN"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 50,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 150.0,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "OON"
    },
    "resource" : {
      "_uri" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc"
    },
    "type" : "DentalPolicy_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Basic", "Major", "Preventive" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_IndividualMaximum_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 750,
    "extension" : {
      "_type" : "JsonType",
      "term" : {
        "effectiveDate" : "2024-08-01T00:00:00Z",
        "expirationDate" : "2025-07-31T23:59:59.999Z"
      },
      "appliedToServices" : [ "Implants" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_ImplantsMaximum_Annual_Implants",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "TMJ" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_TMJMaximum_Lifetime_TMJ",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "INN"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//f62c553b-3c0d-4b47-810d-c712b81a6c47"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 1000,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "CosmeticServices" ],
      "networkType" : "OON"
    },
    "type" : "DentalPolicy_CosmeticMaximum_Lifetime_CosmeticServices",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "geroot://Customer/INDIVIDUALCUSTOMER//5515a607-c15e-402b-9b84-e423eac2ad7f"
    },
    "_type" : "CapAccumulatorTransactionData"
  } ],
  "sourceURI" : "gentity://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc/2",
  "policyURI" : "capPolicy://PAS/geroot://PolicySummary/DNIndividual/policy/c5fe9c6b-8b7d-3b80-9f24-933b00dcaabc",
  "transactionTimestamp" : ignore,
  "customerURI" : "geroot://Customer/INDIVIDUALCUSTOMER//b2b3776b-7901-341c-8264-413cb7ee2164",
  "_modelName" : "CapAccumulatorTransaction",
  "_modelVersion" : "1",
  "_modelType" : "CapAccumulatorTransactionEntry",
  "_type" : "CapAccumulatorTransactionEntity"
}