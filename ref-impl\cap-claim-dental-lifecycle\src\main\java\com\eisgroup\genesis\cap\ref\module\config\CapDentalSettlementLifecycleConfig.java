/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.policy.api.ClaimPolicyProjectionTypeResolver;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementInitInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator.CapDentalSettlementValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.generator.impl.SimpleNumberGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import org.springframework.context.annotation.Bean;

/**
 * Claim Dental Settlement lifecycle services configuration.
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalSettlementLifecycleConfig {

    @Bean
    public EntityNumberGenerator settlementNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("claim_settlement_number", "S%d", sequenceGenerator);
    }

    @Bean
    public ClaimPolicyProjectionTypeResolver settlementPolicyProjectionTypeResolver() {
        return targetEntity -> {
            if (targetEntity instanceof RootEntity && CapSettlement.NAME.equals(((RootEntity) targetEntity).getModelType())) {
                return "settlement";
            } else {
                throw new IllegalArgumentException("Cannot resolve claim policy projection type from the provided target entity.");
            }
        };
    }

    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalSettlementValidator capDentalSettlementValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalSettlementValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalSettlementInitInputValidator capDentalSettlementInitInputValidator(
            CapDentalSettlementIndexResolver capDentalSettlementIndexResolver,
            EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalSettlementInitInputValidator(capDentalSettlementIndexResolver, entityLinkResolverRegistry);
    }

}
