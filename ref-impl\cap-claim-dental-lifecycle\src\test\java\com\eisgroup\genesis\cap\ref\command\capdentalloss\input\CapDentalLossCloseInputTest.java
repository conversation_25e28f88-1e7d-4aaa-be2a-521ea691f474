/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.input;

import com.eisgroup.genesis.test.utils.JsonUtils;
import org.junit.Test;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalLossCloseInputTest {
    private CapDentalLossCloseInput input = new CapDentalLossCloseInput(JsonUtils.load(
            "requestSamples/lossCloseInput.json"));

    @Test
    public void shouldReturnReasonCd() {
        //when
        String result = input.getReasonCd();

        //then
        assertThat(result, equalTo("Paid"));
    }
}
