/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalUnsuspendLossPaymentSchedulesInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.model.dentalinternal.SuspendActivePaymentSchedulesOutput;
import com.eisgroup.genesis.factory.model.dentalinternal.UnsuspendPaymentSchedulesOutput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.Variation;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.cap.financial.command.CapPaymentScheduleCommands.UNSUSPEND_PAYMENT_SCHEDULE;

/**
 * Unsuspends all Active {@link com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity}
 * related to specific {@link com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity}
 *
 * <AUTHOR>
 * @since 22.10
 */
public class CapDentalUnsuspendLossPaymentSchedulesHandler implements ProductCommandHandler<CapDentalUnsuspendLossPaymentSchedulesInput, UnsuspendPaymentSchedulesOutput> {

    private static final List<String> APPLICABLE_STATES = List.of("Suspended");
    private static final String DENTAL_INTERNAL_MODEL = "DentalInternal";

    @Autowired
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;

    @Autowired
    private CommandPublisher commandPublisher;

    @Nonnull
    @Override
    public UnsuspendPaymentSchedulesOutput load(@Nonnull CapDentalUnsuspendLossPaymentSchedulesInput request) {
        return Lazy.of(ModelInstanceFactory.createInstance(DENTAL_INTERNAL_MODEL, "1", UnsuspendPaymentSchedulesOutput.class.getSimpleName()))
            .cast(UnsuspendPaymentSchedulesOutput.class).get();
    }

    @Nonnull
    @Override
    public UnsuspendPaymentSchedulesOutput execute(@Nonnull CapDentalUnsuspendLossPaymentSchedulesInput request, @Nonnull UnsuspendPaymentSchedulesOutput entity) {
        return Lazy.from(() ->buildPaymentSchedulesLinks(request, entity))
            .map(unsuspendedPaymentSchedules -> {
                entity.toJson().remove(BaseKey.ATTRIBUTE_NAME);
                entity.setOriginSource(request.getOriginSource());
                entity.setUnsuspendedSchedules(unsuspendedPaymentSchedules.get());
                return entity;
            }).get();
    }

    public Lazy<List<EntityLink<RootEntity>>> buildPaymentSchedulesLinks(@Nonnull CapDentalUnsuspendLossPaymentSchedulesInput request, @Nonnull UnsuspendPaymentSchedulesOutput entity) {
        return Lazy.from(()->capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(request.getOriginSource(), getApplicableStates())
            .flatMap(this::unsuspendPaymentSchedule).collect(Collectors.toList()));
    }



    @Nonnull
    @Override
    public UnsuspendPaymentSchedulesOutput save(@Nonnull CapDentalUnsuspendLossPaymentSchedulesInput request, @Nonnull UnsuspendPaymentSchedulesOutput entity) {
        return Lazy.of(entity).get();
    }

    protected List<String> getApplicableStates() {
        return APPLICABLE_STATES;
    }

    protected Lazy<EntityLink<RootEntity>> unsuspendPaymentSchedule(CapDentalPaymentScheduleIdxEntity indexEntity) {
        EntityLink<RootEntity> paymentScheduleLink = new EntityLink<>(RootEntity.class, indexEntity.getPaymentSchedule());
        FactoryLink link = new FactoryLink(paymentScheduleLink);
        Command command = new Command(Variation.INVARIANT.getName(), UNSUSPEND_PAYMENT_SCHEDULE, createCommandInput(link));
        return commandPublisher.publishLocally(command, link.getModelName())
                .filter(commandResult -> !commandResult.isFailure())
                .map(commandResult -> paymentScheduleLink);
    }

    private JsonObject createCommandInput(FactoryLink link) {
        RootEntityKey key = new RootEntityKey(link.getRootId(), link.getRevisionNo());
        return new IdentifierRequest(key).toJson();
    }

    @Override
    public String getName() {
        return CapDentalPaymentScheduleCommands.UNSUSPEND_LOSS_PAYMENT_SCHEDULES;
    }
}
