/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade;

import java.util.Arrays;
import java.util.Collection;

import com.eisgroup.genesis.cap.transformation.endpoint.CapEntityEndpoint;
import com.eisgroup.genesis.cap.transformation.endpoint.ModelAwareTransformationEndpoint;
import com.eisgroup.genesis.facade.endpoint.command.CommandEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.LoadEntityRestEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.LoadHistoryEndpoint;
import com.eisgroup.genesis.facade.endpoint.load.link.EntityLinkEndpoint;
import com.eisgroup.genesis.facade.endpoint.model.ModelFacade;
import com.eisgroup.genesis.facade.module.EndpointPackage;
import com.eisgroup.genesis.facade.module.FacadeModule;

/**
 * Facade for Dental Payment Schedule endpoints.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleFacade implements FacadeModule {

    @Override
    public Collection<EndpointPackage> getEndpoints() {
        return Arrays.asList(new ModelFacade(),
                new CommandEndpoint(),
                new LoadEntityRestEndpoint<>(),
                new LoadHistoryEndpoint<>(),
                new CapEntityEndpoint(),
                new ModelAwareTransformationEndpoint(),
                new EntityLinkEndpoint());
    }

    @Override
    public String getModelType() {
        return "CapPaymentSchedule";
    }

    @Override
    public String getModelName() {
        return "CapDentalPaymentSchedule";
    }

    @Override
    public int getFacadeVersion() {
        return 1;
    }

    @Override
    public String getModelVersion() {
        return "1";
    }
}
