Common.projectSettings

lazy val integrationEisGenesisDentalLoss  = project.in(file("integration-eisgenesis-dental-loss"))
lazy val integrationEisGenesisDentalSearch = project.in(file("integration-eisgenesis-dental-search"))
lazy val integrationEisGenesisDentalSettlement = project.in(file("integration-eisgenesis-dental-settlement"))

lazy val integrationEisGenesisDental = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .aggregate(
    integrationEisGenesisDentalLoss,
    integrationEisGenesisDentalSearch,
    integrationEisGenesisDentalSettlement
  )
  .dependsOn(
    integrationEisGenesisDentalLoss,
    integrationEisGenesisDentalSearch,
    integrationEisGenesisDentalSettlement
  )
