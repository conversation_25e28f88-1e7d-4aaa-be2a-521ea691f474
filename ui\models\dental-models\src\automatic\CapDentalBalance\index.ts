// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "ModelType"
  ],
  "moduleType":"CapBalance",
  "name":"CapDentalBalance",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalBalanceEntity"
  },
  "storeDeterminants":[
    "ModelName"
  ],
  "types":{
    "CapDentalBalanceAmountAllocationRulesInput":{
      "attributes":{
        "balanceTransaction":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance amount allocation rules engine input attribute. Balance transcation with amount is allocated."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance amount allocation rules engine input attribute. Balance transcation with amount is allocated.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balanceTransaction",
          "type":{
            "type":"CapDentalBalancePaymentEntity"
          }
        },
        "balances":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance amount allocation rules engine input attribute. List of the balances."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance amount allocation rules engine input attribute. List of the balances.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balances",
          "type":{
            "type":"CapDentalBalanceEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Input for balance amount allocation rules."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Input for balance amount allocation rules.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceAmountAllocationRulesInput",
      "references":{
      }
    },
    "CapDentalBalanceAmountAllocationRulesOutput":{
      "attributes":{
        "balanceTransaction":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance amount allocation rules engine output attribute. List of the generated allocations."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance amount allocation rules engine output attribute. List of the generated allocations.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balanceTransaction",
          "type":{
            "type":"CapDentalBalancePaymentEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Output of balance amount allocation rules."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Output of balance amount allocation rules.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceAmountAllocationRulesOutput",
      "references":{
      }
    },
    "CapDentalBalanceCalculationRulesInput":{
      "attributes":{
        "actualPayments":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance calculation rules engine input attribute. List of the payment transactions in the databse."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance calculation rules engine input attribute. List of the payment transactions in the databse.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"actualPayments",
          "type":{
            "type":"CapDentalBalancePaymentEntity"
          }
        },
        "adjustmentPayments":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance calculation rules engine input attribute. List of the balance adjustment payment variations."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance calculation rules engine input attribute. List of the balance adjustment payment variations.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"adjustmentPayments",
          "type":{
            "type":"CapDentalBalancePaymentEntity"
          }
        },
        "scheduledPayments":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance calculation rules engine input attribute. List of the scheduled payments."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance calculation rules engine input attribute. List of the scheduled payments.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"scheduledPayments",
          "type":{
            "type":"CapDentalBalancePaymentEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Input for balance calculation rules."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Input for balance calculation rules.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceCalculationRulesInput",
      "references":{
      }
    },
    "CapDentalBalanceCalculationRulesOutput":{
      "attributes":{
        "balances":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance calculation rules engine output attribute. List of generated balances."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance calculation rules engine output attribute. List of generated balances.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balances",
          "type":{
            "type":"CapDentalBalanceEntity"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Output of balance calculation rules."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Output of balance calculation rules.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceCalculationRulesOutput",
      "references":{
      }
    },
    "CapDentalBalanceEntity":{
      "attributes":{
        "balanceItems":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of how the balance was calculated."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of how the balance was calculated.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balanceItems",
          "type":{
            "type":"CapDentalBalanceItemEntity"
          }
        },
        "creationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The date when the balance entity was created."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The date when the balance entity was created.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"creationDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "totalBalanceAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Total calculated balance."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Total calculated balance.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"totalBalanceAmount",
          "type":{
            "type":"MONEY"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The Root Entity of Balance Domain."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      [
                        "CapBalance"
                      ]
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The Root Entity of Balance Domain.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
                "CapBalance"
              ]
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "modelTypes":[
                  ]
                }
              },
              "parents":[
              ],
              "typeName":"RootEntity"
            }
          ],
          "typeName":"CapDentalBalance"
        }
      ],
      "baseTypes":[
        "CapDentalBalance"
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the related dental claim."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the related dental claim.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "payee":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the CEM."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the CEM.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"payee",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "The Root Entity of Balance Details Domain."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"The Root Entity of Balance Details Domain.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemActualAllocationAdditionEntity":{
      "attributes":{
        "additionSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition subtype."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition subtype.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"additionSubType",
          "type":{
            "type":"STRING"
          }
        },
        "additionType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition type. For example: Rehabilitation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition type. For example: Rehabilitation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"additionType",
          "type":{
            "type":"STRING"
          }
        },
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "balancedAppliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Actual addition amount after applying adjustment allocations addition amounts."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Actual addition amount after applying adjustment allocations addition amounts.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balancedAppliedAmount",
          "type":{
            "type":"MONEY"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationAddition"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationAddition"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Addition details applied to the actual allocation."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Addition details applied to the actual allocation.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemActualAllocationAdditionEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemActualAllocationEntity":{
      "attributes":{
        "adjustmentAllocations":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of allocations related to the payment from the balance adjustment payment variations."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of allocations related to the payment from the balance adjustment payment variations.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"adjustmentAllocations",
          "type":{
            "type":"CapDentalBalanceItemAdjustmentAllocationEntity"
          }
        },
        "allocationAdditions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of additions applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of additions applied to the allocation.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"allocationAdditions",
          "type":{
            "type":"CapDentalBalanceItemActualAllocationAdditionEntity"
          }
        },
        "allocationBalancedGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Actual gross amount after applying adjustment allocations gross amounts."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Actual gross amount after applying adjustment allocations gross amounts.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"allocationBalancedGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Gross Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Gross Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationLossInfo":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation loss details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation loss details.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"allocationLossInfo",
          "type":{
            "type":"CapDentalBalanceItemAllocationLossInfoEntity"
          }
        },
        "allocationNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated allocation NET Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated allocation NET Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationPayableItem":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of what allocation is paid for."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of what allocation is paid for.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationPayableItem",
          "type":{
            "type":"CapDentalPaymentAllocationPayableItemEntity"
          }
        },
        "allocationReductions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of reductions applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of reductions applied to the allocation.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"allocationReductions",
          "type":{
            "type":"CapDentalBalanceItemActualAllocationReductionEntity"
          }
        },
        "allocationTaxes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of taxes applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of taxes applied to the allocation.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"allocationTaxes",
          "type":{
            "type":"CapDentalBalanceItemActualAllocationTaxEntity"
          }
        },
        "scheduledAllocations":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of allocations related to the payment from the schedule."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of allocations related to the payment from the schedule.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"scheduledAllocations",
          "type":{
            "type":"CapDentalBalanceItemScheduledAllocationEntity"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance item allocation details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance item allocation details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocation"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocation"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the claim settlement."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the claim settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Actual allocation details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Actual allocation details.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemActualAllocationEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemActualAllocationReductionEntity":{
      "attributes":{
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "balancedAppliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Actual reduction amount after applying adjustment allocations reduction amounts."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Actual reduction amount after applying adjustment allocations reduction amounts.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balancedAppliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "reductionSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax subtype. For example: Deduction CHSP."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax subtype. For example: Deduction CHSP.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reductionSubType",
          "type":{
            "type":"STRING"
          }
        },
        "reductionType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction type. For example: Offset."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction type. For example: Offset.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reductionType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationReduction"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationReduction"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Reduction details applied to the actual allocation."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Reduction details applied to the actual allocation.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemActualAllocationReductionEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemActualAllocationTaxEntity":{
      "attributes":{
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "balancedAppliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Actual tax amount after applying adjustment allocations tax amounts."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Actual tax amount after applying adjustment allocations tax amounts.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balancedAppliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "taxSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax subtype. For example: FSST."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax subtype. For example: FSST.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"taxSubType",
          "type":{
            "type":"STRING"
          }
        },
        "taxType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax type. For example: Federal."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax type. For example: Federal.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"taxType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationTax"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationTax"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Tax details applied to the actual allocation."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Tax details applied to the actual allocation.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemActualAllocationTaxEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemAdjustmentAllocationEntity":{
      "attributes":{
        "allocationAdditions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of additions applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of additions applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationAdditions",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationAddition"
          }
        },
        "allocationGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Gross Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Gross Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated allocation NET Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated allocation NET Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationPayableItem":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of what allocation is paid for."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of what allocation is paid for.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationPayableItem",
          "type":{
            "type":"CapDentalPaymentAllocationPayableItemEntity"
          }
        },
        "allocationReductions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of reductions applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of reductions applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationReductions",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationReduction"
          }
        },
        "allocationTaxes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of taxes applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of taxes applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationTaxes",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationTax"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance item allocation details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance item allocation details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocation"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocation"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the claim settlement."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the claim settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Adjustment allocation details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Adjustment allocation details.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemAdjustmentAllocationEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemAllocationLossInfoEntity":{
      "attributes":{
        "lossType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Claim loss type."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Claim loss type.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"lossType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "lossSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the claim."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the claim.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"lossSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Actual allocation loss details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Actual allocation loss details.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemAllocationLossInfoEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemEntity":{
      "attributes":{
        "actualAllocations":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of allocations in the payments."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of allocations in the payments.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"actualAllocations",
          "type":{
            "type":"CapDentalBalanceItemActualAllocationEntity"
          }
        },
        "actualNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Amount that was paid."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Amount that was paid.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"actualNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "balancedNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Amount that was paid including applied adjustments."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Amount that was paid including applied adjustments.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"balancedNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "paymentDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment post date."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment post date.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"paymentDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "paymentNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment number where payable item appeared."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment number where payable item appeared.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"paymentNumber",
          "type":{
            "type":"STRING"
          }
        },
        "scheduledNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Amount that had to be paid."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Amount that had to be paid.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"scheduledNetAmount",
          "type":{
            "type":"MONEY"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Details of how the balance was calculated."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Details of how the balance was calculated.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemEntity",
      "references":{
      }
    },
    "CapDentalBalanceItemScheduledAllocationEntity":{
      "attributes":{
        "allocationAdditions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of additions applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of additions applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationAdditions",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationAddition"
          }
        },
        "allocationGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Gross Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Gross Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Calculated allocation NET Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Calculated allocation NET Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationPayableItem":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of what allocation is paid for."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of what allocation is paid for.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationPayableItem",
          "type":{
            "type":"CapDentalPaymentAllocationPayableItemEntity"
          }
        },
        "allocationReductions":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of reductions applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of reductions applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationReductions",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationReduction"
          }
        },
        "allocationTaxes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "List of taxes applied to the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"List of taxes applied to the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationTaxes",
          "type":{
            "type":"CapDentalBaseBalanceItemAllocationTax"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Balance item allocation details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Balance item allocation details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocation"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocation"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the claim settlement."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the claim settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Scheduled allocation details."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Scheduled allocation details.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalanceItemScheduledAllocationEntity",
      "references":{
      }
    },
    "CapDentalBalancePaymentEntity":{
      "attributes":{
        "creationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "A date when the payment was created."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"A date when the payment was created.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"creationDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "direction":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines if payment is incoming or outgoing."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.lookups.constraints.Lookup":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "PaymentDirection"
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines if payment is incoming or outgoing.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            },
            "class com.eisgroup.genesis.lookups.constraints.Lookup":{
              "lookupName":"PaymentDirection"
            }
          },
          "name":"direction",
          "type":{
            "type":"STRING"
          }
        },
        "messages":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment messages."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment messages.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"messages",
          "type":{
            "type":"MessageType"
          }
        },
        "paymentDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment transaction details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment transaction details.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"paymentDetails",
          "type":{
            "type":"CapDentalPaymentDetailsEntity"
          }
        },
        "paymentNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Final total NET amount of the payment."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Final total NET amount of the payment.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"paymentNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "paymentNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "A unique ID, that is assigned to a new payment."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"A unique ID, that is assigned to a new payment.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"paymentNumber",
          "type":{
            "type":"STRING"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment transaction state."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment transaction state.",
              "messageBundle":"domain-messages/CapDentalBalance/description"
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines manual payment/recovery information."
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines manual payment/recovery information.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"CapPaymentInfo"
        }
      ],
      "baseTypes":[
        "CapPaymentInfo"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Defines payment transaction information."
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Defines payment transaction information.",
          "messageBundle":"domain-messages/CapDentalBalance/description"
        }
      },
      "links":{
      },
      "name":"CapDentalBalancePaymentEntity",
      "references":{
      }
    },
    "CapDentalBaseBalanceItemAllocationAddition":{
      "attributes":{
        "additionSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition subtype."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition subtype.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"additionSubType",
          "type":{
            "type":"STRING"
          }
        },
        "additionType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition type. For example: Rehabilitation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition type. For example: Rehabilitation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"additionType",
          "type":{
            "type":"STRING"
          }
        },
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Addition details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Addition details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationAddition"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationAddition"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Addition details applied to the allocation."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Addition details applied to the allocation.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalBaseBalanceItemAllocationAddition",
      "references":{
      }
    },
    "CapDentalBaseBalanceItemAllocationReduction":{
      "attributes":{
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "reductionSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax subtype. For example: Deduction CHSP."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax subtype. For example: Deduction CHSP.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reductionSubType",
          "type":{
            "type":"STRING"
          }
        },
        "reductionType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction type. For example: Offset."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction type. For example: Offset.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reductionType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Reduction details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Reduction details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationReduction"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationReduction"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Reduction details applied to the allocation."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Reduction details applied to the allocation.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalBaseBalanceItemAllocationReduction",
      "references":{
      }
    },
    "CapDentalBaseBalanceItemAllocationTax":{
      "attributes":{
        "appliedAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax amount applied to the allocaiton."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax amount applied to the allocaiton.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliedAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "taxSubType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax subtype. For example: FSST."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax subtype. For example: FSST.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"taxSubType",
          "type":{
            "type":"STRING"
          }
        },
        "taxType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax type. For example: Federal."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax type. For example: Federal.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"taxType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Tax details applied to the allocation."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Tax details applied to the allocation.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalBaseBalanceItemAllocationTax"
        }
      ],
      "baseTypes":[
        "CapDentalBaseBalanceItemAllocationTax"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Tax details applied to the allocation."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Tax details applied to the allocation.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalBaseBalanceItemAllocationTax",
      "references":{
      }
    },
    "CapDentalPaymentAllocationAccumulatorDetailsEntity":{
      "attributes":{
        "accumulatorAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator amount used for the allocation."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator amount used for the allocation.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"accumulatorAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "accumulatorType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"accumulatorType",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategories":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure category(s) maximum/deductible applies to."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure category(s) maximum/deductible applies to.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliesToProcedureCategories",
          "type":{
            "type":"STRING"
          }
        },
        "appliesToProcedureCategory":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines to which procedure category accumulator applies to."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines to which procedure category accumulator applies to.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"appliesToProcedureCategory",
          "type":{
            "type":"STRING"
          }
        },
        "networkType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Network type for accumulator."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Network type for accumulator.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"networkType",
          "type":{
            "type":"STRING"
          }
        },
        "renewalType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Renewal type for accumulator."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Renewal type for accumulator.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"renewalType",
          "type":{
            "type":"STRING"
          }
        },
        "term":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.comparison.features.NonComparable":{
            },
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.comparison.features.NonComparable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Accumulator Term."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Accumulator Term.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"term",
          "type":{
            "type":"Term"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation acumulator details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation acumulator details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalPaymentAllocationAccumulatorDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationAccumulatorDetailsEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Allocation acumulator details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Allocation acumulator details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationAccumulatorDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationDentalDetailsEntity":{
      "attributes":{
        "accumulatorDetails":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation acumulator details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation acumulator details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"accumulatorDetails",
          "type":{
            "type":"CapDentalPaymentAllocationAccumulatorDetailsEntity"
          }
        },
        "transactionTypeCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental claim transaction type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental claim transaction type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"transactionTypeCd",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"CapDentalPaymentAllocationDentalDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationDentalDetailsEntity"
      ],
      "extLinks":{
        "patient":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "URI to the patient."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"URI to the patient.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"patient",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Dental allocation details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Dental allocation details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationDentalDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationEntity":{
      "attributes":{
        "allocationDentalDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationDentalDetails",
          "type":{
            "type":"CapDentalPaymentAllocationDentalDetailsEntity"
          }
        },
        "allocationGrossAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Prorated Gross Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Prorated Gross Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationGrossAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationLobCd":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation Line Of Business code."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation Line Of Business code.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationLobCd",
          "type":{
            "type":"STRING"
          }
        },
        "allocationLossInfo":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation claim details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation claim details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationLossInfo",
          "type":{
            "type":"CapDentalPaymentAllocationLossInfoEntity"
          }
        },
        "allocationNetAmount":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Prorated NET Amount."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Prorated NET Amount.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"allocationNetAmount",
          "type":{
            "type":"MONEY"
          }
        },
        "allocationPayableItem":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Details of what is paid for."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Details of what is paid for.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationPayableItem",
          "type":{
            "type":"CapDentalPaymentAllocationPayableItemEntity"
          }
        },
        "reserveType":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation reserve type."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation reserve type.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"reserveType",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment allocations details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment allocations details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Entity for Payment/Recovery allocation information."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Entity for Payment/Recovery allocation information.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocation"
            }
          ],
          "typeName":"CapDentalPaymentAllocationEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationEntity"
      ],
      "extLinks":{
        "allocationSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim Loss settlement."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim Loss settlement.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"allocationSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment allocations details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation tax split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation tax split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"taxSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationTaxSplitResult"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation addition split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation addition split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"additionSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationAdditionSplitResult"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment allocation reduction split results"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment allocation reduction split results",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"reductionSplitResults",
                      "type":{
                        "type":"CapPaymentAllocationReductionSplitResult"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment allocations details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationLossInfoEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Allocation claim details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Allocation claim details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment allocation loss info details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment allocation loss info details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocationLossInfo"
            }
          ],
          "typeName":"CapDentalPaymentAllocationLossInfoEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationLossInfoEntity"
      ],
      "extLinks":{
        "lossSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the Claim Loss."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the Claim Loss.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"lossSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Allocation claim details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Allocation claim details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationLossInfoEntity",
      "references":{
      }
    },
    "CapDentalPaymentAllocationPayableItemEntity":{
      "attributes":{
        "orthoMonth":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Month number for which allocation is paid."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Month number for which allocation is paid.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"orthoMonth",
          "type":{
            "type":"NUMBER"
          }
        },
        "procedureID":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Related Settlement Result entry's procedure ID."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Related Settlement Result entry's procedure ID.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"procedureID",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Dental allocation payable item details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Dental allocation payable item details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment allocation payable item."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment allocation payable item.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentAllocationPayableItem"
            }
          ],
          "typeName":"CapDentalPaymentAllocationPayableItemEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentAllocationPayableItemEntity"
      ],
      "extLinks":{
        "claimSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to dental claim."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to dental claim.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"claimSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Dental allocation payable item details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Dental allocation payable item details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentAllocationPayableItemEntity",
      "references":{
      }
    },
    "CapDentalPaymentDetailsEntity":{
      "attributes":{
        "payeeDetails":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment payee details."
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.model.features.KrakenChildContext":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                },
                "class com.eisgroup.genesis.model.features.KrakenField":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment payee details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            },
            "class com.eisgroup.genesis.model.features.KrakenChildContext":{
            },
            "class com.eisgroup.genesis.model.features.KrakenField":{
            }
          },
          "name":"payeeDetails",
          "type":{
            "type":"CapDentalPaymentPayeeDetailsEntity"
          }
        },
        "paymentAllocations":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Payment allocations details."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Payment allocations details.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"paymentAllocations",
          "type":{
            "type":"CapDentalPaymentAllocationEntity"
          }
        },
        "paymentDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "The payment post date."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"The payment post date.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"paymentDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Subentity which extends to payment/recovery payee details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Subentity which extends to payment/recovery payee details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentDetails"
            }
          ],
          "typeName":"CapDentalPaymentDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentDetailsEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment additions"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment additions",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentAdditions",
                      "type":{
                        "type":"CapPaymentAddition"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment reductions"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment reductions",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentReductions",
                      "type":{
                        "type":"CapPaymentReduction"
                      }
                    },
                    {
                      "cardinality":"MULTIPLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment taxes"
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment taxes",
                          "messageBundle":"domain-messages/description"
                        }
                      },
                      "name":"paymentTaxes",
                      "type":{
                        "type":"CapPaymentTax"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentDetailsEntity",
      "references":{
      }
    },
    "CapDentalPaymentPayeeDetailsEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "An object which extends payment payee details."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"An object which extends payment payee details.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
            {
              "features":{
                "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                  "features":{
                    "class com.eisgroup.genesis.factory.features.Description":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                          "Defines payment payee details."
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    },
                    "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                      "constructorArgs":{
                        "@array":"java.lang.Object",
                        "values":[
                        ]
                      },
                      "inherited":false
                    }
                  }
                },
                "class com.eisgroup.genesis.factory.features.Description":{
                  "descriptionText":"Defines payment payee details.",
                  "messageBundle":"domain-messages/description"
                },
                "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                }
              },
              "parents":[
              ],
              "typeName":"CapPaymentPayeeDetails"
            }
          ],
          "typeName":"CapDentalPaymentPayeeDetailsEntity"
        }
      ],
      "baseTypes":[
        "CapDentalPaymentPayeeDetailsEntity"
      ],
      "extLinks":{
        "payee":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Link to the CEM."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Link to the CEM.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":true
            }
          },
          "name":"payee",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "An object which extends payment payee details."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                    {
                      "cardinality":"SINGLE",
                      "features":{
                        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
                          "features":{
                            "class com.eisgroup.genesis.factory.features.Exclude":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.features.Description":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                  "Payment method details"
                                ]
                              },
                              "inherited":true
                            },
                            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                              "constructorArgs":{
                                "@array":"java.lang.Object",
                                "values":[
                                ]
                              },
                              "inherited":true
                            }
                          }
                        },
                        "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
                          "complexType":true
                        },
                        "class com.eisgroup.genesis.factory.features.Exclude":{
                        },
                        "class com.eisgroup.genesis.factory.features.Description":{
                          "descriptionText":"Payment method details",
                          "messageBundle":"domain-messages/description"
                        },
                        "class com.eisgroup.genesis.factory.model.features.Deprecated":{
                        }
                      },
                      "name":"paymentMethodDetails",
                      "type":{
                        "type":"CapPaymentMethodDetails"
                      }
                    }
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.features.Deprecated":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":true
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"An object which extends payment payee details.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.features.Deprecated":{
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentPayeeDetailsEntity",
      "references":{
      }
    },
    "MessageType":{
      "attributes":{
        "code":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message code."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message code.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"code",
          "type":{
            "type":"STRING"
          }
        },
        "message":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message text."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message text.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"message",
          "type":{
            "type":"STRING"
          }
        },
        "severity":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message severity."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message severity.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"severity",
          "type":{
            "type":"STRING"
          }
        },
        "source":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Message source."
                    ]
                  },
                  "inherited":true
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Message source.",
              "messageBundle":"domain-messages/description"
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"source",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.features.Description":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "Defines a message that can be forwarded to the user on some exceptions."
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.features.Description":{
              "descriptionText":"Defines a message that can be forwarded to the user on some exceptions.",
              "messageBundle":"domain-messages/description"
            }
          },
          "parents":[
          ],
          "typeName":"MessageType"
        }
      ],
      "baseTypes":[
        "MessageType"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Description":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  "Defines a message that can be forwarded to the user on some exceptions."
                ]
              },
              "inherited":true
            },
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.features.Description":{
          "descriptionText":"Defines a message that can be forwarded to the user on some exceptions.",
          "messageBundle":"domain-messages/description"
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"MessageType",
      "references":{
      }
    },
    "Term":{
      "attributes":{
        "effectiveDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"effectiveDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "expirationDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            },
            "class com.eisgroup.genesis.factory.model.types.modeled.features.Inherited":{
              "complexType":false
            }
          },
          "name":"expirationDate",
          "type":{
            "type":"DATETIME"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            }
          },
          "parents":[
          ],
          "typeName":"Term"
        }
      ],
      "baseTypes":[
        "Term"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                ]
              },
              "inherited":false
            }
          }
        },
        "class com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType":{
        }
      },
      "links":{
      },
      "name":"Term",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalBalance {
    export type Variations = never


    export class CapDentalBalanceAmountAllocationRulesInput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceAmountAllocationRulesInput.name) }
        readonly balanceTransaction?: CapDentalBalancePaymentEntity
        readonly balances: CapDentalBalanceEntity[] = []
    }

    export class CapDentalBalanceAmountAllocationRulesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceAmountAllocationRulesOutput.name) }
        readonly balanceTransaction?: CapDentalBalancePaymentEntity
    }

    export class CapDentalBalanceCalculationRulesInput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceCalculationRulesInput.name) }
        readonly actualPayments: CapDentalBalancePaymentEntity[] = []
        readonly adjustmentPayments: CapDentalBalancePaymentEntity[] = []
        readonly scheduledPayments: CapDentalBalancePaymentEntity[] = []
    }

    export class CapDentalBalanceCalculationRulesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceCalculationRulesOutput.name) }
        readonly balances: CapDentalBalanceEntity[] = []
    }

    export class CapDentalBalanceEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBalance, MAPI.RootBusinessType {
    constructor() { super(CapDentalBalanceEntity.name) }
        readonly _modelName: string = 'CapDentalBalance'
        readonly _modelType: string = 'CapBalance'
        readonly _modelVersion?: string = '1'
        readonly balanceItems: CapDentalBalanceItemEntity[] = []
        readonly creationDate?: Date
        readonly originSource?: MAPI.ExternalLink
        readonly payee?: MAPI.ExternalLink
        readonly totalBalanceAmount?: Money
    }

    export class CapDentalBalanceItemActualAllocationAdditionEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationAddition {
    constructor() { super(CapDentalBalanceItemActualAllocationAdditionEntity.name) }
        readonly additionSubType?: string
        readonly additionType?: string
        readonly appliedAmount?: Money
        readonly balancedAppliedAmount?: Money
    }

    export class CapDentalBalanceItemActualAllocationEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocation {
    constructor() { super(CapDentalBalanceItemActualAllocationEntity.name) }
        readonly adjustmentAllocations: CapDentalBalanceItemAdjustmentAllocationEntity[] = []
        readonly allocationAdditions: CapDentalBalanceItemActualAllocationAdditionEntity[] = []
        readonly allocationBalancedGrossAmount?: Money
        readonly allocationGrossAmount?: Money
        readonly allocationLossInfo?: CapDentalBalanceItemAllocationLossInfoEntity
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationReductions: CapDentalBalanceItemActualAllocationReductionEntity[] = []
        readonly allocationSource?: MAPI.ExternalLink
        readonly allocationTaxes: CapDentalBalanceItemActualAllocationTaxEntity[] = []
        readonly scheduledAllocations: CapDentalBalanceItemScheduledAllocationEntity[] = []
    }

    export class CapDentalBalanceItemActualAllocationReductionEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationReduction {
    constructor() { super(CapDentalBalanceItemActualAllocationReductionEntity.name) }
        readonly appliedAmount?: Money
        readonly balancedAppliedAmount?: Money
        readonly reductionSubType?: string
        readonly reductionType?: string
    }

    export class CapDentalBalanceItemActualAllocationTaxEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationTax {
    constructor() { super(CapDentalBalanceItemActualAllocationTaxEntity.name) }
        readonly appliedAmount?: Money
        readonly balancedAppliedAmount?: Money
        readonly taxSubType?: string
        readonly taxType?: string
    }

    export class CapDentalBalanceItemAdjustmentAllocationEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocation {
    constructor() { super(CapDentalBalanceItemAdjustmentAllocationEntity.name) }
        readonly allocationAdditions: CapDentalBaseBalanceItemAllocationAddition[] = []
        readonly allocationGrossAmount?: Money
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationReductions: CapDentalBaseBalanceItemAllocationReduction[] = []
        readonly allocationSource?: MAPI.ExternalLink
        readonly allocationTaxes: CapDentalBaseBalanceItemAllocationTax[] = []
    }

    export class CapDentalBalanceItemAllocationLossInfoEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceItemAllocationLossInfoEntity.name) }
        readonly lossSource?: MAPI.ExternalLink
        readonly lossType?: string
    }

    export class CapDentalBalanceItemEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalBalanceItemEntity.name) }
        readonly actualAllocations: CapDentalBalanceItemActualAllocationEntity[] = []
        readonly actualNetAmount?: Money
        readonly balancedNetAmount?: Money
        readonly paymentDate?: Date
        readonly paymentNumber?: string
        readonly scheduledNetAmount?: Money
    }

    export class CapDentalBalanceItemScheduledAllocationEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocation {
    constructor() { super(CapDentalBalanceItemScheduledAllocationEntity.name) }
        readonly allocationAdditions: CapDentalBaseBalanceItemAllocationAddition[] = []
        readonly allocationGrossAmount?: Money
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationReductions: CapDentalBaseBalanceItemAllocationReduction[] = []
        readonly allocationSource?: MAPI.ExternalLink
        readonly allocationTaxes: CapDentalBaseBalanceItemAllocationTax[] = []
    }

    export class CapDentalBalancePaymentEntity extends MAPI.BusinessEntity implements BusinessTypes.CapPaymentInfo {
    constructor() { super(CapDentalBalancePaymentEntity.name) }
        readonly creationDate?: Date
        readonly direction?: string
        readonly messages: MessageType[] = []
        readonly paymentDetails?: CapDentalPaymentDetailsEntity
        readonly paymentNetAmount?: Money
        readonly paymentNumber?: string
        readonly state?: string
    }

    export class CapDentalBaseBalanceItemAllocationAddition extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationAddition {
    constructor() { super(CapDentalBaseBalanceItemAllocationAddition.name) }
        readonly additionSubType?: string
        readonly additionType?: string
        readonly appliedAmount?: Money
    }

    export class CapDentalBaseBalanceItemAllocationReduction extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationReduction {
    constructor() { super(CapDentalBaseBalanceItemAllocationReduction.name) }
        readonly appliedAmount?: Money
        readonly reductionSubType?: string
        readonly reductionType?: string
    }

    export class CapDentalBaseBalanceItemAllocationTax extends MAPI.BusinessEntity implements BusinessTypes.CapDentalBaseBalanceItemAllocationTax {
    constructor() { super(CapDentalBaseBalanceItemAllocationTax.name) }
        readonly appliedAmount?: Money
        readonly taxSubType?: string
        readonly taxType?: string
    }

    export class CapDentalPaymentAllocationAccumulatorDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationAccumulatorDetailsEntity {
    constructor() { super(CapDentalPaymentAllocationAccumulatorDetailsEntity.name) }
        readonly accumulatorAmount?: Money
        readonly accumulatorType?: string
        readonly appliesToProcedureCategories: string[] = []
        readonly appliesToProcedureCategory?: string
        readonly networkType?: string
        readonly renewalType?: string
        readonly term?: Term
    }

    export class CapDentalPaymentAllocationDentalDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationDentalDetailsEntity {
    constructor() { super(CapDentalPaymentAllocationDentalDetailsEntity.name) }
        readonly accumulatorDetails: CapDentalPaymentAllocationAccumulatorDetailsEntity[] = []
        readonly patient?: MAPI.ExternalLink
        readonly transactionTypeCd?: string
    }

    export class CapDentalPaymentAllocationEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationEntity {
    constructor() { super(CapDentalPaymentAllocationEntity.name) }
        readonly allocationDentalDetails?: CapDentalPaymentAllocationDentalDetailsEntity
        readonly allocationGrossAmount?: Money
        readonly allocationLobCd?: string
        readonly allocationLossInfo?: CapDentalPaymentAllocationLossInfoEntity
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationSource?: MAPI.ExternalLink
        readonly reserveType?: string
    }

    export class CapDentalPaymentAllocationLossInfoEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationLossInfoEntity {
    constructor() { super(CapDentalPaymentAllocationLossInfoEntity.name) }
        readonly lossSource?: MAPI.ExternalLink
    }

    export class CapDentalPaymentAllocationPayableItemEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentAllocationPayableItemEntity {
    constructor() { super(CapDentalPaymentAllocationPayableItemEntity.name) }
        readonly claimSource?: MAPI.ExternalLink
        readonly orthoMonth?: number
        readonly procedureID?: string
    }

    export class CapDentalPaymentDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentDetailsEntity {
    constructor() { super(CapDentalPaymentDetailsEntity.name) }
        readonly payeeDetails?: CapDentalPaymentPayeeDetailsEntity
        readonly paymentAllocations: CapDentalPaymentAllocationEntity[] = []
        readonly paymentDate?: Date
    }

    export class CapDentalPaymentPayeeDetailsEntity extends MAPI.BusinessEntity implements BusinessTypes.CapDentalPaymentPayeeDetailsEntity {
    constructor() { super(CapDentalPaymentPayeeDetailsEntity.name) }
        readonly payee?: MAPI.ExternalLink
    }

    export class MessageType extends MAPI.BusinessEntity implements BusinessTypes.MessageType {
    constructor() { super(MessageType.name) }
        readonly code?: string
        readonly message?: string
        readonly severity?: string
        readonly source?: string
    }

    export class Term extends MAPI.BusinessEntity implements BusinessTypes.Term {
    constructor() { super(Term.name) }
        readonly effectiveDate?: Date
        readonly expirationDate?: Date
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalBalanceAmountAllocationRulesInput, ()=> new CapDentalBalanceAmountAllocationRulesInput)
    factory.registerEntity(CapDentalBalanceAmountAllocationRulesOutput, ()=> new CapDentalBalanceAmountAllocationRulesOutput)
    factory.registerEntity(CapDentalBalanceCalculationRulesInput, ()=> new CapDentalBalanceCalculationRulesInput)
    factory.registerEntity(CapDentalBalanceCalculationRulesOutput, ()=> new CapDentalBalanceCalculationRulesOutput)
    factory.registerEntity(CapDentalBalanceEntity, ()=> new CapDentalBalanceEntity)
    factory.registerEntity(CapDentalBalanceItemActualAllocationAdditionEntity, ()=> new CapDentalBalanceItemActualAllocationAdditionEntity)
    factory.registerEntity(CapDentalBalanceItemActualAllocationEntity, ()=> new CapDentalBalanceItemActualAllocationEntity)
    factory.registerEntity(CapDentalBalanceItemActualAllocationReductionEntity, ()=> new CapDentalBalanceItemActualAllocationReductionEntity)
    factory.registerEntity(CapDentalBalanceItemActualAllocationTaxEntity, ()=> new CapDentalBalanceItemActualAllocationTaxEntity)
    factory.registerEntity(CapDentalBalanceItemAdjustmentAllocationEntity, ()=> new CapDentalBalanceItemAdjustmentAllocationEntity)
    factory.registerEntity(CapDentalBalanceItemAllocationLossInfoEntity, ()=> new CapDentalBalanceItemAllocationLossInfoEntity)
    factory.registerEntity(CapDentalBalanceItemEntity, ()=> new CapDentalBalanceItemEntity)
    factory.registerEntity(CapDentalBalanceItemScheduledAllocationEntity, ()=> new CapDentalBalanceItemScheduledAllocationEntity)
    factory.registerEntity(CapDentalBalancePaymentEntity, ()=> new CapDentalBalancePaymentEntity)
    factory.registerEntity(CapDentalBaseBalanceItemAllocationAddition, ()=> new CapDentalBaseBalanceItemAllocationAddition)
    factory.registerEntity(CapDentalBaseBalanceItemAllocationReduction, ()=> new CapDentalBaseBalanceItemAllocationReduction)
    factory.registerEntity(CapDentalBaseBalanceItemAllocationTax, ()=> new CapDentalBaseBalanceItemAllocationTax)
    factory.registerEntity(CapDentalPaymentAllocationAccumulatorDetailsEntity, ()=> new CapDentalPaymentAllocationAccumulatorDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationDentalDetailsEntity, ()=> new CapDentalPaymentAllocationDentalDetailsEntity)
    factory.registerEntity(CapDentalPaymentAllocationEntity, ()=> new CapDentalPaymentAllocationEntity)
    factory.registerEntity(CapDentalPaymentAllocationLossInfoEntity, ()=> new CapDentalPaymentAllocationLossInfoEntity)
    factory.registerEntity(CapDentalPaymentAllocationPayableItemEntity, ()=> new CapDentalPaymentAllocationPayableItemEntity)
    factory.registerEntity(CapDentalPaymentDetailsEntity, ()=> new CapDentalPaymentDetailsEntity)
    factory.registerEntity(CapDentalPaymentPayeeDetailsEntity, ()=> new CapDentalPaymentPayeeDetailsEntity)
    factory.registerEntity(MessageType, ()=> new MessageType)
    factory.registerEntity(Term, ()=> new Term)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}