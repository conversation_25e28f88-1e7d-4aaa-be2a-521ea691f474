/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossCloseHandler;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalsettlementindex.CapDentalSettlementIdx;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolver;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

import java.util.Comparator;

/**
 * Validates {@link CapDentalLossCloseHandler} settlements are not in approved state
 *
 * <AUTHOR>
 * @since 22.7
 */
public class CapDentalClaimLossCloseValidator {

    private static final String APPROVED_SETTLEMENT_STATE = "Approved";

    private final EntityLinkResolverRegistry entityLinkResolverRegistry;
    private final CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    public CapDentalClaimLossCloseValidator(EntityLinkResolverRegistry entityLinkResolverRegistry, CapDentalSettlementIndexResolver capDentalSettlementIndexResolver) {
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
        this.capDentalSettlementIndexResolver = capDentalSettlementIndexResolver;
    }

    public Streamable<ErrorHolder> validateSettlementNotApproved(EntityLink<RootEntity> capLossUri) {
        return Streamable.from(resolveSettlement(capLossUri)
                .filter(settlement -> APPROVED_SETTLEMENT_STATE.equalsIgnoreCase(settlement.getState()))
                .map(closedLoss -> CapDentalClaimLossCloseErrorDefinition.SETTLEMENT_APPROVED.builder().build()));
    }

    private Lazy<CapSettlement> resolveSettlement(EntityLink<RootEntity> capLossUri) {
        return capDentalSettlementIndexResolver.resolveSettlementIndex(capLossUri)
                .sorted(Comparator.comparing(CapDentalSettlementIdx::getCreatedOn,Comparator.reverseOrder()))
                .findFirst()
                .flatMap(this::resolveSettlementFromIndex);
    }

    private Lazy<CapSettlement> resolveSettlementFromIndex(CapDentalSettlementIdx settlementIndex) {
        EntityLink<RootEntity> settlementId = new EntityLink<>(RootEntity.class, settlementIndex.getSettlementId());
        EntityLinkResolver linkResolver = entityLinkResolverRegistry.getByURIScheme(settlementId.getSchema());
        return linkResolver.resolve(settlementId, ReadContext.empty());
    }

    public static class CapDentalClaimLossCloseErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalClaimLossCloseErrorDefinition SETTLEMENT_APPROVED = new CapDentalClaimLossCloseErrorDefinition("clc001",
                "This command cannot be performed when Settlement is in Approved state.");

        private CapDentalClaimLossCloseErrorDefinition(String code, String message) {
            super(code, message);
        }

    }
}