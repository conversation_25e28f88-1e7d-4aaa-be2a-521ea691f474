Common.projectSettings

lazy val dentalLoss = project.in(file("dental-loss"))
lazy val dentalSearch = project.in(file("dental-search"))
lazy val dentalSettlement = project.in(file("dental-settlement"))

lazy val dental = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .aggregate(
    dentalLoss,
    dentalSearch,
    dentalSettlement
  )
  .dependsOn(
    dentalLoss,
    dentalSearch,
    dentalSettlement
  )
