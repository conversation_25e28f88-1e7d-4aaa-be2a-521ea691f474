/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

/**
 * Command names of dental balance.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalBalanceCommands {

    public static final String INIT_DENTAL_BALANCE = "initBalance";
    public static final String UPDATE_DENTAL_BALANCE = "updateBalance";

    public static final String CALCULATE_DENTAL_BALANCE ="calculateLossBalance";
}
