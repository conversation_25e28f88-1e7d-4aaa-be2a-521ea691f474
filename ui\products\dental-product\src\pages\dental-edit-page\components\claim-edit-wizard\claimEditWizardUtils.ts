import {Step} from '@eisgroup/form'
import {t} from '@eisgroup/i18n'

import {hasAuthorities, Privileges} from '../../../../utils'
import {CLAIM_MODE, CLAIM_MODE_TYPE, EditWizardTabIDs} from '../../constants'

export const getIsNextButtonDisabled = (loss, intakeWizardStore) => {
    const missingRequiredFields = !(loss?.policyId && loss?.lossDetail?.claimData?.patientRole?.registryId)
    if (missingRequiredFields) {
        return true
    }
    if (
        !hasAuthorities([Privileges.SUBMIT_LOSS]) &&
        intakeWizardStore.currentStep === EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB
    ) {
        return true
    }
    return false
}

export const getSteps = (from: CLAIM_MODE_TYPE, intakeWizardStore) => {
    const availableSteps = [
        {
            id: 'policyAndPatient',
            status: intakeWizardStore.stepsStatus?.[EditWizardTabIDs.POLICY_AND_PATIENT_TAB],
            title: t('dental-product:dental-claim-intake-wizard-policy-and-patient')
        },
        {
            id: 'claimInfo',
            status: intakeWizardStore.stepsStatus?.[EditWizardTabIDs.CLAIM_INFO_TAB],
            title: t('dental-product:dental-claim-intake-wizard-claim-info')
        },
        {
            id: 'uploadDocuments',
            status: intakeWizardStore.stepsStatus?.[EditWizardTabIDs.UPLOAD_DOCUMENTS_TAB],
            title: t('dental-product:dental-claim-intake-wizard-upload-documents')
        },
        {
            id: 'reviewClaimInfo',
            status: intakeWizardStore.stepsStatus?.[EditWizardTabIDs.REVIEW_CLAIM_INFO_TAB],
            title: t('dental-product:dental-claim-intake-wizard-review-claim-info')
        }
    ] as Step[]
    if (from === CLAIM_MODE.ADJUST) {
        availableSteps.splice(2, 1)
    }
    return availableSteps
}
