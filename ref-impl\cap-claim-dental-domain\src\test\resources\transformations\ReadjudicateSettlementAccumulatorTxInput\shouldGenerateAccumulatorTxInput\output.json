{
  "data" : [ {
    "amount" : -40,
    "extension" : {
      "_type" : "JsonType",
      "networkType" : "INN"
    },
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : -40,
    "extension" : {
      "_type" : "JsonType",
      "networkType" : "INN"
    },
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : -40,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Orthodontics" ],
      "networkType" : "INN"
    },
    "type" : "DentalSettlement_OrthoMaximum_Lifetime_Orthodontics",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : -40,
    "extension" : {
      "_type" : "JsonType",
      "networkType" : "INN"
    },
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 0,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "INN"
    },
    "type" : "DentalSettlement_IndividualDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "party" : {
      "_uri" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111"
    },
    "_type" : "CapAccumulatorTransactionData"
  }, {
    "amount" : 0,
    "extension" : {
      "_type" : "JsonType",
      "appliedToServices" : [ "Major", "Basic", "Preventive" ],
      "networkType" : "INN"
    },
    "resource" : {
      "_uri" : "capMock://CapDentalUnverifiedPolicy/cf133357-061c-4913-9d0b-34a665c20137/1"
    },
    "type" : "DentalSettlement_FamilyDeductible_Annual_Dental",
    "transactionDate" : ignore,
    "_type" : "CapAccumulatorTransactionData"
  } ],
  "sourceURI" : "gentity://CapSettlement/CapDentalSettlement//1a6634f3-1591-43c2-bc8d-71bcf98ef484/1",
  "policyURI" : "capMock://CapDentalUnverifiedPolicy/cf133357-061c-4913-9d0b-34a665c20137/1",
  "transactionTimestamp" : ignore,
  "customerURI" : "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111",
  "_modelName" : "CapAccumulatorTransaction",
  "_modelVersion" : "1",
  "_modelType" : "CapAccumulatorTransactionEntry",
  "_type" : "CapAccumulatorTransactionEntity"
}