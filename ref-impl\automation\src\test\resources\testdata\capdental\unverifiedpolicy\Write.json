{"TestData": {"entity": {"term": {"effectiveDate": "$<today-1M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "expirationDate": "$<today+11M:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "Term"}, "policyPaidToDate": "$<today:yyyy-MM-dd>", "productCd": "DNIndividual", "policyPaidToDateWithGracePeriod": "$<today:yyyy-MM-dd>", "plan": "High", "planCategory": "PPO", "isImplantsMaximumAppliedTowardPlanMaximum": false, "deductibleDetails": [{"individualPreventiveINNAnnualDeductible": {"currency": "USD", "amount": 10}, "individualBasicINNAnnualDeductible": {"currency": "USD", "amount": 10}, "individualMajorINNAnnualDeductible": {"currency": "USD", "amount": 100}, "_type": "CapDeductibleDetailEntity"}], "dentalMaximums": [{"individualMajorINNAnnualMaximum": {"currency": "USD", "amount": 1000}, "orthoINNAnnualMaximum": {"amount": 1000, "currency": "USD"}, "_type": "CapDentalMaximumEntity"}], "coinsurances": [{"coinsuranceServiceType": "Preventive", "coinsuranceINPct": 99, "coinsuranceOONPct": 97, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}, {"coinsuranceServiceType": "Major", "coinsuranceINPct": 93, "coinsuranceOONPct": 77, "_type": "CapDentalPolicyInfoCoinsuranceEntity"}], "insureds": [{"isFullTimeStudent": false, "relationshipToPrimaryInsuredCd": "Dependent<PERSON><PERSON><PERSON>", "isMain": true, "registryTypeId": "{{partyRegistryId}}", "_type": "CapDentalPolicyInfoInsuredDetailsEntity"}], "_modelName": "CapDentalUnverifiedPolicy", "_modelVersion": "1", "_modelType": "UnverifiedPolicy", "_archived": false, "_type": "CapDentalUnverifiedPolicy"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl.CapDentalPolicyInfoWrapperModel"}}