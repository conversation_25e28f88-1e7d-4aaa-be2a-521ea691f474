/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpayment.validator.CapDentalPaymentUpdateInputValidator.CapDentalPaymentUpdateInputValidatorErrorDefinition.PAYEE_INCORRECT;

import java.util.List;
import java.util.Optional;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalPaymentUpdateInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentDetailsEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentPayeeDetailsEntity;
import com.eisgroup.genesis.factory.modeling.types.CapPaymentDetails;

/**
 * Class for {@link CapDentalPaymentUpdateInput} related validations.
 *
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalPaymentUpdateInputValidator extends CapInputValidator<CapDentalPaymentUpdateInput> {

    private static final List<String> PAYEE_TYPES = List.of("Customer", "Provider");

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalPaymentUpdateInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPaymentUpdateInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPaymentUpdateInput input) {
        return validatePayee(input.getEntity());
    }

    private Streamable<ErrorHolder> validatePayee(CapPaymentDetails paymentDetails) {
        return Optional.ofNullable(paymentDetails)
                .filter(CapDentalPaymentDetailsEntity.class::isInstance)
                .map(CapDentalPaymentDetailsEntity.class::cast)
                .map(CapDentalPaymentDetailsEntity::getPayeeDetails)
                .map(CapDentalPaymentPayeeDetailsEntity::getPayee)
                .map(payee -> capDentalLinkValidator.validateLink(payee, PAYEE_TYPES, PAYEE_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    public static class CapDentalPaymentUpdateInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static CapDentalPaymentUpdateInputValidatorErrorDefinition PAYEE_INCORRECT =
                new CapDentalPaymentUpdateInputValidatorErrorDefinition("cpuv001", "payeeDetails.payee URI is not valid");

        protected CapDentalPaymentUpdateInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
