[{"folderStructure": {"_key": {"rootId": "16038367-ba3c-47cc-b753-59f26c287e99", "revisionNo": 1}, "owners": [{"ownerName": "CapDentalLoss", "ownerType": "CapLoss", "_type": "OwnerEntity"}], "folder": {"_key": {"id": "1d878f23-9a2c-401f-966a-fff6193822a9"}, "name": "losses", "folders": [{"_key": {"id": "125c5a6d-c0fd-4574-9f0b-7ff4a03fa995"}, "name": "1-Intake", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "dfedebf0-d514-4682-9f33-cbae783b1e6a"}, "name": "2-Financials", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "1561f091-4f06-4f16-a24e-1d06ccd97fc4"}, "name": "3-Appeals", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "0e7ab9ad-4953-4771-95ab-b8a2bdf5dd3a"}, "name": "4-General", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "c17e537b-3f21-47e9-8de8-efe5b5687efd"}, "name": "5-Inbound", "folders": [{"_key": {"id": "a8c49e8b-8fcc-4076-9955-b5a5ae194b87"}, "name": "1-Member", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "91893007-bd8d-4c8b-9a18-3f2b1f524e54"}, "name": "2-Provider", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "7203e7b1-bd70-49cf-987d-620c9c88f366"}, "name": "3-Other", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "1d35d528-ca04-40cc-b85d-3bc60abda699"}, "name": "6-Outbound", "folders": [{"_key": {"id": "578bb5d4-d3f3-4b4d-a839-f79e23589cad"}, "name": "1-Member", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "07dd9940-5fa0-4ae4-851a-6df55630d094"}, "name": "2-Provider", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}, {"_key": {"id": "494babb8-dd71-4103-848b-5966db950efe"}, "name": "3-Other", "folders": [], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}], "readPrivileges": [], "writePrivileges": [], "allowedDocumentTypes": ["SUPPORTING_DOCUMENTS"], "_type": "FolderEntity"}], "_type": "FolderEntity"}, "_type": "FolderStructureEntity", "_modelType": "EFolder", "_modelName": "folders"}}]