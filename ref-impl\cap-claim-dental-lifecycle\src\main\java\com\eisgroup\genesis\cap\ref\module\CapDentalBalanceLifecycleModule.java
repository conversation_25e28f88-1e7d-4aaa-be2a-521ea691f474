/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceCalculateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalBalanceConfig;
import com.eisgroup.genesis.cap.ref.repository.config.CapDentalBalanceRepositoryConfig;
import com.eisgroup.genesis.factory.modeling.types.CapDentalBalance;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

/**
 * Lifecycle configuration for {@link CapDentalBalance}
 */
public class CapDentalBalanceLifecycleModule implements LifecycleModule {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        return List.of(
                new CapDentalBalanceInitHandler(),
                new CapDentalBalanceUpdateHandler(),
                new CapDentalBalanceCalculateHandler()
        );
    }

    @Override
    public String getModelType() {
        return "CapBalance";
    }

    @Override
    public String getModelName() {
        return "CapDentalBalance";
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[]{
                CapDentalBalanceConfig.class,
                CapBaseCommandConfig.class,
                CapDentalBalanceRepositoryConfig.class
        };
    }
}
