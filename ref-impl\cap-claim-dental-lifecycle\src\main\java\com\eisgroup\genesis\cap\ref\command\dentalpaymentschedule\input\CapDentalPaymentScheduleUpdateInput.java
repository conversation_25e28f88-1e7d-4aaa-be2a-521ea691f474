/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input;

import com.eisgroup.genesis.cap.financial.command.paymentschedule.input.CapPaymentScheduleUpdateInput;
import com.google.gson.JsonObject;

/**
 * Command input for updating Dental Payment Schedule.
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPaymentScheduleUpdateInput extends CapPaymentScheduleUpdateInput {

    public CapDentalPaymentScheduleUpdateInput(JsonObject original) {
        super(original);
    }
}
