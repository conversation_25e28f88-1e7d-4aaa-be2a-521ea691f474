StateMachine CapDentalPaymentSchedule {
    EntryState uninitialized {
        Using initPaymentSchedule command transit to Open
    }

    State Canceled {
    }

    State Active {
        Using completePaymentSchedule command transit to Completed
        Using cancelPaymentSchedule command transit to Canceled
        Using suspendPaymentSchedule command transit to Suspended
    }

    State Open {
        Using cancelPaymentSchedule command transit to Canceled
        Using activatePaymentSchedule command transit to Active
        Using updatePaymentSchedule command transit to Open
    }

    State Suspended {
        Using cancelPaymentSchedule command transit to Canceled
        Using unsuspendPaymentSchedule command transit to Active
    }

    State Completed {
    }
}