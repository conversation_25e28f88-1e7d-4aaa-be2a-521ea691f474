/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalrecovery;

import com.eisgroup.genesis.cap.financial.command.recovery.CapRecoveryCancelHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;

/**
 * Command handler for recoveries cancellation.
 *
 * <AUTHOR>
 * @since 22.14
 */
@Modifying
public class CapDentalRecoveryCancelHandler extends CapRecoveryCancelHandler<IdentifierRequest, CapDentalPaymentEntity> {
}
