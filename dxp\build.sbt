Common.projectSettings

libraryDependencies ++= Seq(caffeine)

lazy val dxpCore = project.in(file("modules/dxp-core"))
  .enablePlugins(PlayMinimalJava, BuildInfoPlugin)
  .settings(
    PlaySettings.playSettings,
    BuildPluginsSettings.buildInfoSettings
  )

lazy val dental = project.in(file("modules/dental"))
lazy val integrationEisGenesis = project.in(file("modules/integration-eisgenesis"))

lazy val gateway = project.in(file("."))
  .enablePlugins(PlayMinimalJava)
  .dependsOn(
    dxpCore,
    dental,
    integrationEisGenesis
  )
  .aggregate(
    dxpCore,
    dental,
    integrationEisGenesis
  )
  .settings(
    Release.releaseSettings,
    BuildPluginsSettings.buildInfoSettings,
    dist / aggregate := false // Remove .zip package distribution for submodules
  )

publish / skip := true