/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.service;

import com.eis.automation.tzappa_v20.service.IFacadeService;
import com.eis.automation.tzappa_v20.service.ITestDataService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.payment.facade.ICapDentalPayment;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;

public interface ICapDentalPaymentService extends ITestDataService, IFacadeService<ICapDentalPayment> {

    ICapDentalPaymentModel initDentalPayment(ICapDentalPaymentModel capDentalPaymentModel);

    ICapDentalPaymentModel initDentalPayment(ICustomerModel payee, ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalPaymentModel createDentalPaymentModel(ICustomerModel payee, ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel);

    ICapDentalPaymentModel loadDentalPayment(ICapDentalPaymentModel capDentalPaymentModel);

    ICapDentalPaymentModel requestIssueDentalPayment(ICapDentalPaymentModel capDentalPaymentModel);

    ICapDentalPaymentModel issueDentalPayment(ICapDentalPaymentModel capDentalPaymentModel);

    ICapDentalPaymentModel clearDentalPayment(ICapDentalPaymentModel capDentalPaymentModel);
}
