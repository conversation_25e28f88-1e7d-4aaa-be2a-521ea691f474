/*
 * Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.claimsearch.service.impl;

import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.capdental.entity.claimsearch.facade.IClaimSearch;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.platform.common.modeling.generic.search.Matcher;
import com.eis.automation.v20.platform.common.modeling.generic.search.QueryContent;
import com.eis.automation.v20.platform.common.modeling.generic.wrapper.SearchResponse;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.stream.Collectors;

import static com.eis.automation.v20.capdental.constant.CapDentalConstant.CapModelNames.CAP_DENTAL_PAYMENT_SCHEDULE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.CapModelNames.CAP_DENTAL_PAYMENT_TEMPLATE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.CapModelNames.CAP_DENTAL_SETTLEMENT;
import static com.eis.automation.v20.platform.utils.PlatformPredicates.searchResponseEqualOrMore;

@Lazy
@Component("claimSearch")
public class ClaimSearchService implements IClaimSearchService {

    @Autowired
    private IClaimSearch claimSearch;

    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdClaimSearchLocation;
    private TestData tdSpecificClaimSearchLocation;

    @Override
    public TestData getTestData(String... strings) {
        return tdClaimSearchLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificClaimSearchLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdClaimSearchLocation = testDataProvider.getJSONTestData("/capdental/search");
        tdSpecificClaimSearchLocation = testDataProvider.getJSONTestData("/capdental/search/specific");
    }

    @Override
    public IClaimSearch getFacade() {
        return claimSearch;
    }

    public List<ICapDentalSettlementModel> searchDentalSettlement(ICapDentalLossModel capDentalLossModel, int responseCount) {
        QueryContent queryContent = new QueryContent().addMatcher("claimLossIdentification",
                Matcher.create(capDentalLossModel.getGentityUri().getUri()));
        SearchResponse<ICapDentalSettlementModel> searchResponse = getFacade().searchDentalSettlement()
                .performNoSuccessAnalysis(b -> b.setQueryContent(queryContent),
                        searchResponseEqualOrMore(responseCount)).safeGetResponseBody();
        return searchResponse.getResult().stream().filter(s -> CAP_DENTAL_SETTLEMENT.equals(s.getModelName()))
                .collect(Collectors.toList());
    }

    public List<ICapDentalPaymentTemplateModel> searchDentalPaymentTemplate(ICapDentalLossModel capDentalLossModel, int responseCount) {
        QueryContent queryContent = new QueryContent().addMatcher("originSource",
                Matcher.create(capDentalLossModel.getGentityUri().getUri()));
        SearchResponse<ICapDentalPaymentTemplateModel> searchResponse = getFacade().searchDentalPaymentTemplate()
                .performNoSuccessAnalysis(b -> b.setQueryContent(queryContent),
                        searchResponseEqualOrMore(responseCount)).safeGetResponseBody();
        return searchResponse.getResult().stream().filter(s -> CAP_DENTAL_PAYMENT_TEMPLATE.equals(s.getModelName()))
                .collect(Collectors.toList());
    }

    public List<ICapDentalPaymentScheduleModel> searchDentalPaymentSchedule(ICapDentalLossModel capDentalLossModel, int responseCount) {
        QueryContent queryContent = new QueryContent().addMatcher("originSource",
                Matcher.create(capDentalLossModel.getGentityUri().getUri()));
        SearchResponse<ICapDentalPaymentScheduleModel> searchResponse = getFacade().searchDentalPaymentSchedule()
                .performNoSuccessAnalysis(b -> b.setQueryContent(queryContent),
                        searchResponseEqualOrMore(responseCount)).safeGetResponseBody();
        return searchResponse.getResult().stream().filter(s -> CAP_DENTAL_PAYMENT_SCHEDULE.equals(s.getModelName()))
                .collect(Collectors.toList());
    }
}
