StateMachine CapDentalPaymentDefinition {

    /* Payment, Underpayment */
    State Approved {
        Using requestIssueUnderpayment command transit to IssueRequested
        Using cancelUnderpayment command transit to Canceled
        Using requestIssuePayment command transit to IssueRequested
        Using cancelPayment command transit to Canceled
        Using updatePayment command transit to Approved
    }

    /* Payment, Recovery, Underpayment, Overpayment Waive */
    State Canceled {
    }

    /* Payment, Underpayment */
    State Cleared {
    }

    /* Payment, Recovery, Underpayment, Overpayment Waive */
    State Issued {
        Using declineUnderpayment command transit to Declined
        Using updateOverpaymentWaive command transit to Issued
        Using updateRecovery command transit to Issued
        Using declinePayment command transit to Declined
        Using requestStopPayment command transit to StopRequested
        Using clearPayment command transit to Cleared
        Using requestStopUnderpayment command transit to StopRequested
        Using voidUnderpayment command transit to Voided
        Using clearUnderpayment command transit to Cleared
        Using voidPayment command transit to Voided
        Using cancelRecovery command transit to Canceled
        Using cancelOverpaymentWaive command transit to Canceled
    }

    /* Payment, Underpayment */
    State IssueRequested {
        Using cancelUnderpayment command transit to Canceled
        Using issueUnderpayment command transit to Issued
        Using issuePayment command transit to Issued
        Using requestIssueFailPayment command transit to Approved
        Using cancelPayment command transit to Canceled
    }

    /* Payment, Underpayment */
    State Declined {
    }

    /* Payment, Underpayment */
    State Stopped {
    }

    /* Payment, Underpayment */
    State StopRequested {
        Using stopUnderpayment command transit to Stopped
        Using clearPayment command transit to Cleared
        Using voidUnderpayment command transit to Voided
        Using clearUnderpayment command transit to Cleared
        Using voidPayment command transit to Voided
        Using stopPayment command transit to Stopped
    }

    /* Payment, Recovery, Underpayment, Overpayment Waive */
    EntryState uninitialized {
        Using initOverpaymentWaive command transit to Issued
        Using initRecovery command transit to Issued
        Using initUnderpayment command transit to Pending
        Using initPayment command transit to Approved
    }

    /* Underpayment */
    State Disapproved {
    }

    /* Underpayment */
    State Pending {
        Using cancelUnderpayment command transit to Canceled
        Using approveUnderpayment command transit to Approved
        Using updateUnderpayment command transit to Pending
        Using disapproveUnderpayment command transit to Disapproved
    }

    /* Payment, Underpayment */
    State Voided {
    }
}
