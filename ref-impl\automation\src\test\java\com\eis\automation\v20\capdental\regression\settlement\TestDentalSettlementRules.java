/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.settlement;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalClaimOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalServiceOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.ADJUDICATING;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;
import static com.eis.automation.v20.cap.constant.CapErrorConstant.LOOKUP_CODE_MUST_BE_ONE_OF_THESE_VALUES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_SETTLEMENT;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.*;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;

public class TestDentalSettlementRules extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private ICapDentalSettlementService capDentalSettlement;
    @Autowired
    private IClaimSearchService claimSearch;
    private final String TEST_DATA1 = "TestData_Readjudicate1";
    private final String TEST_DATA2 = "TestData_Readjudicate2";

    public ICapDentalSettlementModel createPreconditions() {
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        String createdDentalPolicyId = capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer)).getCapPolicyId();
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(
                capDentalLoss.createDentalLossModel(createdDentalPolicyId, individualCustomer, individualProvider));
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        ICapDentalSettlementModel settlementModel = claimSearch.searchDentalSettlement(submitDentalClaim, 1).get(0);
        return capDentalSettlement.loadDentalSettlement(settlementModel, APPROVED);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156965", component = CAP_DENTAL_SETTLEMENT)
    public void testInitAndReadjudicateSettlement() {
        // Dental Claim and Settlement (create automatically)
        ICapDentalSettlementModel loadSettlement = createPreconditions();

        // Steps 2.1 - Can not create Dental Settlement manually, so verification moved to readjudicate action
        ICapDentalSettlementModel readjudicateModel = createReadjudicateModel(loadSettlement, TEST_DATA1);
        readjudicateModel.setClaimLossIdentification(null);
        IFailureResponse failure = getError(() -> capDentalSettlement.getFacade().init().perform(b -> b.setModel(readjudicateModel)));
        FailureAssertion.assertThat(failure).hasError(CLAIM_LOSS_IDENTIFICATION_LINK_TO_CLAIM_IS_MANDATORY);

        // Step 1.1 - Removed cause can not initiate Dental Settlement manually

        // Step 1.2
        ICapDentalSettlementModel readjudicateModel2 = createReadjudicateModel(loadSettlement, TEST_DATA1);
        readjudicateModel2.getEntity().getServiceOverrides().get(0).setServiceSource(null);
        ICapDentalSettlementModel readjudicatedSettlement = capDentalSettlement.readjudicateDentalSettlement(readjudicateModel2);

        ICapDentalClaimOverrideModel actualClaimOverrideModel = readjudicatedSettlement.getSettlementDetail().getClaimOverride();
        ICapDentalClaimOverrideModel expectedClaimOverrideModel = readjudicateModel2.getEntity().getClaimOverride();
        ICapDentalServiceOverrideModel actualServiceOverrideModel = readjudicatedSettlement.getSettlementDetail().getServiceOverrides().get(0);
        ICapDentalServiceOverrideModel expectedServiceOverrideModel = readjudicateModel2.getEntity().getServiceOverrides().get(0);

        assertSoftly(softly -> {
            softly.assertThat(readjudicatedSettlement.getState()).isEqualTo(ADJUDICATING);
            softly.assertThat(actualClaimOverrideModel.getIsAllowed()).isEqualTo(expectedClaimOverrideModel.getIsAllowed());
            softly.assertThat(actualClaimOverrideModel.getIsDenied()).isEqualTo(expectedClaimOverrideModel.getIsDenied());
            softly.assertThat(actualServiceOverrideModel.getIsAllowed()).isEqualTo(expectedServiceOverrideModel.getIsAllowed());
            softly.assertThat(actualServiceOverrideModel.getIsDenied()).isEqualTo(expectedServiceOverrideModel.getIsDenied());
        });

        modelAssertion.assertThat(actualClaimOverrideModel.getOverrideClaimValue()).covers(expectedClaimOverrideModel.getOverrideClaimValue());
        modelAssertion.assertThat(actualServiceOverrideModel.getOverrideServiceValue()).covers(expectedServiceOverrideModel.getOverrideServiceValue());
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156965", component = CAP_DENTAL_SETTLEMENT)
    public void testDentalSettlementRules() {
        // Dental Claim and Settlement (create automatically)
        ICapDentalSettlementModel loadSettlement = createPreconditions();

        // Step 3.1
        ICapDentalSettlementModel readjudicateModel = createReadjudicateModel(loadSettlement, TEST_DATA1);
        readjudicateModel.getEntity().getClaimOverride().setIsAllowed(true);
        readjudicateModel.getEntity().getClaimOverride().setIsDenied(true);
        readjudicateModel.getEntity().getServiceOverrides().get(0).setIsAllowed(true);
        readjudicateModel.getEntity().getServiceOverrides().get(0).setIsDenied(true);
        IFailureResponse failure = getError(() -> capDentalSettlement.getFacade().readjudicate().perform(b -> b.setModel(readjudicateModel)));
        FailureAssertion.assertThat(failure)
                .hasError(CLAIM_IS_DENIED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_ALLOWED_OVERRIDE)
                .hasError(CLAIM_OVERRIDE_IS_ALLOWED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_DENIED_OVERRIDE)
                .hasError(SERVICE_OVERRIDE_IS_ALLOWED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_DENIED_OVERRIDE)
                .hasError(SERVICE_OVERRIDE_IS_DENIED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_ALLOWED_OVERRIDE);

        // Step 3.2
        readjudicateModel.getEntity().getClaimOverride().setIsAllowed(false);
        readjudicateModel.getEntity().getClaimOverride().setIsDenied(false);
        readjudicateModel.getEntity().getServiceOverrides().get(0).setIsAllowed(false);
        readjudicateModel.getEntity().getServiceOverrides().get(0).setIsDenied(false);
        ICapDentalSettlementModel readjudicatedSettlement = capDentalSettlement.readjudicateDentalSettlement(readjudicateModel);

        assertSoftly(softly -> {
            softly.assertThat(readjudicatedSettlement.getState()).isEqualTo(ADJUDICATING);
            softly.assertThat(readjudicatedSettlement.getSettlementDetail().getClaimOverride().getIsAllowed()).isEqualTo(false);
            softly.assertThat(readjudicatedSettlement.getSettlementDetail().getClaimOverride().getIsDenied()).isEqualTo(false);
            softly.assertThat(readjudicatedSettlement.getSettlementDetail().getServiceOverrides().get(0).getIsAllowed()).isEqualTo(false);
            softly.assertThat(readjudicatedSettlement.getSettlementDetail().getServiceOverrides().get(0).getIsDenied()).isEqualTo(false);
        });
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156965", component = CAP_DENTAL_SETTLEMENT)
    public void testAssertRules() {
        // Dental Claim and Settlement (create automatically)
        ICapDentalSettlementModel loadSettlement = createPreconditions();

        // Step 3.3
        ICapDentalSettlementModel readjudicateModel = createReadjudicateModel(loadSettlement, TEST_DATA2);
        IFailureResponse failure = getError(() -> capDentalSettlement.getFacade().readjudicate().perform(b -> b.setModel(readjudicateModel)));
        FailureAssertion.assertThat(failure)
                .hasError(OVERRIDE_ORTHO_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(OVERRIDE_DEDUCTIBLE_CANNOT_BE_NEGATIVE)
                .hasError(OVERRIDE_MAXIMUM_AMOUNT_CANNOT_BE_NEGATIVE)
                .hasError(SERVICE_OVERRIDE_GRACE_PERIOD_CANNOT_BE_NEGATIVE)
                .hasError(SERVICE_OVERRIDE_PAYMENT_INTEREST_AMOUNT_CANNOT_BE_NEGATIVE)
                .hasError(CLAIM_OVERRIDE_PAYMENT_INTEREST_DAYS_CANNOT_BE_NEGATIVE)
                .hasError(OVERRIDE_PREVENTIVE_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(CLAIM_OVERRIDE_LATE_ENTRANT_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(OVERRIDE_BASIC_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(OVERRIDE_MAJOR_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(SERVICE_OVERRIDE_LATE_ENTRANT_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0)
                .hasError(CLAIM_OVERRIDE_PAYMENT_INEREST_AMOUNT_CANNOT_BE_NEGATIVE);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156965", component = CAP_DENTAL_SETTLEMENT)
    public void testServiceCategoryLookup() {
        // Dental Claim and Settlement (create automatically)
        ICapDentalSettlementModel loadSettlement = createPreconditions();

        // Step 4.1
        ICapDentalSettlementModel readjudicateModel = createReadjudicateModel(loadSettlement, TEST_DATA1);
        readjudicateModel.getEntity().getServiceOverrides().get(0).getOverrideServiceValue().setOverrideServiceCategory("FANTA");
        IFailureResponse failure = getError(() -> capDentalSettlement.getFacade().readjudicate().perform(b -> b.setModel(readjudicateModel)));
        FailureAssertion.assertThat(failure).hasError(LOOKUP_CODE_MUST_BE_ONE_OF_THESE_VALUES, "ServiceCategory");
    }

    private ICapDentalSettlementModel createReadjudicateModel(ICapDentalSettlementModel settlementModel, String testData) {
        ICapDentalSettlementModel loadSettlement = capDentalSettlement.loadDentalSettlement(settlementModel);
        ICapDentalSettlementModel readjudicateModel = modelUtils.create(
                capDentalSettlement.getSpecificTestData("TestDentalSettlementRules", testData));
        readjudicateModel.setClaimLossIdentification(loadSettlement.getClaimLossIdentification());
        readjudicateModel.getEntity().setTimestamp(loadSettlement.getTimestamp());
        return readjudicateModel;
    }
}