package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentTemplateLifecycleConfigTest {

    @InjectMocks
    private CapDentalPaymentTemplateLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private ModeledTransformationService modeledTransformationService;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;

    @Test
    public void testReturnCapValidatorRegistry() {
        var result = config.capValidatorRegistry();
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnPaymentTemplateNumberGenerator() {
        var result = config.paymentTemplateNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapPaymentTemplateValidator() {
        var result = config.capPaymentTemplateValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPaymentTemplateIndexResolver() {
        var result = config.capDentalPaymentTemplateIndexResolver(modeledTransformationService);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapFinancialDataResolver() {
        var result = config.capFinancialDataResolver(modeledTransformationService);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapFinancialDataPreviewResolver() {
        var result = config.capFinancialDataPreviewResolver(modeledTransformationService);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.capDentalLinkValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPaymentTemplateInitInputValidator() {
        var result = config.capDentalPaymentTemplateInitInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPaymentTemplateUpdateInputValidator() {
        var result = config.capDentalPaymentTemplateUpdateInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }
}





