package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.loss.command.ClaimLossCreateHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossInitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossOpenHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossPendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossReopenHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossRequestCloseHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSubStatusHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSubmitHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossSuspendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUnsuspendHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUpdateDraftHandler;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.CapDentalLossUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalLossLifecycleConfig;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

public class CapDentalLossLifecycleModuleTest {

    private static final String MODEL_NAME = "CapDentalLoss";

    private static final String MODEL_TYPE = "CapLoss";

    private CapDentalLossLifecycleModule module;

    @Before
    public void setUp() {
        module = new CapDentalLossLifecycleModule();
    }

    @Test
    public void shouldReturnCorrectCommands() {
        //given
        var expectedCommands = Set.of(
                CapDentalLossInitHandler.class,
                ClaimLossCreateHandler.class,
                CapDentalLossReopenHandler.class,
                CapDentalLossSubmitHandler.class,
                CapDentalLossSubStatusHandler.class,
                CapDentalLossUpdateHandler.class,
                CapDentalLossCloseHandler.class,
                CapDentalLossUpdateDraftHandler.class,
                CapDentalLossOpenHandler.class,
                CapDentalLossPendHandler.class,
                CapDentalLossSuspendHandler.class,
                CapDentalLossUnsuspendHandler.class,
                CapDentalLossRequestCloseHandler.class
        );
        // when
        var result = module.getCommands();

        // then
        assertThat(result, notNullValue());
        var handlerCommands = result.stream()
                .map(Object::getClass)
                .collect(Collectors.toSet());
        assertThat(handlerCommands, equalTo(expectedCommands));

    }

    @Test
    public void shouldReturnModelName() {
        assertThat(module.getModelName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnModelType() {
        assertThat(module.getModelType(), equalTo(MODEL_TYPE));
    }

    @Test
    public void shouldReturnModelVersion() {
        assertThat(module.getModelVersion(), equalTo("1"));
    }

    @Test
    public void shouldReturnStateMachineName() {
        assertThat(module.getStateMachineName(), equalTo(MODEL_NAME));
    }

    @Test
    public void shouldReturnConfigurationResources() {
        //when
        var configurationClasses = Arrays.stream(module.getConfigResources()).toList();

        //then
        assertThat(configurationClasses.contains(CapDentalLossLifecycleConfig.class), equalTo(true));
    }
}
