/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.handlers;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.events.StreamEvent;
import com.eisgroup.genesis.events.handler.StreamEventHandler;
import com.eisgroup.genesis.json.wrapper.JsonWrapperFactoryProvider;
import com.eisgroup.genesis.streams.MessageMetadata;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * Event handler that handles PH events.
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalOutboundPaymentEventHandler implements StreamEventHandler<StreamEvent> {

    private static final List<String> SUPPORTED_EVENTS = List.of("OutboundPaymentFailedEvent",
        "OutboundPaymentCancelledEvent", "OutboundPaymentPaidEvent", "OutboundPaymentCreatedEvent");
    private static final String PROCESS_PAYMENT_LIFECYCLE = "processPaymentLifecycle";
    private static final String DOMAIN_NAME_CLAIM = "CLAIM";

    private final CommandPublisher commandPublisher;

    public CapDentalOutboundPaymentEventHandler(CommandPublisher commandPublisher) {
        this.commandPublisher = commandPublisher;
    }

    @Override
    public void handle(StreamEvent streamEvent) { Lazy.defer(() -> {
        JsonObject eventAsJson = JsonWrapperFactoryProvider.getJsonWrapperFactory().unwrap(streamEvent)
                .getAsJsonObject();
        Optional<JsonObject> original = Optional.of(eventAsJson).map(jsonObject -> jsonObject.get("original"))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject);
        Optional<JsonObject> paymentPH = original.map(jsonObject -> jsonObject.get("payment"))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject);
        String domainName = getAsString(paymentPH, "domainName");
        String domainCorrelationId = getAsString(paymentPH, "domainCorrelationId");
        if (!DOMAIN_NAME_CLAIM.equals(domainName) || (StringUtils.isNotEmpty(domainCorrelationId) && !domainCorrelationId.contains("CapDentalPaymentDefinition"))) {
            return Lazy.empty();
        }
        String eventName = getAsString(original, "name");
        String paymentUri = getAsString(paymentPH, "domainCorrelationId");
        String paymentMethodType = getAsString(paymentPH, "paymentMethodType");
        return Lazy.from(() -> commandPublisher.publish(constructCommand(paymentUri, paymentMethodType, eventName),
                "CapDentalPaymentDefinition"));
    }).get();
    }

    private static String getAsString(Optional<JsonObject> paymentPH, String elementName) {
        return paymentPH.map(jsonObject -> jsonObject.get(elementName))
            .filter(JsonElement::isJsonPrimitive)
            .map(JsonElement::getAsString)
            .orElse(null);
    }

    @Override
    public boolean supports(MessageMetadata message) {
        for (String supportedEvent : SUPPORTED_EVENTS) {
            String typeName = message.getMessageType().getTypeName();
            String name = typeName.substring(typeName.lastIndexOf(".") + 1);
            if (name.equals(supportedEvent)) {
                return true;
            }
        }
        return false;
    }

    private Command constructCommand(String paymentUri, String paymentMethodType, String paymentEvent) {
        JsonObject input = new JsonObject();
        input.addProperty("paymentUri", paymentUri);
        input.addProperty("paymentMethodType", paymentMethodType);
        input.addProperty("paymentEvent", paymentEvent);
        return new Command("payment", PROCESS_PAYMENT_LIFECYCLE, input);
    }
}
