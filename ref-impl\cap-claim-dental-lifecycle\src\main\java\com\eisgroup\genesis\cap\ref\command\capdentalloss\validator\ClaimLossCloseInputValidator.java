/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossCloseInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;

/**
 * Validates {@link ClaimLossCloseInput} request
 *
 * <AUTHOR>
 * @since 22.6
 */
public class ClaimLossCloseInputValidator extends CapInputValidator<ClaimLossCloseInput> {

    public ClaimLossCloseInputValidator() {
        super(ClaimLossCloseInput.class);
    }

    @Override
    public Streamable<ErrorHolder> validate(ClaimLossCloseInput input) {
        if (input.getReasonDescription() != null) {
            return Streamable.of(ClaimLossCloseInputErrorDefinition.REASON_DESCRIPTION_PROVIDED.builder().build());
        }
        return Streamable.empty();
    }

    public static class ClaimLossCloseInputErrorDefinition extends BaseErrorDefinition {
        public static final ClaimLossCloseInputErrorDefinition REASON_DESCRIPTION_PROVIDED = new ClaimLossCloseInputErrorDefinition("clci001", "This command does not accept reasonDescription attribute.");

        private ClaimLossCloseInputErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
