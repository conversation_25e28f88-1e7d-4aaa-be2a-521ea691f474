Transformation CapDentalUnverifiedPolicyToCapDentalLoss {
    Input {
        Ext CapPolicyHolder.CapPolicyEntity as policy
    }
    Output {
        CapDentalLoss.CapDentalLossPolicyEntity
    }

    Attr capPolicyId is policy.capPolicyId
    Attr capPolicyVersionId is policy.capPolicyVersionId
    Attr productCd is policy.productCd
    Attr policyNumber is policy.policyNumber
    Attr isVerified is false
    Attr term is policy.term
    Attr txEffectiveDate is policy.txEffectiveDate
    Attr policyStatus is "Active"
    Attr riskStateCd is policy.riskStateCd
    Attr plan is policy.plan
    Attr planName is policy.planName
}