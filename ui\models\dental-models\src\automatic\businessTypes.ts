// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import {Money} from '@eisgroup/models-api'

export namespace BusinessTypes {
    export interface AbsenceRefHolder extends MAPI.BusinessType {
        readonly absence?: MAPI.ExternalLink
    }

    export interface AccessTrackInfo extends MAPI.BusinessType {
        readonly createdBy?: string
        readonly createdOn?: Date
        readonly updatedBy?: string
        readonly updatedOn?: Date
    }

    export interface AccessTrackableEntity extends MAPI.BusinessType {
        readonly accessTrackInfo?: AccessTrackInfo
    }

    export interface Accumulative extends MAPI.BusinessType {
        readonly accumulatorType?: string
        readonly amount?: number
    }

    export interface AddressInfo extends MAPI.BusinessType {
        readonly addressType?: string
    }

    export interface AdjudicationResult extends MAPI.BusinessType, SubstateAware {
        readonly applicability?: Applicability
        readonly compensationSchedule?: CompensationSchedule
        readonly eligibility?: Eligibility
        readonly error?: MessageType
        readonly estimatedLoss?: EstimatedLoss
        readonly reserve?: Reserve
    }

    export interface Applicability extends MAPI.BusinessType, MessageTypeHolder {
        readonly itemHolder?: CoverableItemHolder
    }

    export interface BLOBEntity extends MAPI.BusinessType {
        readonly blobCd?: string
    }

    export interface CapAlternatePayeeRole extends ClaimPartyRole, MAPI.BusinessType {
        readonly registryId?: string
        readonly roleCd?: string[]
    }

    export interface CapBalance extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly balanceItems?: CapBalanceItem[]
        readonly creationDate?: Date
        readonly originSource?: MAPI.ExternalLink
        readonly payee?: MAPI.ExternalLink
        readonly suspenseItems?: CapBalanceSuspenseItem[]
        readonly totalBalanceAmount?: Money
    }

    export interface CapBalanceChangeLog extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly balanceChangeLogDetails?: CapBalanceChangeLogDetails
        readonly creationDate?: Date
        readonly eventCaseLink?: MAPI.ExternalLink
        readonly originSource?: MAPI.ExternalLink
        readonly payee?: MAPI.ExternalLink
    }

    export interface CapBalanceChangeLogDetails extends MAPI.BusinessType {
        readonly description?: string
        readonly totalBalanceAmount?: Money
        readonly transactionNumber?: string
        readonly transactionTypeCd?: string
    }

    export interface CapBalanceItem extends MAPI.BusinessType {
        readonly actualAllocations?: CapBalanceItemAllocation[]
        readonly actualNetAmount?: Money
        readonly balancedNetAmount?: Money
        readonly paymentDate?: Date
        readonly paymentNumber?: string
        readonly scheduledNetAmount?: Money
    }

    export interface CapBalanceItemAllocation extends MAPI.BusinessType {
        readonly allocationAdditions?: CapBalanceItemAllocationAddition[]
        readonly allocationGrossAmount?: Money
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapPaymentAllocationPayableItem
        readonly allocationReductions?: CapBalanceItemAllocationReduction[]
        readonly allocationSource?: MAPI.ExternalLink
        readonly allocationTaxes?: CapBalanceItemAllocationTax[]
        readonly isInterestOnly?: boolean
    }

    export interface CapBalanceItemAllocationAddition extends MAPI.BusinessType {
        readonly additionSubType?: string
        readonly additionType?: string
        readonly appliedAmount?: Money
    }

    export interface CapBalanceItemAllocationReduction extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly reductionSubType?: string
        readonly reductionType?: string
    }

    export interface CapBalanceItemAllocationTax extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly taxState?: string
        readonly taxSubType?: string
        readonly taxType?: string
    }

    export interface CapBalanceSuspenseItem extends MAPI.BusinessType {
        readonly direction?: string
        readonly paymentDate?: Date
        readonly paymentNumber?: string
        readonly suspenseAmount?: Money
    }

    export interface CapClaim extends MAPI.BusinessType {
    }

    export interface CapClaimEvent extends MAPI.BusinessType {
    }

    export interface CapClaimHeader extends ClaimHeaderLink, MAPI.BusinessType {
        readonly caseNumber?: string
        readonly claimCase?: CapClaimHeaderCase
        readonly claimDate?: Date
        readonly claimDates?: Date[]
        readonly claimModelName?: string
        readonly claimNumber?: string
        readonly claimType?: string
        readonly claimant?: ClaimantAware
        readonly insured?: InsuredAware
        readonly mappingType?: string
        readonly policies?: CapClaimHeaderPolicy[]
        readonly reportedDate?: Date
        readonly state?: string
        readonly subjects?: ClaimSubjectInfo[]
        readonly totalIncurred?: Money
    }

    export interface CapClaimHeaderCase extends MAPI.BusinessType {
        readonly caseNumber?: string
    }

    export interface CapClaimHeaderPolicy extends ClaimPolicyAware, MAPI.BusinessType {
        readonly productCd?: string
    }

    export interface CapDentalBalance extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly originSource?: MAPI.ExternalLink
        readonly payee?: MAPI.ExternalLink
    }

    export interface CapDentalBaseBalanceItemAllocation extends MAPI.BusinessType {
        readonly allocationAdditions?: CapDentalBaseBalanceItemAllocationAddition[]
        readonly allocationGrossAmount?: Money
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly allocationReductions?: CapDentalBaseBalanceItemAllocationReduction[]
        readonly allocationSource?: MAPI.ExternalLink
        readonly allocationTaxes?: CapDentalBaseBalanceItemAllocationTax[]
    }

    export interface CapDentalBaseBalanceItemAllocationAddition extends MAPI.BusinessType {
        readonly additionSubType?: string
        readonly additionType?: string
        readonly appliedAmount?: Money
    }

    export interface CapDentalBaseBalanceItemAllocationReduction extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly reductionSubType?: string
        readonly reductionType?: string
    }

    export interface CapDentalBaseBalanceItemAllocationTax extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly taxSubType?: string
        readonly taxType?: string
    }

    export interface CapDentalBaseClaimData extends MAPI.BusinessType {
        readonly alternatePayeeRole?: CapAlternatePayeeRole
        readonly cleanClaimDate?: Date
        readonly cob?: CapDentalClaimCoordinationOfBenefitsEntity[]
        readonly dateOfBirth?: Date
        readonly digitalImageNumbers?: string[]
        readonly initialDateOfService?: Date
        readonly isUnknownOrIntProvider?: boolean
        readonly missingTooths?: string[]
        readonly patientRole?: CapPatientRole
        readonly payeeType?: string
        readonly placeOfTreatment?: string
        readonly policyholderRole?: CapPolicyholderRole
        readonly providerDiscount?: CapDentalProviderDiscountEntity
        readonly providerFees?: CapDentalProviderFeesEntity[]
        readonly providerRole?: CapProviderRole
        readonly receivedDate?: Date
        readonly remark?: string
        readonly source?: string
        readonly transactionType?: string
        readonly treatmentReason?: CapDentalTreatmentReasonEntity
    }

    export interface CapDentalBaseProcedure extends MAPI.BusinessType {
        readonly cob?: CapDentalProcedureCoordinationOfBenefitsEntity
        readonly dateOfService?: Date
        readonly description?: string
        readonly diagnosisCodes?: CapDentalDiagnosisCodeEntity[]
        readonly ortho?: CapDentalOrthodonticEntity
        readonly preauthorization?: CapDentalPreauthorizationEntity
        readonly preauthorizationNumber?: string
        readonly predetInd?: boolean
        readonly priorProsthesisPlacementDate?: Date
        readonly procedureCode?: string
        readonly procedureType?: string
        readonly quantity?: number
        readonly submittedFee?: Money
        readonly surfaces?: string[]
        readonly toothArea?: string
        readonly toothCodes?: string[]
        readonly toothSystem?: string
    }

    export interface CapDentalClaimCoordinationOfBenefitsEntity extends MAPI.BusinessType {
        readonly PolicyNumber?: string
        readonly address?: string
        readonly otherCoverageType?: string
        readonly otherInsuranceCompany?: string
        readonly otherPolicyType?: string
        readonly period?: Term
        readonly plan?: string
        readonly policyholderDateOfBirth?: Date
        readonly policyholderFirstName?: string
        readonly policyholderGender?: string
        readonly policyholderLastName?: string
        readonly policyholderRelationshipToPatient?: string
        readonly typeOfCob?: string
    }

    export interface CapDentalDiagnosisCodeEntity extends MAPI.BusinessType {
        readonly code?: string
        readonly qualifier?: string
    }

    export interface CapDentalOrthodonticEntity extends MAPI.BusinessType {
        readonly appliancePlacedDate?: Date
        readonly downPayment?: Money
        readonly frequency?: string
        readonly months?: number
        readonly orthoFrequencyCd?: string
        readonly orthoMonthQuantity?: number
    }

    export interface CapDentalPaymentAllocationAccumulatorDetailsEntity extends MAPI.BusinessType {
        readonly accumulatorAmount?: Money
        readonly accumulatorType?: string
        readonly appliesToProcedureCategories?: string[]
        readonly appliesToProcedureCategory?: string
        readonly networkType?: string
        readonly renewalType?: string
        readonly term?: Term
    }

    export interface CapDentalPaymentAllocationDentalDetailsEntity extends MAPI.BusinessType {
        readonly accumulatorDetails?: CapDentalPaymentAllocationAccumulatorDetailsEntity[]
        readonly patient?: MAPI.ExternalLink
        readonly transactionTypeCd?: string
    }

    export interface CapDentalPaymentAllocationEntity extends CapPaymentAllocation, MAPI.BusinessType {
        readonly additionSplitResults?: CapPaymentAllocationAdditionSplitResult[]
        readonly allocationDentalDetails?: CapDentalPaymentAllocationDentalDetailsEntity
        readonly allocationLossInfo?: CapDentalPaymentAllocationLossInfoEntity
        readonly allocationPayableItem?: CapDentalPaymentAllocationPayableItemEntity
        readonly reductionSplitResults?: CapPaymentAllocationReductionSplitResult[]
        readonly taxSplitResults?: CapPaymentAllocationTaxSplitResult[]
    }

    export interface CapDentalPaymentAllocationLossInfoEntity extends CapPaymentAllocationLossInfo, MAPI.BusinessType {
    }

    export interface CapDentalPaymentAllocationPayableItemEntity extends CapPaymentAllocationPayableItem, MAPI.BusinessType {
        readonly claimSource?: MAPI.ExternalLink
        readonly orthoMonth?: number
        readonly procedureID?: string
    }

    export interface CapDentalPaymentDetailsEntity extends CapPaymentDetails, MAPI.BusinessType {
        readonly payeeDetails?: CapDentalPaymentPayeeDetailsEntity
        readonly paymentAdditions?: CapPaymentAddition[]
        readonly paymentAllocations?: CapDentalPaymentAllocationEntity[]
        readonly paymentReductions?: CapPaymentReduction[]
        readonly paymentTaxes?: CapPaymentTax[]
    }

    export interface CapDentalPaymentPayeeDetailsEntity extends CapPaymentPayeeDetails, MAPI.BusinessType {
        readonly paymentMethodDetails?: CapPaymentMethodDetails
    }

    export interface CapDentalPreauthorizationEntity extends MAPI.BusinessType {
        readonly authorizationPeriod?: Period
        readonly authorizedBy?: string
        readonly isProcedureAuthorized?: boolean
    }

    export interface CapDentalProcedureCoordinationOfBenefitsEntity extends MAPI.BusinessType {
        readonly allowed?: Money
        readonly considered?: Money
        readonly coverageType?: string
        readonly innOnn?: string
        readonly paid?: Money
        readonly primaryCoverageStatus?: string
    }

    export interface CapDentalProviderDiscountEntity extends MAPI.BusinessType {
        readonly discountAmount?: Money
        readonly discountName?: string
        readonly discountPercentage?: number
        readonly discountType?: string
    }

    export interface CapDentalProviderDiscountsFeesEntity extends MAPI.BusinessType {
    }

    export interface CapDentalProviderFeesEntity extends MAPI.BusinessType {
        readonly fee?: Money
        readonly type?: string
    }

    export interface CapDentalTreatmentReasonEntity extends MAPI.BusinessType {
        readonly autoAccidentState?: string
        readonly dateOfAccident?: Date
        readonly treatmentResultingFrom?: string
    }

    export interface CapEstimationResult extends MAPI.BusinessType {
    }

    export interface CapEventCaseRefHolder extends MAPI.BusinessType {
        readonly eventCaseLink?: MAPI.ExternalLink
    }

    export interface CapFrequencyConfiguration extends MAPI.BusinessType {
        readonly type?: string
    }

    export interface CapInsuredInfo extends InsuredInfoAware, MAPI.BusinessType {
        readonly registryTypeId?: string
    }

    export interface CapLoss extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly lossDetail?: LossDetail
        readonly lossNumber?: string
        readonly lossSubStatusCd?: string
        readonly reasonCd?: string
        readonly reasonDescription?: string
        readonly state?: string
    }

    export interface CapPatientRole extends ClaimPartyRole, MAPI.BusinessType {
        readonly registryId?: string
        readonly roleCd?: string[]
    }

    export interface CapPayment extends CapPaymentInfo, MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly originSource?: MAPI.ExternalLink
        readonly paymentDetails?: CapPaymentDetails
        readonly state?: string
    }

    export interface CapPaymentAddition extends MAPI.BusinessType {
        readonly additionNumber?: string
        readonly additionSource?: MAPI.ExternalLink
        readonly additionType?: string
    }

    export interface CapPaymentAdditionTemplate extends CapPaymentAddition, MAPI.BusinessType {
    }

    export interface CapPaymentAllocation extends MAPI.BusinessType {
        readonly additionSplitResults?: CapPaymentAllocationAdditionSplitResult[]
        readonly allocationGrossAmount?: Money
        readonly allocationLobCd?: string
        readonly allocationLossInfo?: CapPaymentAllocationLossInfo
        readonly allocationNetAmount?: Money
        readonly allocationPayableItem?: CapPaymentAllocationPayableItem
        readonly allocationSource?: MAPI.ExternalLink
        readonly reductionSplitResults?: CapPaymentAllocationReductionSplitResult[]
        readonly reserveType?: string
        readonly taxSplitResults?: CapPaymentAllocationTaxSplitResult[]
    }

    export interface CapPaymentAllocationAdditionSplitResult extends MAPI.BusinessType {
        readonly additionNumber?: string
        readonly appliedAmount?: Money
    }

    export interface CapPaymentAllocationLossInfo extends MAPI.BusinessType {
        readonly lossSource?: MAPI.ExternalLink
    }

    export interface CapPaymentAllocationPayableItem extends MAPI.BusinessType {
    }

    export interface CapPaymentAllocationReductionSplitResult extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly reductionNumber?: string
    }

    export interface CapPaymentAllocationTaxSplitResult extends MAPI.BusinessType {
        readonly appliedAmount?: Money
        readonly taxNumber?: string
    }

    export interface CapPaymentAllocationTemplate extends MAPI.BusinessType {
        readonly allocationLobCd?: string
        readonly allocationLossInfo?: CapPaymentAllocationTemplateLossInfo
        readonly allocationPayeeDetails?: CapPaymentAllocationTemplatePayeeDetails
        readonly allocationSource?: MAPI.ExternalLink
    }

    export interface CapPaymentAllocationTemplateLossInfo extends MAPI.BusinessType {
        readonly lossSource?: MAPI.ExternalLink
    }

    export interface CapPaymentAllocationTemplatePayeeDetails extends MAPI.BusinessType {
        readonly payee?: MAPI.ExternalLink
    }

    export interface CapPaymentDetails extends MAPI.BusinessType {
        readonly payeeDetails?: CapPaymentPayeeDetails
        readonly paymentAdditions?: CapPaymentAddition[]
        readonly paymentAllocations?: CapPaymentAllocation[]
        readonly paymentDate?: Date
        readonly paymentReductions?: CapPaymentReduction[]
        readonly paymentTaxes?: CapPaymentTax[]
    }

    export interface CapPaymentDetailsTemplate extends MAPI.BusinessType {
        readonly paymentAllocationTemplates?: CapPaymentAllocationTemplate[]
    }

    export interface CapPaymentInfo extends MAPI.BusinessType {
        readonly creationDate?: Date
        readonly direction?: string
        readonly messages?: MessageType[]
        readonly paymentNetAmount?: Money
        readonly paymentNumber?: string
    }

    export interface CapPaymentMethodDetails extends MAPI.BusinessType {
    }

    export interface CapPaymentPayeeDetails extends MAPI.BusinessType {
        readonly payee?: MAPI.ExternalLink
        readonly paymentMethodDetails?: CapPaymentMethodDetails
    }

    export interface CapPaymentReduction extends MAPI.BusinessType {
        readonly reductionNumber?: string
        readonly reductionSource?: MAPI.ExternalLink
        readonly reductionType?: string
    }

    export interface CapPaymentReductionTemplate extends CapPaymentReduction, MAPI.BusinessType {
    }

    export interface CapPaymentSchedule extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly creationDate?: Date
        readonly originSource?: MAPI.ExternalLink
        readonly paymentTemplate?: MAPI.ExternalLink
        readonly payments?: CapScheduledPayment[]
        readonly scheduleNumber?: string
        readonly state?: string
    }

    export interface CapPaymentTax extends MAPI.BusinessType {
        readonly taxNumber?: string
        readonly taxSource?: MAPI.ExternalLink
        readonly taxType?: string
    }

    export interface CapPaymentTaxTemplate extends CapPaymentTax, MAPI.BusinessType {
    }

    export interface CapPaymentTemplate extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly creationDate?: Date
        readonly originSource?: MAPI.ExternalLink
        readonly paymentDetailsTemplate?: CapPaymentDetailsTemplate
        readonly state?: string
    }

    export interface CapPolicy extends CapPolicyInfo, CoverableItemHolder, MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly exclusions?: CapPolicyExclusion[]
        readonly risks?: InsurableRisk[]
    }

    export interface CapPolicyAware extends MAPI.BusinessType {
        readonly policyIds?: string[]
    }

    export interface CapPolicyExclusion extends MAPI.BusinessType {
        readonly code?: string
        readonly isExclusion?: boolean
    }

    export interface CapPolicyHolder extends MAPI.BusinessType {
        readonly policies?: CapPolicyInfo[]
    }

    export interface CapPolicyInfo extends CapClaimHeaderPolicy, MAPI.BusinessType {
        readonly capPolicyVersionId?: string
        readonly currencyCd?: string
        readonly insureds?: CapInsuredInfo[]
        readonly isVerified?: boolean
        readonly policyStatus?: string
        readonly policyType?: string
        readonly riskStateCd?: string
        readonly term?: Term
        readonly txEffectiveDate?: string
    }

    export interface CapPolicyholderRole extends ClaimPartyRole, MAPI.BusinessType {
        readonly registryId?: string
        readonly roleCd?: string[]
    }

    export interface CapProviderRole extends ClaimPartyRole, MAPI.BusinessType {
        readonly providerLink?: string
        readonly registryId?: string
        readonly roleCd?: string[]
    }

    export interface CapRootIdentifier extends MAPI.BusinessType, RootEntity {
        readonly links: MAPI.ExternalLink[]
    }

    export interface CapScheduledPayment extends CapPaymentInfo, MAPI.BusinessType {
        readonly paymentDetails?: CapPaymentDetails
    }

    export interface CapSettlement extends MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
        readonly accessTrackInfo?: AccessTrackInfo
        readonly claimLossIdentification?: MAPI.ExternalLink
        readonly settlementAbsenceInfo?: CapSettlementAbsenceInfo
        readonly settlementDetail?: CapSettlementDetail
        readonly settlementLossInfo?: CapSettlementLossInfo
        readonly settlementNumber?: string
        readonly settlementResult?: CapSettlementResult
        readonly settlementType?: string
        readonly state?: string
    }

    export interface CapSettlementAbsenceInfo extends MAPI.BusinessType {
    }

    export interface CapSettlementDetail extends MAPI.BusinessType {
    }

    export interface CapSettlementLossInfo extends MAPI.BusinessType {
    }

    export interface CapSettlementResult extends MAPI.BusinessType {
        readonly reserve?: number
    }

    export interface CapTimeline extends MAPI.BusinessType, MessageTypeHolder {
        readonly rows?: CapTimelineRow[]
    }

    export interface CapTimelineClaimInfo extends MAPI.BusinessType, SelfAware {
        readonly appliedCoverage?: string
        readonly caseNumber?: string
        readonly claimNumber?: string
        readonly claimType?: string
        readonly examinerName?: string
        readonly state?: string
    }

    export interface CapTimelineEvent extends MAPI.BusinessType {
        readonly eventDate?: Date
        readonly manuallyAdded?: boolean
        readonly type?: string
    }

    export interface CapTimelineMetric extends MAPI.BusinessType {
        readonly type?: string
        readonly unit?: string
        readonly value?: number
    }

    export interface CapTimelinePeriod extends MAPI.BusinessType, Period {
        readonly type?: string
    }

    export interface CapTimelineRow extends MAPI.BusinessType {
        readonly claimInfo?: CapTimelineClaimInfo
        readonly metrics?: CapTimelineMetric[]
        readonly periods?: CapTimelinePeriod[]
        readonly rowEvents?: CapTimelineEvent[]
        readonly type?: string
    }

    export interface ClaimHeaderLink extends MAPI.BusinessType {
        readonly primaryURI?: MAPI.ExternalLink
    }

    export interface ClaimPartyAware extends MAPI.BusinessType {
        readonly registryId?: string
    }

    export interface ClaimPartyInfo extends MAPI.BusinessType, PartyInfo {
    }

    export interface ClaimPartyRole extends ClaimPartyAware, MAPI.BusinessType {
        readonly roleCd?: string[]
    }

    export interface ClaimPolicyAware extends MAPI.BusinessType {
        readonly capPolicyId?: string
        readonly policyNumber?: string
    }

    export interface ClaimReserveAmounts extends MAPI.BusinessType {
        readonly expenseAmount?: Money
        readonly indemnityAmount?: Money
        readonly recoveryAmount?: Money
    }

    export interface ClaimSubjectInfo extends MAPI.BusinessType {
        readonly subject?: string
        readonly subjectId?: string
    }

    export interface ClaimSystemIdentifierHolder extends MAPI.BusinessType {
        readonly caseSystemId?: MAPI.ExternalLink
        readonly claimModelName?: string
        readonly claimSystemId?: MAPI.ExternalLink
        readonly rootId?: string
    }

    export interface Claimant extends ClaimPartyRole, ClaimantAware, MAPI.BusinessType {
    }

    export interface ClaimantAware extends ClaimPartyAware, MAPI.BusinessType {
    }

    export interface ClaimantInfo extends MAPI.BusinessType, PartyInfo {
    }

    export interface CompensationSchedule extends MAPI.BusinessType, MessageTypeHolder {
        readonly initiatePayment?: boolean
        readonly paymentAmount?: Money
        readonly schedule?: string
        readonly scheduleTypeCd?: string
    }

    export interface CompositeCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly items?: CoverableItem[]
    }

    export interface CorrelationIdHolder extends MAPI.BusinessType, RootEntity {
        readonly correlationId?: string
    }

    export interface CoverableItem extends MAPI.BusinessType {
        readonly code?: string
        readonly compositeCoverableItemCondition?: CompositeCoverableItemCondition
        readonly delayableCoverableItemCondition?: DelayableCoverableItemCondition
        readonly distanceBoundCoverableItemCondition?: DistanceBoundCoverableItemCondition
        readonly exclusions?: CapPolicyExclusion[]
        readonly limitedCoverableItemCondition?: LimitedCoverableItemCondition
        readonly occurrableCoverableItemCondition?: OccurrableCoverableItemCondition
        readonly preExistingCoverableItemCondition?: PreExistingCoverableItemCondition
        readonly restrictedCoverableItemCondition?: RestrictedCoverableItemCondition
        readonly timedCoverableItemCondition?: TimedCoverableItemCondition
        readonly type?: string
    }

    export interface CoverableItemCondition extends MAPI.BusinessType {
    }

    export interface CoverableItemHolder extends MAPI.BusinessType {
        readonly coverableItems?: CoverableItem[]
    }

    export interface DelayableCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly delay?: string
    }

    export interface DistanceBoundCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly distanceMoreThan?: number
    }

    export interface Eligibility extends MAPI.BusinessType, MessageTypeHolder {
        readonly eligibilityCd?: string
        readonly itemHolder?: CoverableItemHolder
    }

    export interface EligibilityOverride extends MAPI.BusinessType {
        readonly message?: string
        readonly ruleCd?: string
    }

    export interface EntityRef extends MAPI.BusinessType {
        readonly id?: string
        readonly parentId?: string
        readonly revisionNo?: number
        readonly rootId?: string
    }

    export interface EstimatedLoss extends MAPI.BusinessType, MessageTypeHolder {
        readonly amount?: Money
    }

    export interface ExternalCapClaimHeader extends MAPI.BusinessType {
        readonly caseNumber?: string
        readonly claimCase?: CapClaimHeaderCase
        readonly claimDate?: Date
        readonly claimModelName?: string
        readonly claimNumber?: string
        readonly claimType?: string
        readonly claimant?: ClaimantAware
        readonly insured?: InsuredAware
        readonly policies?: CapClaimHeaderPolicy[]
        readonly reportedDate?: Date
        readonly state?: string
        readonly subjects?: ClaimSubjectInfo[]
        readonly totalIncurred?: Money
    }

    export interface ExternalClaimData extends MAPI.BusinessType {
        readonly claim?: ExternalCapClaimHeader
        readonly sourceId?: string
        readonly sourceSubType?: string
        readonly sourceSystem?: string
        readonly sourceType?: string
    }

    export interface ExternalClaimDataHolder extends MAPI.BusinessType {
        readonly externalClaims?: ExternalClaimData[]
    }

    export interface ExternalReference extends MAPI.BusinessType {
        readonly reference?: MAPI.ExternalLink
        readonly sourceId?: string
        readonly sourceSubType?: string
        readonly sourceSystem?: string
        readonly sourceType?: string
    }

    export interface ExternalReferenceHolder extends MAPI.BusinessType {
        readonly externalReference?: ExternalReference[]
    }

    export interface Generatable extends MAPI.BusinessType {
        readonly isGenerated?: boolean
    }

    export interface InsurableRisk extends CoverableItemHolder, MAPI.BusinessType {
        readonly id?: string
    }

    export interface InsuredAware extends ClaimPartyAware, MAPI.BusinessType {
    }

    export interface InsuredInfoAware extends MAPI.BusinessType {
        readonly insuredRoleNameCd?: string
        readonly isMain?: boolean
        readonly registryTypeId?: string
    }

    export interface JsonType extends MAPI.BusinessType {
    }

    export interface LOBEntity extends MAPI.BusinessType {
        readonly lobCd?: string
    }

    export interface LimitedCoverableItemCondition extends Accumulative, CoverableItemCondition, MAPI.BusinessType {
    }

    export interface LossDetail extends MAPI.BusinessType {
        readonly lossDesc?: string
    }

    export interface MainInsuredInfo extends MAPI.BusinessType, PartyInfo {
    }

    export interface MemberAware extends MAPI.BusinessType {
        readonly memberRegistryTypeId?: string
    }

    export interface MessageType extends MAPI.BusinessType {
        readonly code?: string
        readonly message?: string
        readonly severity?: string
        readonly source?: string
    }

    export interface MessageTypeHolder extends MAPI.BusinessType {
        readonly messages?: MessageType[]
    }

    export interface OccurrableCoverableItemCondition extends Accumulative, CoverableItemCondition, MAPI.BusinessType {
        readonly maxOccurrences?: number
    }

    export interface PartyInfo extends MAPI.BusinessType, Retriable {
        readonly city?: string[]
        readonly customerId?: string
        readonly customerNumber?: string
        readonly email?: string[]
        readonly firstName?: string
        readonly lastName?: string
        readonly legalId?: string
        readonly legalName?: string
        readonly phoneNumber?: string[]
        readonly postalCode?: string[]
        readonly stateProvinceCd?: string[]
        readonly streetAddress?: string[]
        readonly taxId?: string
    }

    export interface PaymentDetailAwareSettlementResult extends MAPI.BusinessType {
        readonly allocationPeriod?: Period
        readonly initiatePayment?: boolean
        readonly payee?: MAPI.ExternalLink
    }

    export interface Period extends MAPI.BusinessType {
        readonly endDate?: Date
        readonly startDate?: Date
    }

    export interface PolicyIdentifierHolder extends MAPI.BusinessType {
        readonly policySystem?: string
        readonly policySystemId?: MAPI.ExternalLink
        readonly policySystemRevisionNo?: number
    }

    export interface PreExistingCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly continuouslyInsuredPeriod?: number
        readonly duration?: number
        readonly isApplied?: boolean
        readonly lookBackPeriod?: number
        readonly treatmentFreePeriod?: number
        readonly type?: string
    }

    export interface PrimaryEntityLinkAware extends MAPI.BusinessType {
        readonly primaryURI?: MAPI.ExternalLink
    }

    export interface RequestedReserve extends MAPI.BusinessType {
        readonly items?: RequestedReserveItem[]
        readonly modificationReason?: string
        readonly type?: string
    }

    export interface RequestedReserveItem extends MAPI.BusinessType {
        readonly amount?: Money
        readonly description?: string
    }

    export interface Reserve extends ClaimReserveAmounts, MAPI.BusinessType, MessageTypeHolder {
    }

    export interface RestrictedCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly appliesTo?: string
        readonly restriction?: string
    }

    export interface Retriable extends MAPI.BusinessType, MessageTypeHolder {
        readonly messages?: RetriableMessage[]
    }

    export interface RetriableMessage extends MAPI.BusinessType, MessageType {
    }

    export interface RootEntity extends MAPI.BusinessType, MAPI.RootBusinessType {
    }

    export interface SelfAware extends MAPI.BusinessType {
        readonly self?: MAPI.ExternalLink
    }

    export interface SinglePolicyHolder extends MAPI.BusinessType {
        readonly policy?: CapPolicyInfo
        readonly policyId?: string
    }

    export interface SubstateAware extends MAPI.BusinessType {
        readonly substate?: string
    }

    export interface Term extends MAPI.BusinessType {
        readonly effectiveDate?: Date
        readonly expirationDate?: Date
    }

    export interface TimeZoneInfo extends MAPI.BusinessType {
        readonly timeZoneId?: string
    }

    export interface TimedCoverableItemCondition extends CoverableItemCondition, MAPI.BusinessType {
        readonly period?: string
    }

    export interface UnverifiedPolicy extends CapPolicyInfo, MAPI.BusinessType, MAPI.RootBusinessType, RootEntity {
    }

    export interface WorkflowInfo extends MAPI.BusinessType, Retriable {
        readonly workCaseId?: string
        readonly workQueueCd?: string
        readonly workStatusCd?: string
        readonly workUserId?: string
    }

}