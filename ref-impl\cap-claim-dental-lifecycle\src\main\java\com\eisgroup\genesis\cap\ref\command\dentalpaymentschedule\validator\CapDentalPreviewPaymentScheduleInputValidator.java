/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator.CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition.ORIGIN_SOURCE_URI_INCORRECT;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator.CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition.OTHER_SETTLEMENT_ORIGIN_SOURCE;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator.CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition.PROPOSAL_NOT_PAY_OR_PREDET_APPROVE;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator.CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_NOT_APPROVED;
import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPreviewPaymentScheduleInputValidator.CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition.SETTLEMENT_URI_INCORRECT;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalPreviewPaymentScheduleInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.exception.JsonValueFormatError;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

/**
 * Class for validating {@link CapDentalPreviewPaymentScheduleInput}.
 *
 * <AUTHOR>
 * @since 22.11
 */
public class CapDentalPreviewPaymentScheduleInputValidator extends CapInputValidator<CapDentalPreviewPaymentScheduleInput> {

    private static final String STATE_APPROVED = "Approved";
    private static final List<String> ALLOWED_PROPOSALS = List.of("PAY", "PREDET - APPROVE");

    private final EntityLinkResolverRegistry entityLinkResolverRegistry;
    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalPreviewPaymentScheduleInputValidator(EntityLinkResolverRegistry entityLinkResolverRegistry, CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPreviewPaymentScheduleInput.class);
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPreviewPaymentScheduleInput input) {
        return Streamable.from(input.getSettlements())
            .flatMap(settlementLink ->
                    Streamable.concat(validateSettlement(settlementLink, input.getOriginSource()),validateOriginSource(input.getOriginSource())));

    }

    private Streamable<ErrorHolder> validateOriginSource(EntityLink<RootEntity> originSource) {
        return Optional.ofNullable(originSource)
                .map(origin -> capDentalLinkValidator.validateLink(origin, "CapLoss", ORIGIN_SOURCE_URI_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    /**
     * Validates settlement link. If link is valid, loads settlement and checks whether it has
     * provided origin source, Approved state, and proposal either PAY or PREDET - APPROVE.
     *
     * @param settlementLink link of a settlement to validate
     * @param originSource origin source to check whether it is set in settlement
     *
     * @return observable with error messages if validation fails, empty observable otherwise
     */
    private Streamable<ErrorHolder> validateSettlement(EntityLink<RootEntity> settlementLink, EntityLink<RootEntity> originSource) {

        if (!isSettlementLinkValid(settlementLink)) {
            return Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build());
        }
        return loadSettlement(settlementLink)
            .flatMapMany(settlement -> {
                if (!(settlement instanceof CapDentalSettlementEntity)) {
                    return Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build());
                }
                return validateSettlementData((CapDentalSettlementEntity) settlement, settlementLink, originSource);
            })
            .onErrorResume(e->Streamable.of(SETTLEMENT_URI_INCORRECT.builder().params(settlementLink.getURIString()).build()));

    }

    private boolean isSettlementLinkValid(EntityLink<RootEntity> settlementLink) {
        try {
            return new FactoryLink(settlementLink).getTypeName().equals("CapSettlement");
        } catch (JsonValueFormatError e) {
            return false;
        }
    }

    private Streamable<ErrorHolder> validateSettlementData(CapDentalSettlementEntity settlement,
            EntityLink<RootEntity> settlementLink, EntityLink<RootEntity> originSource) {

        List<ErrorHolder> messages = new ArrayList<>();

        if (!originSource.getURIString().equals(settlement.getClaimLossIdentification().getURIString())) {
            messages.add(OTHER_SETTLEMENT_ORIGIN_SOURCE.builder().params(settlementLink.getURIString()).build());
        }
        if (!STATE_APPROVED.equals(settlement.getState())) {
            messages.add(SETTLEMENT_NOT_APPROVED.builder().params(settlementLink.getURIString()).build());
        }
        if (settlement.getSettlementResult() == null || !ALLOWED_PROPOSALS.contains(settlement.getSettlementResult().getProposal())) {
            messages.add(PROPOSAL_NOT_PAY_OR_PREDET_APPROVE.builder().params(settlementLink.getURIString()).build());
        }

        return Streamable.from(messages);
    }

    private Lazy<RootEntity> loadSettlement(EntityLink<RootEntity> settlementLink) {
        return entityLinkResolverRegistry.getByURIScheme(settlementLink.getSchema())
                .resolve(settlementLink, ReadContext.empty());
    }

    public static class CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition OTHER_SETTLEMENT_ORIGIN_SOURCE = new CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(
                "dpps-001", "Settlement is from a different origin source: {0}");
        public static final CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition SETTLEMENT_NOT_APPROVED = new CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(
                "dpps-002", "Cannot resolve financial data for settlement {0}. Settlement state is not 'Approved'.");
        public static final CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition PROPOSAL_NOT_PAY_OR_PREDET_APPROVE = new CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(
                "dpps-003", "Cannot resolve financial data for settlement {0}. Settlement proposal code is not 'PAY' or 'PREDET - APPROVE'.");
        public static final CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition SETTLEMENT_URI_INCORRECT = new CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(
                "dpps-004", "settlement {0} URI is not valid.");
        public static final CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition ORIGIN_SOURCE_URI_INCORRECT = new CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(
                "dpps-005", "originSource URI is not valid");

        protected CapDentalPreviewPaymentScheduleInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
