{"applicationName": "cap_generic_deployer_app", "timestamp": 1664794184226, "modelType": "com.eisgroup.genesis.factory.model.domain.DomainModel", "modelName": "CapAccumulatorContainer", "modelVersion": "1", "model": "{\"@class\":\"com.eisgroup.genesis.factory.model.domain.DomainModel\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.domain.DomainModelFeature\"},\"identityModel\":null,\"importedFeatures\":[],\"indexDeterminants\":[\"None\"],\"moduleType\":\"CapAccumulatorContainer\",\"name\":\"CapAccumulatorContainer\",\"namespaceIncludes\":[],\"root\":{\"@class\":\"com.eisgroup.genesis.factory.model.domain.RootEntityType\",\"type\":\"CapAccumulatorContainerEntity\"},\"storeDeterminants\":[\"None\"],\"types\":{\"Term\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"expirationDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"expirationDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"effectiveDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"effectiveDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"Term\"}],\"baseTypes\":[\"Term\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"Term\",\"references\":{}},\"CapAccumulatorUpdateRemainingAmountData\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"policyURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"remainingAmount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"remainingAmount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"customerURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"customerURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[],\"baseTypes\":[],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorUpdateRemainingAmountData\",\"references\":{}},\"CapAccumulatorTransactionData\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"transactionDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Date when the transaction was received and consumed. Not necessary when the transaction itself has happened.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Date when the transaction was received and consumed. Not necessary when the transaction itself has happened.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"transactionDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Transaction type, is used to track what kind of change was done.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Transaction type, is used to track what kind of change was done.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"type\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"extension\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"extension\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"JsonType\"}},\"policyTermDetails\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Contains Effective/Expiration dates for the term. Used to calculate accumulators by term.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Contains Effective/Expiration dates for the term. Used to calculate accumulators by term.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyTermDetails\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"Term\"}},\"amount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Unit independent amount changed with this transaction. It can be Days/Money.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Unit independent amount changed with this transaction. It can be Days/Money.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"amount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorTransactionData\"}],\"baseTypes\":[\"CapAccumulatorTransactionData\"],\"extLinks\":{\"resource\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Points to a secondary resource, that this transaction relates to. This is used when a transaction is started by a resource accommodator and the actual transaction affects different resources. Optional\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Points to a secondary resource, that this transaction relates to. This is used when a transaction is started by a resource accommodator and the actual transaction affects different resources. Optional\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"resource\",\"targetType\":\"RootEntity\"},\"party\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Link to secondary customer related to this transaction. Optional\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Link to secondary customer related to this transaction. Optional\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"party\",\"targetType\":\"RootEntity\"}},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"},\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorTransactionData\",\"references\":{}},\"AccessTrackInfo\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"updatedOn\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"updatedOn\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"updatedBy\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"updatedBy\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"createdOn\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"createdOn\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"createdBy\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"createdBy\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"AccessTrackInfo\"}],\"baseTypes\":[\"AccessTrackInfo\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"AccessTrackInfo\",\"references\":{}},\"CapAccumulator\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"policyTermDetails\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Usually accumulators are policy term bound.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Usually accumulators are policy term bound.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyTermDetails\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"Term\"}},\"limitAmount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Maximum amount that can be spent. Probably taken directly from Policy.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Maximum amount that can be spent. Probably taken directly from Policy.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"limitAmount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"amountType\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Represents what units are being counted. Can be Hours, Days, Money or distance. Is purely for documentation purposes and will not participate in calculations.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"amountType\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"usedAmount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Amount that is already used.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Amount that is already used.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"usedAmount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"remainingAmount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Remaining amount that can be used in reserving. Probable formula: limitAmount - reservedAmount - usedAmount.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"remainingAmount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"reservedAmount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Amount that is reserved to be used up.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Amount that is reserved to be used up.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"reservedAmount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Accumulator type and has to be a unique name across the system.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Accumulator type and has to be a unique name across the system.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"type\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"CapAccumulator aggregates values of a single accumulator type.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"CapAccumulator aggregates values of a single accumulator type.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulator\"}],\"baseTypes\":[\"CapAccumulator\"],\"extLinks\":{\"resource\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Claim resource associated with this accumulator. Likely be a settlement or claim. Optional\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Claim resource associated with this accumulator. Likely be a settlement or claim. Optional\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"resource\",\"targetType\":\"RootEntity\"},\"transactions\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Optional for specific accumulator types. Usually FIFO, where new transactions are added and old ones are removed.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Optional for specific accumulator types. Usually FIFO, where new transactions are added and old ones are removed.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"transactions\",\"targetType\":\"CapAccumulatorTransactionEntry\"},\"party\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Party that this accumulator is created for. It can differ from main insured, and be a family member or beneficiary. Optional.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Party that this accumulator is created for. It can differ from main insured, and be a family member or beneficiary. Optional.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"party\",\"targetType\":\"RootEntity\"}},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"},\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"CapAccumulator aggregates values of a single accumulator type.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"CapAccumulator aggregates values of a single accumulator type.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulator\",\"references\":{}},\"CapAccumulatorTransactionEntryInput\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"transactionTimestamp\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"2\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"transactionTimestamp\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"data\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"data\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapAccumulatorTransactionData\"}},\"sourceURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"1\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"sourceURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"customerURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Primary Insured\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Primary Insured\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"},\"com.eisgroup.genesis.generated.features.asm.67937038.PartitionedKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.67937038.PartitionedKey\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"customerURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"policyURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"0\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Transactions track what events are happening and are stored as is.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Transactions track what events are happening and are stored as is.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorTransactionKey\"}],\"typeName\":\"CapAccumulatorTransactionEntry\"}],\"baseTypes\":[\"CapAccumulatorTransactionEntry\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Input for transaction write command invoked on an accumulator container lifecycle.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Input for transaction write command invoked on an accumulator container lifecycle.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapAccumulatorContainer/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorTransactionEntryInput\",\"references\":{}},\"JsonType\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"JsonType\"}],\"baseTypes\":[\"JsonType\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"},\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"JsonType\",\"references\":{}},\"CapAccumulatorContainerEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"accumulators\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"A list of accumulators. Each for different types and different values.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"A list of accumulators. Each for different types and different values.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"accumulators\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapAccumulator\"}},\"accessTrackInfo\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"accessTrackInfo\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"AccessTrackInfo\"}},\"policyURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"0\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.272286290.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"customerURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"},\"com.eisgroup.genesis.generated.features.asm.67937038.PartitionedKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.67937038.PartitionedKey\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"customerURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.RootType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.RootType\\\",\\\"modelTypes\\\":{\\\"@array\\\":\\\"java.lang.String\\\",\\\"values\\\":[]}}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"RootEntity\"},{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"AccumulatorContainer is responsible for storing all accumulators associated with a single policy or customer.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"AccumulatorContainer is responsible for storing all accumulators associated with a single policy or customer.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorKey\"},{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"AccessTrackableEntity\"}],\"typeName\":\"CapAccumulatorContainer\"}],\"baseTypes\":[\"CapAccumulatorContainer\",\"RootEntity\"],\"extLinks\":{\"lastTransaction\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Tracked last transaction, that is persisted together with the container.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Tracked last transaction, that is persisted together with the container.\\\",\\\"messageBundle\\\":\\\"domain-messages/ProjectionTestStrictFeatureProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"lastTransaction\",\"targetType\":\"CapAccumulatorTransactionEntry\"}},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Responsible for storing all accumulators associated with a single policy or customer.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Responsible for storing all accumulators associated with a single policy or customer.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapAccumulatorContainer/description\\\"}\"},\"com.eisgroup.genesis.generated.features.asm.n557409526.Persistable\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"CapAccumulatorContainer\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n557409526.Persistable\\\"}\"},\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.n907295472.CompilerMetadata\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorContainerEntity\",\"references\":{}}},\"variations\":[],\"version\":\"1\"}"}