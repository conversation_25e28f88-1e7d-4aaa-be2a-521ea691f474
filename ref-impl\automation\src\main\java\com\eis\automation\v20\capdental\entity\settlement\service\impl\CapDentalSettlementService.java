/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa.retry.Retry;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.tzappa_v20.analytics.NamedPredicate;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.settlement.facade.ICapDentalSettlement;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.exigen.istf.data.TestData;
import com.exigen.istf.webdriver.controls.waiters.Waiters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

import static com.eis.automation.tzappa.rest.modeling.utils.ModelRetryPredicate.success;
import static com.eis.automation.v20.cap.constant.CapConstant.SettlementStates.APPROVED;

@Lazy
@Component("capDentalSettlement")
public class CapDentalSettlementService implements ICapDentalSettlementService {

    @Autowired
    private ICapDentalSettlement dentalSettlement;
    @Autowired
    private ICapDentalLossService dentalLoss;
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdDentalSettlementLocation;
    private TestData tdSpecificDentalSettlementLocation;

    @Override
    public ICapDentalSettlement getFacade() {
        return dentalSettlement;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdDentalSettlementLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificDentalSettlementLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdDentalSettlementLocation = testDataProvider.getJSONTestData("/capdental/settlement");
        tdSpecificDentalSettlementLocation = testDataProvider.getJSONTestData("/capdental/settlement/specific");
    }

    public ICapDentalSettlementModel initDentalSettlement(ICapDentalSettlementModel dentalSettlementModel) {
        return (ICapDentalSettlementModel) getFacade().init()
                .performNoSuccessAnalysis(b -> b.setModel(dentalSettlementModel), success()).safeGetResponseBody();
    }

    public ICapDentalSettlementModel initDentalSettlement(ICapDentalLossModel dentalClaimModel) {
        return (ICapDentalSettlementModel) getFacade().init()
                .performNoSuccessAnalysis(b -> b.setModel(createDentalSettlementModel(dentalClaimModel)),
                        success()).safeGetResponseBody();
    }

    public ICapDentalSettlementModel createDentalSettlementModel(ICapDentalLossModel dentalClaimModel) {
        return modelUtils.create(getTestData("Write", "TestData")
                .adjust("claimLossIdentification", dentalClaimModel.getGentityUri().getUri()));
                //.adjust("policyId", dentalClaimModel.getPolicyId()));
    }

    public ICapDentalSettlementModel loadDentalSettlement(ICapDentalSettlementModel dentalSettlementModel) {
        return (ICapDentalSettlementModel) getFacade().entities().perform(b -> b
                .setRootId(dentalSettlementModel.rootId())
                .setRevisionNumber(dentalSettlementModel.revisionNumber()));
    }

    public ICapDentalSettlementModel loadDentalSettlement(ICapDentalSettlementModel dentalSettlementModel, String state) {
        //Workaround to avoid performance issues
        Waiters.SLEEP(5000).go();
        NamedPredicate<ICapDentalSettlementModel> predicate = new NamedPredicate<>(
                String.format("Loaded Dental Settlement state is NOT %s as expected", state),
                response -> response.getState().equals(state));

        return Retry.run(predicate, () -> loadDentalSettlement(dentalSettlementModel), 10);
    }

    public ICapDentalSettlementModel loadApprovedDentalSettlement(ICapDentalSettlementModel dentalSettlementModel) {
        return loadDentalSettlement(dentalSettlementModel, APPROVED);
    }

    public ICapDentalSettlementModel adjudicateDentalSettlement(ICapDentalSettlementModel dentalSettlementModel) {
        return (ICapDentalSettlementModel) getFacade().adjudicate().perform(b -> b.setModel(dentalSettlementModel.getKey()));
    }

    public ICapDentalSettlementModel createReadjudicateSettlementModel(ICapDentalSettlementModel settlementModel) {
        ICapDentalSettlementModel loadSettlement = loadDentalSettlement(settlementModel);
        ICapDentalSettlementModel readjudicateModel = modelUtils.create(getTestData("Readjudicate", "TestData"));
        readjudicateModel.setClaimLossIdentification(loadSettlement.getClaimLossIdentification());
        readjudicateModel.getEntity().setTimestamp(loadSettlement.getTimestamp());
        return readjudicateModel;
    }

    public ICapDentalSettlementModel readjudicateDentalSettlement(ICapDentalSettlementModel readjudicateModel) {
        return (ICapDentalSettlementModel) getFacade().readjudicate().perform(b -> b.setModel(readjudicateModel));
    }
}
