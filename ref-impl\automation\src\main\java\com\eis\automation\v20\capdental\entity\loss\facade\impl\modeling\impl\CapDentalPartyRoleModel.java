/*
 * Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalPartyRoleModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPartyRoleModel extends TypeModel implements ICapDentalPartyRoleModel {

    private List<String> roleCd;
    private String registryId;
    private String providerLink;

    public List<String> getRoleCd() {
        return roleCd;
    }

    public void setRoleCd(List<String> roleCd) {
        this.roleCd = roleCd;
    }

    public String getRegistryId() {
        return registryId;
    }

    public void setRegistryId(String registryId) {
        this.registryId = registryId;
    }

    public String getProviderLink() {
        return providerLink;
    }

    public void setProviderLink(String providerLink) {
        this.providerLink = providerLink;
    }
}
