/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.input.CapDentalBalanceCalculateInput;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.CapDentalBalanceCalculator;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceCalculationRulesOutput;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import java.util.UUID;

import static com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceCommands.CALCULATE_DENTAL_BALANCE;

/**
 * Handler for dental balance calculation without saving.
 * <AUTHOR>
 * @since 22.14
 */
public class CapDentalBalanceCalculateHandler implements ProductCommandHandler<CapDentalBalanceCalculateInput, CapDentalBalanceCalculationRulesOutput> {

    @Autowired
    private CapDentalBalanceCalculator capDentalBalanceCalculator;
    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapDentalBalanceCalculationRulesOutput load(@Nonnull CapDentalBalanceCalculateInput request) {
        return Lazy.of(ModelInstanceFactory.createInstance(modelResolver.getModelName(), "1", CapDentalBalanceCalculationRulesOutput.class.getSimpleName()))
            .cast(CapDentalBalanceCalculationRulesOutput.class).get();
    }

    @Nonnull
    @Override
    public CapDentalBalanceCalculationRulesOutput execute(@Nonnull CapDentalBalanceCalculateInput request, @Nonnull CapDentalBalanceCalculationRulesOutput entity) {
        return capDentalBalanceCalculator.calculateLossBalance(request.getOriginSource())
                .map(balance -> {
                    balance.setKey(new EntityKey(UUID.randomUUID(), 1, UUID.randomUUID(), UUID.randomUUID()));
                    return balance;
                }).get();
    }

    @Nonnull
    @Override
    public CapDentalBalanceCalculationRulesOutput save(@Nonnull CapDentalBalanceCalculateInput request, @Nonnull CapDentalBalanceCalculationRulesOutput entity) {
        return Lazy.of(entity).get();
    }

    @Override
    public String getName() {
        return CALCULATE_DENTAL_BALANCE;
    }
}
