/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input;

import com.google.gson.JsonObject;

/**
 * {@link com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.CapDentalPreviewPaymentScheduleHandler} command input.
 * Used to generate {@link com.eisgroup.genesis.factory.modeling.types.CapPaymentSchedule}
 *
 * <AUTHOR>
 * @since 22.5
 */
public class CapDentalPreviewPaymentScheduleInput extends CapDentalBuildPaymentScheduleInput {
    public CapDentalPreviewPaymentScheduleInput(JsonObject original) {
        super(original);
    }
}
