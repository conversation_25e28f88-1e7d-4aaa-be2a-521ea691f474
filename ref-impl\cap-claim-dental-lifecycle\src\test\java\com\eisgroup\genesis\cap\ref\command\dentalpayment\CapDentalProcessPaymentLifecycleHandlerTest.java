/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import com.eisgroup.genesis.cap.financial.model.PaymentVariations;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalProcessPaymentLifecycleInput;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalProcessPaymentLifecycleOutput;
import com.google.gson.JsonObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.equalTo;
import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalProcessPaymentLifecycleHandlerTest {

    private static final String PAYMENT_URI = "testURI";
    private static final String PAYMENT_METHOD = "testMethod";
    private static final String PAYMENT_EVENT = "testEvent";

    @InjectMocks
    private CapDentalProcessPaymentLifecycleHandler handler;

    @Test
    public void shouldReturnHandlerName() {
        assertThat(handler.getName(), equalTo("processPaymentLifecycle"));
    }

    @Test
    public void shouldReturnHandlerVariation() {
        assertThat(handler.getVariation(), equalTo(PaymentVariations.PAYMENT));
    }

    @Test
    public void shouldLoadEntity() {
        //given
        CapDentalProcessPaymentLifecycleInput request = new CapDentalProcessPaymentLifecycleInput(new JsonObject());
        assertThat(
            //when
            handler.load(request).getModelFactory().getModelName(),
            //then
            is("CapDentalPaymentDefinition"));
    }

    @Test
    public void testHandlerExecute() {
        //given
        CapDentalProcessPaymentLifecycleInput request = new CapDentalProcessPaymentLifecycleInput(PAYMENT_URI, PAYMENT_METHOD, PAYMENT_EVENT);
        CapDentalProcessPaymentLifecycleOutput entity = (CapDentalProcessPaymentLifecycleOutput) ModelInstanceFactory.createInstance("CapDentalPaymentDefinition", "1", CapDentalProcessPaymentLifecycleOutput.class.getSimpleName());
        //when
        CapDentalProcessPaymentLifecycleOutput executedOutput = handler.execute(request, entity);
        //then
        assertThat(executedOutput.getPaymentUri(), equalTo(PAYMENT_URI));
        assertThat(executedOutput.getPaymentMethodType(), equalTo(PAYMENT_METHOD));
        assertThat(executedOutput.getPaymentEvent(), equalTo(PAYMENT_EVENT));
    }

    @Test
    public void testHandlerSave() {
        //given
        CapDentalProcessPaymentLifecycleInput request = new CapDentalProcessPaymentLifecycleInput(new JsonObject());
        CapDentalProcessPaymentLifecycleOutput entity = (CapDentalProcessPaymentLifecycleOutput) ModelInstanceFactory.createInstance("CapDentalPaymentDefinition", "1", CapDentalProcessPaymentLifecycleOutput.class.getSimpleName());
        assertThat(
            //when
            handler.save(request, entity),
            //then
            is(entity));
    }
}
