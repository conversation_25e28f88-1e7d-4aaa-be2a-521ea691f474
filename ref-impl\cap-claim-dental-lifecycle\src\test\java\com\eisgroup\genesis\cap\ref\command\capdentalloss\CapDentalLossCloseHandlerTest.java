/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.input.CapDentalLossCloseInput;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalClaimLossCloseValidator;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkBuilder;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.test.utils.JsonUtils;
import com.eisgroup.genesis.test.utils.TestStreamable;
import org.hamcrest.Matchers;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalLossCloseHandlerTest {

    @InjectMocks
    private CapDentalLossCloseHandler handler;

    @Mock
    private CapValidatorRegistry validators;

    @Mock
    private EntityLinkBuilderRegistry linkBuilderRegistry;

    @Mock
    private EntityLinkBuilder linkBuilder;

    @Mock
    private CapDentalClaimLossCloseValidator capDentalClaimLossCloseValidator;

    @Test
    public void shouldValidateAsync() {
        //given
        CapDentalLossCloseInput input = new CapDentalLossCloseInput(JsonUtils.load(
                "requestSamples/lossCloseInput.json"));
        CapDentalLossEntity entity = (CapDentalLossEntity) ModelInstanceFactory.createRootInstance("CapDentalLoss", "1");
        EntityLink<RootEntity> capLossUri = new EntityLink<>(RootEntity.class, "ENTITY_LINK");
        when(linkBuilder.createLink(any(RootEntity.class))).thenReturn(capLossUri);
        when(linkBuilderRegistry.getByType(any())).thenReturn(linkBuilder);
        when(validators.validateRequest(input)).thenReturn(Streamable.empty());
        when(capDentalClaimLossCloseValidator.validateSettlementNotApproved(capLossUri)).thenReturn(Streamable.empty());

        //when
        List<ErrorHolder> errorHolders =TestStreamable.create(handler.validateAsync(input, entity))
                .assertNoErrors()
                .values();
        //then
        assertThat(errorHolders, Matchers.hasSize(0));
    }
}
