@EventListener("write")
@TargetEntityCommand("writeTransaction")
Transformation UPIndToAccumulatorTx {
    Input {
        Ext CapDentalUnverifiedPolicy.CapDentalUnverifiedPolicy as policy
    }
    Output {
        Ext CapAccumulatorTransaction.CapAccumulatorTransactionEntity
    }

     Attr transactionTimestamp is Now()
     Attr policyURI is Add("capPolicy://CapUP/", ToExtLink(policy, "geroot")._uri)
     Attr customerURI is First(policy.insureds[filterMain].registryTypeId)
     Attr sourceURI is ToExtLink(policy)._uri

     Var deductibleBasicTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualDeductible_INN_Annual_Basic", First(policy.deductibleDetails).individualBasicINNAnnualDeductible.amount)
     Var deductibleMajorTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualDeductible_INN_Annual_Major", First(policy.deductibleDetails).individualMajorINNAnnualDeductible.amount)
     Var deductiblePreventiveTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualDeductible_INN_Annual_Preventive", First(policy.deductibleDetails).individualPreventiveINNAnnualDeductible.amount)
     Var maximumBasicTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualMaximum_INN_Annual_Basic", First(policy.dentalMaximums).individualBasicINNAnnualMaximum.amount)
     Var maximumMajorTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualMaximum_INN_Annual_Major", First(policy.dentalMaximums).individualMajorINNAnnualMaximum.amount)
     Var maximumPreventiveTx is mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_IndividualMaximum_INN_Annual_Preventive", First(policy.dentalMaximums).individualPreventiveINNAnnualMaximum.amount)

     Var implantsTx is  mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_ImplantsMaximum_INN_Annual_Implants", First(policy.dentalMaximums).implantsINNAnnualMaximum.amount)
     Var orthoTx is  mapParticipantsToTx(policy.insureds, policy, "DentalPolicy_OrthoMaximum_INN_Annual_Orthodontics", First(policy.dentalMaximums).orthoINNAnnualMaximum.amount)

     Var mappedData is FlatMap(deductibleBasicTx, deductibleMajorTx, deductiblePreventiveTx, maximumBasicTx, maximumMajorTx, maximumPreventiveTx, implantsTx, orthoTx)
     Attr data is mappedData[filterNonNullAmount]

     Producer mapParticipantsToTx(party, policy, txType, txAmount) {
         Attr type is txType
         Attr amount is txAmount
         Attr extension is New() {
            Attr term is Super().policy.term
            Attr _type is "JsonType"
         }
         Attr transactionDate is Now()
         Attr policyTermDetails is policy.term

         Attr party is createExtLink(party.registryTypeId)
         // ExtLink resource : RootEntity
     }

    Filter filterMain {
        isMain == TRUE
    }

    Filter filterNonNullAmount {
        !Equals(amount, Null())
    }

    Producer createExtLink(uri) {
        Attr _uri is uri
    }
}
