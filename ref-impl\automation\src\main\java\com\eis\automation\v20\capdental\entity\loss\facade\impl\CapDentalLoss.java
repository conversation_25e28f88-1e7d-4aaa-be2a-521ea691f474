/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.cap.entity.caseIntake.lifeIntakeLoss.facade.impl.action.CommonKeyAction;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossCloseModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossReopenModel;
import com.eis.automation.v20.capdental.entity.loss.facade.ICapDentalLoss;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.platform.common.action.CommonLoadAction;
import com.eis.automation.v20.platform.common.action.PostModelAction;

import javax.ws.rs.HttpMethod;

public class CapDentalLoss implements ICapDentalLoss {

    private final PostModelAction<ICapDentalLossModel, ICapDentalLossModel> initAction;
    private final PostModelAction<ICapDentalLossModel, ICapDentalLossModel> updateAction;
    private final PostModelAction<ICapDentalLossModel, ICapDentalLossModel> updateDraftAction;
    private final CommonKeyAction<ICapDentalLossModel> submitAction;
    private final CommonLoadAction<ICapDentalLossModel> entitiesAction;
    private final PostModelAction<ICapLossCloseModel, ICapDentalLossModel> closeAction;
    private final PostModelAction<ICapLossReopenModel, ICapDentalLossModel> reopenAction;

    public CapDentalLoss(RestActionConfiguration configuration) {
        initAction = new PostModelAction<>(configuration);
        updateAction = new PostModelAction<>(configuration);
        updateDraftAction = new PostModelAction<>(configuration);
        submitAction = new CommonKeyAction<>(configuration);
        entitiesAction = new CommonLoadAction<>(configuration);
        closeAction = new PostModelAction<>(configuration);
        reopenAction = new PostModelAction<>(configuration);
    }

    @RestActionGetter("initAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/initLoss")
    public PostModelAction<ICapDentalLossModel, ICapDentalLossModel> init() {
        return initAction;
    }

    @RestActionGetter("updateAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/updateLoss")
    public PostModelAction<ICapDentalLossModel, ICapDentalLossModel> update() {
        return updateAction;
    }

    @RestActionGetter("updateDraftAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/updateLossDraft")
    public PostModelAction<ICapDentalLossModel, ICapDentalLossModel> updateDraft() {
        return updateDraftAction;
    }

    @RestActionGetter("submitAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/submitLoss")
    public CommonKeyAction<ICapDentalLossModel> submit() {
        return submitAction;
    }

    @RestActionGetter("entitiesAction")
    @RestActionContext(
            target = "/api/caploss/{product}/{version}/entities/{rootId}/{revisionNo}",
            method = HttpMethod.GET)
    public CommonLoadAction<ICapDentalLossModel> entities() {
        return entitiesAction;
    }

    @RestActionGetter("closeAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/closeLoss")
    public PostModelAction<ICapLossCloseModel, ICapDentalLossModel> close() {
        return closeAction;
    }

    @RestActionGetter("reopenAction")
    @RestActionContext(target = "/api/caploss/{product}/{version}/command/reopenLoss")
    public PostModelAction<ICapLossReopenModel, ICapDentalLossModel> reopen() {
        return reopenAction;
    }
}
