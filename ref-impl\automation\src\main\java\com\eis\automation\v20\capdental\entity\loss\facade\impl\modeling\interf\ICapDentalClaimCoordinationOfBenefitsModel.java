/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf;

import com.eis.automation.v20.cap.entity.common.modeling.interf.ITermModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;

import java.time.LocalDate;

public interface ICapDentalClaimCoordinationOfBenefitsModel extends ITypeModel {

    ITermModel getPeriod();

    String getAddress();

    String getOtherPolicyType();

    String getPolicyNumber();

    String getOtherInsuranceCompany();

    String getPolicyholderFirstName();

    String getTypeOfCob();

    String getOtherCoverageType();

    String getPolicyholderRelationshipToPatient();

    String getPolicyholderGender();

    String getPolicyholderLastName();

    String getPlan();

    LocalDate getPolicyholderDateOfBirth();
}
