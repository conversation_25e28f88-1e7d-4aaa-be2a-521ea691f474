<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/CMMN/20151109/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:flowable="http://flowable.org/cmmn" xmlns:cmmndi="http://www.omg.org/spec/CMMN/20151109/CMMNDI" xmlns:dc="http://www.omg.org/spec/CMMN/20151109/DC" xmlns:di="http://www.omg.org/spec/CMMN/20151109/DI" xmlns:design="http://flowable.org/design" targetNamespace="http://www.flowable.org/casedef" design:palette="flowable-work-case-palette">
  <case id="claim.dental.cmmn.Case" name="Dental Claim case management" flowable:initiatorVariableName="initiator">
    <extensionElements>
      <flowable:eventType><![CDATA[CapDentalLoss_initLoss]]></flowable:eventType>
      <flowable:startEventCorrelationConfiguration><![CDATA[storeAsUniqueReferenceId]]></flowable:startEventCorrelationConfiguration>
      <flowable:eventOutParameter source="_key" target="_key"></flowable:eventOutParameter>
      <flowable:eventOutParameter source="_modelName" target="_modelName"></flowable:eventOutParameter>
      <flowable:eventOutParameter source="_modelVersion" target="_modelVersion"></flowable:eventOutParameter>
      <flowable:eventOutParameter source="_uri" target="_uri"></flowable:eventOutParameter>
      <flowable:eventOutParameter source="payload" target="lossPayload"></flowable:eventOutParameter>
    </extensionElements>
    <casePlanModel id="casePlanModel" flowable:formFieldValidation="true">
      <extensionElements>
        <flowable:default-menu-navigation-size><![CDATA[expanded]]></flowable:default-menu-navigation-size>
        <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
      </extensionElements>
      <planItem id="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" name="Open Claim Readjudicate" definitionRef="sid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${(variables:equals('proposal', 'PAY') || variables:equals('proposal', 'PREDET - APPROVE'))}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01" sentryRef="sentrysid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01"></entryCriterion>
        <entryCriterion id="sid-C89E199A-BF24-40C5-8B6C-BDF716A12A39" sentryRef="sentrysid-C89E199A-BF24-40C5-8B6C-BDF716A12A39"></entryCriterion>
        <exitCriterion id="sid-F48AFF3B-4496-4F5B-BDA6-71751CA21047" sentryRef="sentrysid-F48AFF3B-4496-4F5B-BDA6-71751CA21047"></exitCriterion>
        <exitCriterion id="sid-589A19D6-BA6D-4F70-AA09-62CA62214E91" sentryRef="sentrysid-589A19D6-BA6D-4F70-AA09-62CA62214E91"></exitCriterion>
      </planItem>
      <planItem id="planItemPend" name="Pend" definitionRef="Pend">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${variables:equals('proposal', 'PEND')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-653CD7C8-886F-4288-9CF2-28C55F339BA9" sentryRef="sentrysid-653CD7C8-886F-4288-9CF2-28C55F339BA9"></entryCriterion>
        <entryCriterion id="sid-3F35791D-2309-41CE-8C78-CA20F61CF2AA" sentryRef="sentrysid-3F35791D-2309-41CE-8C78-CA20F61CF2AA"></entryCriterion>
        <entryCriterion id="sid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB" sentryRef="sentrysid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB"></entryCriterion>
        <entryCriterion id="sid-D604CCA6-F908-4132-A02A-180ADD837CEE" sentryRef="sentrysid-D604CCA6-F908-4132-A02A-180ADD837CEE"></entryCriterion>
        <exitCriterion id="sid-24A71277-0B30-4892-BD49-F84990E30BCE" sentryRef="sentrysid-24A71277-0B30-4892-BD49-F84990E30BCE"></exitCriterion>
      </planItem>
      <planItem id="planItemSuspended" name="Suspended" definitionRef="Suspended">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${variables:equals('lossState', 'Suspended')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6" sentryRef="sentrysid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6"></entryCriterion>
        <exitCriterion id="sid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63" sentryRef="sentrysid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63"></exitCriterion>
        <exitCriterion id="sid-37163CF3-78C1-4C22-A8CC-3405619614D4" sentryRef="sentrysid-37163CF3-78C1-4C22-A8CC-3405619614D4"></exitCriterion>
      </planItem>
      <planItem id="planItemcloseStage" name="Close " definitionRef="closeStage">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${variables:equals('proposal', 'PREDET - DENY') || variables:equals('proposal', 'DENY') || variables:equals('proposal', 'DENY - ERR')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-963D3343-4F85-4F93-9492-E85A59E870DA" sentryRef="sentrysid-963D3343-4F85-4F93-9492-E85A59E870DA"></entryCriterion>
        <entryCriterion id="sid-6CCECD25-A64D-4A87-A420-EE4234935E23" sentryRef="sentrysid-6CCECD25-A64D-4A87-A420-EE4234935E23"></entryCriterion>
        <entryCriterion id="sid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3" sentryRef="sentrysid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3"></entryCriterion>
        <exitCriterion id="sid-7C03A3CD-634F-4FE6-A581-07C83915A7F8" sentryRef="sentrysid-7C03A3CD-634F-4FE6-A581-07C83915A7F8"></exitCriterion>
      </planItem>
      <planItem id="planIteminitial" name="Initial" definitionRef="initial">
        <exitCriterion id="sid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE" sentryRef="sentrysid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE"></exitCriterion>
      </planItem>
      <planItem id="planItemsid-1D1E6DBF-1A34-4D4B-B63D-D1AA4212BA9A" name="cancel loss schedules for Predet" definitionRef="sid-1D1E6DBF-1A34-4D4B-B63D-D1AA4212BA9A">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${variables:equals('proposal', 'PREDET - APPROVE')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-59355F41-2733-4E97-9943-1CCC36470303" sentryRef="sentrysid-59355F41-2733-4E97-9943-1CCC36470303"></entryCriterion>
        <exitCriterion id="sid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0" sentryRef="sentrysid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0"></exitCriterion>
      </planItem>
      <planItem id="planItemOpenClaimId" name="Open Claim" definitionRef="OpenClaimId">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${(variables:equals('proposal', 'PAY') || variables:equals('proposal', 'PREDET - APPROVE')) && variables:equals('lossState', 'Pending')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="OpenEntry" sentryRef="sentryOpenEntry"></entryCriterion>
        <entryCriterion id="sid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797" sentryRef="sentrysid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797"></entryCriterion>
        <entryCriterion id="sid-09147AA8-01D6-405D-BD49-02AB82DC6E5B" sentryRef="sentrysid-09147AA8-01D6-405D-BD49-02AB82DC6E5B"></entryCriterion>
        <entryCriterion id="sid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2" sentryRef="sentrysid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2"></entryCriterion>
        <exitCriterion id="sid-6216687F-621F-430F-B0E9-BA2E412332AF" sentryRef="sentrysid-6216687F-621F-430F-B0E9-BA2E412332AF"></exitCriterion>
      </planItem>
      <planItem id="planItemsid-6D99DF6F-1C74-4D3D-8B60-F86A6A67F4EE" definitionRef="sid-6D99DF6F-1C74-4D3D-8B60-F86A6A67F4EE">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
            <condition><![CDATA[${variables:equals('proposal', 'PAY')}]]></condition>
          </repetitionRule>
        </itemControl>
        <entryCriterion id="sid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF" sentryRef="sentrysid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF"></entryCriterion>
        <exitCriterion id="sid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F" sentryRef="sentrysid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F"></exitCriterion>
      </planItem>
      <planItem id="planItemsid-CFBEC711-9CCE-4F29-8F97-E453251CF189" name="Validate Dental Claim Data" definitionRef="sid-CFBEC711-9CCE-4F29-8F97-E453251CF189">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
          </repetitionRule>
          <manualActivationRule></manualActivationRule>
        </itemControl>
      </planItem>
      <planItem id="planItemsid-AD857ABF-E271-4D68-A4E1-C8168E913B1E" name="Perform Dental Claim Review" definitionRef="sid-AD857ABF-E271-4D68-A4E1-C8168E913B1E">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
          </repetitionRule>
          <manualActivationRule></manualActivationRule>
        </itemControl>
      </planItem>
      <planItem id="planItemsid-72D0908B-98F1-479F-8BE1-ABB44D01D3AF" name="Audit Dental Claim" definitionRef="sid-72D0908B-98F1-479F-8BE1-ABB44D01D3AF">
        <itemControl>
          <repetitionRule flowable:counterVariable="repetitionCounter">
            <extensionElements></extensionElements>
          </repetitionRule>
          <manualActivationRule></manualActivationRule>
        </itemControl>
      </planItem>
      <planItem id="planItemclose_event" name="Close event" definitionRef="close_event"></planItem>
      <planItem id="planItemreactivate_event" name="Reactivate Listener" definitionRef="reactivate_event"></planItem>
      <sentry id="sentrysid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356" name="case_exit">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356" sourceRef="planItemclose_event">
          <standardEvent>occur</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-F48AFF3B-4496-4F5B-BDA6-71751CA21047">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-F48AFF3B-4496-4F5B-BDA6-71751CA21047" sourceRef="planItempend_adjudicate_process">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01" sourceRef="planItemSuspended">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-C89E199A-BF24-40C5-8B6C-BDF716A12A39">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-C89E199A-BF24-40C5-8B6C-BDF716A12A39" sourceRef="planItemOpenClaimId">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-589A19D6-BA6D-4F70-AA09-62CA62214E91">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-589A19D6-BA6D-4F70-AA09-62CA62214E91" sourceRef="planItemsuspendLossSuspendLossSchedule">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-653CD7C8-886F-4288-9CF2-28C55F339BA9">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-653CD7C8-886F-4288-9CF2-28C55F339BA9" sourceRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-3F35791D-2309-41CE-8C78-CA20F61CF2AA">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-3F35791D-2309-41CE-8C78-CA20F61CF2AA" sourceRef="planItemPend">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-24A71277-0B30-4892-BD49-F84990E30BCE" name="pendStage_exit">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-24A71277-0B30-4892-BD49-F84990E30BCE" sourceRef="planItempendStage_readjudication_process">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB" name="pend from init">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB" sourceRef="planIteminitial">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-D604CCA6-F908-4132-A02A-180ADD837CEE">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-D604CCA6-F908-4132-A02A-180ADD837CEE" sourceRef="planItemSuspended">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6" sourceRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63" sourceRef="planItemsuspendedLossReadjudicate">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-37163CF3-78C1-4C22-A8CC-3405619614D4">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-37163CF3-78C1-4C22-A8CC-3405619614D4" sourceRef="planItemunsuspendLossUnsuspendTemplates">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-7C03A3CD-634F-4FE6-A581-07C83915A7F8">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-7C03A3CD-634F-4FE6-A581-07C83915A7F8" sourceRef="planItemcloseStage_closeLoss">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-963D3343-4F85-4F93-9492-E85A59E870DA">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-963D3343-4F85-4F93-9492-E85A59E870DA" sourceRef="planItemPend">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-6CCECD25-A64D-4A87-A420-EE4234935E23">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-6CCECD25-A64D-4A87-A420-EE4234935E23" sourceRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3" name="close from init">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3" sourceRef="planIteminitial">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE" name="initialStage_exit">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE" sourceRef="planIteminitialStage_submit_process">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-59355F41-2733-4E97-9943-1CCC36470303">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-59355F41-2733-4E97-9943-1CCC36470303" sourceRef="planItemOpenClaimId">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0" sourceRef="planItemcancelStage_closeLoss">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentryOpenEntry" name="openStage_initial_open_entry">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartOpenEntry" sourceRef="planIteminitial">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797" name="enter from openReadjudicate">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797" sourceRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-6216687F-621F-430F-B0E9-BA2E412332AF" name="openStage_exit">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-6216687F-621F-430F-B0E9-BA2E412332AF" sourceRef="planItemopenStage_readjudication_process">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-09147AA8-01D6-405D-BD49-02AB82DC6E5B" name="open from pend">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-09147AA8-01D6-405D-BD49-02AB82DC6E5B" sourceRef="planItemPend">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2" sourceRef="planItemSuspended">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F">
        <extensionElements>
          <design:stencilid><![CDATA[ExitCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F" sourceRef="planItemsid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A">
          <standardEvent>complete</standardEvent>
        </planItemOnPart>
      </sentry>
      <sentry id="sentrysid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF">
        <extensionElements>
          <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
        </extensionElements>
        <planItemOnPart id="sentryOnPartsid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF" sourceRef="planItemOpenClaimId">
          <standardEvent>exit</standardEvent>
        </planItemOnPart>
      </sentry>
      <stage id="sid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" name="Open Claim Readjudicate">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItempend_adjudicate_process" name="Call pendClaim readjudicateSettlement" definitionRef="pend_adjudicate_process">
          <entryCriterion id="sid_335c7473-1d28-483e-a7b9-fe249edb36b2" sentryRef="sentrysid_335c7473-1d28-483e-a7b9-fe249edb36b2"></entryCriterion>
          <entryCriterion id="sid-E7D71951-B672-41B2-9FB6-5328B91CAA62" sentryRef="sentrysid-E7D71951-B672-41B2-9FB6-5328B91CAA62"></entryCriterion>
        </planItem>
        <planItem id="planItemopenLossRefreshSettlement" name="call refreshSettlement " definitionRef="openLossRefreshSettlement">
          <entryCriterion id="sid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05" sentryRef="sentrysid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05"></entryCriterion>
        </planItem>
        <planItem id="planItemsuspendLossSuspendLossSchedule" name="call suspendLossPaymentSchedules" definitionRef="suspendLossSuspendLossSchedule">
          <entryCriterion id="sid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F" sentryRef="sentrysid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F"></entryCriterion>
        </planItem>
        <planItem id="planItemupdateLossEvent" name="update Loss" definitionRef="updateLossEvent"></planItem>
        <planItem id="planItemopenLossReadjudicateSettlementEvent" name="readjudicateSettlement" definitionRef="openLossReadjudicateSettlementEvent"></planItem>
        <planItem id="planItemsuspendLossEvent" name="Suspend Loss" definitionRef="suspendLossEvent"></planItem>
        <sentry id="sentrysid_335c7473-1d28-483e-a7b9-fe249edb36b2">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid_335c7473-1d28-483e-a7b9-fe249edb36b2" sourceRef="planItemopenLossRefreshSettlement">
            <standardEvent>complete</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-E7D71951-B672-41B2-9FB6-5328B91CAA62">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-E7D71951-B672-41B2-9FB6-5328B91CAA62" sourceRef="planItemopenLossReadjudicateSettlementEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05" sourceRef="planItemupdateLossEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F" sourceRef="planItemsuspendLossEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="pend_adjudicate_process" name="Call pendClaim readjudicateSettlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementNextKey" target="settlementKey"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:out sourceExpression="${adjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
            <flowable:out source="settlementURI" target="settlementURI"></flowable:out>
            <flowable:out sourceExpression="${adjudicationResult._key}" target="settlementKey"></flowable:out>
            <flowable:out source="settlementModelName" target="settlementModelName"></flowable:out>
            <flowable:out source="settlementVersion" target="settlementVersion"></flowable:out>
            <flowable:out sourceExpression="${adjudicationResult._key.revisionNo}" target="settlementRevisionNo"></flowable:out>
            <flowable:out sourceExpression="{&quot;rootId&quot; : &quot;${adjudicationResult._key.rootId}&quot;, &quot;revisionNo&quot;: ${adjudicationResult._key.revisionNo+1}}" target="settlementNextKey"></flowable:out>
            <flowable:out sourceExpression="gentity://${adjudicationResult._modelType}/${adjudicationResult._modelName}//${adjudicationResult._key.rootId}/${adjudicationResult._key.revisionNo+1}" target="settlementNextUri"></flowable:out>
            <flowable:out sourceExpression="${lossPayload.state}" target="lossState"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[pendclaimReadjudicateSettlement]]></processRefExpression>
        </processTask>
        <processTask id="openLossRefreshSettlement" name="call refreshSettlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="_modelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="_modelVersion"></flowable:in>
            <flowable:out source="_key" target="settlementKey"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[refreshSettlementCall]]></processRefExpression>
        </processTask>
        <processTask id="suspendLossSuspendLossSchedule" name="call suspendLossPaymentSchedules" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in sourceExpression="${lossPayload.state}" target="lossState"></flowable:in>
            <flowable:out source="lossState" target="lossState"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[suspendLossPaymentSchedulesProcess]]></processRefExpression>
        </processTask>
        <eventListener id="updateLossEvent" name="update Loss">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_updateLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
        <eventListener id="openLossReadjudicateSettlementEvent" name="readjudicateSettlement">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalSettlement_readjudicateSettlement]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${settlementNextUri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="_key" target="settlementKey"></flowable:eventOutParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
        <eventListener id="suspendLossEvent" name="Suspend Loss">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_suspendLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="payload" target="lossPayload"></flowable:eventOutParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
      </stage>
      <stage id="Pend" name="Pend">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemPendState_refreshSettlement" name="call refreshSettlement(Pend)" definitionRef="PendState_refreshSettlement">
          <entryCriterion id="sid-B8819ABF-7C9F-4DBA-9343-98A580988607" sentryRef="sentrysid-B8819ABF-7C9F-4DBA-9343-98A580988607"></entryCriterion>
        </planItem>
        <planItem id="planItempendStage_readjudication_process" name="Pend adjudicateSettlement" definitionRef="pendStage_readjudication_process">
          <entryCriterion id="sid_a9a4da51-f256-4192-8953-762492cad8b9" sentryRef="sentrysid_a9a4da51-f256-4192-8953-762492cad8b9"></entryCriterion>
          <entryCriterion id="sid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32" sentryRef="sentrysid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32"></entryCriterion>
        </planItem>
        <planItem id="planItempendStage_professional_review_required_task" name="Professional Review Required" definitionRef="pendStage_professional_review_required_task"></planItem>
        <planItem id="planItempendClaimCancelPaymentSchedules" name="Pend Claim Cancel Payment Schedules" definitionRef="pendClaimCancelPaymentSchedules"></planItem>
        <planItem id="planItempendLossReadjudicateEvent" name="Readjudicate event" definitionRef="pendLossReadjudicateEvent"></planItem>
        <planItem id="planItemupdatePendLossEvent" name="Update Loss (Pend)" definitionRef="updatePendLossEvent"></planItem>
        <sentry id="sentrysid-B8819ABF-7C9F-4DBA-9343-98A580988607">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-B8819ABF-7C9F-4DBA-9343-98A580988607" sourceRef="planItemupdatePendLossEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid_a9a4da51-f256-4192-8953-762492cad8b9">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid_a9a4da51-f256-4192-8953-762492cad8b9" sourceRef="planItemPendState_refreshSettlement">
            <standardEvent>complete</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32" sourceRef="planItempendLossReadjudicateEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="PendState_refreshSettlement" name="call refreshSettlement(Pend)" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="_modelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="_modelVersion"></flowable:in>
            <flowable:out source="_key" target="settlementKey"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[refreshSettlementCall]]></processRefExpression>
        </processTask>
        <processTask id="pendStage_readjudication_process" name="Pend adjudicateSettlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementNextKey" target="_key"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:out sourceExpression="${adjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
            <flowable:out sourceExpression="{&quot;rootId&quot; : &quot;${adjudicationResult._key.rootId}&quot;, &quot;revisionNo&quot;: ${adjudicationResult._key.revisionNo+1}}" target="settlementNextKey"></flowable:out>
            <flowable:out sourceExpression="${adjudicationResult._key}" target="settlementKey"></flowable:out>
            <flowable:out sourceExpression="gentity://${adjudicationResult._modelType}/${adjudicationResult._modelName}//${adjudicationResult._key.rootId}/${adjudicationResult._key.revisionNo+1}" target="settlementNextUri"></flowable:out>
            <flowable:out source="settlementURI" target="settlementURI"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[pendAdjudicateSettlementProcess]]></processRefExpression>
        </processTask>
        <humanTask id="pendStage_professional_review_required_task" name="Professional Review Required" flowable:formFieldValidation="true" flowable:priority="1">
          <extensionElements>
            <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
            <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_professional_review', 'candidate')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Professional Review Required')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_uri', _uri)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_modelName', 'CapLoss')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityModelName', _modelName)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityRefNo', lossPayload.lossNumber)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityURI', _uri)}"></flowable:taskListener>
          </extensionElements>
        </humanTask>
        <processTask id="pendClaimCancelPaymentSchedules" name="Pend Claim Cancel Payment Schedules" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="settlementURI" target="settlementURI"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in sourceExpression="${proposal == 'PEND' ? 'ProfessionalReview' : ''}" target="lossSubStatusCd"></flowable:in>
          </extensionElements>
          <processRefExpression><![CDATA[pendClaimCancelPaymentSchedule]]></processRefExpression>
        </processTask>
        <eventListener id="pendLossReadjudicateEvent" name="Readjudicate event">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalSettlement_readjudicateSettlement]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${settlementNextUri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
        <eventListener id="updatePendLossEvent" name="Update Loss (Pend)">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_updateLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
      </stage>
      <stage id="Suspended" name="Suspended">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemunsuspendLossUnsuspendTemplates" name="call unsuspendLossPaymentTemplates" definitionRef="unsuspendLossUnsuspendTemplates">
          <entryCriterion id="sid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B" sentryRef="sentrysid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B"></entryCriterion>
        </planItem>
        <planItem id="planItemsuspendedLossReadjudicate" name="Call pendClaim readjudicateSettlement" definitionRef="suspendedLossReadjudicate">
          <entryCriterion id="sid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe" sentryRef="sentrysid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe"></entryCriterion>
          <entryCriterion id="sid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293" sentryRef="sentrysid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293"></entryCriterion>
        </planItem>
        <planItem id="planItemsuspendedLossRefreshSettlement" name="call refreshSettlement " definitionRef="suspendedLossRefreshSettlement">
          <entryCriterion id="sid-670CB7BA-DA6B-40C0-9366-28368FF9EF39" sentryRef="sentrysid-670CB7BA-DA6B-40C0-9366-28368FF9EF39"></entryCriterion>
        </planItem>
        <planItem id="planItemsuspendStage_review_suspended_claim_task" name="Review Suspended Dental Claim" definitionRef="suspendStage_review_suspended_claim_task"></planItem>
        <planItem id="planItemupdateSuspendedLossEvent" name="update Loss" definitionRef="updateSuspendedLossEvent"></planItem>
        <planItem id="planItemunsuspendLossEvent" name="Unsuspend Loss" definitionRef="unsuspendLossEvent"></planItem>
        <planItem id="planItemsuspendedLossReadjudicateSettlementEvent" name="readjudicateSettlement" definitionRef="suspendedLossReadjudicateSettlementEvent"></planItem>
        <sentry id="sentrysid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B" sourceRef="planItemunsuspendLossEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe" sourceRef="planItemsuspendedLossRefreshSettlement">
            <standardEvent>complete</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293" sourceRef="planItemsuspendedLossReadjudicateSettlementEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <sentry id="sentrysid-670CB7BA-DA6B-40C0-9366-28368FF9EF39">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-670CB7BA-DA6B-40C0-9366-28368FF9EF39" sourceRef="planItemupdateSuspendedLossEvent">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="unsuspendLossUnsuspendTemplates" name="call unsuspendLossPaymentTemplates" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in sourceExpression="${lossPayload.state}" target="lossState"></flowable:in>
            <flowable:out source="lossState" target="lossState"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[unsuspendLossPaymentSchedules]]></processRefExpression>
        </processTask>
        <processTask id="suspendedLossReadjudicate" name="Call pendClaim readjudicateSettlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementNextKey" target="settlementKey"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:out sourceExpression="${adjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
            <flowable:out source="settlementURI" target="settlementURI"></flowable:out>
            <flowable:out source="settlementKey" target="settlementKey"></flowable:out>
            <flowable:out source="settlementModelName" target="settlementModelName"></flowable:out>
            <flowable:out source="settlementVersion" target="settlementVersion"></flowable:out>
            <flowable:out sourceExpression="${adjudicationResult._key.revisionNo}" target="settlementRevisionNo"></flowable:out>
            <flowable:out sourceExpression="{&quot;rootId&quot;: &quot;${adjudicationResult._key.rootId}&quot;, &quot;revisionNo&quot;:${adjudicationResult._key.revisionNo+1}}" target="settlementNextKey"></flowable:out>
            <flowable:out sourceExpression="gentity://${adjudicationResult._modelType}/${adjudicationResult._modelName}//${adjudicationResult._key.rootId}/${adjudicationResult._key.revisionNo+1}" target="settlementNextUri"></flowable:out>
            <flowable:out sourceExpression="${lossPayload.state}" target="lossState"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[pendclaimReadjudicateSettlement]]></processRefExpression>
        </processTask>
        <processTask id="suspendedLossRefreshSettlement" name="call refreshSettlement " flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="_modelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="_modelVersion"></flowable:in>
            <flowable:out source="_key" target="settlementKey"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[refreshSettlementCall]]></processRefExpression>
        </processTask>
        <humanTask id="suspendStage_review_suspended_claim_task" name="Review Suspended Dental Claim" flowable:formFieldValidation="true" flowable:priority="1">
          <extensionElements>
            <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
            <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Review Suspended Dental Claim')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_management', 'candidate')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_uri', _uri)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_modelName', 'CapLoss')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityModelName', _modelName)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityRefNo', lossPayload.lossNumber)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityURI', _uri)}"></flowable:taskListener>
          </extensionElements>
        </humanTask>
        <eventListener id="updateSuspendedLossEvent" name="update Loss">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_updateLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
        <eventListener id="unsuspendLossEvent" name="Unsuspend Loss">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_unsuspendLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="payload" target="lossPayload"></flowable:eventOutParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
        <eventListener id="suspendedLossReadjudicateSettlementEvent" name="readjudicateSettlement">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalSettlement_readjudicateSettlement]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${settlementNextUri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="_key" target="settlementKey"></flowable:eventOutParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
      </stage>
      <stage id="closeStage" name="Close ">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemcloseStage_closeLoss" name="Request Close Loss" definitionRef="closeStage_closeLoss">
          <entryCriterion id="sid-5A155F15-E3EA-45F4-8B74-29598406647D" sentryRef="sentrysid-5A155F15-E3EA-45F4-8B74-29598406647D"></entryCriterion>
        </planItem>
        <planItem id="planItemcloseStage_disapproveSettlement" name="Disapprove settlement" definitionRef="closeStage_disapproveSettlement"></planItem>
        <sentry id="sentrysid-5A155F15-E3EA-45F4-8B74-29598406647D">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-5A155F15-E3EA-45F4-8B74-29598406647D" sourceRef="planItemcloseStage_disapproveSettlement">
            <standardEvent>complete</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="closeStage_closeLoss" name="Request Close Loss" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in sourceExpression="${proposal == 'PREDET - DENY' ? 'Predetermined' : 'Denied'}" target="reasonCd"></flowable:in>
          </extensionElements>
          <processRefExpression><![CDATA[requestCloseLossCall]]></processRefExpression>
        </processTask>
        <processTask id="closeStage_disapproveSettlement" name="Disapprove settlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="settlementModelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="settlementModelVersion"></flowable:in>
          </extensionElements>
          <processRefExpression><![CDATA[disapproveSettlement]]></processRefExpression>
        </processTask>
      </stage>
      <stage id="initial" name="Initial">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planIteminitialStage_submit_process" name="Initial settlement creation adjudication" definitionRef="initialStage_submit_process">
          <entryCriterion id="sid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC" sentryRef="sentrysid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC"></entryCriterion>
        </planItem>
        <planItem id="planIteminitStage_finish_dental_claim_intake_task" name="Finish Dental Claim Intake" definitionRef="initStage_finish_dental_claim_intake_task"></planItem>
        <planItem id="planIteminitialStage_submit_event" name="Submit event" definitionRef="initialStage_submit_event"></planItem>
        <sentry id="sentrysid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC" sourceRef="planIteminitialStage_submit_event">
            <standardEvent>occur</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="initialStage_submit_process" name="Initial settlement creation adjudication" flowable:async="true" flowable:exclusive="false" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="settlementURI" target="settlementURI"></flowable:in>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="settlementModelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="settlementModelVersion"></flowable:in>
            <flowable:in sourceExpression="${lossPayload.state}" target="lossState"></flowable:in>
            <flowable:out source="settlementKey" target="settlementKey"></flowable:out>
            <flowable:out source="settlementURI" target="settlementURI"></flowable:out>
            <flowable:out sourceExpression="${adjudicationResult.settlementResult.proposal}" target="proposal"></flowable:out>
            <flowable:out source="settlementModelName" target="settlementModelName"></flowable:out>
            <flowable:out source="settlementModelVersion" target="settlementModelVersion"></flowable:out>
            <flowable:out sourceExpression="gentity://${adjudicationResult._modelType}/${adjudicationResult._modelName}//${adjudicationResult._key.rootId}/${adjudicationResult._key.revisionNo+1}" target="settlementNextUri"></flowable:out>
            <flowable:out sourceExpression="{&quot;rootId&quot; : &quot;${settlementCreationResult._key.rootId}&quot;, &quot;revisionNo&quot;: ${settlementCreationResult._key.revisionNo+1}}" target="settlementNextKey"></flowable:out>
            <flowable:out source="lossState" target="lossState"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[initial_adjudicate_settlement]]></processRefExpression>
        </processTask>
        <humanTask id="initStage_finish_dental_claim_intake_task" name="Finish Dental Claim Intake" flowable:formFieldValidation="true" flowable:priority="1">
          <extensionElements>
            <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
            <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_intake', 'candidate')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Finish Dental Claim Intake')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_uri', _uri)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('_modelName', 'CapLoss')}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityModelName', _modelName)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityRefNo', lossPayload.lossNumber)}"></flowable:taskListener>
            <flowable:taskListener event="create" expression="${task.setVariableLocal('entityURI', _uri)}"></flowable:taskListener>
          </extensionElements>
        </humanTask>
        <eventListener id="initialStage_submit_event" name="Submit event">
          <extensionElements>
            <flowable:eventType><![CDATA[CapDentalLoss_submitLoss]]></flowable:eventType>
            <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
            <flowable:eventOutParameter source="payload" target="lossPayload"></flowable:eventOutParameter>
            <flowable:eventOutParameter source="userId" target="userId"></flowable:eventOutParameter>
            <design:stencilid><![CDATA[EventListener]]></design:stencilid>
            <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
          </extensionElements>
        </eventListener>
      </stage>
      <stage id="sid-1D1E6DBF-1A34-4D4B-B63D-D1AA4212BA9A" name="cancel loss schedules for Predet">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemcancelStage_closeLoss" name="Request Close Loss" definitionRef="cancelStage_closeLoss">
          <entryCriterion id="sid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A" sentryRef="sentrysid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A"></entryCriterion>
        </planItem>
        <planItem id="planItemcancelStage_closeSettlement" name="Close settlement" definitionRef="cancelStage_closeSettlement"></planItem>
        <sentry id="sentrysid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A">
          <extensionElements>
            <design:stencilid><![CDATA[EntryCriterion]]></design:stencilid>
          </extensionElements>
          <planItemOnPart id="sentryOnPartsid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A" sourceRef="planItemcancelStage_closeSettlement">
            <standardEvent>complete</standardEvent>
          </planItemOnPart>
        </sentry>
        <processTask id="cancelStage_closeLoss" name="Request Close Loss" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="_key" target="lossKey"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_modelName" target="_modelName"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in sourceExpression="Predetermined" target="reasonCd"></flowable:in>
          </extensionElements>
          <processRefExpression><![CDATA[requestCloseLossCall]]></processRefExpression>
        </processTask>
        <processTask id="cancelStage_closeSettlement" name="Close settlement" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="settlementModelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="settlementModelVersion"></flowable:in>
          </extensionElements>
          <processRefExpression><![CDATA[closeSettlement]]></processRefExpression>
        </processTask>
      </stage>
      <stage id="OpenClaimId" name="Open Claim">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemopenStage_readjudication_process" name="Open" definitionRef="openStage_readjudication_process"></planItem>
        <processTask id="openStage_readjudication_process" name="Open" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementKey" target="settlementKey"></flowable:in>
            <flowable:in source="settlementModelName" target="_modelName"></flowable:in>
            <flowable:in source="settlementModelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="_key" target="lossKey"></flowable:in>
            <flowable:out sourceExpression="gentity://${approvedSettlement._modelType}/${approvedSettlement._modelName}//${approvedSettlement._key.rootId}/${approvedSettlement._key.revisionNo+1}" target="settlementNextUri"></flowable:out>
            <flowable:out source="settlementURI" target="settlementURI"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[openClaim]]></processRefExpression>
        </processTask>
      </stage>
      <stage id="sid-6D99DF6F-1C74-4D3D-8B60-F86A6A67F4EE">
        <extensionElements>
          <design:stencilid><![CDATA[ExpandedStage]]></design:stencilid>
        </extensionElements>
        <planItem id="planItemsid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A" name="Build Payment Schedule" definitionRef="sid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A"></planItem>
        <processTask id="sid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A" name="Build Payment Schedule" flowable:async="true" flowable:exclusive="true" flowable:fallbackToDefaultTenant="true">
          <extensionElements>
            <design:stencilid><![CDATA[ProcessTask]]></design:stencilid>
            <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
            <flowable:in source="settlementURI" target="settlementURI"></flowable:in>
            <flowable:in source="_key" target="_key"></flowable:in>
            <flowable:in source="_modelVersion" target="_modelVersion"></flowable:in>
            <flowable:in source="_uri" target="_uri"></flowable:in>
            <flowable:in source="userId" target="userId"></flowable:in>
            <flowable:out sourceExpression="${_uri}" target="paymentScheduleUri"></flowable:out>
          </extensionElements>
          <processRefExpression><![CDATA[claimdentalbpmnbuildPaymentSchedule]]></processRefExpression>
        </processTask>
      </stage>
      <humanTask id="sid-CFBEC711-9CCE-4F29-8F97-E453251CF189" name="Validate Dental Claim Data" flowable:formFieldValidation="true" flowable:priority="1">
        <extensionElements>
          <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
          <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
          <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
          <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Validate Dental Claim Data')}"></flowable:taskListener>
          <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_review', 'candidate')}"></flowable:taskListener>
        </extensionElements>
      </humanTask>
      <humanTask id="sid-AD857ABF-E271-4D68-A4E1-C8168E913B1E" name="Perform Dental Claim Review" flowable:formFieldValidation="true" flowable:priority="1">
        <extensionElements>
          <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
          <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
          <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
          <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Perform Dental Claim Review')}"></flowable:taskListener>
          <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_review', 'candidate')}"></flowable:taskListener>
        </extensionElements>
      </humanTask>
      <humanTask id="sid-72D0908B-98F1-479F-8BE1-ABB44D01D3AF" name="Audit Dental Claim" flowable:formFieldValidation="true" flowable:priority="1">
        <extensionElements>
          <flowable:task-candidates-type><![CDATA[all]]></flowable:task-candidates-type>
          <design:stencilid><![CDATA[HumanTask]]></design:stencilid>
          <design:stencilsuperid><![CDATA[Task]]></design:stencilsuperid>
          <flowable:taskListener event="create" expression="${task.setVariableLocal('description', 'Audit Dental Claim')}"></flowable:taskListener>
          <flowable:taskListener event="create" expression="${task.addGroupIdentityLink('claim_audit', 'candidate')}"></flowable:taskListener>
        </extensionElements>
      </humanTask>
      <eventListener id="close_event" name="Close event">
        <extensionElements>
          <flowable:eventType><![CDATA[CapDentalLoss_closeLoss]]></flowable:eventType>
          <flowable:eventCorrelationParameter name="_uri" value="${_uri}"></flowable:eventCorrelationParameter>
          <design:stencilid><![CDATA[EventListener]]></design:stencilid>
          <design:display_ref_in_diagram><![CDATA[true]]></design:display_ref_in_diagram>
        </extensionElements>
      </eventListener>
      <eventListener id="reactivate_event" name="Reactivate Listener" flowable:eventType="reactivate">
      </eventListener>
      <exitCriterion id="sid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356" sentryRef="sentrysid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356"></exitCriterion>
    </casePlanModel>
  </case>
  <cmmndi:CMMNDI>
    <cmmndi:CMMNDiagram id="CMMNDiagram_claim.dental.cmmn.Case">
      <cmmndi:CMMNShape id="CMMNShape_casePlanModel" cmmnElementRef="casePlanModel">
        <dc:Bounds height="1349.0" width="2367.0" x="-1287.8" y="76.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356" cmmnElementRef="sid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356">
        <dc:Bounds height="28.0" width="18.0" x="1075.0" y="1244.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" cmmnElementRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30">
        <dc:Bounds height="462.0" width="635.0" x="-398.79999999999995" y="607.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01" cmmnElementRef="sid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01">
        <dc:Bounds height="28.0" width="18.0" x="-405.79999999999995" y="877.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-C89E199A-BF24-40C5-8B6C-BDF716A12A39" cmmnElementRef="sid-C89E199A-BF24-40C5-8B6C-BDF716A12A39">
        <dc:Bounds height="28.0" width="18.0" x="-95.79999999999995" y="596.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-F48AFF3B-4496-4F5B-BDA6-71751CA21047" cmmnElementRef="sid-F48AFF3B-4496-4F5B-BDA6-71751CA21047">
        <dc:Bounds height="28.0" width="18.0" x="230.20000000000005" y="824.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-589A19D6-BA6D-4F70-AA09-62CA62214E91" cmmnElementRef="sid-589A19D6-BA6D-4F70-AA09-62CA62214E91">
        <dc:Bounds height="28.0" width="18.0" x="230.20000000000005" y="947.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItempend_adjudicate_process" cmmnElementRef="planItempend_adjudicate_process">
        <dc:Bounds height="77.0" width="162.0" x="-24.799999999999955" y="788.8627450980392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid_335c7473-1d28-483e-a7b9-fe249edb36b2" cmmnElementRef="sid_335c7473-1d28-483e-a7b9-fe249edb36b2">
        <dc:Bounds height="28.0" width="18.0" x="50.200000000000045" y="774.8627450980392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-E7D71951-B672-41B2-9FB6-5328B91CAA62" cmmnElementRef="sid-E7D71951-B672-41B2-9FB6-5328B91CAA62">
        <dc:Bounds height="28.0" width="18.0" x="-33.799999999999955" y="813.8627450980392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemopenLossRefreshSettlement" cmmnElementRef="planItemopenLossRefreshSettlement">
        <dc:Bounds height="72.0" width="161.0" x="-78.29999999999995" y="620.5"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05" cmmnElementRef="sid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05">
        <dc:Bounds height="28.0" width="18.0" x="-86.29999999999995" y="655.5"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendLossSuspendLossSchedule" cmmnElementRef="planItemsuspendLossSuspendLossSchedule">
        <dc:Bounds height="80.0" width="134.0" x="-10.799999999999955" y="909.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F" cmmnElementRef="sid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F">
        <dc:Bounds height="28.0" width="18.0" x="-18.799999999999955" y="936.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemupdateLossEvent" cmmnElementRef="planItemupdateLossEvent">
        <dc:Bounds height="30.0" width="30.0" x="-157.79999999999995" y="651.5"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="65.0" x="-175.29999999999995" y="685.5"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemopenLossReadjudicateSettlementEvent" cmmnElementRef="planItemopenLossReadjudicateSettlementEvent">
        <dc:Bounds height="30.0" width="30.0" x="-157.79999999999995" y="810.8627450980392"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="123.0" x="-204.29999999999995" y="844.8627450980392"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendLossEvent" cmmnElementRef="planItemsuspendLossEvent">
        <dc:Bounds height="30.0" width="30.0" x="-157.79999999999995" y="932.0"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="76.0" x="-180.79999999999995" y="966.0"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemPend" cmmnElementRef="planItemPend">
        <dc:Bounds height="362.0" width="504.0" x="-1251.8" y="406.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-653CD7C8-886F-4288-9CF2-28C55F339BA9" cmmnElementRef="sid-653CD7C8-886F-4288-9CF2-28C55F339BA9">
        <dc:Bounds height="28.0" width="18.0" x="-753.8" y="698.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-3F35791D-2309-41CE-8C78-CA20F61CF2AA" cmmnElementRef="sid-3F35791D-2309-41CE-8C78-CA20F61CF2AA">
        <dc:Bounds height="28.0" width="18.0" x="-900.8" y="758.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB" cmmnElementRef="sid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB">
        <dc:Bounds height="28.0" width="18.0" x="-993.8" y="395.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-D604CCA6-F908-4132-A02A-180ADD837CEE" cmmnElementRef="sid-D604CCA6-F908-4132-A02A-180ADD837CEE">
        <dc:Bounds height="28.0" width="18.0" x="-809.8" y="758.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-24A71277-0B30-4892-BD49-F84990E30BCE" cmmnElementRef="sid-24A71277-0B30-4892-BD49-F84990E30BCE">
        <dc:Bounds height="28.0" width="18.0" x="-753.8" y="482.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemPendState_refreshSettlement" cmmnElementRef="planItemPendState_refreshSettlement">
        <dc:Bounds height="96.0" width="185.0" x="-999.3" y="617.5"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-B8819ABF-7C9F-4DBA-9343-98A580988607" cmmnElementRef="sid-B8819ABF-7C9F-4DBA-9343-98A580988607">
        <dc:Bounds height="28.0" width="18.0" x="-1006.3" y="654.5"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItempendStage_readjudication_process" cmmnElementRef="planItempendStage_readjudication_process">
        <dc:Bounds height="82.0" width="196.0" x="-1032.8" y="451.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid_a9a4da51-f256-4192-8953-762492cad8b9" cmmnElementRef="sid_a9a4da51-f256-4192-8953-762492cad8b9">
        <dc:Bounds height="28.0" width="18.0" x="-911.8" y="519.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32" cmmnElementRef="sid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32">
        <dc:Bounds height="28.0" width="18.0" x="-1039.8" y="491.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItempendStage_professional_review_required_task" cmmnElementRef="planItempendStage_professional_review_required_task">
        <dc:Bounds height="80.0" width="100.0" x="-1236.8" y="436.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItempendClaimCancelPaymentSchedules" cmmnElementRef="planItempendClaimCancelPaymentSchedules">
        <dc:Bounds height="80.0" width="100.0" x="-1217.8" y="545.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItempendLossReadjudicateEvent" cmmnElementRef="planItempendLossReadjudicateEvent">
        <dc:Bounds height="30.0" width="30.0" x="-1115.3" y="488.0"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="103.0" x="-1151.8" y="522.0"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemupdatePendLossEvent" cmmnElementRef="planItemupdatePendLossEvent">
        <dc:Bounds height="30.0" width="30.0" x="-1167.8" y="650.5"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="107.0" x="-1206.3" y="684.5"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemSuspended" cmmnElementRef="planItemSuspended">
        <dc:Bounds height="384.0" width="431.0" x="-927.8" y="936.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6" cmmnElementRef="sid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6">
        <dc:Bounds height="28.0" width="18.0" x="-502.79999999999995" y="1021.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63" cmmnElementRef="sid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63">
        <dc:Bounds height="28.0" width="18.0" x="-505.79999999999995" y="1075.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-37163CF3-78C1-4C22-A8CC-3405619614D4" cmmnElementRef="sid-37163CF3-78C1-4C22-A8CC-3405619614D4">
        <dc:Bounds height="28.0" width="18.0" x="-505.79999999999995" y="1228.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemunsuspendLossUnsuspendTemplates" cmmnElementRef="planItemunsuspendLossUnsuspendTemplates">
        <dc:Bounds height="85.0" width="162.0" x="-745.8" y="1186.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B" cmmnElementRef="sid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B">
        <dc:Bounds height="28.0" width="18.0" x="-753.8" y="1220.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendedLossReadjudicate" cmmnElementRef="planItemsuspendedLossReadjudicate">
        <dc:Bounds height="74.0" width="162.0" x="-745.8" y="1074.7664739884392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe" cmmnElementRef="sid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe">
        <dc:Bounds height="28.0" width="18.0" x="-663.8" y="1060.7664739884392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293" cmmnElementRef="sid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293">
        <dc:Bounds height="28.0" width="18.0" x="-753.8" y="1102.7664739884392"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendedLossRefreshSettlement" cmmnElementRef="planItemsuspendedLossRefreshSettlement">
        <dc:Bounds height="72.0" width="161.0" x="-687.8" y="965.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-670CB7BA-DA6B-40C0-9366-28368FF9EF39" cmmnElementRef="sid-670CB7BA-DA6B-40C0-9366-28368FF9EF39">
        <dc:Bounds height="28.0" width="18.0" x="-695.8" y="985.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendStage_review_suspended_claim_task" cmmnElementRef="planItemsuspendStage_review_suspended_claim_task">
        <dc:Bounds height="80.0" width="100.0" x="-912.8" y="961.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemupdateSuspendedLossEvent" cmmnElementRef="planItemupdateSuspendedLossEvent">
        <dc:Bounds height="30.0" width="30.0" x="-777.8" y="980.8819559750978"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="65.0" x="-795.3" y="1014.8819559750978"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemunsuspendLossEvent" cmmnElementRef="planItemunsuspendLossEvent">
        <dc:Bounds height="30.0" width="30.0" x="-859.8" y="1213.5"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="89.0" x="-887.0" y="1247.0476498715489"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsuspendedLossReadjudicateSettlementEvent" cmmnElementRef="planItemsuspendedLossReadjudicateSettlementEvent">
        <dc:Bounds height="30.0" width="30.0" x="-877.8" y="1096.7664739884392"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="123.0" x="-923.4000000000001" y="1144.7664739884392"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemcloseStage" cmmnElementRef="planItemcloseStage">
        <dc:Bounds height="181.0" width="580.0" x="-552.8" y="91.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-963D3343-4F85-4F93-9492-E85A59E870DA" cmmnElementRef="sid-963D3343-4F85-4F93-9492-E85A59E870DA">
        <dc:Bounds height="28.0" width="18.0" x="-517.7389582010669" y="261.34403124371073"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-6CCECD25-A64D-4A87-A420-EE4234935E23" cmmnElementRef="sid-6CCECD25-A64D-4A87-A420-EE4234935E23">
        <dc:Bounds height="28.0" width="18.0" x="-4.854038350905512" y="261.32427851986"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3" cmmnElementRef="sid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3">
        <dc:Bounds height="28.0" width="18.0" x="-560.8" y="186.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-7C03A3CD-634F-4FE6-A581-07C83915A7F8" cmmnElementRef="sid-7C03A3CD-634F-4FE6-A581-07C83915A7F8">
        <dc:Bounds height="28.0" width="18.0" x="21.200000000000045" y="195.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemcloseStage_closeLoss" cmmnElementRef="planItemcloseStage_closeLoss">
        <dc:Bounds height="86.0" width="219.0" x="-282.79999999999995" y="163.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-5A155F15-E3EA-45F4-8B74-29598406647D" cmmnElementRef="sid-5A155F15-E3EA-45F4-8B74-29598406647D">
        <dc:Bounds height="28.0" width="18.0" x="-289.79999999999995" y="203.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemcloseStage_disapproveSettlement" cmmnElementRef="planItemcloseStage_disapproveSettlement">
        <dc:Bounds height="80.0" width="100.0" x="-472.69136429704224" y="159.81501211249687"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planIteminitial" cmmnElementRef="planIteminitial">
        <dc:Bounds height="185.0" width="486.0" x="-1229.8" y="113.8150121124969"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE" cmmnElementRef="sid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE">
        <dc:Bounds height="28.0" width="18.0" x="-749.8" y="186.8150121124969"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planIteminitialStage_submit_process" cmmnElementRef="planIteminitialStage_submit_process">
        <dc:Bounds height="80.0" width="100.0" x="-956.8" y="159.8150121124969"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC" cmmnElementRef="sid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC">
        <dc:Bounds height="28.0" width="18.0" x="-964.8" y="190.8150121124969"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planIteminitStage_finish_dental_claim_intake_task" cmmnElementRef="planIteminitStage_finish_dental_claim_intake_task">
        <dc:Bounds height="80.0" width="100.0" x="-1206.8" y="181.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planIteminitialStage_submit_event" cmmnElementRef="planIteminitialStage_submit_event">
        <dc:Bounds height="30.0" width="30.0" x="-1077.8" y="166.50000000000006"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="70.0" x="-1097.8" y="200.50000000000006"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-1D1E6DBF-1A34-4D4B-B63D-D1AA4212BA9A" cmmnElementRef="planItemsid-1D1E6DBF-1A34-4D4B-B63D-D1AA4212BA9A">
        <dc:Bounds height="196.0" width="455.0" x="183.20000000000005" y="241.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-59355F41-2733-4E97-9943-1CCC36470303" cmmnElementRef="sid-59355F41-2733-4E97-9943-1CCC36470303">
        <dc:Bounds height="28.0" width="18.0" x="176.20000000000005" y="321.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0" cmmnElementRef="sid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0">
        <dc:Bounds height="28.0" width="18.0" x="633.2" y="328.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemcancelStage_closeLoss" cmmnElementRef="planItemcancelStage_closeLoss">
        <dc:Bounds height="86.0" width="219.0" x="362.20000000000005" y="288.8985507246377"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A" cmmnElementRef="sid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A">
        <dc:Bounds height="28.0" width="18.0" x="355.20000000000005" y="328.8985507246377"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemcancelStage_closeSettlement" cmmnElementRef="planItemcancelStage_closeSettlement">
        <dc:Bounds height="80.0" width="100.0" x="197.20000000000005" y="299.8985507246377"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemOpenClaimId" cmmnElementRef="planItemOpenClaimId">
        <dc:Bounds height="149.0" width="315.0" x="-432.79999999999995" y="301.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_OpenEntry" cmmnElementRef="OpenEntry">
        <dc:Bounds height="28.0" width="18.0" x="-439.79999999999995" y="319.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797" cmmnElementRef="sid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797">
        <dc:Bounds height="28.0" width="18.0" x="-290.79999999999995" y="440.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-09147AA8-01D6-405D-BD49-02AB82DC6E5B" cmmnElementRef="sid-09147AA8-01D6-405D-BD49-02AB82DC6E5B">
        <dc:Bounds height="28.0" width="18.0" x="-431.79999999999995" y="439.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2" cmmnElementRef="sid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2">
        <dc:Bounds height="28.0" width="18.0" x="-439.79999999999995" y="380.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-6216687F-621F-430F-B0E9-BA2E412332AF" cmmnElementRef="sid-6216687F-621F-430F-B0E9-BA2E412332AF">
        <dc:Bounds height="28.0" width="18.0" x="-123.79999999999995" y="370.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemopenStage_readjudication_process" cmmnElementRef="planItemopenStage_readjudication_process">
        <dc:Bounds height="80.0" width="100.0" x="-267.79999999999995" y="341.22079844998825"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-6D99DF6F-1C74-4D3D-8B60-F86A6A67F4EE" cmmnElementRef="planItemsid-6D99DF6F-1C74-4D3D-8B60-F86A6A67F4EE">
        <dc:Bounds height="129.0" width="235.0" x="92.20000000000005" y="451.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF" cmmnElementRef="sid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF">
        <dc:Bounds height="28.0" width="18.0" x="84.20000000000005" y="499.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_sid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F" cmmnElementRef="sid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F">
        <dc:Bounds height="28.0" width="18.0" x="321.20000000000005" y="498.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A" cmmnElementRef="planItemsid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A">
        <dc:Bounds height="80.0" width="100.0" x="159.70000000000005" y="469.1904453593362"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-CFBEC711-9CCE-4F29-8F97-E453251CF189" cmmnElementRef="planItemsid-CFBEC711-9CCE-4F29-8F97-E453251CF189">
        <dc:Bounds height="80.0" width="100.0" x="542.2" y="811.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-AD857ABF-E271-4D68-A4E1-C8168E913B1E" cmmnElementRef="planItemsid-AD857ABF-E271-4D68-A4E1-C8168E913B1E">
        <dc:Bounds height="80.0" width="100.0" x="542.2" y="918.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemsid-72D0908B-98F1-479F-8BE1-ABB44D01D3AF" cmmnElementRef="planItemsid-72D0908B-98F1-479F-8BE1-ABB44D01D3AF">
        <dc:Bounds height="80.0" width="100.0" x="547.2" y="1036.0"></dc:Bounds>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemclose_event" cmmnElementRef="planItemclose_event">
        <dc:Bounds height="30.0" width="30.0" x="317.20000000000005" y="1234.7664739884392"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="18.0" width="63.0" x="300.70000000000005" y="1268.7664739884392"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNShape id="CMMNShape_planItemreactivate_event" cmmnElementRef="planItemreactivate_event">
        <dc:Bounds height="30.0" width="30.0" x="152.20000000000005" y="1169.0"></dc:Bounds>
        <cmmndi:CMMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="104.5" y="1203.0"></dc:Bounds>
        </cmmndi:CMMNLabel>
      </cmmndi:CMMNShape>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-48257373-01AE-473B-834C-3C583EFC33E7" cmmnElementRef="planItemopenLossReadjudicateSettlementEvent" targetCMMNElementRef="sid-E7D71951-B672-41B2-9FB6-5328B91CAA62">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-125.0" y="825.8627450980392"></di:waypoint>
        <di:waypoint x="-31.0" y="827.8627450980392"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-28F989A3-1471-42FF-B02E-59F303FA8D82" cmmnElementRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" targetCMMNElementRef="sid-F9E24EB5-CF7E-409D-B04A-430AF9AF2797">
        <di:extension>
          <flowable:docker type="source" x="318.0" y="231.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-78.0" y="607.0"></di:waypoint>
        <di:waypoint x="-281.0" y="586.0"></di:waypoint>
        <di:waypoint x="-279.0" y="468.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-9605BB92-F310-4140-8605-CAA43819C47A" cmmnElementRef="planItemsuspendedLossReadjudicateSettlementEvent" targetCMMNElementRef="sid-B3112E7C-C2DF-4541-9D1B-B83D5EE1C293">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-845.0" y="1111.7664739884392"></di:waypoint>
        <di:waypoint x="-751.0" y="1116.7664739884392"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-52E811AB-7C15-4878-AECA-47EC7C751171" cmmnElementRef="planItemcloseStage_disapproveSettlement" targetCMMNElementRef="sid-5A155F15-E3EA-45F4-8B74-29598406647D">
        <di:extension>
          <flowable:docker type="source" x="50.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-369.8913642970423" y="199.8150121124969"></di:waypoint>
        <di:waypoint x="-287.0" y="217.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-10EF01CF-C97C-4BCE-A8FE-7DFA0FFD84E0" cmmnElementRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" targetCMMNElementRef="sid-FC3C61BB-29C6-4880-9C9C-D36FFEF447C6">
        <di:extension>
          <flowable:docker type="source" x="318.0" y="231.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-395.5" y="838.0"></di:waypoint>
        <di:waypoint x="-491.0" y="1021.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-7C068171-6FEF-4D10-8DED-C98CF177B077" cmmnElementRef="planItemOpenClaimId" targetCMMNElementRef="sid-C89E199A-BF24-40C5-8B6C-BDF716A12A39">
        <di:extension>
          <flowable:docker type="source" x="158.0" y="75.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-272.0" y="450.5"></di:waypoint>
        <di:waypoint x="-144.0" y="549.0"></di:waypoint>
        <di:waypoint x="-84.0" y="596.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-3AD8E630-3D0B-49C5-ACFF-5602DA569680" cmmnElementRef="planItemsuspendedLossReadjudicate" targetCMMNElementRef="sid-2E7C74BE-AEE2-442A-901E-DCA8E5E20A63">
        <di:extension>
          <flowable:docker type="source" x="81.0" y="37.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-581.0" y="1111.7664739884392"></di:waypoint>
        <di:waypoint x="-503.0" y="1089.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-2ED5B000-7FE2-43F4-9CC6-B57AC69E2C2D" cmmnElementRef="planItemopenStage_readjudication_process" targetCMMNElementRef="sid-6216687F-621F-430F-B0E9-BA2E412332AF">
        <di:extension>
          <flowable:docker type="source" x="50.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-165.0" y="381.22079844998825"></di:waypoint>
        <di:waypoint x="-121.0" y="384.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-80882CD2-9E52-4E68-A8F6-61C69FBF12C7" cmmnElementRef="planIteminitial" targetCMMNElementRef="sid-BD30425E-BD5E-49C2-A3DD-66E49AC8A3D3">
        <di:extension>
          <flowable:docker type="source" x="243.0" y="93.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-741.0" y="206.8150121124969"></di:waypoint>
        <di:waypoint x="-707.0" y="206.3150121124969"></di:waypoint>
        <di:waypoint x="-558.0" y="200.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-CBA04E22-DC28-4054-8962-E94A56B0AD8C" cmmnElementRef="planItempend_adjudicate_process" targetCMMNElementRef="sid-F48AFF3B-4496-4F5B-BDA6-71751CA21047">
        <di:extension>
          <flowable:docker type="source" x="81.0" y="39.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="140.0" y="827.8627450980392"></di:waypoint>
        <di:waypoint x="233.0" y="838.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-7C5AA1F9-411D-4F58-917D-4F056CB7D5F2" cmmnElementRef="planItemunsuspendLossEvent" targetCMMNElementRef="sid-6A157ECB-4E1F-499B-8AA5-A1CCB16A7D8B">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-827.0" y="1228.5"></di:waypoint>
        <di:waypoint x="-751.0" y="1234.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-651AE1F3-4BE5-4D0D-B62C-F5E1111BCFD3" cmmnElementRef="planIteminitialStage_submit_process" targetCMMNElementRef="sid-70CA74CD-ABB2-4B4C-9609-CF6AC7616BBE">
        <di:extension>
          <flowable:docker type="source" x="50.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-854.0" y="199.8150121124969"></di:waypoint>
        <di:waypoint x="-747.0" y="200.8150121124969"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-4CADC061-055F-4302-9388-1860AB5419CC" cmmnElementRef="planItemcancelStage_closeSettlement" targetCMMNElementRef="sid-6B79EDCC-5E4F-4038-B256-BC7375D8FD7A">
        <di:extension>
          <flowable:docker type="source" x="50.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="300.0" y="339.8985507246377"></di:waypoint>
        <di:waypoint x="358.0" y="342.8985507246377"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-BBA6F157-2503-4E26-8DAF-AB0EF1A9134C" cmmnElementRef="planIteminitial" targetCMMNElementRef="sid-EB15A795-04EE-4152-B5A5-A69C7E13F0AB">
        <di:extension>
          <flowable:docker type="source" x="243.0" y="93.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-984.0" y="299.3150121124969"></di:waypoint>
        <di:waypoint x="-982.0" y="395.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-627891D1-4DAD-4BC7-B8EF-64E51DFC946C" cmmnElementRef="planItemSuspended" targetCMMNElementRef="sid-D604CCA6-F908-4132-A02A-180ADD837CEE">
        <di:extension>
          <flowable:docker type="source" x="216.0" y="192.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-709.0" y="936.0"></di:waypoint>
        <di:waypoint x="-800.1166649635261" y="913.0"></di:waypoint>
        <di:waypoint x="-798.0" y="786.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-13717D1D-A3B3-4907-A08D-F9FF7FB57C3C" cmmnElementRef="planItemOpenClaimId" targetCMMNElementRef="sid-592365B6-EBF6-450B-B9D8-45DCA24AC4BF">
        <di:extension>
          <flowable:docker type="source" x="158.0" y="75.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-114.5" y="376.0"></di:waypoint>
        <di:waypoint x="87.0" y="513.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-8DFD0B61-4CA8-4AB6-AFB6-AD8291DD2E9B" cmmnElementRef="planItemclose_event" targetCMMNElementRef="sid-4CA2FD86-8E9E-4DFD-898D-1DF3782A2356">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="347.1991005246226" y="1249.9307403948233"></di:waypoint>
        <di:waypoint x="1075.0629207115398" y="1257.9021233376045"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-4CADC061-055F-4302-9388-1860AB5419CD" cmmnElementRef="planItemcancelStage_closeLoss" targetCMMNElementRef="sid-46EA8D50-8ED0-44E1-9240-A0D2D1544CA0">
        <di:extension>
          <flowable:docker type="source" x="110.0" y="43.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="584.5" y="331.8985507246377"></di:waypoint>
        <di:waypoint x="636.0" y="342.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid_e5d74051-5cdf-4e04-9dd2-55f442097cdb" cmmnElementRef="planItemopenLossRefreshSettlement" targetCMMNElementRef="sid_335c7473-1d28-483e-a7b9-fe249edb36b2">
        <di:extension>
          <flowable:docker type="source" x="81.0" y="36.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="5.5" y="692.5"></di:waypoint>
        <di:waypoint x="5.0" y="733.6813725490197"></di:waypoint>
        <di:waypoint x="62.0" y="733.6813725490197"></di:waypoint>
        <di:waypoint x="62.0" y="774.8627450980392"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-5426E654-5993-412D-9051-03DC32D68F51" cmmnElementRef="planItemunsuspendLossUnsuspendTemplates" targetCMMNElementRef="sid-37163CF3-78C1-4C22-A8CC-3405619614D4">
        <di:extension>
          <flowable:docker type="source" x="81.0" y="43.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-581.0" y="1229.0"></di:waypoint>
        <di:waypoint x="-503.0" y="1242.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-7722C2F9-3CB4-4A80-99BF-FD41096DBCD2" cmmnElementRef="planIteminitialStage_submit_event" targetCMMNElementRef="sid-0F8AFBD7-8136-4FB7-B8D1-D63C18D496FC">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-1045.0" y="181.50000000000006"></di:waypoint>
        <di:waypoint x="-962.0" y="204.8150121124969"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-BD4EF922-200E-4491-9B19-3BBAA67CFF19" cmmnElementRef="planItemPend" targetCMMNElementRef="sid-963D3343-4F85-4F93-9492-E85A59E870DA">
        <di:extension>
          <flowable:docker type="source" x="252.0" y="181.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-745.0" y="587.0"></di:waypoint>
        <di:waypoint x="-492.93162736566217" y="530.0"></di:waypoint>
        <di:waypoint x="-505.93895820106695" y="289.34403124371073"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-3D8F769D-C5C9-45AF-B818-AB03EB3D2DD2" cmmnElementRef="planItemsid-168A0529-BBDE-48B4-AA0A-8150C4A71D2A" targetCMMNElementRef="sid-D46F77AA-9A62-49C1-AC9B-5D16F72BFD2F">
        <di:extension>
          <flowable:docker type="source" x="50.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="262.5" y="509.1904453593362"></di:waypoint>
        <di:waypoint x="324.0" y="512.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-3D0D4DE1-195A-455B-A8E8-D666A57822AA" cmmnElementRef="planItemsuspendLossEvent" targetCMMNElementRef="sid-4EF50887-8EC3-42CF-A52E-14988DB2DA1F">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-125.0" y="947.0"></di:waypoint>
        <di:waypoint x="-16.0" y="950.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-64656048-8B22-4244-86E7-205F85340055" cmmnElementRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" targetCMMNElementRef="sid-6CCECD25-A64D-4A87-A420-EE4234935E23">
        <di:extension>
          <flowable:docker type="source" x="318.0" y="231.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-78.0" y="607.0"></di:waypoint>
        <di:waypoint x="-10.060151486654604" y="555.0"></di:waypoint>
        <di:waypoint x="6.945961649094443" y="289.32427851986"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid_85bca9c0-5a61-47f8-835e-bb0b4f4a62a8" cmmnElementRef="planItemPendState_refreshSettlement" targetCMMNElementRef="sid_a9a4da51-f256-4192-8953-762492cad8b9">
        <di:extension>
          <flowable:docker type="source" x="93.0" y="48.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-903.5" y="617.5"></di:waypoint>
        <di:waypoint x="-904.0" y="582.25"></di:waypoint>
        <di:waypoint x="-900.0" y="582.25"></di:waypoint>
        <di:waypoint x="-900.0" y="547.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-F3B2E56E-DA23-43ED-BF2C-3BDB9B5DDCAF" cmmnElementRef="planIteminitial" targetCMMNElementRef="OpenEntry">
        <di:extension>
          <flowable:docker type="source" x="243.0" y="93.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-741.0" y="206.8150121124969"></di:waypoint>
        <di:waypoint x="-641.0" y="330.0"></di:waypoint>
        <di:waypoint x="-437.0" y="333.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-63891CF5-0CDD-42BE-A8D5-4F1B1060D8B9" cmmnElementRef="planItemSuspended" targetCMMNElementRef="sid-83893D23-C54C-4FC0-8B8A-3DAEB00C0EF2">
        <di:extension>
          <flowable:docker type="source" x="216.0" y="192.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-709.0" y="936.0"></di:waypoint>
        <di:waypoint x="-552.0" y="928.0"></di:waypoint>
        <di:waypoint x="-552.0" y="391.44736175701183"></di:waypoint>
        <di:waypoint x="-437.0" y="394.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-B5424F98-9DCD-424D-800C-29AC4F9D7473" cmmnElementRef="planItemSuspended" targetCMMNElementRef="sid-EC5C1C5D-AE7A-4F18-B7E4-892F26A9BB01">
        <di:extension>
          <flowable:docker type="source" x="216.0" y="192.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-709.0" y="936.0"></di:waypoint>
        <di:waypoint x="-709.5" y="890.5593372169776"></di:waypoint>
        <di:waypoint x="-403.0" y="891.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-C765E823-F259-4AE5-9260-31ACC30C86CB" cmmnElementRef="planItemcloseStage_closeLoss" targetCMMNElementRef="sid-7C03A3CD-634F-4FE6-A581-07C83915A7F8">
        <di:extension>
          <flowable:docker type="source" x="110.0" y="43.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-60.5" y="206.0"></di:waypoint>
        <di:waypoint x="24.0" y="209.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-2D2FF63A-49A4-438C-AAA5-FE896218967E" cmmnElementRef="planItemsid-8CBF3E6B-17B9-4B67-AA92-B4A4B735BD30" targetCMMNElementRef="sid-653CD7C8-886F-4288-9CF2-28C55F339BA9">
        <di:extension>
          <flowable:docker type="source" x="318.0" y="231.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-395.5" y="838.0"></di:waypoint>
        <di:waypoint x="-733.0" y="712.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-43E071F2-8026-4353-A9F9-35F309CEF15F" cmmnElementRef="planItemupdatePendLossEvent" targetCMMNElementRef="sid-B8819ABF-7C9F-4DBA-9343-98A580988607">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-1135.0" y="665.5"></di:waypoint>
        <di:waypoint x="-1003.5" y="668.5"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-96A54C1C-77A8-4FE9-B428-5969DC4FB7B8" cmmnElementRef="planItemsuspendLossSuspendLossSchedule" targetCMMNElementRef="sid-589A19D6-BA6D-4F70-AA09-62CA62214E91">
        <di:extension>
          <flowable:docker type="source" x="67.0" y="40.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="126.0" y="949.0"></di:waypoint>
        <di:waypoint x="233.0" y="961.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-10E87031-79D3-4B01-AE17-5C3F4B89355B" cmmnElementRef="planItempendLossReadjudicateEvent" targetCMMNElementRef="sid-6AA98B5E-8B9E-469F-9120-AF3DC54D5B32">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-1082.5" y="503.0"></di:waypoint>
        <di:waypoint x="-1037.0" y="505.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-2B19F5CB-B2AF-4438-B569-55BD823C6358" cmmnElementRef="planItemupdateLossEvent" targetCMMNElementRef="sid-01BB0637-CB7B-4F5F-A942-DF51B0A24C05">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-125.0" y="666.5"></di:waypoint>
        <di:waypoint x="-83.5" y="669.5"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_exitFromPendToOpen" cmmnElementRef="planItemPend" targetCMMNElementRef="sid-09147AA8-01D6-405D-BD49-02AB82DC6E5B">
        <di:extension>
          <flowable:docker type="source" x="252.0" y="181.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-745.0" y="587.0"></di:waypoint>
        <di:waypoint x="-414.8848503891418" y="598.0"></di:waypoint>
        <di:waypoint x="-420.0" y="467.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-2869E689-B10B-4E8F-93C0-8BF8BFD867AA" cmmnElementRef="planItemOpenClaimId" targetCMMNElementRef="sid-59355F41-2733-4E97-9943-1CCC36470303">
        <di:extension>
          <flowable:docker type="source" x="158.0" y="75.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-114.5" y="376.0"></di:waypoint>
        <di:waypoint x="179.0" y="335.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-3D3381F9-1170-4AC1-83FD-8B0FA1804BF5" cmmnElementRef="planItempendStage_readjudication_process" targetCMMNElementRef="sid-24A71277-0B30-4892-BD49-F84990E30BCE">
        <di:extension>
          <flowable:docker type="source" x="98.0" y="41.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-834.0" y="492.0"></di:waypoint>
        <di:waypoint x="-751.0" y="496.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid_d7722414-184f-4562-b24a-b911fe1ea3ab" cmmnElementRef="planItemsuspendedLossRefreshSettlement" targetCMMNElementRef="sid_b8d4f390-58cc-4bd5-bd8c-867ddf5362fe">
        <di:extension>
          <flowable:docker type="source" x="81.0" y="36.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-604.0" y="1037.0"></di:waypoint>
        <di:waypoint x="-604.5" y="1048.8832369942197"></di:waypoint>
        <di:waypoint x="-652.0" y="1048.8832369942197"></di:waypoint>
        <di:waypoint x="-652.0" y="1060.7664739884392"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-4553EA83-003B-440C-BF3D-D5FAAD37E3B5" cmmnElementRef="planItemupdateSuspendedLossEvent" targetCMMNElementRef="sid-670CB7BA-DA6B-40C0-9366-28368FF9EF39">
        <di:extension>
          <flowable:docker type="source" x="15.0" y="15.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-745.0" y="995.8819559750978"></di:waypoint>
        <di:waypoint x="-693.0" y="999.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
      <cmmndi:CMMNEdge id="CMMNEdge_sid-F452AC56-DBFF-4F3A-B254-04B5B32E58EF" cmmnElementRef="planItemPend" targetCMMNElementRef="sid-3F35791D-2309-41CE-8C78-CA20F61CF2AA">
        <di:extension>
          <flowable:docker type="source" x="252.0" y="181.0"></flowable:docker>
          <flowable:docker type="target" x="9.0" y="14.0"></flowable:docker>
        </di:extension>
        <di:waypoint x="-997.0" y="768.0"></di:waypoint>
        <di:waypoint x="-997.0" y="793.0"></di:waypoint>
        <di:waypoint x="-890.6260348374092" y="793.0"></di:waypoint>
        <di:waypoint x="-889.0" y="786.0"></di:waypoint>
        <cmmndi:CMMNLabel></cmmndi:CMMNLabel>
      </cmmndi:CMMNEdge>
    </cmmndi:CMMNDiagram>
  </cmmndi:CMMNDI>
</definitions>