// tslint:disable
/**
 * CapDentalLoss model API facade
 * API for CapDentalLoss
 *
 * OpenAPI spec version: 1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


import * as url from "url";
import * as portableFetch from "portable-fetch";
import { Configuration } from "./configuration";

const BASE_PATH = "https://localhost".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface FetchAPI
 */
export interface FetchAPI {
    (url: string, init?: any): Promise<Response>;
}

/**
 *  
 * @export
 * @interface FetchArgs
 */
export interface FetchArgs {
    url: string;
    options: any;
}

/**
 * 
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected fetch: FetchAPI = portableFetch) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath || this.basePath;
        }
    }
};

/**
 * 
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    name: "RequiredError"
    constructor(public field: string, msg?: string) {
        super(msg);
    }
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
 */
export interface CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity {
    /**
     * 
     * @type {CapDentalLossTerm}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    period?: CapDentalLossTerm;
    /**
     * Policyholder Address of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    address?: string;
    /**
     * Other Policy Type of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherPolicyType?: string;
    /**
     * Policy Number of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    PolicyNumber?: string;
    /**
     * Other Insurance Company Name of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherInsuranceCompany?: string;
    /**
     * First Name of Policyholder of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderFirstName?: string;
    /**
     * Other Coverage Type of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherCoverageType?: string;
    /**
     * Type of Coordination of Benefits.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    typeOfCob?: string;
    /**
     * Patient Relationship to Policyholder of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderRelationshipToPatient?: string;
    /**
     * Policyholder Gender of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderGender?: string;
    /**
     * Policyholder Date of Birth of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderDateOfBirth?: string;
    /**
     * Plan or Group Number of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    plan?: string;
    /**
     * Last Name of Policyholder of COB.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderLastName?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalClaimDataEntity
 */
export interface CapDentalLossCapDentalClaimDataEntity {
    /**
     * 
     * @type {Array<CapDentalLossCapDentalProviderFeesEntity>}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    providerFees?: Array<CapDentalLossCapDentalProviderFeesEntity>;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    digitalImageNumbers?: Array<string>;
    /**
     * Payee Type.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    payeeType?: string;
    /**
     * Clean Claim Date.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    cleanClaimDate?: string;
    /**
     * Flag to identify if the provider is unknown or international.
     * @type {boolean}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    isUnknownOrIntProvider?: boolean;
    /**
     * Deprected this should come from customer.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    dateOfBirth?: string;
    /**
     * Comments and Remarks.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    remark?: string;
    /**
     * Source EDI NONEDI.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    source?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalProviderDiscountEntity}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    providerDiscount?: CapDentalLossCapDentalProviderDiscountEntity;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    missingTooths?: Array<string>;
    /**
     * Type of Claim.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    transactionType?: string;
    /**
     * Place of Treatment.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    placeOfTreatment?: string;
    /**
     * 
     * @type {Array<CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity>}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    cob?: Array<CapDentalLossCapDentalClaimCoordinationOfBenefitsEntity>;
    /**
     * Received Date.
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    receivedDate?: string;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    policyholder?: EntityLink;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    provider?: EntityLink;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    patient?: EntityLink;
    /**
     * 
     * @type {EntityLink}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    alternatePayee?: EntityLink;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalClaimDataEntity
     */
    _key?: EntityKey;
}

/**
 * Defines what loss it is and what happened.
 * @export
 * @interface CapDentalLossCapDentalDetailEntity
 */
export interface CapDentalLossCapDentalDetailEntity {
    /**
     * 
     * @type {Array<CapDentalLossCapDentalProcedureEntity>}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    submittedProcedures?: Array<CapDentalLossCapDentalProcedureEntity>;
    /**
     * 
     * @type {CapDentalLossCapDentalClaimDataEntity}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    claimData?: CapDentalLossCapDentalClaimDataEntity;
    /**
     * Description of loss itself
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    lossDesc?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalDetailEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalDiagnosisCodeEntity
 */
export interface CapDentalLossCapDentalDiagnosisCodeEntity {
    /**
     * Diagnosis Code.
     * @type {string}
     * @memberof CapDentalLossCapDentalDiagnosisCodeEntity
     */
    code?: string;
    /**
     * Diagnosis Code List Qualifier.
     * @type {string}
     * @memberof CapDentalLossCapDentalDiagnosisCodeEntity
     */
    qualifier?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalDiagnosisCodeEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalDiagnosisCodeEntity
     */
    _key?: EntityKey;
}

/**
 * Main object for the CAP Loss Domain.
 * @export
 * @interface CapDentalLossCapDentalLossEntity
 */
export interface CapDentalLossCapDentalLossEntity {
    /**
     * Loss Sub Status.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    lossSubStatusCd?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalLossPolicyEntity}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    policy?: CapDentalLossCapDentalLossPolicyEntity;
    /**
     * Loss Sub Status Reason.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    reasonCd?: string;
    /**
     * This attribute allows to enter the description of close/reopen reason.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    reasonDescription?: string;
    /**
     * A unique loss number.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    lossNumber?: string;
    /**
     * Current status of the Loss. Updated each time a new status is gained through state machine.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    state?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    policyId?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalDetailEntity}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    lossDetail?: CapDentalLossCapDentalDetailEntity;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _type: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalLossCapDentalLossEntity
     */
    _key?: RootEntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalLossEntitySuccess
 */
export interface CapDentalLossCapDentalLossEntitySuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntitySuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalLossEntity}
     * @memberof CapDentalLossCapDentalLossEntitySuccess
     */
    success?: CapDentalLossCapDentalLossEntity;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalLossEntitySuccessBody
 */
export interface CapDentalLossCapDentalLossEntitySuccessBody {
    /**
     * 
     * @type {CapDentalLossCapDentalLossEntitySuccess}
     * @memberof CapDentalLossCapDentalLossEntitySuccessBody
     */
    body?: CapDentalLossCapDentalLossEntitySuccess;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossEntitySuccessBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossCapDentalLossEntitySuccessBody
     */
    finalResponse?: boolean;
}

/**
 * An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.).
 * @export
 * @interface CapDentalLossCapDentalLossPolicyEntity
 */
export interface CapDentalLossCapDentalLossPolicyEntity {
    /**
     * Situs state
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    riskStateCd?: string;
    /**
     * Indicates if policy is verified.
     * @type {boolean}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    isVerified?: boolean;
    /**
     * 
     * @type {Array<CapDentalLossCapInsuredInfo>}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    insureds?: Array<CapDentalLossCapInsuredInfo>;
    /**
     * Identification number of the policy in CAP subsystem.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    capPolicyId?: string;
    /**
     * Indicates whether policy type is master or certificate (individual).
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    policyType?: string;
    /**
     * Indicates policy number.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    policyNumber?: string;
    /**
     * 
     * @type {CapDentalLossTerm}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    term?: CapDentalLossTerm;
    /**
     * Policy version status
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    policyStatus?: string;
    /**
     * The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    txEffectiveDate?: string;
    /**
     * Defines policy version stored on CAP side.
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    capPolicyVersionId?: string;
    /**
     * The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    productCd?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalLossPolicyEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalOrthodonticEntity
 */
export interface CapDentalLossCapDentalOrthodonticEntity {
    /**
     * Deprecated
     * @type {number}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    months?: number;
    /**
     * Orthodontic Payment Frequency.
     * @type {string}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    orthoFrequencyCd?: string;
    /**
     * Number of Months of Treatment.
     * @type {number}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    orthoMonthQuantity?: number;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    downPayment?: Money;
    /**
     * Date Appliance Placed.
     * @type {string}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    appliancePlacedDate?: string;
    /**
     * Deprecated
     * @type {string}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    frequency?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalOrthodonticEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalPreauthorizationEntity
 */
export interface CapDentalLossCapDentalPreauthorizationEntity {
    /**
     * Name of the person who authorized the procedure.
     * @type {string}
     * @memberof CapDentalLossCapDentalPreauthorizationEntity
     */
    authorizedBy?: string;
    /**
     * 
     * @type {CapDentalLossPeriod}
     * @memberof CapDentalLossCapDentalPreauthorizationEntity
     */
    authorizationPeriod?: CapDentalLossPeriod;
    /**
     * Is procedure authorized?
     * @type {boolean}
     * @memberof CapDentalLossCapDentalPreauthorizationEntity
     */
    isProcedureAuthorized?: boolean;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalPreauthorizationEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalPreauthorizationEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
 */
export interface CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity {
    /**
     * Type of Coverage.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    coverageType?: string;
    /**
     * Primary Coverage Status.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    primaryCoverageStatus?: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    allowed?: Money;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    considered?: Money;
    /**
     * Paid INN/Paid OON.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    innOnn?: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    paid?: Money;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalProcedureEntity
 */
export interface CapDentalLossCapDentalProcedureEntity {
    /**
     * Procedure Type.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    procedureType?: string;
    /**
     * Tooth System.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    toothSystem?: string;
    /**
     * Number of services.
     * @type {number}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    quantity?: number;
    /**
     * CDT Code.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    procedureCode?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalPreauthorizationEntity}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    preauthorization?: CapDentalLossCapDentalPreauthorizationEntity;
    /**
     * Procedure Description.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    description?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalOrthodonticEntity}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    ortho?: CapDentalLossCapDentalOrthodonticEntity;
    /**
     * 
     * @type {CapDentalLossCapDentalTreatmentReasonEntity}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    treatmentReason?: CapDentalLossCapDentalTreatmentReasonEntity;
    /**
     * 
     * @type {Array<CapDentalLossCapDentalDiagnosisCodeEntity>}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    diagnosisCodes?: Array<CapDentalLossCapDentalDiagnosisCodeEntity>;
    /**
     * Preauthorization Number.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    preauthorizationNumber?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    surfaces?: Array<string>;
    /**
     * Area of Oral Cavity.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    toothArea?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    cob?: CapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity;
    /**
     * Is this procedure part of predetermination or real service?
     * @type {boolean}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    predetInd?: boolean;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    submittedFee?: Money;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    toothCodes?: Array<string>;
    /**
     * Date of Service.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    dateOfService?: string;
    /**
     * Date of Prior Prosthesis Placement.
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    priorProsthesisPlacementDate?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalProcedureEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalProviderDiscountEntity
 */
export interface CapDentalLossCapDentalProviderDiscountEntity {
    /**
     * Concession %.
     * @type {number}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    discountPercentage?: number;
    /**
     * Concession Name.
     * @type {string}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    discountName?: string;
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    discountAmount?: Money;
    /**
     * Concession Type.
     * @type {string}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    discountType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalProviderDiscountEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalProviderFeesEntity
 */
export interface CapDentalLossCapDentalProviderFeesEntity {
    /**
     * 
     * @type {Money}
     * @memberof CapDentalLossCapDentalProviderFeesEntity
     */
    fee?: Money;
    /**
     * Provicer Fee Type.
     * @type {string}
     * @memberof CapDentalLossCapDentalProviderFeesEntity
     */
    type?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalProviderFeesEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalProviderFeesEntity
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCapDentalTreatmentReasonEntity
 */
export interface CapDentalLossCapDentalTreatmentReasonEntity {
    /**
     * Treatment Resulting From.
     * @type {string}
     * @memberof CapDentalLossCapDentalTreatmentReasonEntity
     */
    treatmentResultingFrom?: string;
    /**
     * Auto Accident State.
     * @type {string}
     * @memberof CapDentalLossCapDentalTreatmentReasonEntity
     */
    autoAccidentState?: string;
    /**
     * Date of Accident.
     * @type {string}
     * @memberof CapDentalLossCapDentalTreatmentReasonEntity
     */
    dateOfAccident?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapDentalTreatmentReasonEntity
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapDentalTreatmentReasonEntity
     */
    _key?: EntityKey;
}

/**
 * An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.
 * @export
 * @interface CapDentalLossCapInsuredInfo
 */
export interface CapDentalLossCapInsuredInfo {
    /**
     * Indicates if a party is a primary insured.
     * @type {boolean}
     * @memberof CapDentalLossCapInsuredInfo
     */
    isMain?: boolean;
    /**
     * The unique registry ID that identifies the subject of the claim.
     * @type {string}
     * @memberof CapDentalLossCapInsuredInfo
     */
    registryTypeId?: string;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCapInsuredInfo
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossCapInsuredInfo
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossCloseInput
 */
export interface CapDentalLossCloseInput {
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCloseInput
     */
    reasonDescription?: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalLossCloseInput
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCloseInput
     */
    reasonCd?: string;
}

/**
 * 
 * @export
 * @interface CapDentalLossCloseInputBody
 */
export interface CapDentalLossCloseInputBody {
    /**
     * 
     * @type {CapDentalLossCloseInput}
     * @memberof CapDentalLossCloseInputBody
     */
    body?: CapDentalLossCloseInput;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossCloseInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossCloseInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalLossInitInput
 */
export interface CapDentalLossInitInput {
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossInitInput
     */
    policyId?: string;
    /**
     * 
     * @type {CapDentalLossCapDentalDetailEntity}
     * @memberof CapDentalLossInitInput
     */
    entity: CapDentalLossCapDentalDetailEntity;
}

/**
 * 
 * @export
 * @interface CapDentalLossInitInputBody
 */
export interface CapDentalLossInitInputBody {
    /**
     * 
     * @type {CapDentalLossInitInput}
     * @memberof CapDentalLossInitInputBody
     */
    body?: CapDentalLossInitInput;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossInitInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossInitInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalLossKrakenBundleRequest
 */
export interface CapDentalLossKrakenBundleRequest {
    /**
     * 
     * @type {any}
     * @memberof CapDentalLossKrakenBundleRequest
     */
    dimensions?: any;
}

/**
 * 
 * @export
 * @interface CapDentalLossKrakenBundleRequestBody
 */
export interface CapDentalLossKrakenBundleRequestBody {
    /**
     * 
     * @type {CapDentalLossKrakenBundleRequest}
     * @memberof CapDentalLossKrakenBundleRequestBody
     */
    body?: CapDentalLossKrakenBundleRequest;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossKrakenBundleRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossKrakenBundleRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalLossLoadHistoryResult
 */
export interface CapDentalLossLoadHistoryResult {
    /**
     * 
     * @type {Array<CapDentalLossCapDentalLossEntity>}
     * @memberof CapDentalLossLoadHistoryResult
     */
    result?: Array<CapDentalLossCapDentalLossEntity>;
    /**
     * 
     * @type {number}
     * @memberof CapDentalLossLoadHistoryResult
     */
    count?: number;
}

/**
 * 
 * @export
 * @interface CapDentalLossLoadHistoryResultSuccess
 */
export interface CapDentalLossLoadHistoryResultSuccess {
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossLoadHistoryResultSuccess
     */
    response?: string;
    /**
     * 
     * @type {CapDentalLossLoadHistoryResult}
     * @memberof CapDentalLossLoadHistoryResultSuccess
     */
    success?: CapDentalLossLoadHistoryResult;
}

/**
 * 
 * @export
 * @interface CapDentalLossLoadHistoryResultSuccessBody
 */
export interface CapDentalLossLoadHistoryResultSuccessBody {
    /**
     * 
     * @type {CapDentalLossLoadHistoryResultSuccess}
     * @memberof CapDentalLossLoadHistoryResultSuccessBody
     */
    body?: CapDentalLossLoadHistoryResultSuccess;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossLoadHistoryResultSuccessBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossLoadHistoryResultSuccessBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalLossPeriod
 */
export interface CapDentalLossPeriod {
    /**
     * 
     * @type {Date}
     * @memberof CapDentalLossPeriod
     */
    endDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalLossPeriod
     */
    startDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossPeriod
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossPeriod
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossSubStatusInitInput
 */
export interface CapDentalLossSubStatusInitInput {
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossSubStatusInitInput
     */
    lossSubStatusCd?: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof CapDentalLossSubStatusInitInput
     */
    _key: RootEntityKey;
}

/**
 * 
 * @export
 * @interface CapDentalLossSubStatusInitInputBody
 */
export interface CapDentalLossSubStatusInitInputBody {
    /**
     * 
     * @type {CapDentalLossSubStatusInitInput}
     * @memberof CapDentalLossSubStatusInitInputBody
     */
    body?: CapDentalLossSubStatusInitInput;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossSubStatusInitInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapDentalLossSubStatusInitInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface CapDentalLossTerm
 */
export interface CapDentalLossTerm {
    /**
     * 
     * @type {Date}
     * @memberof CapDentalLossTerm
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapDentalLossTerm
     */
    expirationDate?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapDentalLossTerm
     */
    _type: string;
    /**
     * 
     * @type {EntityKey}
     * @memberof CapDentalLossTerm
     */
    _key?: EntityKey;
}

/**
 * 
 * @export
 * @interface CapEndpointRequest
 */
export interface CapEndpointRequest {
    /**
     * 
     * @type {number}
     * @memberof CapEndpointRequest
     */
    offset?: number;
    /**
     * 
     * @type {number}
     * @memberof CapEndpointRequest
     */
    limit?: number;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapEndpointRequest
     */
    fields?: Array<string>;
    /**
     * 
     * @type {GeneratePayment}
     * @memberof CapEndpointRequest
     */
    _key?: GeneratePayment;
}

/**
 * 
 * @export
 * @interface CapEndpointRequestBody
 */
export interface CapEndpointRequestBody {
    /**
     * 
     * @type {CapEndpointRequest}
     * @memberof CapEndpointRequestBody
     */
    body?: CapEndpointRequest;
    /**
     * 
     * @type {string}
     * @memberof CapEndpointRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof CapEndpointRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface ClaimLossReopenInput
 */
export interface ClaimLossReopenInput {
    /**
     * 
     * @type {string}
     * @memberof ClaimLossReopenInput
     */
    reasonDescription?: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof ClaimLossReopenInput
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof ClaimLossReopenInput
     */
    reasonCd?: string;
}

/**
 * 
 * @export
 * @interface ClaimLossReopenInputBody
 */
export interface ClaimLossReopenInputBody {
    /**
     * 
     * @type {ClaimLossReopenInput}
     * @memberof ClaimLossReopenInputBody
     */
    body?: ClaimLossReopenInput;
    /**
     * 
     * @type {string}
     * @memberof ClaimLossReopenInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ClaimLossReopenInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface ClaimLossUpdateInput
 */
export interface ClaimLossUpdateInput {
    /**
     * 
     * @type {string}
     * @memberof ClaimLossUpdateInput
     */
    _updateStrategy?: string;
    /**
     * 
     * @type {RootEntityKey}
     * @memberof ClaimLossUpdateInput
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {CapDentalLossCapDentalDetailEntity}
     * @memberof ClaimLossUpdateInput
     */
    entity: CapDentalLossCapDentalDetailEntity;
}

/**
 * 
 * @export
 * @interface ClaimLossUpdateInputBody
 */
export interface ClaimLossUpdateInputBody {
    /**
     * 
     * @type {ClaimLossUpdateInput}
     * @memberof ClaimLossUpdateInputBody
     */
    body?: ClaimLossUpdateInput;
    /**
     * 
     * @type {string}
     * @memberof ClaimLossUpdateInputBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ClaimLossUpdateInputBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface EndpointFailure
 */
export interface EndpointFailure {
    /**
     * 
     * @type {ErrorHolder}
     * @memberof EndpointFailure
     */
    data?: ErrorHolder;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailure
     */
    failure?: boolean;
    /**
     * 
     * @type {number}
     * @memberof EndpointFailure
     */
    httpCode?: number;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailure
 */
export interface EndpointFailureFailure {
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailure
     */
    response?: string;
    /**
     * 
     * @type {EndpointFailure}
     * @memberof EndpointFailureFailure
     */
    failure?: EndpointFailure;
}

/**
 * 
 * @export
 * @interface EndpointFailureFailureBody
 */
export interface EndpointFailureFailureBody {
    /**
     * 
     * @type {EndpointFailureFailure}
     * @memberof EndpointFailureFailureBody
     */
    body?: EndpointFailureFailure;
    /**
     * 
     * @type {string}
     * @memberof EndpointFailureFailureBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof EndpointFailureFailureBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface EntityKey
 */
export interface EntityKey {
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    rootId?: string;
    /**
     * 
     * @type {number}
     * @memberof EntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    parentId?: string;
    /**
     * 
     * @type {string}
     * @memberof EntityKey
     */
    id?: string;
}

/**
 * 
 * @export
 * @interface EntityLink
 */
export interface EntityLink {
    /**
     * 
     * @type {string}
     * @memberof EntityLink
     */
    _uri?: string;
}

/**
 * 
 * @export
 * @interface EntityLinkRequest
 */
export interface EntityLinkRequest {
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    offset?: number;
    /**
     * 
     * @type {number}
     * @memberof EntityLinkRequest
     */
    limit?: number;
    /**
     * 
     * @type {Array<EntityLink>}
     * @memberof EntityLinkRequest
     */
    links?: Array<EntityLink>;
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof EntityLinkRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface EntityLinkRequestBody
 */
export interface EntityLinkRequestBody {
    /**
     * 
     * @type {EntityLinkRequest}
     * @memberof EntityLinkRequestBody
     */
    body?: EntityLinkRequest;
    /**
     * 
     * @type {string}
     * @memberof EntityLinkRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof EntityLinkRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface ErrorHolder
 */
export interface ErrorHolder {
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    code?: string;
    /**
     * 
     * @type {any}
     * @memberof ErrorHolder
     */
    details?: any;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    errorCode?: string;
    /**
     * 
     * @type {Array<ErrorHolder>}
     * @memberof ErrorHolder
     */
    errors?: Array<ErrorHolder>;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    field?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    logReference?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    message?: string;
    /**
     * 
     * @type {string}
     * @memberof ErrorHolder
     */
    path?: string;
}

/**
 * 
 * @export
 * @interface GeneratePayment
 */
export interface GeneratePayment {
    /**
     * 
     * @type {string}
     * @memberof GeneratePayment
     */
    rootId?: string;
    /**
     * 
     * @type {number}
     * @memberof GeneratePayment
     */
    revisionNo?: number;
}

/**
 * 
 * @export
 * @interface IdentifierRequest
 */
export interface IdentifierRequest {
    /**
     * 
     * @type {RootEntityKey}
     * @memberof IdentifierRequest
     */
    _key: RootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof IdentifierRequest
     */
    _version?: string;
}

/**
 * 
 * @export
 * @interface IdentifierRequestBody
 */
export interface IdentifierRequestBody {
    /**
     * 
     * @type {IdentifierRequest}
     * @memberof IdentifierRequestBody
     */
    body?: IdentifierRequest;
    /**
     * 
     * @type {string}
     * @memberof IdentifierRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof IdentifierRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequest
 */
export interface LoadEntityByBusinessKeyRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityByBusinessKeyRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadEntityByBusinessKeyRequestBody
 */
export interface LoadEntityByBusinessKeyRequestBody {
    /**
     * 
     * @type {LoadEntityByBusinessKeyRequest}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    body?: LoadEntityByBusinessKeyRequest;
    /**
     * 
     * @type {string}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof LoadEntityByBusinessKeyRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface LoadEntityRootRequest
 */
export interface LoadEntityRootRequest {
    /**
     * 
     * @type {number}
     * @memberof LoadEntityRootRequest
     */
    offset?: number;
    /**
     * 
     * @type {number}
     * @memberof LoadEntityRootRequest
     */
    limit?: number;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityRootRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadEntityRootRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadEntityRootRequestBody
 */
export interface LoadEntityRootRequestBody {
    /**
     * 
     * @type {LoadEntityRootRequest}
     * @memberof LoadEntityRootRequestBody
     */
    body?: LoadEntityRootRequest;
    /**
     * 
     * @type {string}
     * @memberof LoadEntityRootRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof LoadEntityRootRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequest
 */
export interface LoadSingleEntityRootRequest {
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof LoadSingleEntityRootRequest
     */
    fields?: Array<string>;
}

/**
 * 
 * @export
 * @interface LoadSingleEntityRootRequestBody
 */
export interface LoadSingleEntityRootRequestBody {
    /**
     * 
     * @type {LoadSingleEntityRootRequest}
     * @memberof LoadSingleEntityRootRequestBody
     */
    body?: LoadSingleEntityRootRequest;
    /**
     * 
     * @type {string}
     * @memberof LoadSingleEntityRootRequestBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof LoadSingleEntityRootRequestBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface Money
 */
export interface Money {
    /**
     * 
     * @type {number}
     * @memberof Money
     */
    amount: number;
    /**
     * 
     * @type {string}
     * @memberof Money
     */
    currency: string;
}

/**
 * 
 * @export
 * @interface ObjectSuccess
 */
export interface ObjectSuccess {
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccess
     */
    response?: string;
    /**
     * 
     * @type {any}
     * @memberof ObjectSuccess
     */
    success?: any;
}

/**
 * 
 * @export
 * @interface ObjectSuccessBody
 */
export interface ObjectSuccessBody {
    /**
     * 
     * @type {ObjectSuccess}
     * @memberof ObjectSuccessBody
     */
    body?: ObjectSuccess;
    /**
     * 
     * @type {string}
     * @memberof ObjectSuccessBody
     */
    requestId?: string;
    /**
     * 
     * @type {boolean}
     * @memberof ObjectSuccessBody
     */
    finalResponse?: boolean;
}

/**
 * 
 * @export
 * @interface RootEntityKey
 */
export interface RootEntityKey {
    /**
     * 
     * @type {string}
     * @memberof RootEntityKey
     */
    rootId?: string;
    /**
     * 
     * @type {number}
     * @memberof RootEntityKey
     */
    revisionNo?: number;
}


/**
 * DefaultApi - fetch parameter creator
 * @export
 */
export const DefaultApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossCloseInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCloseLossPost(params: { body?: CapDentalLossCloseInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/closeLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalLossCloseInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/createLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapDentalLossInitInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/initLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalLossInitInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandOpenLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/openLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandPendLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/pendLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossReopenInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandReopenLossPost(params: { body?: ClaimLossReopenInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/reopenLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"ClaimLossReopenInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossSubStatusInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params: { body?: CapDentalLossSubStatusInitInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/setLossSubStatus`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalLossSubStatusInitInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSubmitLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/submitLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSuspendLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/suspendLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params: { body?: IdentifierRequestBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/unsuspendLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"IdentifierRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params: { body?: ClaimLossUpdateInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/updateLossDraft`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'PATCH' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"ClaimLossUpdateInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params: { body?: ClaimLossUpdateInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/updateLossDraft`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"ClaimLossUpdateInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPatch(params: { body?: ClaimLossUpdateInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/updateLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'PATCH' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"ClaimLossUpdateInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: ClaimLossUpdateInputBody,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/command/updateLoss`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"ClaimLossUpdateInputBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.businessKey' is not null or undefined
            if (params.businessKey === null || params.businessKey === undefined) {
                throw new RequiredError('params.businessKey','Required parameter params.businessKey was null or undefined when calling apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet.');
            }
            const localVarPath = `/api/caploss/CapDentalLoss/v1/entities/{businessKey}/{revisionNo}`
                .replace(`{${"businessKey"}}`, encodeURIComponent(String(params.businessKey)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet.');
            }
            const localVarPath = `/api/caploss/CapDentalLoss/v1/entities/{rootId}/{revisionNo}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.businessKey' is not null or undefined
            if (params.businessKey === null || params.businessKey === undefined) {
                throw new RequiredError('params.businessKey','Required parameter params.businessKey was null or undefined when calling apiCaplossCapDentalLossV1HistoryBusinessKeyPost.');
            }
            const localVarPath = `/api/caploss/CapDentalLoss/v1/history/{businessKey}`
                .replace(`{${"businessKey"}}`, encodeURIComponent(String(params.businessKey)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"LoadEntityByBusinessKeyRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling apiCaplossCapDentalLossV1HistoryRootIdPost.');
            }
            const localVarPath = `/api/caploss/CapDentalLoss/v1/history/{rootId}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"LoadEntityRootRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/link/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"EntityLinkRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1ModelGet(params: { modelType?: string,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/model/`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.modelType !== undefined) {
                localVarQueryParameter['modelType'] = params.modelType;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * endpoint for internal communication for Kraken UI engine
         * @param {string} entryPoint 
         * @param {CapDentalLossKrakenBundleRequestBody} [body] No description provided
         * @param {boolean} [delta] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, body?: CapDentalLossKrakenBundleRequestBody, delta?: boolean,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.entryPoint' is not null or undefined
            if (params.entryPoint === null || params.entryPoint === undefined) {
                throw new RequiredError('params.entryPoint','Required parameter params.entryPoint was null or undefined when calling apiCaplossCapDentalLossV1RulesEntryPointPost.');
            }
            const localVarPath = `/api/caploss/CapDentalLoss/v1/rules/{entryPoint}`
                .replace(`{${"entryPoint"}}`, encodeURIComponent(String(params.entryPoint)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.delta !== undefined) {
                localVarQueryParameter['delta'] = params.delta;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapDentalLossKrakenBundleRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * No description provided
         * @param {CapEndpointRequestBody} [body] No description provided
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params: { body?: CapEndpointRequestBody, fields?: string, limit?: number, offset?: number,  }, options: any = {}): FetchArgs {
            const localVarPath = `/api/caploss/CapDentalLoss/v1/transformation/capLoadNotIssuedPayments`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapEndpointRequestBody" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * DefaultApi - functional programming interface
 * @export
 */
export const DefaultApiFp = function(configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossCloseInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCloseLossPost(params: { body?: CapDentalLossCloseInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandCloseLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandCreateLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapDentalLossInitInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandInitLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandOpenLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandOpenLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandPendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandPendLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossReopenInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandReopenLossPost(params: { body?: ClaimLossReopenInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandReopenLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossSubStatusInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params: { body?: CapDentalLossSubStatusInitInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSubmitLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandSubmitLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandSuspendLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandUpdateLossPatch(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1CommandUpdateLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossCapDentalLossEntitySuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossLoadHistoryResultSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapDentalLossLoadHistoryResultSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1HistoryRootIdPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1LinkPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1ModelGet(params: { modelType?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1ModelGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * endpoint for internal communication for Kraken UI engine
         * @param {string} entryPoint 
         * @param {CapDentalLossKrakenBundleRequestBody} [body] No description provided
         * @param {boolean} [delta] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, body?: CapDentalLossKrakenBundleRequestBody, delta?: boolean,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1RulesEntryPointPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * No description provided
         * @param {CapEndpointRequestBody} [body] No description provided
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params: { body?: CapEndpointRequestBody, fields?: string, limit?: number, offset?: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<ObjectSuccessBody> {
            const localVarFetchArgs = DefaultApiFetchParamCreator(configuration).apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * DefaultApi - factory interface
 * @export
 */
export const DefaultApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossCloseInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCloseLossPost(params: { body?: CapDentalLossCloseInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandCloseLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandCreateLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapDentalLossInitInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandInitLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandOpenLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandOpenLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandPendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandPendLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossReopenInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandReopenLossPost(params: { body?: ClaimLossReopenInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandReopenLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {CapDentalLossSubStatusInitInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params: { body?: CapDentalLossSubStatusInitInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSubmitLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandSubmitLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandSuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandSuspendLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {IdentifierRequestBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and partial-request data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandUpdateLossPatch(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @param {ClaimLossUpdateInputBody} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1CommandUpdateLossPost(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} businessKey 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} businessKey 
         * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory entity root records for a given path parameters.
         * @param {string} rootId 
         * @param {LoadEntityRootRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1HistoryRootIdPost(params, options)(fetch, basePath);
        },
        /**
         * Returns all factory model root entity records for given links.
         * @param {EntityLinkRequestBody} [body] No description provided
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1LinkPost(params, options)(fetch, basePath);
        },
        /**
         * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
         * @param {string} [modelType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1ModelGet(params: { modelType?: string,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1ModelGet(params, options)(fetch, basePath);
        },
        /**
         * endpoint for internal communication for Kraken UI engine
         * @param {string} entryPoint 
         * @param {CapDentalLossKrakenBundleRequestBody} [body] No description provided
         * @param {boolean} [delta] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, body?: CapDentalLossKrakenBundleRequestBody, delta?: boolean,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1RulesEntryPointPost(params, options)(fetch, basePath);
        },
        /**
         * No description provided
         * @param {CapEndpointRequestBody} [body] No description provided
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params: { body?: CapEndpointRequestBody, fields?: string, limit?: number, offset?: number,  }, options?: any) {
            return DefaultApiFp(configuration).apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params, options)(fetch, basePath);
        },
    };
};

/**
 * DefaultApi - object-oriented interface
 * @export
 * @class DefaultApi
 * @extends {BaseAPI}
 */
export class DefaultApi extends BaseAPI {
    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalLossCloseInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandCloseLossPost(params: { body?: CapDentalLossCloseInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandCloseLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandCreateLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalLossInitInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapDentalLossInitInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandInitLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandOpenLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandOpenLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandPendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandPendLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {ClaimLossReopenInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandReopenLossPost(params: { body?: ClaimLossReopenInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandReopenLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {CapDentalLossSubStatusInitInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params: { body?: CapDentalLossSubStatusInitInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandSetLossSubStatusPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandSubmitLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandSubmitLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandSuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandSuspendLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {IdentifierRequestBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params: { body?: IdentifierRequestBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandUnsuspendLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and partial-request data.
     * @param {ClaimLossUpdateInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPatch(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {ClaimLossUpdateInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandUpdateLossDraftPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and partial-request data.
     * @param {ClaimLossUpdateInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandUpdateLossPatch(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandUpdateLossPatch(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @param {ClaimLossUpdateInputBody} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: ClaimLossUpdateInputBody,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1CommandUpdateLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} businessKey 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params: { businessKey: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1EntitiesBusinessKeyRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory entity root records for a given path parameters.
     * @param {string} businessKey 
     * @param {LoadEntityByBusinessKeyRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params: { businessKey: string, body?: LoadEntityByBusinessKeyRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1HistoryBusinessKeyPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory entity root records for a given path parameters.
     * @param {string} rootId 
     * @param {LoadEntityRootRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1HistoryRootIdPost(params: { rootId: string, body?: LoadEntityRootRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1HistoryRootIdPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns all factory model root entity records for given links.
     * @param {EntityLinkRequestBody} [body] No description provided
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1LinkPost(params: { body?: EntityLinkRequestBody, embed?: string, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1LinkPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns model schema for given path and query parameters of the model type provided (defaulting to DomainModel)
     * @param {string} [modelType] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1ModelGet(params: { modelType?: string,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1ModelGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * endpoint for internal communication for Kraken UI engine
     * @param {string} entryPoint 
     * @param {CapDentalLossKrakenBundleRequestBody} [body] No description provided
     * @param {boolean} [delta] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, body?: CapDentalLossKrakenBundleRequestBody, delta?: boolean,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1RulesEntryPointPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * No description provided
     * @param {CapEndpointRequestBody} [body] No description provided
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof DefaultApi
     */
    public apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params: { body?: CapEndpointRequestBody, fields?: string, limit?: number, offset?: number,  }, options?: any) {
        return DefaultApiFp(this.configuration).apiCaplossCapDentalLossV1TransformationCapLoadNotIssuedPaymentsPost(params, options)(this.fetch, this.basePath);
    }

}

