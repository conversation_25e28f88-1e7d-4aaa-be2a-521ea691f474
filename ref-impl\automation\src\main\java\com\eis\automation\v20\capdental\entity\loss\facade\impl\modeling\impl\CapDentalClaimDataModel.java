/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.*;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalClaimDataModel extends TypeModel implements ICapDentalClaimDataModel {

    private List<ICapDentalProviderFeesModel> providerFees;
    private List<String> digitalImageNumbers;
    private String payeeType;
    private String remark;
    private String source;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate cleanClaimDate;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate dateOfBirth;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD)
    private LocalDate receivedDate;
    private String transactionType;
    private String placeOfTreatment;
    private List<String> missingTooths;
    private ICapDentalPartyRoleModel policyholderRole;
    private ICapDentalPartyRoleModel providerRole;
    private ICapDentalPartyRoleModel patientRole;
    private UriModel alternatePayee;
    private UriModel payee;
    private ICapDentalProviderDiscountModel providerDiscount;
    private List<ICapDentalClaimCoordinationOfBenefitsModel> cob;

    @JsonSerialize(as = List.class, contentAs = CapDentalProviderFeesModel.class)
    public List<ICapDentalProviderFeesModel> getProviderFees() {
        return providerFees;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalProviderFeesModel.class)
    public void setProviderFees(List<ICapDentalProviderFeesModel> providerFees) {
        this.providerFees = providerFees;
    }

    public List<String> getDigitalImageNumbers() {
        return digitalImageNumbers;
    }

    public void setDigitalImageNumbers(List<String> digitalImageNumbers) {
        this.digitalImageNumbers = digitalImageNumbers;
    }

    public String getPayeeType() {
        return payeeType;
    }

    public void setPayeeType(String payeeType) {
        this.payeeType = payeeType;
    }

    public UriModel getAlternatePayee() {
        return alternatePayee;
    }

    public void setAlternatePayee(UriModel alternatePayee) {
        this.alternatePayee = alternatePayee;
    }

    public UriModel getPayee() { return payee; }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public LocalDate getCleanClaimDate() {
        return cleanClaimDate;
    }

    public void setCleanClaimDate(LocalDate cleanClaimDate) {
        this.cleanClaimDate = cleanClaimDate;
    }

    public LocalDate getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(LocalDate dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public LocalDate getReceivedDate() {
        return receivedDate;
    }

    public void setReceivedDate(LocalDate receivedDate) {
        this.receivedDate = receivedDate;
    }

    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    public String getPlaceOfTreatment() {
        return placeOfTreatment;
    }

    public void setPlaceOfTreatment(String placeOfTreatment) {
        this.placeOfTreatment = placeOfTreatment;
    }

    public List<String> getMissingTooths() {
        return missingTooths;
    }

    public void setMissingTooths(List<String> missingTooths) {
        this.missingTooths = missingTooths;
    }

    @JsonSerialize(as = CapDentalPartyRoleModel.class)
    public ICapDentalPartyRoleModel getPolicyholderRole() {
        return policyholderRole;
    }

    @JsonDeserialize(as = CapDentalPartyRoleModel.class)
    public void setPolicyholderRole(ICapDentalPartyRoleModel policyholderRole) {
        this.policyholderRole = policyholderRole;
    }

    @JsonSerialize(as = CapDentalPartyRoleModel.class)
    public ICapDentalPartyRoleModel getProviderRole() {
        return providerRole;
    }

    @JsonDeserialize(as = CapDentalPartyRoleModel.class)
    public void setProviderRole(ICapDentalPartyRoleModel providerRole) {
        this.providerRole = providerRole;
    }

    @JsonSerialize(as = CapDentalPartyRoleModel.class)
    public ICapDentalPartyRoleModel getPatientRole() {
        return patientRole;
    }

    @JsonDeserialize(as = CapDentalPartyRoleModel.class)
    public void setPatientRole(ICapDentalPartyRoleModel patientRole) {
        this.patientRole = patientRole;
    }

    @JsonSerialize(as = CapDentalProviderDiscountModel.class)
    public ICapDentalProviderDiscountModel getProviderDiscount() {
        return providerDiscount;
    }

    @JsonDeserialize(as = CapDentalProviderDiscountModel.class)
    public void setProviderDiscount(ICapDentalProviderDiscountModel providerDiscount) {
        this.providerDiscount = providerDiscount;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalClaimCoordinationOfBenefitsModel.class)
    public List<ICapDentalClaimCoordinationOfBenefitsModel> getCob() {
        return cob;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalClaimCoordinationOfBenefitsModel.class)
    public void setCob(List<ICapDentalClaimCoordinationOfBenefitsModel> cob) {
        this.cob = cob;
    }
}
