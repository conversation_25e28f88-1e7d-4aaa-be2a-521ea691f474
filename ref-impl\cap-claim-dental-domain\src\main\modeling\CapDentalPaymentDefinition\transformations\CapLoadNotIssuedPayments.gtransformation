@CapEndpoint("capLoadNotIssuedPayments")
Transformation CapLoadNotIssuedPayments {
    Input {
        CapDentalLoss.CapDentalLossEntity as loss
    }
    Output {
        ? as output
    }

    Var paymentIndex is Load(createOriginSourceKey(loss), "CapDentalPaymentIndex", "CapDentalPaymentIdxEntity")
    Var filteredPaymentIndex is paymentIndex[filterNotIssued]
    Var loadedPayments is loadPayment(filteredPaymentIndex.paymentId)
    Attr payments is FlatMap(loadedPayments.payment)

    Producer createOriginSourceKey(loss) {
        Attr originSource is ToExtLink(loss)._uri
    }

    Producer loadPayment(paymentId){
        Attr payment is ExtLink(SafeInvoke(paymentId, AsExtLink(paymentId)))
    }

    Filter filterNotIssued {
        state == "Approved" || state == "IssueRequested"
    }
}