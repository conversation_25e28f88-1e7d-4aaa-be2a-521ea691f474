/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl;

import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl.CapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.util.List;

public class CapDentalPaymentSchedulePreviewModel extends TypeModel implements ICapDentalPaymentSchedulePreviewModel {
    private UriModel originSource;
    private List<ICapDentalPaymentModel> payments;
    private Money paymentNetAmount;
    @JsonProperty(value = "_modelName")
    private String modelName;
    @JsonProperty(value = "_modelVersion")
    private String modelVersion;
    @JsonProperty(value = "_modelType")
    private String modelType;


    public UriModel getOriginSource() {
        return originSource;
    }

    public void setOriginSource(UriModel originSource) {
        this.originSource = originSource;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalPaymentModel.class)
    public List<ICapDentalPaymentModel> getPayments() {
        return payments;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalPaymentModel.class)
    public void setPayments(List<ICapDentalPaymentModel> payments) {
        this.payments = payments;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getModelVersion() {
        return modelVersion;
    }

    public void setModelVersion(String modelVersion) {
        this.modelVersion = modelVersion;
    }

    public String getModelType() {
        return modelType;
    }

    public void setModelType(String modelType) {
        this.modelType = modelType;
    }

    public Money getPaymentNetAmount() {
        return paymentNetAmount;
    }

    public void setPaymentNetAmount(Money paymentNetAmount) {
        this.paymentNetAmount = paymentNetAmount;
    }
}

