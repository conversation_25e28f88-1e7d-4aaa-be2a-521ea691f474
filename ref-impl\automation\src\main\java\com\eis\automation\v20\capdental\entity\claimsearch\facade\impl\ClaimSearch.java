/*
 * Copyright © 2020 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.claimsearch.facade.impl;

import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionContext;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionGetter;
import com.eis.automation.tzappa_v20.service.rest.annotation.RestActionSerialization;
import com.eis.automation.tzappa_v20.service.rest.conf.RestActionConfiguration;
import com.eis.automation.v20.capdental.entity.claimsearch.facade.IClaimSearch;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl.CapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl.CapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl.CapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.platform.common.action.CommonSearchAction;

public class ClaimSearch implements IClaimSearch {
    private CommonSearchAction<ICapDentalSettlementModel> searchDentalSettlementAction;
    private CommonSearchAction<ICapDentalPaymentTemplateModel> searchDentalPaymentTemplateAction;
    private CommonSearchAction<ICapDentalPaymentScheduleModel> searchDentalPaymentScheduleAction;

    public ClaimSearch(RestActionConfiguration configuration) {
        searchDentalSettlementAction = new CommonSearchAction<>(configuration);
        searchDentalPaymentTemplateAction = new CommonSearchAction<>(configuration);
        searchDentalPaymentScheduleAction = new CommonSearchAction<>(configuration);
    }

    @RestActionGetter("searchDentalSettlementAction")
    @RestActionSerialization(
            type = "SEARCH",
            serializationClazz = CapDentalSettlementModel.class
    )
    @RestActionContext(
            target = "/api/common/{product}/{version}/CapDentalSettlement"
    )
    public CommonSearchAction<ICapDentalSettlementModel> searchDentalSettlement() {
        return searchDentalSettlementAction;
    }

    @RestActionGetter("searchDentalPaymentTemplateAction")
    @RestActionSerialization(
            type = "SEARCH",
            serializationClazz = CapDentalPaymentTemplateModel.class
    )
    @RestActionContext(
            target = "/api/common/{product}/{version}/CapPaymentTemplate"
    )
    public CommonSearchAction<ICapDentalPaymentTemplateModel> searchDentalPaymentTemplate() {
        return searchDentalPaymentTemplateAction;
    }

    @RestActionGetter("searchDentalPaymentScheduleAction")
    @RestActionSerialization(
            type = "SEARCH",
            serializationClazz = CapDentalPaymentScheduleModel.class
    )
    @RestActionContext(
            target = "/api/common/{product}/{version}/CapPaymentSchedule"
    )
    public CommonSearchAction<ICapDentalPaymentScheduleModel> searchDentalPaymentSchedule() {
        return searchDentalPaymentScheduleAction;
    }

}