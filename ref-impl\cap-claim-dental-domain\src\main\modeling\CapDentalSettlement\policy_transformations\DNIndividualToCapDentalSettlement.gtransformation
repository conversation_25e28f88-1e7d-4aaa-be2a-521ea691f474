//transformation which is responsible to convert individual dental policy information to Dental Settlement policy information
Transformation DNIndividualToCapDentalSettlement {
    Input {
        Ext CapPolicyHolder.CapPolicyEntity as policy
        Ext CapDentalSettlement.CapDentalProcedureEntity as inProcedure
    }
    Output {
        CapDentalSettlement.CapDentalPolicyInfoEntity
    }

    Var plan is policy.individualPackagingDetail.plan
    Var covDef is plan.covDef
    Var waitingPeriod is covDef.waitingPeriods
    Var aggregatedParties is New() {
        Attr parties is Super().policy.parties
    }

    Var dateOfService is inProcedure.dateOfService
    Var policyEffectiveDate is policy.termDetails.termEffectiveDate

    Var diffYear is Ternary(CompareDates(dateOfService,policyEffectiveDate) < 0  ,0,
                               Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P365D")))<1, 1,
                                   Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P731D")))<1, 2,
                                           Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P1096D"))) <1, 3,
                                                  4
                                                  ))))


    Var filteredCoinsurancesByGradedYear is Ternary(Equals(covDef.coinsuranceDetails.coinsuranceNumberOfGradedYears, Null()) ,
                                         covDef.coinsuranceDetails.coinsurances,
                                         covDef.coinsuranceDetails.coinsurances[FilterByDiffYear])


    Attr capPolicyId is policy.capPolicyId
    Attr capPolicyVersionId is policy.capPolicyVersionId
    Attr term is produceTerm(policy.termDetails)
    Attr riskStateCd is policy.riskStateCd
    Attr plan is plan.planCd
    Attr planName is plan.planName
    Attr orthoINNCoinsurancePercent is SafeInvoke(covDef.orthodonticCoverage.orthoCoinsuranceIN, Mult(covDef.orthodonticCoverage.orthoCoinsuranceIN, 100))
    Attr orthoOONCoinsurancePercent is SafeInvoke(covDef.orthodonticCoverage.orthoCoinsuranceOON, Mult(covDef.orthodonticCoverage.orthoCoinsuranceOON, 100))
    Attr preventWaitingPeriod is "" + waitingPeriod.preventiveWaitingPeriod
    Attr basicWaitingPeriod is "" + waitingPeriod.basicWaitingPeriod
    Attr majorWaitingPeriod is "" + waitingPeriod.majorWaitingPeriod
    Attr applyLateEntrantBenefitWaitingPeriods is waitingPeriod.applyLateEntrantBenefitWaitingPeriods
    Attr orthoWaitingPeriod is "" + covDef.orthodonticCoverage.orthoWaitingPeriod
    Attr fullTimeStudentAgeCd is covDef.dependentEligibilityDetails.fullTimeStudentAgeCd
    Attr childMaxAgeCd is covDef.dependentEligibilityDetails.childMaxAgeCd
    Attr coinsurances is SafeInvoke(filteredCoinsurancesByGradedYear,
                    produceCoinsurance(filteredCoinsurancesByGradedYear))
    Attr insureds is SafeInvoke(policy.insureds,
            produceInsuredDetails(policy.insureds, aggregatedParties))
    Attr lateEntrantWaitingPeriodsDetails is SafeInvoke(waitingPeriod.lateEntrantWaitingPeriodsDetails,
            FlatMap(produceLateEntrantWaitingPeriod(waitingPeriod.lateEntrantWaitingPeriodsDetails)))
    Attr isImplantsMaximumAppliedTowardPlanMaximum is covDef.implantsCoverage.isImplantsMaximumAppliedTowardPlanMaximum
    Attr orthoDeductibleType is covDef.orthodonticCoverage.orthoDeductibleType
    Attr tmjDeductibleType is covDef.tmjCoverage.tmjDeductibleType
    Attr cosmeticDeductibleType is covDef.cosmeticServicesCoverage.cosmeticDeductibleType

    //GENESIS-353761
    Attr enrollmentTypeCd is policy.enrollmentTypeCd
    Attr waitingPeriodApplyTo is waitingPeriod.waitingPeriodApplyTo
    Attr coverageEligibility is covDef.eligibilities {
      Attr waitingPeriodAmount is waitingPeriodAmount
      Attr waitingPeriodModeCd is waitingPeriodModeCd
      Attr eligibilityTypeCd is eligibilityTypeCd
      Attr waitingPeriodDefCd is waitingPeriodDefCd
    }

    Attr serviceCategory is covDef.serviceCategories {
        Attr scOralEvaluations is scOralEvaluations
        Attr scSurgicalPeriodontics is scSurgicalPeriodontics
        Attr scCrowns is scCrowns
        Attr scImplantServices is scImplantServices
        Attr scFillings is scFillings
        Attr scFluorides is scFluorides
        Attr scStainlessSteelCrowns is scStainlessSteelCrowns
        Attr scBitewingRadiographs is scBitewingRadiographs
        Attr scFullMouthRadiographs is scFullMouthRadiographs
        Attr scAllOtherRadiographs is scAllOtherRadiographs
        Attr scRootCanals is scRootCanals
    }

    Attr frequencyLimitations is produceFrequencyLimitations(covDef)
    Attr orthoLateEntrantWaitingPeriod is "" + covDef.orthodonticCoverage.orthoLateEntrantWaitingPeriod
    Attr cosmeticWaitingPeriod is "" + covDef.cosmeticServicesCoverage.cosmeticWaitingPeriod

    Attr isOrthoCoverageIncluded is Ternary(Equals(covDef.orthodonticCoverage, Null()) || (!Equals(covDef.orthodonticCoverage, Null()) && Equals(covDef.orthodonticCoverage.orthoAvailability, Null())) , false, true)
    Attr isImplantCoverageIncluded is Ternary(Equals(covDef.implantsCoverage, Null()) || (!Equals(covDef.implantsCoverage, Null()) && Equals(covDef.implantsCoverage.isImplantsMaximumAppliedTowardPlanMaximum, Null())), false, true)
    Attr isTmjCoverageIncluded is Ternary(Equals(covDef.tmjCoverage, Null()) || (!Equals(covDef.tmjCoverage, Null()) && Equals(covDef.tmjCoverage.tmjDeductibleType, Null())), false, true)
    Attr isCosmeticServicesIncluded is Ternary(Equals(covDef.cosmeticServicesCoverage, Null()) || (!Equals(covDef.cosmeticServicesCoverage, Null()) && Equals(covDef.cosmeticServicesCoverage.cosmeticDeductibleType, Null())), false, true)

    Attr orthoAvailability is covDef.orthodonticCoverage.orthoAvailability
    Attr orthoChildAgeLimit is covDef.orthodonticCoverage.orthoChildAgeLimit

    Producer produceFrequencyLimitations(covDef) {
        Var preventiveLimitations is First(covDef.limitations[PreventiveLimitationsFilter])
        Var basicLimitations is First(covDef.limitations[BasicLimitationsFilter])
        Var majorLimitations is First(covDef.limitations[MajorLimitationsFilter])

        Attr preventiveLimitations is SafeInvoke(preventiveLimitations, producePreventiveLimitations(preventiveLimitations))
        Attr basicLimitations is SafeInvoke(basicLimitations, produceBasicLimitations(basicLimitations))
        Attr majorLimitations is SafeInvoke(majorLimitations, produceMajorLimitations(majorLimitations))
    }

    Producer producePreventiveLimitations(preventiveLimitation) {
        Attr preventiveOralEvaluations is preventiveLimitation.preventiveOralEvaluations
        Attr preventiveFluorideTreatment is preventiveLimitation.preventiveFluorideTreatment
        Attr preventiveFluorideTreatmentAgeLimit is preventiveLimitation.preventiveFluorideTreatmentAgeLimit
        Attr preventiveBitewingRadiographs is preventiveLimitation.preventiveBitewingRadiographs
    }

    Producer produceBasicLimitations(basicLimitation) {
        Attr basicPeriodontalSurgery is basicLimitation.basicPeriodontalSurgery
        Attr basicStainlessSteelCrowns is basicLimitation.basicStainlessSteelCrowns
        Attr basicStainlessSteelCrownsAgeLimit is basicLimitation.basicStainlessSteelCrownsAgeLimit
    }

    Producer produceMajorLimitations(majorLimitation) {
        Attr majorCrowns is majorLimitation.majorCrowns
        Attr majorImplants is majorLimitation.majorImplants
        Attr majorDentureAdjustments is majorLimitation.majorDentureAdjustments
    }

    Producer produceCoinsurance(coinsurance) {
        Attr coinsuranceServiceType is coinsurance.coinsuranceServiceType
        Attr coinsuranceINPct is SafeInvoke(coinsurance.coinsuranceINAmount, Mult(coinsurance.coinsuranceINAmount, 100))
        Attr coinsuranceOONPct is SafeInvoke(coinsurance.coinsuranceOONAmount, Mult(coinsurance.coinsuranceOONAmount, 100))
    }

    Producer produceInsuredDetails(insured, aggregatedParties) {
        Var insuredRef is insured.insuredInfo._ref
        Var party is First(aggregatedParties.parties[FilterByInsuredRef])

        Attr relationshipToPrimaryInsuredCd is insured.relationshipToPrimaryInsuredCd
        Attr isFullTimeStudent is First(party.partyInfo).personBaseDetails.isFullTimeStudent
        Attr insuredRoleNameCd is insured.insuredRoleNameCd
        Attr isMain is insured.insuredRoleNameCd == "PrimaryInsured"
        Attr registryTypeId is First(party.partyInfo).personBaseDetails.registryTypeId

        //GENESIS-353761
        Attr hireDate is Elvis(insured.relationshipDetails.rehireDate, insured.relationshipDetails.originalHireDate)
    }

    Producer produceLateEntrantWaitingPeriod(latePeriodDetails) {
        Attr preventWaitingPeriod is "" + latePeriodDetails.preventiveWaitingPeriod
        Attr basicWaitingPeriod is "" + latePeriodDetails.basicWaitingPeriod
        Attr majorWaitingPeriod is "" + latePeriodDetails.majorWaitingPeriod
    }

    Producer produceTerm(termDetails) {
        Attr effectiveDate is termDetails.termEffectiveDate
        Attr expirationDate is termDetails.termExpirationDate
    }

    Filter FilterByInsuredRef {
        Equals(_key.id, Super().insuredRef)
    }

    Filter PreventiveLimitationsFilter {
        Equals(_type, "DNPreventiveDiagnostic")
    }

    Filter BasicLimitationsFilter {
        Equals(_type, "DNBasic")
    }

    Filter MajorLimitationsFilter {
        Equals(_type, "DNMajor")
    }

    Filter FilterByDiffYear {
        coinsuranceGradedYear == Root().diffYear
    }
}