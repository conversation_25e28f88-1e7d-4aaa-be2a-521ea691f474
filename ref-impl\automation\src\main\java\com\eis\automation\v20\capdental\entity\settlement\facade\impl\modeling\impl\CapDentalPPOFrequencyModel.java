/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalPPOFrequencyModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPPOFrequencyModel extends TypeModel implements ICapDentalPPOFrequencyModel {

    private String procedureType;
    private String frequencyComment;
    private String frequencyRange;
    private String frequencyRule;
    private String frequencyPeriodType;
    private String frequency;
    private Integer frequencyPeriod;

    public String getProcedureType() {
        return procedureType;
    }

    public void setProcedureType(String procedureType) {
        this.procedureType = procedureType;
    }

    public String getFrequencyComment() {
        return frequencyComment;
    }

    public void setFrequencyComment(String frequencyComment) {
        this.frequencyComment = frequencyComment;
    }

    public String getFrequencyRange() {
        return frequencyRange;
    }

    public void setFrequencyRange(String frequencyRange) {
        this.frequencyRange = frequencyRange;
    }

    public String getFrequencyRule() {
        return frequencyRule;
    }

    public void setFrequencyRule(String frequencyRule) {
        this.frequencyRule = frequencyRule;
    }

    public String getFrequencyPeriodType() {
        return frequencyPeriodType;
    }

    public void setFrequencyPeriodType(String frequencyPeriodType) {
        this.frequencyPeriodType = frequencyPeriodType;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    public Integer getFrequencyPeriod() {
        return frequencyPeriod;
    }

    public void setFrequencyPeriod(Integer frequencyPeriod) {
        this.frequencyPeriod = frequencyPeriod;
    }
}
