/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.ICapDentalPaymentSchedule;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewBuildModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentSchedulePreviewModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.service.ICapDentalPaymentScheduleService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

import static com.eis.automation.tzappa.rest.modeling.utils.ModelRetryPredicate.success;

@Lazy
@Component("previewPaymentSchedule")
public class CapDentalPaymentScheduleService implements ICapDentalPaymentScheduleService {
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;

    @Autowired
    private ICapDentalPaymentSchedule capDentalPaymentSchedule;

    private TestData tdDentalPaymentPreviewLocation;

    public TestData getTestData(String... strings) {
        return tdDentalPaymentPreviewLocation.getTestData(strings);
    }


    public CapDentalPaymentScheduleService(ICapDentalPaymentSchedule capDentalPaymentSchedule) {
        this.capDentalPaymentSchedule = capDentalPaymentSchedule;
    }

    public ICapDentalPaymentSchedule getFacade() {
        return capDentalPaymentSchedule;
    }



    @PostConstruct
    private void testDataResolver() {
        tdDentalPaymentPreviewLocation = testDataProvider.getJSONTestData("/capdental/paymentSchedule");
    }


    public ICapDentalPaymentSchedulePreviewBuildModel createPreviewModel(ICapDentalLossModel dentalClaimModel, ICapDentalSettlementModel dentalSettlementModel) {
        ICapDentalPaymentSchedulePreviewBuildModel previewSchedule = modelUtils.create(getTestData("Preview", "TestData"));
        previewSchedule.setOriginSource(dentalClaimModel.getGentityUri().getUriModel());
        previewSchedule.setSettlements(List.of(dentalSettlementModel.getGentityUri().getUriModel()));
        return previewSchedule;
    }

    public ICapDentalPaymentSchedulePreviewModel previewPaymentSchedule(ICapDentalLossModel dentalClaimModel,
                                                                        ICapDentalSettlementModel dentalSettlementModel) {
        return getFacade().preview().performNoSuccessAnalysis(b -> b.setModel(createPreviewModel(dentalClaimModel, dentalSettlementModel)), success()).safeGetResponseBody();
    }

}
