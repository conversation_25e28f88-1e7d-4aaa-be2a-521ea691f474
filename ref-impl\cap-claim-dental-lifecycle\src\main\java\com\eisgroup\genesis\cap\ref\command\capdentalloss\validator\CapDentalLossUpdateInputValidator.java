/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;

/**
 * Validator class for {@link ClaimLossUpdateInput}.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalLossUpdateInputValidator extends CapInputValidator<ClaimLossUpdateInput> {

    private final CapDentalLossValidator capDentalLossValidator;

    public CapDentalLossUpdateInputValidator(CapDentalLossValidator capDentalLossValidator) {
        super(ClaimLossUpdateInput.class);
        this.capDentalLossValidator = capDentalLossValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(ClaimLossUpdateInput input) {
        return capDentalLossValidator.validateLinks(input.getEntity());
    }
}
