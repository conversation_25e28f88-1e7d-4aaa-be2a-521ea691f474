{"TestData": {"details": "@Adjustment_Details", "communicationInfo": "@Adjustment_CommunicationInfo", "brandCd": "brandCdValue", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.IndividualCustomerModel"}, "TestData_Full": {"details": "@Adjustment_Details", "businessEntities": ["@Adjustment_BusinessEntities"], "customerGroupInfos": ["@Adjustment_GroupInfo"], "communicationInfo": "@Adjustment_CommunicationInfo", "productsOwned": ["@Adjustment_ProductsOwned"], "employmentDetails": [{"_type": "GenesisCrmEmploymentDetails", "employerName": "abc", "occupationCd": "Accountant", "occupationStatusCd": "OTHER", "jobTitleCd": "CLAIMS_REPRESENT", "jobTitleDescription": "Claims Representative", "asOfDate": "$<today:yyyy-MM-dd>", "communicationInfo": "@Adjustment_CommunicationInfo"}], "taxExemptComment": "taxExemptComment", "taxExempt": true, "segments": ["EMPLOYEE", "VIP"], "additionalNames": ["@additionalNames1", "@additionalNames2"], "claimInfos": [{"claimFileOwner": "string", "policyProduct": "string", "claimants": ["string"], "dateOfLoss": "$<today:yyyy-MM-dd>", "incurred": "0", "link": "value1", "claimId": "$<rx:\\d{10}>", "claimFileOwnerPhone": "string", "status": "string", "policy": "string", "_type": "GenesisCrmClaimInfo", "_modelName": "INDIVIDUALCUSTOMER"}], "billingInfos": [{"totalPaid": 1.0, "unpaidBalance": 1.0, "link": "value1", "currentDueAmount": 1.0, "currentDueDate": "$<today:yyyy-MM-dd>", "billingAccount": "$<rx:\\d{10}>", "policy": "string", "_type": "GenesisCrmBillingInfo", "_modelName": "INDIVIDUALCUSTOMER"}], "source": "OTHER", "sourceDescription": "sourceDescription", "leadOwner": "$<rx:[a-zA-Z]{8}>", "rating": "WARM", "brandCd": "brandCdValue", "preferredCurrency": "USD", "agencyCodes": ["$<runtime:agencyProcessor:qa1Agency>"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.IndividualCustomerModel"}, "Adjustment_Details": {"_type": "GenesisCrmIndividualDetails", "person": {"_type": "GenesisCrm<PERSON>erson", "firstName": "$<realData:firstName>", "lastName": "$<realData:lastName>", "genderCd": "Female", "birthDate": "$<today-30y:yyyy-MM-dd>", "maritalStatus": "Single", "taxId": "$<rx:\\d{9}>", "deceased": false}, "tobaccoCd": "No", "occupation": "Student", "occupationDescription": "3-C", "designation": "OTHER", "designationDescription": "designationDescription", "nickname": "nickname", "deathNotificationReceived": false, "citizenship": "KH", "customerInterests": ["SPORTS", "READING"], "disabilities": ["SPINAL_CORD_INJURY", "HEARING_LOSS_AND_DEAFNESS"], "registerOnline": false, "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmIndividualDetailsModel"}, "Adjustment_BusinessEntities": {"_type": "GenesisCrmBusinessEntity", "details": {"_type": "GenesisCrmBusinessDetails", "legalEntity": {"_type": "GenesisCrmLegalEntity", "taxIdentificationId": "$<rx:\\d{9}>", "publicName": "$<rx:[a-zA-Z]{8}>", "legalName": "$<realData:companyName>", "legalId": "$<rx:\\d{2}-\\d{7}>", "dbaName": "dbaName$<rx:\\d{7}>", "dateStarted": "$<today:yyyy-MM-dd>", "industryCd": "ICD"}, "businessType": "LLC", "sicCode": "5021", "naicsCode": "111110"}, "communicationInfo": {"_type": "GenesisCrmCommunicationInfo", "emails": ["@email1"], "phones": ["@phone1"], "addresses": ["@address1"]}, "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmBusinessEntityModel"}, "Adjustment_GroupInfo_Blank": {"_type": "GenesisCustomerGroupInfo", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCustomerGroupInfoModel"}, "Adjustment_GroupInfo": {"comment": "commentValue", "membershipDate": "$<today:yyyy-MM-dd>", "_type": "GenesisCustomerGroupInfo"}, "Adjustment_CommunicationInfo_Blank": {"_type": "GenesisCrmCommunicationInfo", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmCommunicationInfoModel"}, "Adjustment_CommunicationInfo": {"_type": "GenesisCrmCommunicationInfo", "webAddresses": ["@webAddress1"], "chats": ["@chat1"], "socialNets": ["@socialNet1"], "emails": ["@email1"], "phones": ["@phone1"], "addresses": ["@address1"], "preferredContactMethod": "WebUrl", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmCommunicationInfoModel"}, "Adjustment_ProductsOwned_Blank": {"_type": "GenesisCrmProductOwned", "_modelName": "INDIVIDUALCUSTOMER", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmProductOwnedModel"}, "Adjustment_ProductsOwneds": [{"_type": "GenesisCrmProductOwned", "productOwnedId": "$<rx:\\d{5}>", "policyTypeCd": "Auto", "carrierNameCd": "UNKNOWN", "carrierNameDescription": "$<rx:[a-zA-Z]{8}>", "policyNumber": "PA$<rx:\\d{8}>", "policyExpirationDate": "$<today+1y:yyyy-MM-dd>", "link": "www.$<rx:randomDomainName\\d{5}>.com", "_modelName": "INDIVIDUALCUSTOMER", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmProductOwnedModel"}, {"_type": "GenesisCrmProductOwned", "productOwnedId": "$<rx:\\d{5}>", "policyTypeCd": "Auto", "carrierNameCd": "UNKNOWN", "carrierNameDescription": "$<rx:[a-zA-Z]{8}>", "policyNumber": "PA$<rx:\\d{8}>", "policyExpirationDate": "$<today+1y:yyyy-MM-dd>", "link": "www.$<rx:randomDomainName\\d{5}>.com", "_modelName": "INDIVIDUALCUSTOMER"}], "Adjustment_ProductsOwned": {"_type": "GenesisCrmProductOwned", "productOwnedId": "$<rx:\\d{5}>", "policyTypeCd": "Auto", "carrierNameCd": "UNKNOWN", "carrierNameDescription": "$<rx:[a-zA-Z]{8}>", "policyNumber": "PA$<rx:\\d{8}>", "policyExpirationDate": "$<today+1y:yyyy-MM-dd>", "link": "www.$<rx:randomDomainName\\d{5}>.com", "_modelName": "INDIVIDUALCUSTOMER", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmProductOwnedModel"}, "Adjustment_ProductsOwned2": {"_type": "GenesisCrmProductOwned", "productOwnedId": "$<rx:\\d{5}>", "policyTypeCd": "Auto", "carrierNameCd": "UNKNOWN", "carrierNameDescription": "$<rx:[a-zA-Z]{8}>", "policyNumber": "PA$<rx:\\d{8}>", "policyExpirationDate": "$<today+1y:yyyy-MM-dd>", "link": "www.$<rx:randomDomainName\\d{5}>.com", "_modelName": "INDIVIDUALCUSTOMER"}, "Adjustment_WrongPreferredType": {"webAddresses": [{"_type": "GenesisCrmWebAddress", "type": "CORPORATE", "value": "$<rx:[a-zA-Z]{8}>.$<rx:[a-zA-Z]{8}>", "preferred": "NOT A TRUE", "doNotSolicit": "false"}]}, "Adjustment_ParticipationInfoWithSalary": {"_type": "GenesisParticipationInfo", "employments": [{"originalHireDate": "$<today:yyyy-MM-dd>", "jobTitle": "title", "jobCode": "VH", "employmentStatus": "ACT", "employmentType": "FullTime", "payrollFrequency": "BiWeekly", "payType": "Salary", "payClass": "EXE", "hourlyWage": "50$", "salary": [{"salaryEffectiveDate": "$<today+1m:yyyy-MM-dd>", "salaryAmount": "1520.68$", "_type": "Salary"}, {"salaryEffectiveDate": "$<today+2m:yyyy-MM-dd>", "salaryAmount": "6000$", "_type": "Salary"}], "customer": {"_type": "EmploymentDetailsCustomerAssociation", "link": ""}, "_type": "EmploymentDetails"}], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisParticipationInfoModel"}, "Adjustment_WrongDoNotSolicitType": {"webAddresses": [{"_type": "GenesisCrmWebAddress", "type": "CORPORATE", "value": "$<rx:[a-zA-Z]{8}>.$<rx:[a-zA-Z]{8}>", "preferred": "true", "doNotSolicit": "NOT A FALSE"}]}, "email1": {"_type": "GenesisCrmEmail", "type": "Personal", "value": "<EMAIL>", "preferred": "true", "doNotSolicit": "false", "consentInfo": {"_type": "GenesisConsentInfo", "consentStatus": "GRANTED", "consentDate": "$<today:yyyy-MM-dd>"}, "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmEmailModel"}, "phone1": {"_type": "GenesisCrmPhone", "type": "Home", "value": "$<rx:\\d{10}>", "preferred": "true", "doNotSolicit": "false", "preferredTimesToContact": ["EVENING", "AFTERNOON"], "consentToTextStatus": "DENIED", "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "consentToTextDate": "$<today:yyyy-MM-dd>", "consentInfo": {"_type": "GenesisConsentInfo", "consentStatus": "GRANTED", "consentDate": "$<today:yyyy-MM-dd>"}, "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmPhoneModel"}, "socialNet1": {"_type": "GenesisCrmSocialNet", "type": "FCB", "value": "https://facebook.com/page?id=$<rx:\\d{9}>", "preferred": "true", "doNotSolicit": "false", "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmSocialNetModel"}, "address1": {"_type": "GenesisCrmAddress", "location": {"_type": "GenesisLocation", "addressType": "Residence", "countryCd": "US", "stateProvinceCd": "CA", "postalCode": "$<rx:\\d{5}>", "city": "$<realData:city>", "addressLine1": "$<realData:streetName>", "addressLine2": "$<realData:streetName>", "addressLine3": "$<realData:streetName>"}, "preferred": "true", "doNotSolicit": "false", "inCareOf": "inCareOf", "attention": "attention", "subdivision": "subdivision", "addressValidatedInd": "addressValidatedInd", "referenceId": "referenceId", "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "schedulingContactInfo": "@schedulingContactInfo1", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmAddressModel"}, "webAddress1": {"_type": "GenesisCrmWebAddress", "type": "CORPORATE", "value": "https://$<realData:firstName>.$<realData:lastName>.com/web/address", "preferred": "true", "doNotSolicit": "false", "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmWebAddressModel"}, "chat1": {"_type": "GenesisCrmChat", "type": "SKYPE", "value": "Skype:$<rx:\\d{9}>", "preferred": "true", "doNotSolicit": "false", "communicationPreferences": ["BILL_STATE", "PLAN_NEWS", "SendReimbursement", "MailingAddressForCheck"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmChatModel"}, "additionalNames1": {"_type": "GenesisIndividualCustomerAdditionalName", "salutation": "Doctor", "firstName": "Sakura$<rx:[a-zA-Z]{8}>", "middleName": "middleName1", "lastName": "Suzuki$<rx:[A-Z]{8}>", "suffix": "PhD", "designation": "OTHER", "description": "description1"}, "additionalNames2": {"_type": "GenesisIndividualCustomerAdditionalName", "salutation": "MasterSergeant", "firstName": "Sakura$<rx:[a-zA-Z]{8}>", "middleName": "middleName2", "lastName": "Suzuki$<rx:[A-Z]{8}>", "suffix": "<PERSON>", "designation": "OTHER", "description": "description2"}, "schedulingContactInfo1": {"_type": "GenesisCrmSchedulingContactInfo", "effectiveFrom": "$<today:yyyy-MM-dd>", "effectiveTo": "$<today+1y:yyyy-MM-dd>", "temporary": false}, "Adjustment_ParticipationInfo": {"_type": "GenesisParticipationInfo", "employments": ["@Adjustment_Employment"], "students": ["@Adjustment_Student"], "memberships": ["@Adjustment_Membership"], "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisParticipationInfoModel"}, "Adjustment_Employment": {"originalHireDate": "$<today:yyyy-MM-dd>", "jobTitle": " ", "payType": "Salary", "payrollFrequency": "Weekly", "jobCode": "LI", "employmentStatus": "ACT", "customer": {"link": {"_uri": "$<static:uriOrg>"}, "_type": "EmploymentDetailsCustomerAssociation"}, "associatingDate": "$<today:yyyy-MM-dd>", "employerName": "employerName$<rx:\\d{7}>", "employmentStatusDate": "$<today:yyyy-MM-dd>", "isFlsaExempt": false, "isHourly": true, "tempRehired": false, "occupationCategory": "Light", "payEffectiveDate": "$<today:yyyy-MM-dd>", "jobStartDate": "$<today:yyyy-MM-dd>", "serviceDate": "$<today+1y:yyyy-MM-dd>", "terminationDate": "$<today+1y:yyyy-MM-dd>", "lastDateWorked": "$<today+1y:yyyy-MM-dd>", "hourlyWage": "50$", "salary": [{"salaryEffectiveDate": "$<today+1m:yyyy-MM-dd>", "salaryAmount": "1520.68$", "_type": "Salary"}, {"salaryEffectiveDate": "$<today+2m:yyyy-MM-dd>", "salaryAmount": "6000$", "_type": "Salary"}], "unionName": "union name", "unionMember": true, "totalHoursWorkedLast12Months": "60", "hoursWorked": [{"typeHoursWorked": "AW180DPTO", "hours": 50, "_type": "HoursWorked"}, {"typeHoursWorked": "T20W", "hours": 10, "_type": "HoursWorked"}], "workSchedule": [{"workScheduleFrom": "$<today:yyyy-MM-dd>", "weeklyHours": 40, "workScheduleThru": "$<today+1y:yyyy-MM-dd>", "weekdayHours": {"hoursThu": 1, "hoursTue": 2, "hoursWed": 3, "hoursSat": 4, "hoursMon": 5, "hoursSun": 6, "hoursFri": 7, "_type": "WeekdayHours"}, "repeatingHours": [{"hoursDay": "8 hours a day", "hours": 8, "_type": "RepeatingHours"}, {"hoursDay": "4 hours a day", "hours": 4, "_type": "RepeatingHours"}], "_type": "WorkSchedule"}, {"workScheduleFrom": "$<today:yyyy-MM-dd>", "weeklyHours": 30, "workScheduleThru": "$<today+1y:yyyy-MM-dd>", "weekdayHours": {"hoursThu": 2, "hoursTue": 5, "hoursWed": 5, "hoursSat": 4, "hoursMon": 8, "hoursSun": 9, "hoursFri": 1, "_type": "WeekdayHours"}, "_type": "WorkSchedule"}], "keyEmployee": false, "classInfo": [{"employeeClass": "employee class 1", "classEffectiveDate": "$<today+1y:yyyy-MM-dd>", "_type": "ClassInfo"}, {"employeeClass": "employee class 2", "classEffectiveDate": "$<today+1y:yyyy-MM-dd>", "_type": "ClassInfo"}], "workSiteOrgCode": "org code", "workCountryCodeCd": "US", "workStateCodeCd": "IL", "icdInfo": [{"icdCode": "A92.0", "primaryCode": true, "_type": "IcdInfo"}, {"icdCode": "B44.0", "primaryCode": false, "_type": "IcdInfo"}], "priorDisability": [{"priorDisabilityDateTo": "$<today:yyyy-MM-dd>", "priorDisabilityDateFrom": "$<today-1y:yyyy-MM-dd>", "_type": "PriorDisability"}], "_type": "EmploymentDetails", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.EmploymentDetailsModel"}, "Adjustment_Student": {"studentStatus": "INA", "studentStartDate": "$<today:yyyy-MM-dd>", "customer": {"customerNumber": "12345", "link": {"_uri": "$<static:uriOrg>"}, "_type": "StudentDetailsCustomerAssociation"}, "_type": "StudentDetails"}, "Adjustment_Membership": {"memberId": "$<rx:[a-zA-Z]{8}>", "membershipId": "$<rx:[a-zA-Z]{8}>", "membershipStatus": "INA", "membershipStartDate": "$<today:yyyy-MM-dd>", "customer": {"customerNumber": "12345", "link": {"_uri": "$<static:uriOrg>"}, "_type": "MembershipDetailsCustomerAssociation"}, "_type": "MembershipDetails"}, "Adjustment_ICDCode": {"icdCode": "B44.0", "primaryCode": false, "_type": "IcdInfo", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.IcdInfoModel"}, "Adjustment_Address": {"subdivision": "subdivision", "_type": "GenesisCrmAddress", "ModelImplClassPath": "com.eis.automation.v20.cem.entity.customer.individual.impl.modeling.GenesisCrmAddressModel"}}