// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "CapDentalPatientHistory"
  ],
  "moduleType":"CapProcedure",
  "name":"CapDentalProcedure",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalProcedureProjection"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CapDentalProcedureProjection":{
      "attributes":{
        "DOSDate":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"DOSDate",
          "type":{
            "type":"DATETIME"
          }
        },
        "claimNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"claimNumber",
          "type":{
            "type":"STRING"
          }
        },
        "patientNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"patientNumber",
          "type":{
            "type":"STRING"
          }
        },
        "planCategory":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"planCategory",
          "type":{
            "type":"STRING"
          }
        },
        "policyNumber":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"policyNumber",
          "type":{
            "type":"STRING"
          }
        },
        "providerTIN":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"providerTIN",
          "type":{
            "type":"STRING"
          }
        },
        "state":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"state",
          "type":{
            "type":"STRING"
          }
        },
        "toothCodes":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
            }
          },
          "name":"toothCodes",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
        "uri":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                      "_uri"
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.features.Searchable":{
              "fieldName":"_uri"
            }
          },
          "name":"uri",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalProcedureProjection",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace CapDentalProcedure {
    export type Variations = never


    export class CapDentalProcedureProjection extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalProcedureProjection.name) }
        readonly DOSDate?: Date
        readonly _modelName: string = 'CapDentalProcedure'
        readonly _modelType: string = 'CapProcedure'
        readonly _modelVersion?: string = '1'
        readonly claimNumber?: string
        readonly patientNumber?: string
        readonly planCategory?: string
        readonly policyNumber?: string
        readonly providerTIN?: string
        readonly state?: string
        readonly toothCodes: string[] = []
        readonly uri?: MAPI.ExternalLink
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CapDentalProcedureProjection, ()=> new CapDentalProcedureProjection)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}