/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input;

import com.eisgroup.genesis.cap.financial.command.template.input.CapPaymentTemplateUpdateInput;
import com.google.gson.JsonObject;

/**
 * {@link com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity} update request input
 *
 * <AUTHOR>
 * @since 22.2
 */
public class CapDentalPaymentTemplateUpdateInput extends CapPaymentTemplateUpdateInput {

    public CapDentalPaymentTemplateUpdateInput(JsonObject original) {
        super(original);
    }
}
