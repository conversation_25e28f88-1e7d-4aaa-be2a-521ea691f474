BaseType CapDentalClaimCoordinationOfBenefitsEntity {

    @Description("Policyholder Address of COB.")
    Attr address: String

    @Description("Other Coverage Type of COB.")
    Attr otherCoverageType: String

    @Description("Other Insurance Company Name of COB.")
    Attr otherInsuranceCompany: String

    @Description("Other Policy Type of COB.")
    Attr otherPolicyType: String

    @Description("Start and End Dates of COB.")
    Attr period: Term

    @Description("Plan or Group Number of COB.")
    Attr plan: String

    @Description("Policyholder Date of Birth of COB.")
    Attr policyholderDateOfBirth: Date

    @Description("First Name of Policyholder of COB.")
    Attr policyholderFirstName: String

    @Description("Policyholder Gender of COB.")
    Attr policyholderGender: String

    @Description("Last Name of Policyholder of COB.")
    Attr policyholderLastName: String

    @Description("Patient Relationship to Policyholder of COB.")
    Attr policyholderRelationshipToPatient: String

    @Description("Policy Number of COB.")
    Attr PolicyNumber: String

    @Description("Type of Coordination of Benefits.")
    Attr typeOfCob: String
}