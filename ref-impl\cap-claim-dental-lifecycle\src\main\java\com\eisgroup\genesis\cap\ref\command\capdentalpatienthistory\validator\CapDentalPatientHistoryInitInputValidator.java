/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator;

import static com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator.CapDentalPatientHistoryInitInputValidator.CapDentalPatientHistoryInitInputValidatorErrorDefinition.PATIENT_LINK_INCORRECT;

import java.util.Optional;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.input.CapDentalPatientHistoryInitInput;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalClaimDataEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryDataEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;

/**
 * Validator class for {@link CapDentalPatientHistoryInitInput}.
 *
 * <AUTHOR>
 * @since 22.13
 */
public class CapDentalPatientHistoryInitInputValidator extends CapInputValidator<CapDentalPatientHistoryInitInput> {

    private final CapDentalLinkValidator capDentalLinkValidator;

    public CapDentalPatientHistoryInitInputValidator(CapDentalLinkValidator capDentalLinkValidator) {
        super(CapDentalPatientHistoryInitInput.class);
        this.capDentalLinkValidator = capDentalLinkValidator;
    }

    @Override
    public Streamable<ErrorHolder> validate(CapDentalPatientHistoryInitInput input) {
        return validatePatientHistory(input.getEntity());
    }


    private Streamable<ErrorHolder> validatePatientHistory(CapDentalPatientHistoryEntity capDentalPatientHistoryEntity) {
        return Optional.ofNullable(capDentalPatientHistoryEntity)
                .map(CapDentalPatientHistoryEntity::getPatientHistoryData)
                .map(CapDentalPatientHistoryDataEntity::getClaimData)
                .map(CapDentalClaimDataEntity::getPatient)
                .map(patient -> capDentalLinkValidator.validateLink(patient, "Customer", PATIENT_LINK_INCORRECT.builder().build()))
                .orElse(Streamable.empty());
    }

    public static class CapDentalPatientHistoryInitInputValidatorErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalPatientHistoryInitInputValidatorErrorDefinition PATIENT_LINK_INCORRECT = new CapDentalPatientHistoryInitInputValidatorErrorDefinition("ciph001", "patient URI is not valid");

        protected CapDentalPatientHistoryInitInputValidatorErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
