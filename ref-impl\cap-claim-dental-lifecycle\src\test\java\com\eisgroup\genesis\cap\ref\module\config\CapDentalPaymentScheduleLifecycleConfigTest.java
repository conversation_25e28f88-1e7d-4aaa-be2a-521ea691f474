package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

@RunWith(MockitoJUnitRunner.class)
public class CapDentalPaymentScheduleLifecycleConfigTest {

    @InjectMocks
    private CapDentalPaymentScheduleLifecycleConfig config;

    @Mock
    private SequenceGenerator sequenceGenerator;

    @Mock
    private EntityLinkResolverRegistry entityLinkResolverRegistry;

    @Mock
    private CapDentalPaymentScheduleValidator capDentalPaymentScheduleValidator;

    @Mock
    private ModeledTransformationService modeledTransformationService;

    @Mock
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;

    @Mock
    private CapDentalLinkValidator capDentalLinkValidator;

    @Test
    public void testReturnCapDentalPaymentScheduleService() {
        var result = config.capDentalPaymentScheduleService(modeledTransformationService, capDentalPaymentScheduleValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnPaymentScheduleNumberGenerator() {
        var result = config.paymentScheduleNumberGenerator(sequenceGenerator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalLinkValidator() {
        var result = config.capDentalLinkValidator(entityLinkResolverRegistry);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPaymentScheduleValidator() {
        var result = config.capDentalPaymentScheduleValidator(capDentalPaymentScheduleIndexRepository, modeledTransformationService);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalBuildPaymentScheduleInputValidator() {
        var result = config.capDentalBuildPaymentScheduleInputValidator(entityLinkResolverRegistry, capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalPreviewPaymentScheduleInputValidator() {
        var result = config.capDentalPreviewPaymentScheduleInputValidator(entityLinkResolverRegistry, capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalInitPaymentScheduleInputValidator() {
        var result = config.capDentalInitPaymentScheduleInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }

    @Test
    public void testReturnCapDentalUpdatePaymentScheduleInputValidator() {
        var result = config.capDentalUpdatePaymentScheduleInputValidator(capDentalLinkValidator);
        assertThat(result, notNullValue());
    }
}





