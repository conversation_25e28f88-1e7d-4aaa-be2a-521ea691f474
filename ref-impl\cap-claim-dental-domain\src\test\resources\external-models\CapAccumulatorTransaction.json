{"applicationName": "cap_generic_deployer_app", "timestamp": 1652345448911, "modelType": "com.eisgroup.genesis.factory.model.domain.DomainModel", "modelName": "CapAccumulatorTransaction", "modelVersion": "1", "model": "{\"@class\":\"com.eisgroup.genesis.factory.model.domain.DomainModel\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{},\"featureType\":\"class com.eisgroup.genesis.factory.model.domain.DomainModelFeature\"},\"identityModel\":null,\"importedFeatures\":[],\"indexDeterminants\":[\"None\"],\"moduleType\":\"CapAccumulatorTransactionEntry\",\"name\":\"CapAccumulatorTransaction\",\"namespaceIncludes\":[],\"root\":{\"@class\":\"com.eisgroup.genesis.factory.model.domain.RootEntityType\",\"type\":\"CapAccumulatorTransactionEntity\"},\"storeDeterminants\":[\"None\"],\"types\":{\"Term\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"expirationDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"expirationDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"effectiveDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"effectiveDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"Term\"}],\"baseTypes\":[\"Term\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"Term\",\"references\":{}},\"JsonType\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"JsonType\"}],\"baseTypes\":[\"JsonType\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"JsonType\",\"references\":{}},\"CapAccumulatorTransactionData\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"policyTermDetails\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Contains Effective/Expiration dates for the term. Used to calculate accumulators by term.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Contains Effective/Expiration dates for the term. Used to calculate accumulators by term.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyTermDetails\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"Term\"}},\"amount\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Unit independent amount changed with this transaction. It can be Days/Money.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Unit independent amount changed with this transaction. It can be Days/Money.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"amount\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DECIMAL\"}},\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Transaction type, is used to track what kind of change was done.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Transaction type, is used to track what kind of change was done.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"type\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"transactionDate\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Date when the transaction was received and consumed. Not necessary when the transaction itself has happened.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Date when the transaction was received and consumed. Not necessary when the transaction itself has happened.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"transactionDate\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"coverage\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Specific coverage value for which accumulator is created for\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Specific coverage value for which accumulator is created for\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"coverage\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"extension\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Extension for lob specific accumulators, when initial calculations might be too complicated to define in tabular form.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"extension\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"JsonType\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorTransactionData\"}],\"baseTypes\":[\"CapAccumulatorTransactionData\"],\"extLinks\":{\"resource\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Points to a secondary resource, that this transaction relates to. This is used when a transaction is started by a resource accommodator and the actual transaction affects different resources. Optional\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Points to a secondary resource, that this transaction relates to. This is used when a transaction is started by a resource accommodator and the actual transaction affects different resources. Optional\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"resource\",\"targetType\":\"RootEntity\"},\"party\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.ExternalLink\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Link to secondary customer related to this transaction. Optional\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Link to secondary customer related to this transaction. Optional\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.features.ExternalLinkFeature\"},\"name\":\"party\",\"targetType\":\"RootEntity\"}},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.EntityFromBaseType\\\"}\"},\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorTransactionData\",\"references\":{}},\"CapAccumulatorTransactionEntityContainer\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"entries\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Accumulator transaction entry list.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Accumulator transaction entry list.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapAccumulatorTransaction/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"entries\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapAccumulatorTransactionEntity\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Contains multiple transaction entries. Used in commands with multi-transaction output.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Contains multiple transaction entries. Used in commands with multi-transaction output.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorTransactionEntryContainer\"}],\"baseTypes\":[\"CapAccumulatorTransactionEntryContainer\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Contains multiple transaction entries. Added as a temporary extract transaction command output type.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Contains multiple transaction entries. Added as a temporary extract transaction command output type.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapAccumulatorTransaction/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorTransactionEntityContainer\",\"references\":{}},\"CapAccumulatorTransactionEntity\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledEntity\",\"attributes\":{\"policyURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"0\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"policyURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"transactionTimestamp\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"2\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"transactionTimestamp\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"DATETIME\"}},\"customerURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.385045733.PartitionedKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.385045733.PartitionedKey\\\"}\"},\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Primary Insured\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Primary Insured\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"customerURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}},\"durable\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Specifies if transaction is short lived at the time of transaction date or long lasting.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Specifies if transaction is short lived at the time of transaction date or long lasting.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"durable\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"BOOLEAN\"}},\"data\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"MULTIPLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Container for transaction related information.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Container for transaction related information.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":true}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"data\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributeEntityType\",\"type\":\"CapAccumulatorTransactionData\"}},\"sourceURI\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttribute\",\"cardinality\":\"SINGLE\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[{\"@class\":\"java.lang.Long\",\"value\":\"1\"}]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.generated.features.asm.1952627124.ClusteredKey\\\"}\"},\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.modeled.features.Inherited\\\",\\\"complexType\\\":false}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.AttributeFeature\"},\"name\":\"sourceURI\",\"type\":{\"@class\":\"com.eisgroup.genesis.factory.model.types.modeled.ModeledAttributePrimitiveType\",\"type\":\"STRING\"}}},\"baseTypeInfo\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.RootType\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.RootType\\\",\\\"modelTypes\\\":{\\\"@array\\\":\\\"java.lang.String\\\",\\\"values\\\":[]}}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"RootEntity\"},{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Transactions track what events are happening and are stored as is.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Transactions track what events are happening and are stored as is.\\\",\\\"messageBundle\\\":\\\"domain-messages/PaymentTestProduct/description\\\"}\"},\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[{\"@class\":\"com.eisgroup.genesis.factory.model.types.base.BaseTypeInfo\",\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.model.types.base.features.BaseDomain\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.base.features.TypeFeature\"},\"parents\":[],\"typeName\":\"CapAccumulatorTransactionKey\"}],\"typeName\":\"CapAccumulatorTransactionEntry\"}],\"baseTypes\":[\"CapAccumulatorTransactionEntry\",\"RootEntity\"],\"extLinks\":{},\"features\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder\",\"features\":{\"com.eisgroup.genesis.factory.features.Description\":{\"@class\":\"com.eisgroup.genesis.factory.model.utils.dto.FeaturesMapHolder$SerializedFeatureHolder\",\"compilerInfo\":{\"@class\":\"com.eisgroup.genesis.factory.features.CompilerSignature$CompilerInfo\",\"constructorArgs\":{\"@array\":\"java.lang.Object\",\"values\":[\"Transactions track what events are happening and are stored as is.\"]},\"externalInfo\":null,\"inherited\":false},\"serializedFeature\":\"{\\\"@class\\\":\\\"com.eisgroup.genesis.factory.features.Description\\\",\\\"descriptionText\\\":\\\"Transactions track what events are happening and are stored as is.\\\",\\\"messageBundle\\\":\\\"domain-messages/CapAccumulatorTransaction/description\\\"}\"}},\"featureType\":\"class com.eisgroup.genesis.factory.model.types.modeled.features.TypeFeature\"},\"links\":{},\"name\":\"CapAccumulatorTransactionEntity\",\"references\":{}}},\"variations\":[],\"version\":\"1\"}"}