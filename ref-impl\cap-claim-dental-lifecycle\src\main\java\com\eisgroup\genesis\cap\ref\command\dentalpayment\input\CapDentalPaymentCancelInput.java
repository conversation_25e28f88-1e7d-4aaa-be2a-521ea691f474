/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment.input;

import com.eisgroup.genesis.cap.ref.command.dentalpayment.CapDentalPaymentCancelHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentMessageEntity;
import com.google.gson.JsonObject;

import java.util.Collection;

/**
 * Input for {@link CapDentalPaymentCancelHandler} command
 *
 * <AUTHOR>
 * @since 25.10
 */
public class CapDentalPaymentCancelInput extends IdentifierRequest {

    private static final String MESSAGES = "messages";

    public CapDentalPaymentCancelInput(JsonObject original) {
        super(original);
    }

    public Collection<CapDentalPaymentMessageEntity> getMessages() {
        return getCollection(CapDentalPaymentMessageEntity.class, MESSAGES);
    }
}
