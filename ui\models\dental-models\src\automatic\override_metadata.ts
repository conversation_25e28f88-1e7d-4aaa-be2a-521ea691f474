// tslint:disable
/**
  * This is autogenerated code by 'override-invalidator-metadata-generator' maven plugin.
  * It generates metadata for  handling override values changes.
  *
  * @namespace OverrideInvalidatorMetadata
  * @see https://wiki.eisgroup.com/display/GRC/%5BKraken+UI%5D+Code+Generators
  * @see https://wiki.eisgroup.com/display/GRC/Rule+Override
  */
export namespace OverrideInvalidatorMetadata {

    export type Metadata = {
        entityName: string,
        attributeName: string,
        valueDataType: string,
        invalidatorAction: "INCREASE" | "DECREASE"
    }
    /**
     * key is an "entity_name.entity_field"
     * value is override metadata
     */
    export type OverrideInvalidatorMetadataType = Record<string, Metadata>
/**
  * Domain model info:
  *   name: CapDentalPaymentScheduleIndex 
  *   type: CapDentalPaymentScheduleIndex 
  *   Root type: CapDentalPaymentScheduleIdxEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentScheduleIndex: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPaymentDefinition 
  *   type: CapPayment 
  *   Root type: CapDentalPaymentEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentDefinition: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalSettlement 
  *   type: CapSettlement 
  *   Root type: CapDentalSettlementEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalSettlement: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: DentalLossRootIdentifierTransformationModel 
  *   type: DentalLossRootIdentifierTransformationModel 
  *   Root type: CapRootIdentifierEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const DentalLossRootIdentifierTransformationModel: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapPolicyHolder 
  *   type: RootEntity 
  *   Root type: CapPolicyEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapPolicyHolder: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPaymentSchedule 
  *   type: CapPaymentSchedule 
  *   Root type: CapDentalPaymentScheduleEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentSchedule: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPaymentTemplateIndex 
  *   type: CapDentalPaymentTemplateIndex 
  *   Root type: CapDentalPaymentTemplateIdxEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentTemplateIndex: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalSettlementIndex 
  *   type: CapDentalSettlementIndex 
  *   Root type: CapDentalSettlementIdx 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalSettlementIndex: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapHeaderModel 
  *   type: CapClaimHeader 
  *   Root type: CapHeaderEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapHeaderModel: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalBalance 
  *   type: CapBalance 
  *   Root type: CapDentalBalanceEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalBalance: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPaymentTemplate 
  *   type: CapPaymentTemplate 
  *   Root type: CapDentalPaymentTemplateEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentTemplate: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalProcedure 
  *   type: CapProcedure 
  *   Root type: CapDentalProcedureProjection 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalProcedure: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: DentalInternal 
  *   type: DentalInternal 
  *   Root type: CapDentalInternalEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const DentalInternal: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPaymentIndex 
  *   type: CapDentalPaymentIndex 
  *   Root type: CapDentalPaymentIdxEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPaymentIndex: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: DentalSettlementRootIdentifierTransformationModel 
  *   type: DentalSettlementRootIdentifierTransformationModel 
  *   Root type: CapRootIdentifierEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const DentalSettlementRootIdentifierTransformationModel: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalPatientHistory 
  *   type: CapDentalPatientHistoryModelType 
  *   Root type: CapDentalPatientHistoryEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalPatientHistory: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CapDentalLoss 
  *   type: CapLoss 
  *   Root type: CapDentalLossEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CapDentalLoss: OverrideInvalidatorMetadataType = {}
/**
  * Domain model info:
  *   name: CorrelationIdHolder 
  *   type: CorrelationIdHolder 
  *   Root type: CorrelationIdHolderEntity 
  *   version: 1 
  *
  * @memberof OverrideInvalidatorMetadata
  */
export const CorrelationIdHolder: OverrideInvalidatorMetadataType = {}
}
