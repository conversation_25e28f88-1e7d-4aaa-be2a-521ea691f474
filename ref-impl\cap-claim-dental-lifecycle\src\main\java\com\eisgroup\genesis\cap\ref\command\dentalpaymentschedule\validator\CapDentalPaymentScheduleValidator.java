/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.commands.validator.errors.ValidationErrorException;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.exception.ErrorHolderBuilder;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity;

import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleMessageEntity;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.validator.CapDentalPaymentScheduleValidator.CapDentalPaymentScheduleErrorDefinition.ACTIVE_SCHEDULE_EXISTS;


/**
 * Validator service for {@link CapDentalPaymentScheduleEntity} validations
 *
 * <AUTHOR>
 * @since 22.10
 */
public class CapDentalPaymentScheduleValidator {

    private static final List<String> ACTIVE_SCHEDULE_STATES = List.of("Active", "Suspended");
    private static final String CRITICAL_SEVERITY = "critical";
    private static final String ACTIVATE = "Activate";
    private static final String CAP_DENTAL_PAYMENT_SCHEDULE_ACTIVATION_TRANSFORMATION = "CapDentalPaymentScheduleActivation";

    private final CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;

    private final ModeledTransformationService transformationService;
    private final ModelRepository<TransformationModel> transformationRepository;

    public CapDentalPaymentScheduleValidator(CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository,
                                             ModeledTransformationService transformationService,
                                             ModelRepository<TransformationModel> transformationRepository) {
        this.capDentalPaymentScheduleIndexRepository = capDentalPaymentScheduleIndexRepository;
        this.transformationService = transformationService;
        this.transformationRepository = transformationRepository;
    }

    /**
     * Validates if provided {@link CapDentalPaymentScheduleEntity} can be activated and returns validation result.
     *
     * @param schedule {@link CapDentalPaymentScheduleEntity} entity
     * @return validation result
     */
    public Streamable<ErrorHolder> validatePaymentScheduleActivation(CapDentalPaymentScheduleEntity schedule) {
        return Streamable.concat(validateActiveScheduleNotExist(schedule),
            validateActivationResult(schedule));
    }

    /**
     * Checks and validates {@link CapDentalPaymentScheduleEntity#getScheduleMessages()}
     */
    public Lazy<CapDentalPaymentScheduleEntity> validateCalculatedSchedule(CapDentalPaymentScheduleEntity schedule) {
        List<ErrorHolder> errors = collectErrors(schedule);
        if (!errors.isEmpty()) {
            return Lazy.error(() -> new ValidationErrorException(CapDentalPaymentScheduleErrorDefinition.DENTAL_PAYMENT_SCHEDULE_FAILED
                .builder().addChildren(errors).build()));
        }
        return Lazy.of(schedule);
    }

    private List<ErrorHolder> collectErrors(CapDentalPaymentScheduleEntity schedule) {
        return Optional.ofNullable(schedule.getScheduleMessages())
                .orElse(Collections.emptyList())
                .stream()
                .filter(message -> CRITICAL_SEVERITY.equals(message.getSeverity()))
                .map(this::createErrorHolder)
                .collect(Collectors.toList());
    }

    private ErrorHolder createErrorHolder(CapDentalPaymentScheduleMessageEntity validationMessage) {
        return new CapDentalPaymentScheduleErrorDefinition(validationMessage.getCode(),
                validationMessage.getMessage()).builder().build();
    }

    private Streamable<ErrorHolder> validateActiveScheduleNotExist(CapDentalPaymentScheduleEntity schedule) {
        return Optional.ofNullable(schedule.getOriginSource())
                .map(originSource -> capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(originSource, ACTIVE_SCHEDULE_STATES)
                        .flatMap(activeScheduleIndexes -> {
                            if (activeScheduleIndexes == null) {
                                return Streamable.empty();
                            }
                            return Streamable.of(ErrorHolderBuilder.fromDefinition(ACTIVE_SCHEDULE_EXISTS)
                                    .params(originSource).build());
                        }))
                .orElseGet(Streamable::empty);
    }


    public Streamable<ErrorHolder> validateActivationResult(CapDentalPaymentScheduleEntity schedule) {
        CapDentalPaymentScheduleEntity transformedSchedule = verifyPaymentScheduleActivation(schedule);
       return Streamable.defer(() ->{
           if (ACTIVATE.equals(transformedSchedule.getPaymentScheduleActivationResult().getActivationStatus())) {
               return Streamable.empty();
           }
           return Streamable.of(ErrorHolderBuilder.fromDefinition(CapDentalPaymentScheduleErrorDefinition.PAYMENT_SCHEDULE_CANNOT_BE_ACTIVATED).build());
       });

    }

    private CapDentalPaymentScheduleEntity verifyPaymentScheduleActivation(CapDentalPaymentScheduleEntity schedule) {
        return transformationService.transform(transformationRepository.getActiveModel(CAP_DENTAL_PAYMENT_SCHEDULE_ACTIVATION_TRANSFORMATION), schedule);
    }

    public static class CapDentalPaymentScheduleErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalPaymentScheduleErrorDefinition ACTIVE_SCHEDULE_EXISTS = new CapDentalPaymentScheduleErrorDefinition(
                "dapsv-001", "Active or suspended payment schedule already exists for the origin source: {0}");

        public static final CapDentalPaymentScheduleErrorDefinition DENTAL_PAYMENT_SCHEDULE_FAILED = new CapDentalPaymentScheduleErrorDefinition(
                "dapsv-002", "Dental Payment Schedule failed due to validation errors");

        public static final CapDentalPaymentScheduleErrorDefinition PAYMENT_SCHEDULE_CANNOT_BE_ACTIVATED = new CapDentalPaymentScheduleErrorDefinition("fin0013", "User is not able to activate Payment Schedule");

        protected CapDentalPaymentScheduleErrorDefinition(String code, String message) {
            super(code, message);
        }
    }

}
