/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.facade.endpoint;

import com.eisgroup.genesis.facade.request.Body;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.AbstractJsonEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.google.gson.JsonElement;

import java.util.Collection;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Financial data request
 *
 * <AUTHOR>
 * @since 22.2
 */
@Body
public class CapFinancialDataRequest extends AbstractJsonEntity {

    private static final String ORIGIN_SOURCE = "originSource";
    private static final String SETTLEMENTS = "settlements";

    public EntityLink<RootEntity> getOriginSource() {
        return Optional.ofNullable(getRawChild(ORIGIN_SOURCE))
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(source -> new EntityLink(RootEntity.class, source))
                .orElse(null);
    }

    public Collection<EntityLink<RootEntity>> getSettlements() {
        return Optional.ofNullable(getRawChild(SETTLEMENTS))
                .filter(JsonElement::isJsonArray)
                .map(JsonElement::getAsJsonArray)
                .map(array -> StreamSupport.stream(array.spliterator(), false))
                .orElse(Stream.empty())
                .filter(JsonElement::isJsonObject)
                .map(JsonElement::getAsJsonObject)
                .map(uri -> new EntityLink<>(RootEntity.class, uri))
                .collect(Collectors.toList());
    }
}
