/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalClaimOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalServiceOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementDetailModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalSettlementDetailModel extends TypeModel implements ICapDentalSettlementDetailModel {

    private String overrideCd;
    private ICapDentalClaimOverrideModel claimOverride;
    private List<ICapDentalServiceOverrideModel> serviceOverrides;
    @JsonProperty(value = "_modelName")
    private String modelName;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    @JsonProperty(value = "_timestamp")
    private LocalDateTime timestamp;

    public String getOverrideCd() {
        return overrideCd;
    }

    public void setOverrideCd(String overrideCd) {
        this.overrideCd = overrideCd;
    }

    @JsonSerialize(as = CapDentalClaimOverrideModel.class)
    public ICapDentalClaimOverrideModel getClaimOverride() {
        return claimOverride;
    }

    @JsonDeserialize(as = CapDentalClaimOverrideModel.class)
    public void setClaimOverride(ICapDentalClaimOverrideModel claimOverride) {
        this.claimOverride = claimOverride;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalServiceOverrideModel.class)
    public List<ICapDentalServiceOverrideModel> getServiceOverrides() {
        return serviceOverrides;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalServiceOverrideModel.class)
    public void setServiceOverrides(List<ICapDentalServiceOverrideModel> serviceOverrides) {
        this.serviceOverrides = serviceOverrides;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public LocalDateTime getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
}
