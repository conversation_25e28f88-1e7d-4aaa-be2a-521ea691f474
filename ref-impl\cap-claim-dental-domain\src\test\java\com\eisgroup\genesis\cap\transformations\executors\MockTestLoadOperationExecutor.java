/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations.executors;

import com.eisgroup.genesis.exception.InvocationError;
import com.eisgroup.genesis.factory.FileObjectUtil;
import com.eisgroup.genesis.factory.json.ModelRootEntity;
import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.executors.OperationExecutorValidator;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import org.apache.commons.io.IOUtils;
import org.apache.commons.vfs2.FileObject;
import org.apache.commons.vfs2.FileSystemException;
import org.apache.commons.vfs2.VFS;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

/**
 * Testing operator executor which mocks Load operation.
 *
 */
public class MockTestLoadOperationExecutor implements OperationExecutor<GenericOperation, JsonElement> {

    private static Collection<JsonObject> allResources = new ArrayList<>();

    private static String mockedResourcesPath;

    public static void setMockedResourcesPath(String path) {
        mockedResourcesPath = path;
    }

    public MockTestLoadOperationExecutor(String mockedResourcesPath) {
        setMockedResourcesPath(mockedResourcesPath);
    }

    @PostConstruct
    public void fetchJsonResources() {
        allResources.addAll(doFetchJsonResources());
    }

    @Override
    public JsonElement execute(GenericOperation genericOperation, TransformationContext<JsonElement> context) {
        var keyInput = genericOperation.getInput(0);
        var modelNameInput = genericOperation.getInput(1);
        var typeNameInput = genericOperation.getInput(2);
        return toArray(resolveEntityFromResources(context.execute(keyInput), context.execute(modelNameInput), context.execute(typeNameInput)));
    }

    private JsonArray toArray(List<JsonObject> entities) {
        JsonArray jsonArray = new JsonArray();
        entities.forEach(jsonArray::add);
        return jsonArray;
    }

    private List<JsonObject> resolveEntityFromResources(JsonElement key, JsonElement modelName, JsonElement type) {
        JsonObject keyObject = OperationExecutorValidator.validateJsonObject(key, "First argument passed to Load should resolve to json object.");
        String modelNameString = OperationExecutorValidator.validateString(modelName, "Second argument passed to Load should indicate target model name as a string.");
        String typeString = OperationExecutorValidator.validateString(type, "Third argument passed to Load should indicate target type name as a string.");
        return allResources
                .stream()
                .filter(json -> matches(json, modelNameString, typeString, keyObject))
                .toList();
    }

    private static Collection<JsonObject> doFetchJsonResources() {
        Collection<JsonObject> allResources = new ArrayList<>();
        Iterator<URL> dirs = getAllResourceDirectories();
        while (dirs.hasNext()) {
            String urlString = dirs.next().toExternalForm();
            // copied from com.eisgroup.genesis.factory.model.store.VfsUtils#toFileObject()
            if (urlString.indexOf("jar!") != urlString.lastIndexOf("jar!")) {
                urlString = "jar:" + urlString;
            }
            try {
                FileObject dir = VFS.getManager().resolveFile(urlString);
                allResources.addAll(Stream.of(FileObjectUtil.collectFiles(dir, "json"))
                        .map(MockTestLoadOperationExecutor::readFile)
                        .map(JsonParser::parseString)
                        .flatMap(MockTestLoadOperationExecutor::convertToJsonObjects)
                        .toList());
            } catch (FileSystemException e) {
                throw new InvocationError("Can't fetch json resources: " + e.getMessage());
            }
        }
        return allResources;
    }

    private static Iterator<URL> getAllResourceDirectories() {
        try {
            return Thread.currentThread().getContextClassLoader().getResources(mockedResourcesPath).asIterator();
        } catch (IOException e) {
            throw new InvocationError("Can't resolve directory " + mockedResourcesPath);
        }
    }

    private static Stream<JsonObject> convertToJsonObjects(JsonElement jsonElement) {
        if (jsonElement instanceof JsonArray) {
            return StreamSupport.stream(((JsonArray) jsonElement).spliterator(), false)
                    .map(JsonObject.class::cast);
        } else if (jsonElement instanceof JsonObject) {
            return Stream.of((JsonObject) jsonElement);
        } else {
            throw new IllegalArgumentException("Unsupported json element instance.");
        }
    }

    private static String readFile(FileObject file) {
        try {
            return IOUtils.toString(Objects.requireNonNull(file.getContent().getInputStream()),
                    StandardCharsets.UTF_8);
        } catch (IOException e) {
            throw new InvocationError("Can't read file " + file.getName());
        }
    }

    private boolean matches(JsonObject json, String modelName, String type, JsonObject key) {
        return modelNameMatches(json, modelName) && typeMatches(json, type) && keyMatches(json, key);
    }

    private boolean modelNameMatches(JsonObject json, String modelName) {
        return modelName.equals(json.get(ModelRootEntity.MODEL_NAME_ATTR).getAsString());
    }

    private boolean typeMatches(JsonObject json, String type) {
        return type.equals(json.get(ModelRootEntity.TYPE_ATTRIBUTE).getAsString());
    }

    private boolean keyMatches(JsonObject json, JsonObject key) {
        return key.entrySet()
                .stream()
                .allMatch(keyEntry -> keyEntry.getValue().equals(json.get(keyEntry.getKey())));
    }

    @Override
    public String getName() {
        return "Load";
    }
}
