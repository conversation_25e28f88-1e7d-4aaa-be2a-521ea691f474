/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymenttemplate.service.impl;

import com.eis.automation.tzappa.rest.modeling.utils.IModelUtils;
import com.eis.automation.tzappa_v20.TestDataProvider;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.ICapDentalPaymentTemplate;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.impl.CapDentalPaymentAllocationTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateBuildModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.facade.impl.modeling.interf.ICapDentalPaymentTemplateModel;
import com.eis.automation.v20.capdental.entity.paymenttemplate.service.ICapDentalPaymentTemplateService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;
import com.exigen.istf.data.TestData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

@Lazy
@Component("capDentalPaymentTemplate")
public class CapDentalPaymentTemplateService implements ICapDentalPaymentTemplateService {

    @Autowired
    private ICapDentalPaymentTemplate dentalPaymentTemplate;
    @Lazy
    @Autowired
    @Qualifier("ModelingUtilsJson")
    private IModelUtils modelUtils;
    @Autowired
    @Qualifier("TestDataProvider")
    private TestDataProvider testDataProvider;
    private TestData tdDentalPaymentTemplateLocation;
    private TestData tdSpecificDentalPaymentTemplateLocation;

    @Override
    public ICapDentalPaymentTemplate getFacade() {
        return dentalPaymentTemplate;
    }

    @Override
    public TestData getTestData(String... strings) {
        return tdDentalPaymentTemplateLocation.getTestData(strings);
    }

    @Override
    public TestData getSpecificTestData(String... strings) {
        return tdSpecificDentalPaymentTemplateLocation.getTestData(strings);
    }

    @PostConstruct
    private void testDataResolver() {
        tdDentalPaymentTemplateLocation = testDataProvider.getJSONTestData("/capdental/paymenttemplate");
        tdSpecificDentalPaymentTemplateLocation = testDataProvider.getJSONTestData("/capdental/paymenttemplate/specific");
    }

    public ICapDentalPaymentTemplateModel initDentalPaymentTemplate(ICapDentalPaymentTemplateModel paymentTemplateModel) {
        return (ICapDentalPaymentTemplateModel) getFacade().init().perform(b -> b.setModel(paymentTemplateModel));
    }

    public ICapDentalPaymentTemplateModel createDentalPaymentTemplateModel(ICustomerModel customerModel,
                                                                           ICapDentalLossModel dentalClaimModel,
                                                                           ICapDentalSettlementModel dentalSettlementModel) {
        return createDentalPaymentTemplateModel(
                getTestData("Write", "TestData"), customerModel, dentalClaimModel, dentalSettlementModel);
    }

    public ICapDentalPaymentTemplateModel createDentalPaymentTemplateModel(TestData td, ICustomerModel customerModel,
                                                                           ICapDentalLossModel dentalClaimModel,
                                                                           ICapDentalSettlementModel dentalSettlementModel) {
        ICapDentalPaymentTemplateModel dentalPaymentTemplateModel = modelUtils.create(td);
        dentalPaymentTemplateModel.setOriginSource(dentalClaimModel.getGentityUri().getUriModel());
        CapDentalPaymentAllocationTemplateModel allocationModel = (CapDentalPaymentAllocationTemplateModel)
                dentalPaymentTemplateModel.getEntity().getPaymentAllocationTemplates().get(0);
        allocationModel.setAllocationSource(dentalSettlementModel.getGentityUri().getUriModel());
        allocationModel.getAllocationPayeeDetails().setPayee(customerModel.getGerootUri().getUriModel());
        allocationModel.getAllocationLossInfo().setLossSource(dentalClaimModel.getGentityUri().getUriModel());
        return dentalPaymentTemplateModel;
    }

    public ICapDentalPaymentTemplateModel financialData(ICapDentalPaymentTemplateBuildModel dentalPaymentTemplateBuildModel) {
        return getFacade().financialData().perform(b -> b.setModel(dentalPaymentTemplateBuildModel));
    }

    public ICapDentalPaymentTemplateBuildModel createDentalPaymentTemplateBuildModel(ICapDentalLossModel dentalClaimModel,
                                                                                     ICapDentalSettlementModel dentalSettlementModel) {
        ICapDentalPaymentTemplateBuildModel buildModel = modelUtils.create(getTestData("Build", "TestData"));
        buildModel.setOriginSource(dentalClaimModel.getGentityUri().getUriModel());
        buildModel.setSettlements(List.of(dentalSettlementModel.getGentityUri().getUriModel()));
        return buildModel;
    }

}