/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance.service.impl;

import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceCalculationRulesOutput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.test.utils.TestStreamable;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DefaultCapDentalBalanceCalculatorTest {

    @InjectMocks
    private DefaultCapDentalBalanceCalculator calculator;

    @Mock
    private ModeledTransformationService transformationService;

    @Mock
    private ModelRepository<TransformationModel> transformationRepository;

    @Test
    public void shouldCalculateLossBalance() {
        //given
        EntityLink<RootEntity> originSource = new EntityLink<>(RootEntity.class, "test");
        CapDentalBalanceCalculationRulesOutput output = (CapDentalBalanceCalculationRulesOutput) ModelInstanceFactory.createInstance("CapDentalBalance", "1", CapDentalBalanceCalculationRulesOutput.class.getSimpleName());
        when(transformationService.transform(any(), Optional.ofNullable(any()))).thenReturn(output);

        //when
        TestStreamable.create(calculator.calculateLossBalance(originSource))
                //then
                .assertNoErrors()
                .assertComplete();
    }
}
