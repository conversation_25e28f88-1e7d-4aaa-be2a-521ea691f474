// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALPAYMENTDEFINITION } from "./kraken_model_tree_CapDentalPaymentDefinition"

let name = "CapDentalPaymentDefinition"

let namespace = "CapDentalPaymentDefinition"

export type CapDentalPaymentDefinitionEntryPointName = "CapDentalPaymentDefinition:Init Adjustment Payment Validation" | "CapDentalPaymentDefinition:Init Payment Validation"

let entryPointNames = [
    "CapDentalPaymentDefinition:Init Adjustment Payment Validation",
    "CapDentalPaymentDefinition:Init Payment Validation"
] as CapDentalPaymentDefinitionEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALPAYMENTDEFINITION as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalPaymentDefinition = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree}, 'rules-model')));
