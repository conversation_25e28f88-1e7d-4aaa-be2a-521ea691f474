{"swagger": "2.0", "info": {"description": "API for search", "version": "1", "title": "search model API facade"}, "basePath": "/", "schemes": ["https"], "paths": {"/api/common/search/v2/CapBalance": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapDentalLoss": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapDentalPatientHistory": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapDentalPatientHistoryModelType": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapDentalSettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPayment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPayment/overpaymentWaive": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPayment/payment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPayment/recovery": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPayment/underpayment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPaymentSchedule": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v2/CapPaymentTemplate": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapBalance": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapDentalLoss": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapDentalPatientHistory": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapDentalPatientHistoryModelType": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapDentalSettlement": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPayment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPayment/overpaymentWaive": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPayment/payment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPayment/recovery": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPayment/underpayment": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPaymentSchedule": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}, "/api/common/search/v3/CapPaymentTemplate": {"post": {"description": "No description provided", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "body", "description": "No description provided", "required": false, "schema": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3Body"}}, {"name": "embed", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "fields", "in": "query", "description": "", "required": false, "type": "string"}, {"name": "limit", "in": "query", "description": "", "required": false, "type": "integer"}, {"name": "offset", "in": "query", "description": "", "required": false, "type": "integer"}], "responses": {"200": {"description": "No description provided", "schema": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3SuccessBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "422": {"description": "Client request validation failed due to request semantical errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "401": {"description": "User access denied due to failed authentication or user is missing privileges.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "500": {"description": "Request processing failed due to system or unexpected errors.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}, "404": {"description": "The requested entity could not be found by provided criteria.", "schema": {"$ref": "#/definitions/EndpointFailureFailureBody"}, "headers": {"X-Transaction-ID": {"type": "string", "description": "The Business transaction id (sagaId)"}, "X-Request-ID": {"type": "string", "description": "The unique identifier for the request"}}}}}}}, "definitions": {"CapDentalBalance_CapDentalBalanceEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalBalance"}, "_modelType": {"type": "string", "example": "CapBalance"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalBalanceEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "balanceItems": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemEntity"}}, "creationDate": {"type": "string", "format": "date-time", "description": "The date when the balance entity was created."}, "originSource": {"$ref": "#/definitions/EntityLink"}, "payee": {"$ref": "#/definitions/EntityLink"}, "totalBalanceAmount": {"$ref": "#/definitions/Money"}}, "title": "CapDentalBalance CapDentalBalanceEntity", "description": "The Root Entity of Balance Details Domain."}, "CapDentalBalance_CapDentalBalanceItemActualAllocationAdditionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemActualAllocationAdditionEntity"}, "additionSubType": {"type": "string", "description": "Addition subtype."}, "additionType": {"type": "string", "description": "Addition type. For example: Rehabilitation."}, "appliedAmount": {"$ref": "#/definitions/Money"}, "balancedAppliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapDentalBalance CapDentalBalanceItemActualAllocationAdditionEntity", "description": "Addition details applied to the actual allocation."}, "CapDentalBalance_CapDentalBalanceItemActualAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemActualAllocationEntity"}, "adjustmentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemAdjustmentAllocationEntity"}}, "allocationAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemActualAllocationAdditionEntity"}}, "allocationBalancedGrossAmount": {"$ref": "#/definitions/Money"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLossInfo": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalBalance_CapDentalPaymentAllocationPayableItemEntity"}, "allocationReductions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemActualAllocationReductionEntity"}}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemActualAllocationTaxEntity"}}, "scheduledAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemScheduledAllocationEntity"}}}, "title": "CapDentalBalance CapDentalBalanceItemActualAllocationEntity", "description": "Actual allocation details."}, "CapDentalBalance_CapDentalBalanceItemActualAllocationReductionEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemActualAllocationReductionEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "balancedAppliedAmount": {"$ref": "#/definitions/Money"}, "reductionSubType": {"type": "string", "description": "Tax subtype. For example: Deduction CHSP."}, "reductionType": {"type": "string", "description": "Reduction type. For example: Offset."}}, "title": "CapDentalBalance CapDentalBalanceItemActualAllocationReductionEntity", "description": "Reduction details applied to the actual allocation."}, "CapDentalBalance_CapDentalBalanceItemActualAllocationTaxEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemActualAllocationTaxEntity"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "balancedAppliedAmount": {"$ref": "#/definitions/Money"}, "taxSubType": {"type": "string", "description": "Tax subtype. For example: FSST."}, "taxType": {"type": "string", "description": "Tax type. For example: Federal."}}, "title": "CapDentalBalance CapDentalBalanceItemActualAllocationTaxEntity", "description": "Tax details applied to the actual allocation."}, "CapDentalBalance_CapDentalBalanceItemAdjustmentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemAdjustmentAllocationEntity"}, "allocationAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationAddition"}}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalBalance_CapDentalPaymentAllocationPayableItemEntity"}, "allocationReductions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationReduction"}}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationTax"}}}, "title": "CapDentalBalance CapDentalBalanceItemAdjustmentAllocationEntity", "description": "Adjustment allocation details."}, "CapDentalBalance_CapDentalBalanceItemAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemAllocationLossInfoEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}, "lossType": {"type": "string", "description": "Claim loss type."}}, "title": "CapDentalBalance CapDentalBalanceItemAllocationLossInfoEntity", "description": "Actual allocation loss details."}, "CapDentalBalance_CapDentalBalanceItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemEntity"}, "actualAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceItemActualAllocationEntity"}}, "actualNetAmount": {"$ref": "#/definitions/Money"}, "balancedNetAmount": {"$ref": "#/definitions/Money"}, "paymentDate": {"type": "string", "format": "date-time", "description": "Payment post date."}, "paymentNumber": {"type": "string", "description": "Payment number where payable item appeared."}, "scheduledNetAmount": {"$ref": "#/definitions/Money"}}, "title": "CapDentalBalance CapDentalBalanceItemEntity", "description": "Details of how the balance was calculated."}, "CapDentalBalance_CapDentalBalanceItemScheduledAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBalanceItemScheduledAllocationEntity"}, "allocationAdditions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationAddition"}}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalBalance_CapDentalPaymentAllocationPayableItemEntity"}, "allocationReductions": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationReduction"}}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "allocationTaxes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBaseBalanceItemAllocationTax"}}}, "title": "CapDentalBalance CapDentalBalanceItemScheduledAllocationEntity", "description": "Scheduled allocation details."}, "CapDentalBalance_CapDentalBaseBalanceItemAllocationAddition": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBaseBalanceItemAllocationAddition"}, "additionSubType": {"type": "string", "description": "Addition subtype."}, "additionType": {"type": "string", "description": "Addition type. For example: Rehabilitation."}, "appliedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapDentalBalance CapDentalBaseBalanceItemAllocationAddition", "description": "Addition details applied to the allocation."}, "CapDentalBalance_CapDentalBaseBalanceItemAllocationReduction": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBaseBalanceItemAllocationReduction"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "reductionSubType": {"type": "string", "description": "Tax subtype. For example: Deduction CHSP."}, "reductionType": {"type": "string", "description": "Reduction type. For example: Offset."}}, "title": "CapDentalBalance CapDentalBaseBalanceItemAllocationReduction", "description": "Reduction details applied to the allocation."}, "CapDentalBalance_CapDentalBaseBalanceItemAllocationTax": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBaseBalanceItemAllocationTax"}, "appliedAmount": {"$ref": "#/definitions/Money"}, "taxSubType": {"type": "string", "description": "Tax subtype. For example: FSST."}, "taxType": {"type": "string", "description": "Tax type. For example: Federal."}}, "title": "CapDentalBalance CapDentalBaseBalanceItemAllocationTax", "description": "Tax details applied to the allocation."}, "CapDentalBalance_CapDentalPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationPayableItemEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "orthoMonth": {"type": "integer", "format": "int64", "description": "Month number for which allocation is paid."}, "procedureID": {"type": "string", "description": "Related Settlement Result entry's procedure ID."}}, "title": "CapDentalBalance CapDentalPaymentAllocationPayableItemEntity", "description": "Dental allocation payable item details."}, "CapDentalLoss_CapDentalClaimCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"PolicyNumber": {"type": "string", "description": "Policy Number of COB."}, "_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimCoordinationOfBenefitsEntity"}, "address": {"type": "string", "description": "Policyholder Address of COB."}, "otherCoverageType": {"type": "string", "description": "Other Coverage Type of COB."}, "otherInsuranceCompany": {"type": "string", "description": "Other Insurance Company Name of COB."}, "otherPolicyType": {"type": "string", "description": "Other Policy Type of COB."}, "period": {"$ref": "#/definitions/CapDentalLoss_Term"}, "plan": {"type": "string", "description": "Plan or Group Number of COB."}, "policyholderDateOfBirth": {"type": "string", "format": "date", "description": "Policyholder Date of Birth of COB."}, "policyholderFirstName": {"type": "string", "description": "First Name of Policyholder of COB."}, "policyholderGender": {"type": "string", "description": "Policyholder Gender of COB."}, "policyholderLastName": {"type": "string", "description": "Last Name of Policyholder of COB."}, "policyholderRelationshipToPatient": {"type": "string", "description": "Patient Relationship to Policyholder of COB."}, "typeOfCob": {"type": "string", "description": "Type of Coordination of Benefits."}}, "title": "CapDentalLoss CapDentalClaimCoordinationOfBenefitsEntity"}, "CapDentalLoss_CapDentalClaimDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimDataEntity"}, "alternatePayee": {"$ref": "#/definitions/EntityLink"}, "cleanClaimDate": {"type": "string", "format": "date", "description": "Clean Claim Date."}, "cob": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapDentalClaimCoordinationOfBenefitsEntity"}}, "dateOfBirth": {"type": "string", "format": "date", "description": "Deprected this should come from customer."}, "digitalImageNumbers": {"type": "array", "items": {"type": "string", "description": "Digital Image Numbers."}}, "isUnknownOrIntProvider": {"type": "boolean", "description": "Flag to identify if the provider is unknown or international."}, "missingTooths": {"type": "array", "items": {"type": "string", "description": "Missing <PERSON><PERSON>."}}, "patient": {"$ref": "#/definitions/EntityLink"}, "payeeType": {"type": "string", "description": "Payee Type."}, "placeOfTreatment": {"type": "string", "description": "Place of Treatment."}, "policyholder": {"$ref": "#/definitions/EntityLink"}, "provider": {"$ref": "#/definitions/EntityLink"}, "providerDiscount": {"$ref": "#/definitions/CapDentalLoss_CapDentalProviderDiscountEntity"}, "providerFees": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapDentalProviderFeesEntity"}}, "receivedDate": {"type": "string", "format": "date", "description": "Received Date."}, "remark": {"type": "string", "description": "Comments and Remarks."}, "source": {"type": "string", "description": "Source EDI NONEDI."}, "transactionType": {"type": "string", "description": "Type of Claim."}}, "title": "CapDentalLoss CapDentalClaimDataEntity"}, "CapDentalLoss_CapDentalDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapDentalLoss"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "claimData": {"$ref": "#/definitions/CapDentalLoss_CapDentalClaimDataEntity"}, "lossDesc": {"type": "string", "description": "Description of loss itself"}, "submittedProcedures": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapDentalProcedureEntity"}}}, "title": "CapDentalLoss CapDentalDetailEntity", "description": "Defines what loss it is and what happened."}, "CapDentalLoss_CapDentalDiagnosisCodeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalDiagnosisCodeEntity"}, "code": {"type": "string", "description": "Diagnosis Code."}, "qualifier": {"type": "string", "description": "Diagnosis Code List Qualifier."}}, "title": "CapDentalLoss CapDentalDiagnosisCodeEntity"}, "CapDentalLoss_CapDentalLossEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalLoss"}, "_modelType": {"type": "string", "example": "CapLoss"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalLossEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "lossDetail": {"$ref": "#/definitions/CapDentalLoss_CapDentalDetailEntity"}, "lossNumber": {"type": "string", "description": "A unique loss number."}, "lossSubStatusCd": {"type": "string", "description": "Loss Sub Status."}, "policy": {"$ref": "#/definitions/CapDentalLoss_CapDentalLossPolicyEntity"}, "policyId": {"type": "string"}, "reasonCd": {"type": "string", "description": "Loss Sub Status Reason."}, "reasonDescription": {"type": "string", "description": "This attribute allows to enter the description of close/reopen reason."}, "state": {"type": "string", "description": "Current status of the Loss. Updated each time a new status is gained through state machine."}}, "title": "CapDentalLoss CapDentalLossEntity", "description": "Main object for the CAP Loss Domain."}, "CapDentalLoss_CapDentalLossPolicyEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalLossPolicyEntity"}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "currencyCd": {"type": "string", "description": "Currency Code"}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapInsuredInfo"}}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "term": {"$ref": "#/definitions/CapDentalLoss_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}}, "title": "CapDentalLoss CapDentalLossPolicyEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapDentalLoss_CapDentalOrthodonticEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalOrthodonticEntity"}, "appliancePlacedDate": {"type": "string", "format": "date", "description": "Date Appliance Placed."}, "downPayment": {"$ref": "#/definitions/Money"}, "frequency": {"type": "string", "description": "Deprecated"}, "months": {"type": "integer", "format": "int64", "description": "Deprecated"}, "orthoFrequencyCd": {"type": "string", "description": "Orthodontic Payment Frequency."}, "orthoMonthQuantity": {"type": "integer", "format": "int64", "description": "Number of Months of Treatment."}}, "title": "CapDentalLoss CapDentalOrthodonticEntity"}, "CapDentalLoss_CapDentalPreauthorizationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPreauthorizationEntity"}, "authorizationPeriod": {"$ref": "#/definitions/CapDentalLoss_Period"}, "authorizedBy": {"type": "string", "description": "Name of the person who authorized the procedure."}, "isProcedureAuthorized": {"type": "boolean", "description": "Is procedure authorized?"}}, "title": "CapDentalLoss CapDentalPreauthorizationEntity"}, "CapDentalLoss_CapDentalProcedureCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProcedureCoordinationOfBenefitsEntity"}, "allowed": {"$ref": "#/definitions/Money"}, "considered": {"$ref": "#/definitions/Money"}, "coverageType": {"type": "string", "description": "Type of Coverage."}, "innOnn": {"type": "string", "description": "Paid INN/Paid OON."}, "paid": {"$ref": "#/definitions/Money"}, "primaryCoverageStatus": {"type": "string", "description": "Primary Coverage Status."}}, "title": "CapDentalLoss CapDentalProcedureCoordinationOfBenefitsEntity"}, "CapDentalLoss_CapDentalProcedureEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProcedureEntity"}, "cob": {"$ref": "#/definitions/CapDentalLoss_CapDentalProcedureCoordinationOfBenefitsEntity"}, "dateOfService": {"type": "string", "format": "date", "description": "Date of Service."}, "description": {"type": "string", "description": "Procedure Description."}, "diagnosisCodes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapDentalDiagnosisCodeEntity"}}, "ortho": {"$ref": "#/definitions/CapDentalLoss_CapDentalOrthodonticEntity"}, "preauthorization": {"$ref": "#/definitions/CapDentalLoss_CapDentalPreauthorizationEntity"}, "preauthorizationNumber": {"type": "string", "description": "Preauthorization Number."}, "predetInd": {"type": "boolean", "description": "Is this procedure part of predetermination or real service?"}, "priorProsthesisPlacementDate": {"type": "string", "format": "date", "description": "Date of Prior Prosthesis Placement."}, "procedureCode": {"type": "string", "description": "CDT Code."}, "procedureType": {"type": "string", "description": "Procedure Type."}, "quantity": {"type": "integer", "format": "int64", "description": "Number of services."}, "submittedFee": {"$ref": "#/definitions/Money"}, "surfaces": {"type": "array", "items": {"type": "string", "description": "Tooth Surface."}}, "toothArea": {"type": "string", "description": "Area of Oral Cavity."}, "toothCodes": {"type": "array", "items": {"type": "string", "description": "Tooth Numbers/Letters."}}, "toothSystem": {"type": "string", "description": "Tooth System."}, "treatmentReason": {"$ref": "#/definitions/CapDentalLoss_CapDentalTreatmentReasonEntity"}}, "title": "CapDentalLoss CapDentalProcedureEntity"}, "CapDentalLoss_CapDentalProviderDiscountEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProviderDiscountEntity"}, "discountAmount": {"$ref": "#/definitions/Money"}, "discountName": {"type": "string", "description": "Concession Name."}, "discountPercentage": {"type": "number", "description": "Concession %."}, "discountType": {"type": "string", "description": "Concession Type."}}, "title": "CapDentalLoss CapDentalProviderDiscountEntity"}, "CapDentalLoss_CapDentalProviderFeesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProviderFeesEntity"}, "fee": {"$ref": "#/definitions/Money"}, "type": {"type": "string", "description": "Provicer Fee Type."}}, "title": "CapDentalLoss CapDentalProviderFeesEntity"}, "CapDentalLoss_CapDentalTreatmentReasonEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalTreatmentReasonEntity"}, "autoAccidentState": {"type": "string", "description": "Auto Accident State."}, "dateOfAccident": {"type": "string", "format": "date", "description": "Date of Accident."}, "treatmentResultingFrom": {"type": "string", "description": "Treatment Resulting From."}}, "title": "CapDentalLoss CapDentalTreatmentReasonEntity"}, "CapDentalLoss_CapInsuredInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapInsuredInfo"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}}, "title": "CapDentalLoss CapInsuredInfo", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapDentalLoss_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalLoss Period"}, "CapDentalLoss_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalLoss Term"}, "CapDentalPatientHistory_CapDentalAccessTrackInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalAccessTrackInfoEntity"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapDentalPatientHistory CapDentalAccessTrackInfoEntity"}, "CapDentalPatientHistory_CapDentalClaimDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimDataEntity"}, "claim": {"$ref": "#/definitions/EntityLink"}, "claimNumber": {"type": "string", "description": "Claim Number."}, "digitalImageNumbers": {"type": "array", "items": {"type": "string", "description": "Numbers of related attached documents."}}, "patient": {"$ref": "#/definitions/EntityLink"}, "patientNumber": {"type": "string", "description": "Patient Number."}, "planCategory": {"type": "string", "description": "PPO/DHMO product."}, "policy": {"$ref": "#/definitions/EntityLink"}, "policyNumber": {"type": "string", "description": "Policy Number."}, "remark": {"type": "string", "description": "Remark added to Claim by user."}}, "title": "CapDentalPatientHistory CapDentalClaimDataEntity"}, "CapDentalPatientHistory_CapDentalPatientHistoryDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPatientHistoryDataEntity"}, "accessTrackInfo": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalAccessTrackInfoEntity"}, "claimData": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalClaimDataEntity"}, "providerData": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalProviderDataEntity"}, "serviceData": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalServiceDataEntity"}}, "title": "CapDentalPatientHistory CapDentalPatientHistoryDataEntity"}, "CapDentalPatientHistory_CapDentalPatientHistoryEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPatientHistory"}, "_modelType": {"type": "string", "example": "CapDentalPatientHistoryModelType"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPatientHistoryEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "patientHistoryData": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalPatientHistoryDataEntity"}, "state": {"type": "string"}}, "title": "CapDentalPatientHistory CapDentalPatientHistoryEntity"}, "CapDentalPatientHistory_CapDentalProviderDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProviderDataEntity"}, "dentistID": {"type": "string", "description": "Provider ID."}, "inOutNetwork": {"type": "string", "description": "Type of provider fee schedule that was applied during adjudication: INN or ONN."}, "provider": {"$ref": "#/definitions/EntityLink"}, "providerBusinessName": {"type": "string", "description": "Provider business name."}, "providerTIN": {"type": "string", "description": "Provider tax identification number."}}, "title": "CapDentalPatientHistory CapDentalProviderDataEntity"}, "CapDentalPatientHistory_CapDentalServiceDataEntity": {"required": ["_type"], "properties": {"DOSDate": {"type": "string", "format": "date-time", "description": "Date of Service."}, "_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalServiceDataEntity"}, "authorizationPeriod": {"$ref": "#/definitions/CapDentalPatientHistory_Period"}, "benefitAmount": {"$ref": "#/definitions/Money"}, "cdtCoveredCd": {"type": "string", "description": "Procedure code received after adjudication."}, "cdtSubmittedCd": {"type": "string", "description": "Procedure code stored during intake."}, "coinsuranceAmount": {"$ref": "#/definitions/Money"}, "consideredAmount": {"$ref": "#/definitions/Money"}, "copayAmount": {"$ref": "#/definitions/Money"}, "coveredAmount": {"$ref": "#/definitions/Money"}, "decision": {"type": "string", "description": "Calculated resolution if procedure is covered."}, "deductibleAmount": {"$ref": "#/definitions/Money"}, "isPredet": {"type": "boolean", "description": "Indicates if procedure is part of predetermination or actual service."}, "isProcedureAuthorized": {"type": "boolean", "description": "Indicates if predet procedure was preauthorized."}, "paymentDate": {"type": "string", "format": "date-time", "description": "Date payment was made, if applicable."}, "procedureType": {"type": "string", "description": "Covered Procedure area calculated after adjudication"}, "quantity": {"type": "integer", "format": "int64", "description": "Submitted quantity of procedures."}, "remarkCodes": {"type": "array", "items": {"type": "string", "description": "Additional multiple codes that explains how decision achieved."}}, "service": {"$ref": "#/definitions/EntityLink"}, "submittedAmount": {"$ref": "#/definitions/Money"}, "surfaces": {"type": "array", "items": {"type": "string", "description": "Submitted Tooth Surfaces."}}, "toothArea": {"type": "string", "description": "Submitted Area of Oral Cavity."}, "toothCodes": {"type": "array", "items": {"type": "string", "description": "Submitted Tooth Numbers/Letters."}}}, "title": "CapDentalPatientHistory CapDentalServiceDataEntity"}, "CapDentalPatientHistory_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalPatientHistory Period"}, "CapDentalPaymentDefinition_CapDentalPaymentAllocationAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationAccumulatorDetailsEntity"}, "accumulatorAmount": {"$ref": "#/definitions/Money"}, "accumulatorType": {"type": "string", "description": "Accumulator type."}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category accumulator applies to."}, "networkType": {"type": "string", "description": "Network type for accumulator."}, "renewalType": {"type": "string", "description": "Renewal type for accumulator."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentAllocationAccumulatorDetailsEntity", "description": "Allocation acumulator details."}, "CapDentalPaymentDefinition_CapDentalPaymentAllocationDentalDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationDentalDetailsEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentAllocationAccumulatorDetailsEntity"}}, "patient": {"$ref": "#/definitions/EntityLink"}, "transactionTypeCd": {"type": "string", "description": "Dental claim transaction type."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentAllocationDentalDetailsEntity", "description": "Dental allocation details."}, "CapDentalPaymentDefinition_CapDentalPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationEntity"}, "allocationDentalDetails": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentAllocationDentalDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "reserveType": {"type": "string", "description": "Allocation reserve type."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentAllocationEntity", "description": "An object which extends payment allocations details."}, "CapDentalPaymentDefinition_CapDentalPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationLossInfoEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentDefinition CapDentalPaymentAllocationLossInfoEntity", "description": "Allocation claim details."}, "CapDentalPaymentDefinition_CapDentalPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationPayableItemEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "orthoMonth": {"type": "integer", "format": "int64", "description": "Month number for which allocation is paid."}, "procedureID": {"type": "string", "description": "Related Settlement Result entry's procedure ID."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentAllocationPayableItemEntity", "description": "Dental allocation payable item details."}, "CapDentalPaymentDefinition_CapDentalPaymentDetailsEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentDefinition"}, "_modelType": {"type": "string", "example": "CapPayment"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentDetailsEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "payeeDetails": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentPayeeDetailsEntity"}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentDetailsEntity", "description": "An object which extends payment details."}, "CapDentalPaymentDefinition_CapDentalPaymentEntity": {"required": ["_modelName", "_type", "_variation"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentDefinition"}, "_modelType": {"type": "string", "example": "CapPayment"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentEntity"}, "_variation": {"type": "string", "example": "overpaymentWaive"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentDefinition_MessageType"}}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentDetails": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "Unique payment number."}, "paymentSchedule": {"$ref": "#/definitions/EntityLink"}, "state": {"type": "string", "description": "Payment state in the lifecycle."}}, "title": "CapDentalPaymentDefinition CapDentalPaymentEntity", "description": "Payment transaction information. The Root Entity of CAP Payment Domain."}, "CapDentalPaymentDefinition_CapDentalPaymentPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentDefinition CapDentalPaymentPayeeDetailsEntity", "description": "An object which extends payment payee details."}, "CapDentalPaymentDefinition_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalPaymentDefinition MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapDentalPaymentIndex_CapDentalPaymentIdxEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentIndex"}, "_modelType": {"type": "string", "example": "CapDentalPaymentIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentIdxEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "originSource": {"type": "string"}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentId": {"type": "string"}, "state": {"type": "string"}}, "title": "CapDentalPaymentIndex CapDentalPaymentIdxEntity"}, "CapDentalPaymentScheduleIndex_CapDentalPaymentScheduleIdxEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentScheduleIndex"}, "_modelType": {"type": "string", "example": "CapDentalPaymentScheduleIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleIdxEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "originSource": {"type": "string"}, "paymentSchedule": {"type": "string"}, "state": {"type": "string"}}, "title": "CapDentalPaymentScheduleIndex CapDentalPaymentScheduleIdxEntity"}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationAccumulatorDetailsEntity"}, "accumulatorAmount": {"$ref": "#/definitions/Money"}, "accumulatorType": {"type": "string", "description": "Accumulator type."}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category accumulator applies to."}, "networkType": {"type": "string", "description": "Network type for accumulator."}, "renewalType": {"type": "string", "description": "Renewal type for accumulator."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationAccumulatorDetailsEntity", "description": "Allocation acumulator details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationDentalDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationDentalDetailsEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationAccumulatorDetailsEntity"}}, "patient": {"$ref": "#/definitions/EntityLink"}, "transactionTypeCd": {"type": "string", "description": "Dental claim transaction type."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationDentalDetailsEntity", "description": "Dental allocation details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationEntity"}, "allocationDentalDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationDentalDetailsEntity"}, "allocationGrossAmount": {"$ref": "#/definitions/Money"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business code."}, "allocationLossInfo": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationLossInfoEntity"}, "allocationNetAmount": {"$ref": "#/definitions/Money"}, "allocationPayableItem": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationPayableItemEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}, "reserveType": {"type": "string", "description": "Allocation reserve type."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationEntity", "description": "An object which extends payment allocations details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationLossInfoEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationLossInfoEntity", "description": "Allocation claim details."}, "CapDentalPaymentSchedule_CapDentalPaymentAllocationPayableItemEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationPayableItemEntity"}, "claimSource": {"$ref": "#/definitions/EntityLink"}, "orthoMonth": {"type": "integer", "format": "int64", "description": "Month number for which allocation is paid."}, "procedureID": {"type": "string", "description": "Related Settlement Result entry's procedure ID."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentAllocationPayableItemEntity", "description": "Dental allocation payable item details."}, "CapDentalPaymentSchedule_CapDentalPaymentDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentDetailsEntity"}, "payeeDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentPayeeDetailsEntity"}, "paymentAllocations": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentAllocationEntity"}}, "paymentDate": {"type": "string", "format": "date-time", "description": "The payment post date."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentDetailsEntity", "description": "An object which extends payment details."}, "CapDentalPaymentSchedule_CapDentalPaymentPayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentPayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentSchedule CapDentalPaymentPayeeDetailsEntity", "description": "An object which extends payment payee details."}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentSchedule"}, "_modelType": {"type": "string", "example": "CapPaymentSchedule"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the schedule was created."}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentTemplate": {"$ref": "#/definitions/EntityLink"}, "payments": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalScheduledPaymentEntity"}}, "scheduleMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleMessageEntity"}}, "scheduleNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment schedule."}, "state": {"type": "string", "description": "State of a payment schedule lifecycle."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentScheduleEntity", "description": "The Root Entity of CAP Payment Schedule Domain."}, "CapDentalPaymentSchedule_CapDentalPaymentScheduleMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentScheduleMessageEntity"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalPaymentSchedule CapDentalPaymentScheduleMessageEntity", "description": "Stores Payment Schedule messages"}, "CapDentalPaymentSchedule_CapDentalScheduledPaymentEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalScheduledPaymentEntity"}, "creationDate": {"type": "string", "format": "date-time", "description": "A date when the payment was created."}, "direction": {"type": "string", "description": "Defines if payment is incoming or outgoing."}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_MessageType"}}, "paymentDetails": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentDetailsEntity"}, "paymentNetAmount": {"$ref": "#/definitions/Money"}, "paymentNumber": {"type": "string", "description": "A unique ID, that is assigned to a new payment."}}, "title": "CapDentalPaymentSchedule CapDentalScheduledPaymentEntity", "description": "Defines payment transaction information."}, "CapDentalPaymentSchedule_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalPaymentSchedule MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapDentalPaymentTemplateIndex_CapDentalPaymentTemplateIdxEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentTemplateIndex"}, "_modelType": {"type": "string", "example": "CapDentalPaymentTemplateIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentTemplateIdxEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "originSource": {"type": "string"}, "paymentTemplate": {"type": "string"}, "state": {"type": "string"}}, "title": "CapDentalPaymentTemplateIndex CapDentalPaymentTemplateIdxEntity"}, "CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity"}, "accumulatorAmount": {"$ref": "#/definitions/Money"}, "accumulatorType": {"type": "string", "description": "Accumulator type."}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category accumulator applies to."}, "networkType": {"type": "string", "description": "Network type for accumulator."}, "renewalType": {"type": "string", "description": "Renewal type for accumulator."}}, "title": "CapDentalPaymentTemplate CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity", "description": "Object for accumulator details for allocation."}, "CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateDentalDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationTemplateDentalDetailsEntity"}, "accumulatorDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateAccumulatorDetailsEntity"}}, "dateOfService": {"type": "string", "format": "date", "description": "Date of service."}, "grossBenefitAmount": {"$ref": "#/definitions/Money"}, "orthoFrequencyCd": {"type": "string", "description": "Payment frequency for Ortho payments."}, "orthoMonthQuantity": {"type": "integer", "format": "int64", "description": "Number of Months of Treatment."}, "patient": {"$ref": "#/definitions/EntityLink"}, "paymentAmount": {"$ref": "#/definitions/Money"}, "procedureID": {"type": "string", "description": "Related Settlement Result entry's procedure ID."}, "receivedDate": {"type": "string", "format": "date", "description": "The date the claim is received."}, "transactionTypeCd": {"type": "string", "description": "Dental claim transaction type."}}, "title": "CapDentalPaymentTemplate CapDentalPaymentAllocationTemplateDentalDetailsEntity", "description": "Object for dental details for allocation."}, "CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationTemplateEntity"}, "allocationDentalDetails": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateDentalDetailsEntity"}, "allocationLobCd": {"type": "string", "description": "Allocation Line Of Business."}, "allocationLossInfo": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateLossInfoEntity"}, "allocationPayeeDetails": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplatePayeeDetailsEntity"}, "allocationSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentTemplate CapDentalPaymentAllocationTemplateEntity", "description": "Object for payment template allocation details."}, "CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateLossInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationTemplateLossInfoEntity"}, "lossSource": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalPaymentTemplate CapDentalPaymentAllocationTemplateLossInfoEntity", "description": "Object for template allocation loss details."}, "CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplatePayeeDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPaymentAllocationTemplatePayeeDetailsEntity"}, "payee": {"$ref": "#/definitions/EntityLink"}, "payeeTypeCd": {"type": "string", "description": "Allocation payee type."}}, "title": "CapDentalPaymentTemplate CapDentalPaymentAllocationTemplatePayeeDetailsEntity", "description": "Object for template allocation payee details."}, "CapDentalPaymentTemplate_CapDentalPaymentDetailsTemplateEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentTemplate"}, "_modelType": {"type": "string", "example": "CapPaymentTemplate"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentDetailsTemplateEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "paymentAllocationTemplates": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentAllocationTemplateEntity"}}}, "title": "CapDentalPaymentTemplate CapDentalPaymentDetailsTemplateEntity", "description": "Object for payment template details."}, "CapDentalPaymentTemplate_CapDentalPaymentTemplateEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalPaymentTemplate"}, "_modelType": {"type": "string", "example": "CapPaymentTemplate"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalPaymentTemplateEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "creationDate": {"type": "string", "format": "date-time", "description": "The date when the template was created."}, "originSource": {"$ref": "#/definitions/EntityLink"}, "paymentDetailsTemplate": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentDetailsTemplateEntity"}, "state": {"type": "string", "description": "Payment Template state in the lifecycle."}}, "title": "CapDentalPaymentTemplate CapDentalPaymentTemplateEntity", "description": "Main object for the CAP Payment Template Domain."}, "CapDentalProcedure_CapDentalProcedureProjection": {"required": ["_modelName", "_type"], "properties": {"DOSDate": {"type": "string", "format": "date-time"}, "_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalProcedure"}, "_modelType": {"type": "string", "example": "CapProcedure"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalProcedureProjection"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "claimNumber": {"type": "string"}, "patientNumber": {"type": "string"}, "planCategory": {"type": "string"}, "policyNumber": {"type": "string"}, "providerTIN": {"type": "string"}, "state": {"type": "string"}, "toothCodes": {"type": "array", "items": {"type": "string"}}, "uri": {"$ref": "#/definitions/EntityLink"}}, "title": "CapDentalProcedure CapDentalProcedureProjection"}, "CapDentalSettlementIndex_CapDentalSettlementIdx": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalSettlementIndex"}, "_modelType": {"type": "string", "example": "CapDentalSettlementIndex"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalSettlementIdx"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "createdOn": {"type": "string", "format": "date-time"}, "lossId": {"type": "string"}, "settlementId": {"type": "string"}}, "title": "CapDentalSettlementIndex CapDentalSettlementIdx"}, "CapDentalSettlement_AccessTrackInfo": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "AccessTrackInfo"}, "createdBy": {"type": "string"}, "createdOn": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string"}, "updatedOn": {"type": "string", "format": "date-time"}}, "title": "CapDentalSettlement AccessTrackInfo"}, "CapDentalSettlement_CapDeductibleDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDeductibleDetailEntity"}, "individualBasicINNAnnualDeductible": {"$ref": "#/definitions/Money"}, "individualMajorINNAnnualDeductible": {"$ref": "#/definitions/Money"}, "individualPreventiveINNAnnualDeductible": {"$ref": "#/definitions/Money"}}, "title": "CapDentalSettlement CapDeductibleDetailEntity"}, "CapDentalSettlement_CapDentalAccumulatorEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalAccumulatorEntity"}, "accumulatorType": {"type": "string", "description": "Type for the Maximum."}, "appliesToProcedureCategory": {"type": "string", "description": "Defines to which procedure category maximum applies to."}, "networkType": {"type": "string", "description": "Network type for maximum."}, "remainingAmount": {"$ref": "#/definitions/Money"}, "renewalType": {"type": "string", "description": "Renewal type for maximum."}, "reservedAmount": {"$ref": "#/definitions/Money"}}, "title": "CapDentalSettlement CapDentalAccumulatorEntity"}, "CapDentalSettlement_CapDentalBypassClaimLogicEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBypassClaimLogicEntity"}, "bypassAllRules": {"type": "boolean", "description": "Bypass All Rules and Dental Review Logic."}, "bypassClinicalReview": {"type": "boolean", "description": "Bypass Clinical/Consultant Review."}, "bypassCobLogic": {"type": "boolean", "description": "Bypass COB Logic."}, "bypassDuplicateServiceLogic": {"type": "boolean", "description": "Bypass Duplicate Service Logic."}, "bypassGracePeriodLogic": {"type": "boolean", "description": "Bypass Grace Period Logic."}, "bypassInterestLogic": {"type": "boolean", "description": "Bypass Interest Logic."}, "bypassMissingToothLogic": {"type": "boolean", "description": "Bypass Missing Tooth Exclusion Logic."}, "bypassOverpaymentLogic": {"type": "boolean", "description": "Bypass Overpayment Recoupment Logic."}, "bypassToothExtractionRules": {"type": "boolean", "description": "Bypass Tooth Extraction Rules."}}, "title": "CapDentalSettlement CapDentalBypassClaimLogicEntity"}, "CapDentalSettlement_CapDentalBypassServiceLogicEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalBypassServiceLogicEntity"}, "bypassAllRules": {"type": "boolean", "description": "Bypass All Rules and Dental Review Logic."}, "bypassClinicalReview": {"type": "boolean", "description": "Bypass Clinical/Consultant Review."}, "bypassCobLogic": {"type": "boolean", "description": "Bypass COB Logic."}, "bypassDuplicateServiceLogic": {"type": "boolean", "description": "Bypass Duplicate Service Logic."}, "bypassGracePeriodLogic": {"type": "boolean", "description": "Bypass Grace Period Logic."}, "bypassInterestLogic": {"type": "boolean", "description": "Bypass Interest Logic."}, "bypassMissingToothLogic": {"type": "boolean", "description": "Bypass Missing Tooth Exclusion Logic."}, "bypassOverpaymentLogic": {"type": "boolean", "description": "Bypass Overpayment Recoupment Logic."}, "bypassToothExtractionRules": {"type": "boolean", "description": "Bypass Tooth Extraction Rules."}}, "title": "CapDentalSettlement CapDentalBypassServiceLogicEntity"}, "CapDentalSettlement_CapDentalCalculationResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalCalculationResultEntity"}, "allowedFee": {"$ref": "#/definitions/Money"}, "charge": {"$ref": "#/definitions/Money"}, "coinsuranceAmt": {"$ref": "#/definitions/Money"}, "coinsurancePercentage": {"type": "number", "description": "Coinsurance percentage if exists."}, "consideredFee": {"$ref": "#/definitions/Money"}, "contributionToMOOP": {"$ref": "#/definitions/Money"}, "copay": {"$ref": "#/definitions/Money"}, "coveredCode": {"type": "string", "description": "Procedure code received after adjudication"}, "coveredFee": {"$ref": "#/definitions/Money"}, "netBenefitAmount": {"$ref": "#/definitions/Money"}, "patientResponsibility": {"$ref": "#/definitions/Money"}, "payableDeductible": {"$ref": "#/definitions/Money"}, "procedureID": {"type": "string", "description": "Calculation Result Procedure ID."}, "procedureType": {"type": "string", "description": "Covered Procedure area calculated after adjudication.."}, "submittedCode": {"type": "string", "description": "Calculation Result Submitted Code."}}, "title": "CapDentalSettlement CapDentalCalculationResultEntity"}, "CapDentalSettlement_CapDentalCalculationStatusEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalCalculationStatusEntity"}, "code": {"type": "string", "description": "Calculation code."}, "coveredCode": {"type": "string", "description": "Calculation Status Covered code."}, "fee": {"$ref": "#/definitions/Money"}, "flag": {"type": "string", "description": "Status flag - Denied, Allowed, Review Required..."}, "percentage": {"type": "number", "description": "Calculation percentage."}, "preauthorizationNumber": {"type": "string", "description": "Claim number of the history procedure if the service is predet-preauth"}, "predetInd": {"type": "boolean", "description": "Predentermined indicator."}, "procedureID": {"type": "string", "description": "Calculation Status Procedure ID."}, "questions": {"type": "array", "items": {"type": "string", "description": "Question for the procedure."}}, "reasonCode": {"type": "string", "description": "Calculation Status Reason Code."}, "remarkMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalRemarkMessageEntity"}}, "statusReason": {"type": "string", "description": "Calculation Status Reason."}, "submittedCode": {"type": "string", "description": "Calculation Status Submitted procedure code."}}, "title": "CapDentalSettlement CapDentalCalculationStatusEntity"}, "CapDentalSettlement_CapDentalClaimCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"PolicyNumber": {"type": "string", "description": "Policy Number of COB."}, "_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimCoordinationOfBenefitsEntity"}, "address": {"type": "string", "description": "Policyholder Address of COB."}, "otherCoverageType": {"type": "string", "description": "Other Coverage Type of COB."}, "otherInsuranceCompany": {"type": "string", "description": "Other Insurance Company Name of COB."}, "otherPolicyType": {"type": "string", "description": "Other Policy Type of COB."}, "period": {"$ref": "#/definitions/CapDentalSettlement_Term"}, "plan": {"type": "string", "description": "Plan or Group Number of COB."}, "policyholderDateOfBirth": {"type": "string", "format": "date", "description": "Policyholder Date of Birth of COB."}, "policyholderFirstName": {"type": "string", "description": "First Name of Policyholder of COB."}, "policyholderGender": {"type": "string", "description": "Policyholder Gender of COB."}, "policyholderLastName": {"type": "string", "description": "Last Name of Policyholder of COB."}, "policyholderRelationshipToPatient": {"type": "string", "description": "Patient Relationship to Policyholder of COB."}, "typeOfCob": {"type": "string", "description": "Type of Coordination of Benefits."}}, "title": "CapDentalSettlement CapDentalClaimCoordinationOfBenefitsEntity"}, "CapDentalSettlement_CapDentalClaimDataEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimDataEntity"}, "alternatePayee": {"$ref": "#/definitions/EntityLink"}, "cleanClaimDate": {"type": "string", "format": "date", "description": "Clean Claim Date."}, "cob": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalClaimCoordinationOfBenefitsEntity"}}, "dateOfBirth": {"type": "string", "format": "date", "description": "Deprected this should come from customer."}, "digitalImageNumbers": {"type": "array", "items": {"type": "string", "description": "Digital Image Numbers."}}, "isUnknownOrIntProvider": {"type": "boolean", "description": "Flag to identify if the provider is unknown or international."}, "missingTooths": {"type": "array", "items": {"type": "string", "description": "Missing <PERSON><PERSON>."}}, "patient": {"$ref": "#/definitions/EntityLink"}, "payeeType": {"type": "string", "description": "Payee Type."}, "placeOfTreatment": {"type": "string", "description": "Place of Treatment."}, "policyholder": {"$ref": "#/definitions/EntityLink"}, "provider": {"$ref": "#/definitions/EntityLink"}, "providerDiscount": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProviderDiscountEntity"}, "providerFees": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProviderFeesEntity"}}, "receivedDate": {"type": "string", "format": "date", "description": "Received Date."}, "remark": {"type": "string", "description": "Comments and Remarks."}, "source": {"type": "string", "description": "Source EDI NONEDI."}, "transactionType": {"type": "string", "description": "Type of Claim."}}, "title": "CapDentalSettlement CapDentalClaimDataEntity"}, "CapDentalSettlement_CapDentalClaimInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimInfoEntity"}, "claimData": {"$ref": "#/definitions/CapDentalSettlement_CapDentalClaimDataEntity"}, "patient": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPatientEntity"}, "receivedDate": {"type": "string", "format": "date", "description": "<PERSON><PERSON><PERSON> received date."}, "source": {"type": "string", "description": "Source type EDI or NONEDI."}, "submittedProcedures": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureEntity"}}}, "title": "CapDentalSettlement CapDentalClaimInfoEntity", "description": "Business entity that houses the information from loss to use in settlement."}, "CapDentalSettlement_CapDentalClaimOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalClaimOverrideEntity"}, "bypassClaimLogic": {"$ref": "#/definitions/CapDentalSettlement_CapDentalBypassClaimLogicEntity"}, "isAllowed": {"type": "boolean", "description": "Allow Service."}, "isDenied": {"type": "boolean", "description": "Deny Service."}, "overrideClaimValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOverrideClaimValueEntity"}, "suppressMemberEob": {"type": "boolean", "description": "Suppress EOB for Member."}, "suppressProviderEob": {"type": "boolean", "description": "Suppress EOB for Provider."}, "waiveClaimValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalWaiveClaimValueEntity"}}, "title": "CapDentalSettlement CapDentalClaimOverrideEntity"}, "CapDentalSettlement_CapDentalConsultantReviewEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalConsultantReviewEntity"}, "alternateCDTCode": {"type": "string", "description": "Consultant review alternate CDT code."}, "consultantReply": {"type": "string", "description": "Consultant review reply."}, "consultantReplyLetter": {"type": "string", "description": "Consultant review reply letter."}, "surface": {"type": "string", "description": "Surface which had consultant review."}}, "title": "CapDentalSettlement CapDentalConsultantReviewEntity"}, "CapDentalSettlement_CapDentalDHMOFrequencyEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalDHMOFrequencyEntity"}, "frequency": {"type": "string", "description": "DHMO number of times procedure allowed."}, "frequencyPeriod": {"type": "integer", "format": "int64", "description": "DHMO frequency period."}, "frequencyPeriodType": {"type": "string", "description": "DHMO frequency period type."}, "frequencyRange": {"type": "string", "description": "DHMO Range of the frequency."}, "isAppliedTowardsMOOP": {"type": "string", "description": "DHMO applied toward the Maximum Out of Pocket value."}, "isCovered": {"type": "boolean", "description": "If the DHMO covered or not."}}, "title": "CapDentalSettlement CapDentalDHMOFrequencyEntity"}, "CapDentalSettlement_CapDentalDecisionResultEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalDecisionResultEntity"}, "entries": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalResultEntryEntity"}}, "messages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_MessageType"}}, "payeeRef": {"type": "string", "description": "Provider or insured or alternate payee reference for payment generation."}, "paymentAmount": {"$ref": "#/definitions/Money"}, "preprocessMessages": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPreprocessMessageEntity"}}, "proposal": {"type": "string", "description": "Proposal code which identifies next steps for system and/or user: Additional Reviewer or generate payment."}, "reserve": {"type": "number"}, "reservedAccumulators": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalAccumulatorEntity"}}}, "title": "CapDentalSettlement CapDentalDecisionResultEntity", "description": "Business entity defines settlement result."}, "CapDentalSettlement_CapDentalDentistEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalDentistEntity"}, "dentistID": {"type": "string", "description": "Dentist National Provider ID."}, "dentistSpecialties": {"type": "array", "items": {"type": "string", "description": "Dentist specialities."}}, "feeScheduleType": {"type": "string", "description": "Dentist fee schedule type."}, "inOutNetwork": {"type": "string", "description": "If the dentist In or Out of Network."}, "pcdID": {"type": "string", "description": "Primary care dentist in DHMO."}, "practiceTerm": {"$ref": "#/definitions/CapDentalSettlement_Term"}, "practiceType": {"type": "string", "description": "Dentist practice type."}, "providerTIN": {"type": "string", "description": "Provider tax identification number."}}, "title": "CapDentalSettlement CapDentalDentistEntity"}, "CapDentalSettlement_CapDentalDiagnosisCodeEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalDiagnosisCodeEntity"}, "code": {"type": "string", "description": "Diagnosis Code."}, "qualifier": {"type": "string", "description": "Diagnosis Code List Qualifier."}}, "title": "CapDentalSettlement CapDentalDiagnosisCodeEntity"}, "CapDentalSettlement_CapDentalFeeRateEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalFeeRateEntity"}, "feeAmount": {"$ref": "#/definitions/Money"}, "feeCode": {"type": "string", "description": "Code that fee applied to."}}, "title": "CapDentalSettlement CapDentalFeeRateEntity"}, "CapDentalSettlement_CapDentalMaximumEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalMaximumEntity"}, "implantsINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualBasicINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualMajorINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualPreventiveINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "orthoINNAnnualMaximum": {"$ref": "#/definitions/Money"}}, "title": "CapDentalSettlement CapDentalMaximumEntity"}, "CapDentalSettlement_CapDentalOrthodonticEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalOrthodonticEntity"}, "appliancePlacedDate": {"type": "string", "format": "date", "description": "Date Appliance Placed."}, "downPayment": {"$ref": "#/definitions/Money"}, "frequency": {"type": "string", "description": "Deprecated"}, "months": {"type": "integer", "format": "int64", "description": "Deprecated"}, "orthoFrequencyCd": {"type": "string", "description": "Orthodontic Payment Frequency."}, "orthoMonthQuantity": {"type": "integer", "format": "int64", "description": "Number of Months of Treatment."}}, "title": "CapDentalSettlement CapDentalOrthodonticEntity"}, "CapDentalSettlement_CapDentalOverrideClaimValueEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalOverrideClaimValueEntity"}, "overrideBasicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Basic Services Waiting Period."}, "overrideEligibilityPeriod": {"$ref": "#/definitions/CapDentalSettlement_Period"}, "overrideFeeSchedule": {"type": "string", "description": "Override Fee Schedule."}, "overrideGracePeriod": {"type": "integer", "format": "int64", "description": "Override Grace Period."}, "overrideLateEntrantWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Late Entrant Waiting Period."}, "overrideMajorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Major Services Waiting Period."}, "overrideOrthoWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Ortho Services Waiting Period."}, "overridePaymentInterestAmount": {"$ref": "#/definitions/Money"}, "overridePaymentInterestDays": {"type": "integer", "format": "int64", "description": "Override Payment Interest Number of Days."}, "overridePaymentInterestState": {"type": "string", "description": "Override Payment Interest State."}, "overridePreventiveWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Preventive Services Waiting Period."}, "overrideStudentDependentStatus": {"type": "string", "description": "Override Student Dependent Status."}}, "title": "CapDentalSettlement CapDentalOverrideClaimValueEntity"}, "CapDentalSettlement_CapDentalOverrideServiceValueEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalOverrideServiceValueEntity"}, "overrideAllowedAmount": {"$ref": "#/definitions/Money"}, "overrideCobApplied": {"$ref": "#/definitions/Money"}, "overrideCoinsurancePct": {"type": "number", "description": "Override Coinsurance %"}, "overrideConsideredAmount": {"$ref": "#/definitions/Money"}, "overrideCopayAmount": {"$ref": "#/definitions/Money"}, "overrideCoveredAmount": {"$ref": "#/definitions/Money"}, "overrideCoveredCdtCode": {"type": "string", "description": "Override Covered CDT Code."}, "overrideDeductible": {"$ref": "#/definitions/Money"}, "overrideEligibilityPeriod": {"$ref": "#/definitions/CapDentalSettlement_Period"}, "overrideEssentialHealthBenefit": {"type": "string", "description": "Override Essential Health Benefit."}, "overrideFeeSchedule": {"type": "string", "description": "Override Fee Schedule."}, "overrideGracePeriod": {"type": "integer", "format": "int64", "description": "Override Grace Period."}, "overrideLateEntrantWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Late Entrant Waiting Period."}, "overrideMaximumAmount": {"$ref": "#/definitions/Money"}, "overridePatientResponsibility": {"$ref": "#/definitions/Money"}, "overridePaymentInterestAmount": {"$ref": "#/definitions/Money"}, "overridePaymentInterestDays": {"type": "integer", "format": "int64", "description": "Override Payment Interest Number of Days."}, "overridePaymentInterestState": {"type": "string", "description": "Override Payment Interest State."}, "overrideReplacementLimit": {"type": "integer", "format": "int64", "description": "Override Replacement Limit."}, "overrideServiceCategory": {"type": "string", "description": "Override Category of Service."}, "overrideServiceFrequencyLimit": {"type": "integer", "format": "int64", "description": "Override Service Frequency Limit."}, "overrideServiceWaitingPeriod": {"type": "integer", "format": "int64", "description": "Override Service Category Waiting Period."}, "overrideStudentDependentStatus": {"type": "string", "description": "Override Student Dependent Status."}}, "title": "CapDentalSettlement CapDentalOverrideServiceValueEntity"}, "CapDentalSettlement_CapDentalPPOFrequencyEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPPOFrequencyEntity"}, "frequency": {"type": "string", "description": "PPO number of times procedure allowed."}, "frequencyComment": {"type": "string", "description": "PPO frequency comment."}, "frequencyPeriod": {"type": "integer", "format": "int64", "description": "PPO frequency period."}, "frequencyPeriodType": {"type": "string", "description": "PPO frequency period type."}, "frequencyRange": {"type": "string", "description": "PPO Range of the frequency."}, "frequencyRule": {"type": "string", "description": "PPO rule to determine if procedure allowed."}, "procedureType": {"type": "string", "description": "PPO procedure type for the frequency."}}, "title": "CapDentalSettlement CapDentalPPOFrequencyEntity"}, "CapDentalSettlement_CapDentalPatientEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPatientEntity"}, "accumulators": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalAccumulatorEntity"}}, "birthDate": {"type": "string", "format": "date", "description": "<PERSON><PERSON>'s date of birth."}, "dateOfBirth": {"type": "string", "format": "date", "description": "NOT USED"}, "disabilities": {"type": "array", "items": {"type": "string", "description": "Patient's disabilities."}}, "historyProcedures": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureEntity"}}, "isDependentChild": {"type": "boolean", "description": "NOT USED"}, "isDisabledChild": {"type": "boolean", "description": "NOT USED"}, "isFullTimeStudent": {"type": "boolean", "description": "NOT USED"}, "patientID": {"type": "string", "description": "Patient's ID."}}, "title": "CapDentalSettlement CapDentalPatientEntity"}, "CapDentalSettlement_CapDentalPolicyInfoCoinsuranceEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoCoinsuranceEntity"}, "coinsuranceINPct": {"type": "number", "description": "Coinsurance percentage in network."}, "coinsuranceOONPct": {"type": "number", "description": "Coinsurance percentage outside network."}, "coinsuranceServiceType": {"type": "string", "description": "Coinsurance Service Type."}}, "title": "CapDentalSettlement CapDentalPolicyInfoCoinsuranceEntity"}, "CapDentalSettlement_CapDentalPolicyInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoEntity"}, "applyLateEntrantBenefitWaitingPeriods": {"type": "boolean", "description": "Is late entrant benefit period waiting applied."}, "basicINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "basicOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "basicWaitingPeriod": {"type": "string", "description": "Basic waiting period."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "childMaxAgeCd": {"type": "string", "description": "Child Max Age limit."}, "coinsurances": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoCoinsuranceEntity"}}, "currencyCd": {"type": "string", "description": "Currency Code"}, "deductibleDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDeductibleDetailEntity"}}, "dentalMaximums": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalMaximumEntity"}}, "fullTimeStudentAgeCd": {"type": "string", "description": "Full time student Age limit."}, "implantsWaitingPeriod": {"type": "string", "description": "Implants waiting period."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoInsuredDetailsEntity"}}, "isImplantsMaximumAppliedTowardPlanMaximum": {"type": "boolean", "description": "Implants Maximum Applies toward Plan Maximum"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "isWaitingPeriodWaived": {"type": "boolean"}, "lateEntrantWaitingPeriodsDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}}, "majorINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "majorOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "majorWaitingPeriod": {"type": "string", "description": "Major waiting period."}, "nonStandardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "orthoINNCoinsurancePercent": {"type": "number", "description": "Ortho In Network Coinsurance Percentage."}, "orthoOONCoinsurancePercent": {"type": "number", "description": "Ortho Out of Network Coinsurance Percentage."}, "orthoWaitingPeriod": {"type": "string", "description": "Ortho waiting period."}, "patientTerm": {"$ref": "#/definitions/CapDentalSettlement_CapDentalTermEntity"}, "pcdId": {"type": "string", "description": "Primary Care Dentist ID."}, "pcdTerm": {"$ref": "#/definitions/CapDentalSettlement_CapDentalTermEntity"}, "plan": {"type": "string", "description": "Plan type for the policy."}, "planCategory": {"type": "string", "description": "PPO/DHMO product."}, "planName": {"type": "string", "description": "Plan Name."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyPaidToDate": {"type": "string", "format": "date", "description": "Date which the Policy is paid up to."}, "policyPaidToDateWithGracePeriod": {"type": "string", "format": "date", "description": "Date which the Policy is paid up to with Grace period."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "preventWaitingPeriod": {"type": "string", "description": "Preventetive waiting period."}, "preventiveINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "preventiveOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "standardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "term": {"$ref": "#/definitions/CapDentalSettlement_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "unverifiedInfo": {"$ref": "#/definitions/CapDentalSettlement_CapDentalUnverifiedInfoEntity"}, "waiveINNInd": {"type": "boolean", "description": "NOT USED"}, "waiveOONInd": {"type": "boolean", "description": "NOT USED"}}, "title": "CapDentalSettlement CapDentalPolicyInfoEntity", "description": "An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.)."}, "CapDentalSettlement_CapDentalPolicyInfoInsuredDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoInsuredDetailsEntity"}, "insuredRoleNameCd": {"type": "string", "description": "Insure<PERSON>'s role."}, "isFullTimeStudent": {"type": "boolean", "description": "Is patient full time student?"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}, "relationshipToPrimaryInsuredCd": {"type": "string", "description": "Patient's relationship to primary insured."}}, "title": "CapDentalSettlement CapDentalPolicyInfoInsuredDetailsEntity", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapDentalSettlement_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "basicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant basic Waiting Period."}, "majorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant major Waiting Period."}, "preventWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant preventive Waiting Period."}}, "title": "CapDentalSettlement CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "CapDentalSettlement_CapDentalPreauthorizationEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPreauthorizationEntity"}, "authorizationPeriod": {"$ref": "#/definitions/CapDentalSettlement_Period"}, "authorizedBy": {"type": "string", "description": "Name of the person who authorized the procedure."}, "isProcedureAuthorized": {"type": "boolean", "description": "Is procedure authorized?"}}, "title": "CapDentalSettlement CapDentalPreauthorizationEntity"}, "CapDentalSettlement_CapDentalPreprocessMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPreprocessMessageEntity"}, "preprocessCode": {"type": "string", "description": "Preprocess Message Code if any issues with the intake."}, "preprocessMessage": {"type": "string", "description": "Preprocess Message if any issues with the intake."}, "preprocessSeverity": {"type": "string", "description": "Preprocess Message Severity if any issues with the intake - defaulted to Warning."}}, "title": "CapDentalSettlement CapDentalPreprocessMessageEntity"}, "CapDentalSettlement_CapDentalProcedureCoordinationOfBenefitsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProcedureCoordinationOfBenefitsEntity"}, "allowed": {"$ref": "#/definitions/Money"}, "considered": {"$ref": "#/definitions/Money"}, "coverageType": {"type": "string", "description": "Type of Coverage."}, "innOnn": {"type": "string", "description": "Paid INN/Paid OON."}, "paid": {"$ref": "#/definitions/Money"}, "primaryCoverageStatus": {"type": "string", "description": "Primary Coverage Status."}}, "title": "CapDentalSettlement CapDentalProcedureCoordinationOfBenefitsEntity"}, "CapDentalSettlement_CapDentalProcedureEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProcedureEntity"}, "certPolicyInfo": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoEntity"}, "cob": {"$ref": "#/definitions/CapDentalSettlement_CapDentalProcedureCoordinationOfBenefitsEntity"}, "consultantReview": {"$ref": "#/definitions/CapDentalSettlement_CapDentalConsultantReviewEntity"}, "coveredFeeSchedule": {"$ref": "#/definitions/Money"}, "coveredFeeUCR": {"$ref": "#/definitions/Money"}, "coveredFeeUCRME": {"$ref": "#/definitions/Money"}, "dateOfService": {"type": "string", "format": "date", "description": "Date of Service."}, "dentist": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDentistEntity"}, "description": {"type": "string", "description": "Procedure Description."}, "diagnosisCodes": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDiagnosisCodeEntity"}}, "feeSchedule": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "feeUCR": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "feeUCRME": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalFeeRateEntity"}}, "frequencyDHMO": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDHMOFrequencyEntity"}, "frequencyPPO": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPPOFrequencyEntity"}, "lossNumber": {"type": "string", "description": "Claim number of history procedure."}, "masterPolicyInfo": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPolicyInfoEntity"}}, "ortho": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOrthodonticEntity"}, "paidAmount": {"$ref": "#/definitions/Money"}, "preauthorization": {"$ref": "#/definitions/CapDentalSettlement_CapDentalPreauthorizationEntity"}, "preauthorizationNumber": {"type": "string", "description": "Preauthorization Number."}, "predetInd": {"type": "boolean", "description": "Is this procedure part of predetermination or real service?"}, "priorProsthesisPlacementDate": {"type": "string", "format": "date", "description": "Date of Prior Prosthesis Placement."}, "procedureCode": {"type": "string", "description": "CDT Code."}, "procedureStatus": {"type": "string", "description": "Historical Procedures."}, "procedureType": {"type": "string", "description": "Procedure Type."}, "quantity": {"type": "integer", "format": "int64", "description": "Number of services."}, "status": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationStatusEntity"}, "submittedFee": {"$ref": "#/definitions/Money"}, "submittedFeeSchedule": {"$ref": "#/definitions/Money"}, "submittedFeeUCR": {"$ref": "#/definitions/Money"}, "submittedFeeUCRME": {"$ref": "#/definitions/Money"}, "surfaces": {"type": "array", "items": {"type": "string", "description": "Tooth Surface."}}, "toothArea": {"type": "string", "description": "Area of Oral Cavity."}, "toothCodes": {"type": "array", "items": {"type": "string", "description": "Tooth Numbers/Letters."}}, "toothSystem": {"type": "string", "description": "Tooth System."}, "treatmentReason": {"$ref": "#/definitions/CapDentalSettlement_CapDentalTreatmentReasonEntity"}}, "title": "CapDentalSettlement CapDentalProcedureEntity"}, "CapDentalSettlement_CapDentalProviderDiscountEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProviderDiscountEntity"}, "discountAmount": {"$ref": "#/definitions/Money"}, "discountName": {"type": "string", "description": "Concession Name."}, "discountPercentage": {"type": "number", "description": "Concession %."}, "discountType": {"type": "string", "description": "Concession Type."}}, "title": "CapDentalSettlement CapDentalProviderDiscountEntity"}, "CapDentalSettlement_CapDentalProviderFeesEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalProviderFeesEntity"}, "fee": {"$ref": "#/definitions/Money"}, "type": {"type": "string", "description": "Provicer Fee Type."}}, "title": "CapDentalSettlement CapDentalProviderFeesEntity"}, "CapDentalSettlement_CapDentalRemarkMessageEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalRemarkMessageEntity"}, "remarkCode": {"type": "string", "description": "Additional multiple codes that explains how decision achieved."}, "remarkMessage": {"type": "string", "description": "Messages as per the remark codes."}}, "title": "CapDentalSettlement CapDentalRemarkMessageEntity"}, "CapDentalSettlement_CapDentalResultEntryEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalResultEntryEntity"}, "calculationResult": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationResultEntity"}, "reservedAccumulators": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalAccumulatorEntity"}}, "serviceSource": {"type": "string", "description": "This link to Procedure from Intake. Defines which response is associated to which submited procedure."}, "status": {"$ref": "#/definitions/CapDentalSettlement_CapDentalCalculationStatusEntity"}}, "title": "CapDentalSettlement CapDentalResultEntryEntity"}, "CapDentalSettlement_CapDentalServiceOverrideEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalServiceOverrideEntity"}, "bypassServiceLogic": {"$ref": "#/definitions/CapDentalSettlement_CapDentalBypassServiceLogicEntity"}, "isAllowed": {"type": "boolean", "description": "Allow Service."}, "isDenied": {"type": "boolean", "description": "Deny Service."}, "overrideRemark": {"type": "string", "description": "Override Remark."}, "overrideServiceValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalOverrideServiceValueEntity"}, "serviceSource": {"type": "string", "description": "Link to Service."}, "suppressMemberEob": {"type": "boolean", "description": "Suppress EOB for Member."}, "suppressProviderEob": {"type": "boolean", "description": "Suppress EOB for Provider."}, "waiveServiceValue": {"$ref": "#/definitions/CapDentalSettlement_CapDentalWaiveServiceValueEntity"}}, "title": "CapDentalSettlement CapDentalServiceOverrideEntity"}, "CapDentalSettlement_CapDentalSettlementAbsenceInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalSettlementAbsenceInfoEntity"}, "id": {"type": "string"}}, "title": "CapDentalSettlement CapDentalSettlementAbsenceInfoEntity", "description": "The object that includes information taken from Absence case."}, "CapDentalSettlement_CapDentalSettlementDetailEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/EntityKey"}, "_modelName": {"type": "string", "example": "CapDentalSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalSettlementDetailEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "claimOverride": {"$ref": "#/definitions/CapDentalSettlement_CapDentalClaimOverrideEntity"}, "eobFreeFormMessage": {"type": "string", "description": "EOB Free Form Message."}, "overrideCd": {"type": "string", "description": "NOT USED"}, "serviceOverrides": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalServiceOverrideEntity"}}}, "title": "CapDentalSettlement CapDentalSettlementDetailEntity", "description": "This Business entity houses the detail of the settlement."}, "CapDentalSettlement_CapDentalSettlementEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalSettlement"}, "_modelType": {"type": "string", "example": "CapSettlement"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalSettlementEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "accessTrackInfo": {"$ref": "#/definitions/CapDentalSettlement_AccessTrackInfo"}, "claimLossIdentification": {"$ref": "#/definitions/EntityLink"}, "settlementAbsenceInfo": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementAbsenceInfoEntity"}, "settlementDetail": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementDetailEntity"}, "settlementLossInfo": {"$ref": "#/definitions/CapDentalSettlement_CapDentalClaimInfoEntity"}, "settlementNumber": {"type": "string", "description": "A unique settlement number."}, "settlementResult": {"$ref": "#/definitions/CapDentalSettlement_CapDentalDecisionResultEntity"}, "settlementType": {"type": "string", "description": "Defines settlement type"}, "state": {"type": "string", "description": "Current status of the Settlement. Updated each time a new status is gained through state machine."}}, "title": "CapDentalSettlement CapDentalSettlementEntity", "description": "Main object for the CAP Settlement Domain."}, "CapDentalSettlement_CapDentalTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalSettlement CapDentalTermEntity"}, "CapDentalSettlement_CapDentalTreatmentReasonEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalTreatmentReasonEntity"}, "autoAccidentState": {"type": "string", "description": "Auto Accident State."}, "dateOfAccident": {"type": "string", "format": "date", "description": "Date of Accident."}, "treatmentResultingFrom": {"type": "string", "description": "Treatment Resulting From."}}, "title": "CapDentalSettlement CapDentalTreatmentReasonEntity"}, "CapDentalSettlement_CapDentalUnverifiedInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalUnverifiedInfoEntity"}, "employerName": {"type": "string", "description": "Claim Without Policy Employer Name."}, "groupNumber": {"type": "string", "description": "Claim Without Policy Group Number."}}, "title": "CapDentalSettlement CapDentalUnverifiedInfoEntity"}, "CapDentalSettlement_CapDentalWaiveClaimValueEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalWaiveClaimValueEntity"}, "waiveBasicWaitingPeriod": {"type": "boolean", "description": "Waive Basic Services Waiting Period."}, "waiveDeductible": {"type": "boolean", "description": "Waive Deductible."}, "waiveEligibilityAfterStartDate": {"type": "boolean", "description": "Waive Eligibility After Coverage Start Date."}, "waiveEligibilityPriorStartDate": {"type": "boolean", "description": "Waive Eligibility Prior to Coverage Start Date."}, "waiveLateEntrantWaitingPeriod": {"type": "boolean", "description": "Waive Late Entrant Waiting Period."}, "waiveMajorWaitingPeriod": {"type": "boolean", "description": "Waive Major Services Waiting Period."}, "waiveMaximumLimitAmounts": {"type": "boolean", "description": "Waive Maximum Amount Limits."}, "waiveOrthoWaitingPeriod": {"type": "boolean", "description": "Waive Ortho Services Waiting Period."}, "waivePreventiveWaitingPeriod": {"type": "boolean", "description": "Waive Preventive Services Waiting Period."}, "waiveReplacementLimit": {"type": "boolean", "description": "Waive Replacement Limit."}, "waiveServiceFrequencyLimit": {"type": "boolean", "description": "Waive Service Frequency Limit."}}, "title": "CapDentalSettlement CapDentalWaiveClaimValueEntity"}, "CapDentalSettlement_CapDentalWaiveServiceValueEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalWaiveServiceValueEntity"}, "waiveDeductible": {"type": "boolean", "description": "Waive Deductible."}, "waiveEligibilityAfterStartDate": {"type": "boolean", "description": "Waive Eligibility After Coverage Start Date."}, "waiveEligibilityPriorStartDate": {"type": "boolean", "description": "Waive Eligibility Prior to Coverage Start Date."}, "waiveLateEntrantWaitingPeriod": {"type": "boolean", "description": "Waive Late Entrant Waiting Period."}, "waiveMaximumLimitAmounts": {"type": "boolean", "description": "Waive Maximum Amount Limits."}, "waiveReplacementLimit": {"type": "boolean", "description": "Waive Replacement Limit."}, "waiveServiceFrequencyLimit": {"type": "boolean", "description": "Waive Service Frequency Limit."}, "waiveServiceWaitingPeriod": {"type": "boolean", "description": "Waive Service Category Waiting Period."}}, "title": "CapDentalSettlement CapDentalWaiveServiceValueEntity"}, "CapDentalSettlement_MessageType": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "MessageType"}, "code": {"type": "string", "description": "Message code."}, "message": {"type": "string", "description": "Message text."}, "severity": {"type": "string", "description": "Message severity."}, "source": {"type": "string", "description": "Message source."}}, "title": "CapDentalSettlement MessageType", "description": "Defines a message that can be forwarded to the user on some exceptions."}, "CapDentalSettlement_Period": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Period"}, "endDate": {"type": "string", "format": "date-time"}, "startDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalSettlement Period"}, "CapDentalSettlement_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalSettlement Term"}, "CapDentalUnverifiedPolicy_CapDeductibleDetailEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDeductibleDetailEntity"}, "individualBasicINNAnnualDeductible": {"$ref": "#/definitions/Money"}, "individualMajorINNAnnualDeductible": {"$ref": "#/definitions/Money"}, "individualPreventiveINNAnnualDeductible": {"$ref": "#/definitions/Money"}}, "title": "CapDentalUnverifiedPolicy CapDeductibleDetailEntity"}, "CapDentalUnverifiedPolicy_CapDentalMaximumEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalMaximumEntity"}, "implantsINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualBasicINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualMajorINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "individualPreventiveINNAnnualMaximum": {"$ref": "#/definitions/Money"}, "orthoINNAnnualMaximum": {"$ref": "#/definitions/Money"}}, "title": "CapDentalUnverifiedPolicy CapDentalMaximumEntity"}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoCoinsuranceEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoCoinsuranceEntity"}, "coinsuranceINPct": {"type": "number", "description": "Coinsurance percentage in network."}, "coinsuranceOONPct": {"type": "number", "description": "Coinsurance percentage outside network."}, "coinsuranceServiceType": {"type": "string", "description": "Coinsurance Service Type."}}, "title": "CapDentalUnverifiedPolicy CapDentalPolicyInfoCoinsuranceEntity"}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoInsuredDetailsEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoInsuredDetailsEntity"}, "insuredRoleNameCd": {"type": "string", "description": "Insure<PERSON>'s role."}, "isFullTimeStudent": {"type": "boolean", "description": "Is patient full time student?"}, "isMain": {"type": "boolean", "description": "Indicates if a party is a primary insured."}, "registryTypeId": {"type": "string", "description": "Searchable unique registry ID that identifies the subject of the claim."}, "relationshipToPrimaryInsuredCd": {"type": "string", "description": "Patient's relationship to primary insured."}}, "title": "CapDentalUnverifiedPolicy CapDentalPolicyInfoInsuredDetailsEntity", "description": "An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information."}, "CapDentalUnverifiedPolicy_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "basicWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant basic Waiting Period."}, "majorWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant major Waiting Period."}, "preventWaitingPeriod": {"type": "integer", "format": "int64", "description": "Late entrant preventive Waiting Period."}}, "title": "CapDentalUnverifiedPolicy CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}, "CapDentalUnverifiedPolicy_CapDentalTermEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalTermEntity"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalUnverifiedPolicy CapDentalTermEntity"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedInfoEntity": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "CapDentalUnverifiedInfoEntity"}, "employerName": {"type": "string", "description": "Claim Without Policy Employer Name."}, "groupNumber": {"type": "string", "description": "Claim Without Policy Group Number."}}, "title": "CapDentalUnverifiedPolicy CapDentalUnverifiedInfoEntity"}, "CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicy": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapDentalUnverifiedPolicy"}, "_modelType": {"type": "string", "example": "UnverifiedPolicy"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapDentalUnverifiedPolicy"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "applyLateEntrantBenefitWaitingPeriods": {"type": "boolean", "description": "Is late entrant benefit period waiting applied."}, "basicINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "basicOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "basicWaitingPeriod": {"type": "string", "description": "Basic waiting period."}, "capPolicyId": {"type": "string", "description": "CAP policy identifier."}, "capPolicyVersionId": {"type": "string", "description": "Defines policy version stored on CAP side."}, "childMaxAgeCd": {"type": "string", "description": "Child Max Age limit."}, "coinsurances": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoCoinsuranceEntity"}}, "currencyCd": {"type": "string", "description": "Currency Code"}, "deductibleDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDeductibleDetailEntity"}}, "dentalMaximums": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalMaximumEntity"}}, "fullTimeStudentAgeCd": {"type": "string", "description": "Full time student Age limit."}, "implantsWaitingPeriod": {"type": "string", "description": "Implants waiting period."}, "insureds": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoInsuredDetailsEntity"}}, "isImplantsMaximumAppliedTowardPlanMaximum": {"type": "boolean", "description": "Implants Maximum Applies toward Plan Maximum"}, "isVerified": {"type": "boolean", "description": "Indicates if policy is verified."}, "isWaitingPeriodWaived": {"type": "boolean"}, "lateEntrantWaitingPeriodsDetails": {"type": "array", "items": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity"}}, "majorINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "majorOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "majorWaitingPeriod": {"type": "string", "description": "Major waiting period."}, "nonStandardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "orthoINNCoinsurancePercent": {"type": "number", "description": "Ortho In Network Coinsurance Percentage."}, "orthoOONCoinsurancePercent": {"type": "number", "description": "Ortho Out of Network Coinsurance Percentage."}, "orthoWaitingPeriod": {"type": "string", "description": "Ortho waiting period."}, "patientTerm": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalTermEntity"}, "pcdId": {"type": "string", "description": "Primary Care Dentist ID."}, "pcdTerm": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalTermEntity"}, "plan": {"type": "string", "description": "Plan type for the policy."}, "planCategory": {"type": "string", "description": "PPO/DHMO product."}, "planName": {"type": "string", "description": "Plan Name."}, "policyNumber": {"type": "string", "description": "Indicates policy number."}, "policyPaidToDate": {"type": "string", "format": "date", "description": "Date which the Policy is paid up to."}, "policyPaidToDateWithGracePeriod": {"type": "string", "format": "date", "description": "Date which the Policy is paid up to with Grace period."}, "policyStatus": {"type": "string", "description": "Policy version status"}, "policyType": {"type": "string", "description": "Indicates whether policy type is master or certificate (individual)."}, "preventWaitingPeriod": {"type": "string", "description": "Preventetive waiting period."}, "preventiveINNCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "preventiveOONCoinsurancePercent": {"type": "number", "description": "NOT USED"}, "productCd": {"type": "string", "description": "The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster)."}, "riskStateCd": {"type": "string", "description": "Situs state"}, "standardChildAgeLimit": {"type": "integer", "format": "int64", "description": "NOT USED"}, "term": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_Term"}, "txEffectiveDate": {"type": "string", "description": "The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective."}, "unverifiedInfo": {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedInfoEntity"}, "waiveINNInd": {"type": "boolean", "description": "NOT USED"}, "waiveOONInd": {"type": "boolean", "description": "NOT USED"}}, "title": "CapDentalUnverifiedPolicy CapDentalUnverifiedPolicy"}, "CapDentalUnverifiedPolicy_Term": {"required": ["_type"], "properties": {"_key": {"$ref": "#/definitions/EntityKey"}, "_type": {"type": "string", "example": "Term"}, "effectiveDate": {"type": "string", "format": "date-time"}, "expirationDate": {"type": "string", "format": "date-time"}}, "title": "CapDentalUnverifiedPolicy Term"}, "CapMockModel_EntityMock": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CapMockModel"}, "_modelType": {"type": "string", "example": "CapMockModelType"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "EntityMock"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "mock": {"type": "object"}, "uri": {"type": "string"}}, "title": "CapMockModel EntityMock"}, "CorrelationIdHolder_CorrelationIdHolderEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "CorrelationIdHolder"}, "_modelType": {"type": "string", "example": "CorrelationIdHolder"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CorrelationIdHolderEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "correlationId": {"type": "string"}}, "title": "CorrelationIdHolder CorrelationIdHolderEntity"}, "DentalLossRootIdentifierTransformationModel_CapRootIdentifierEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "DentalLossRootIdentifierTransformationModel"}, "_modelType": {"type": "string", "example": "DentalLossRootIdentifierTransformationModel"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapRootIdentifierEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "DentalLossRootIdentifierTransformationModel CapRootIdentifierEntity"}, "DentalSettlementRootIdentifierTransformationModel_CapRootIdentifierEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "DentalSettlementRootIdentifierTransformationModel"}, "_modelType": {"type": "string", "example": "DentalSettlementRootIdentifierTransformationModel"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "CapRootIdentifierEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "links": {"type": "array", "items": {"$ref": "#/definitions/EntityLink"}}}, "title": "DentalSettlementRootIdentifierTransformationModel CapRootIdentifierEntity"}, "EndpointFailure": {"properties": {"data": {"$ref": "#/definitions/ErrorHolder"}, "failure": {"type": "boolean"}, "httpCode": {"type": "integer", "format": "int64"}}}, "EndpointFailureFailure": {"properties": {"failure": {"$ref": "#/definitions/EndpointFailure"}, "response": {"type": "string"}}, "title": "EndpointFailureFailure"}, "EndpointFailureFailureBody": {"properties": {"body": {"$ref": "#/definitions/EndpointFailureFailure"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "EndpointFailureFailureBody"}, "EntityKey": {"properties": {"id": {"type": "string", "format": "uuid"}, "parentId": {"type": "string", "format": "uuid"}, "revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "EntityLink": {"properties": {"_uri": {"type": "string"}}, "title": "EntityLink"}, "ErrorHolder": {"properties": {"code": {"type": "string"}, "details": {"type": "object"}, "errorCode": {"type": "string"}, "errors": {"type": "array", "items": {"$ref": "#/definitions/ErrorHolder"}}, "field": {"type": "string"}, "logReference": {"type": "string"}, "message": {"type": "string"}, "path": {"type": "string"}}}, "IndividualCustomerRegistryIntegration_IndividualCustomerEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "IndividualCustomerRegistryIntegration"}, "_modelType": {"type": "string", "example": "IndividualCustomerEntityType"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "IndividualCustomerEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "birthDate": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "registryTypeId": {"type": "string"}}, "title": "IndividualCustomerRegistryIntegration IndividualCustomerEntity"}, "MockRootModel_MockRootEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "MockRootModel"}, "_modelType": {"type": "string", "example": "RootEntity"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "MockRootEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "stringAttribute": {"type": "string"}}, "title": "MockRootModel MockRootEntity", "description": "Mocks a basic root entity for testing purpose."}, "Money": {"required": ["amount", "currency"], "properties": {"amount": {"type": "number"}, "currency": {"type": "string"}}}, "OrganizationCustomerRegistryIntegration_OrganizationCustomerEntity": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "OrganizationCustomerRegistryIntegration"}, "_modelType": {"type": "string", "example": "OrganizationCustomerEntityType"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "OrganizationCustomerEntity"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "legalId": {"type": "string"}, "registryTypeId": {"type": "string"}}, "title": "OrganizationCustomerRegistryIntegration OrganizationCustomerEntity"}, "ResultCapDentalBalanceEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "creationDate": {"type": "string", "format": "date-time"}, "originSource": {"type": "string"}, "payee": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapDentalBalanceEntityRow"}, "ResultCapDentalBalanceEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceEntity"}}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV2"}, "ResultCapDentalBalanceEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV2Success"}, "ResultCapDentalBalanceEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalBalanceEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalBalanceEntity, ResultCapDentalBalanceEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceEntity"}, {"$ref": "#/definitions/ResultCapDentalBalanceEntityRow"}]}}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV3"}, "ResultCapDentalBalanceEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV3Success"}, "ResultCapDentalBalanceEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalBalanceEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalBalanceEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalLossEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "lossNumber": {"type": "string"}, "registryTypeId": {"type": "array", "items": {"type": "string"}}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapDentalLossEntityRow"}, "ResultCapDentalLossEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalLoss_CapDentalLossEntity"}}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV2"}, "ResultCapDentalLossEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV2Success"}, "ResultCapDentalLossEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalLossEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalLossEntity, ResultCapDentalLossEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalLoss_CapDentalLossEntity"}, {"$ref": "#/definitions/ResultCapDentalLossEntityRow"}]}}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV3"}, "ResultCapDentalLossEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV3Success"}, "ResultCapDentalLossEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalLossEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalLossEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalPatientHistoryEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapDentalPatientHistoryEntityRow"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalPatientHistoryEntity"}}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2Success"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalPatientHistoryEntity, ResultCapDentalPatientHistoryEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalPatientHistory_CapDentalPatientHistoryEntity"}, {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntityRow"}]}}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3Success"}, "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPatientHistoryEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPatientHistoryEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalPaymentEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "_variation": {"type": "string"}, "allocationSource": {"type": "array", "items": {"type": "string"}}, "originSource": {"type": "string"}, "paymentNumber": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}, "state": {"type": "string"}}, "title": "ResultCapDentalPaymentEntityRow"}, "ResultCapDentalPaymentEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentEntity"}}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV2"}, "ResultCapDentalPaymentEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV2Success"}, "ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalPaymentEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalPaymentEntity, ResultCapDentalPaymentEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentEntity"}, {"$ref": "#/definitions/ResultCapDentalPaymentEntityRow"}]}}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV3"}, "ResultCapDentalPaymentEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV3Success"}, "ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalPaymentScheduleEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "allocationSource": {"type": "array", "items": {"type": "string"}}, "creationDate": {"type": "string", "format": "date-time"}, "originSource": {"type": "string"}, "paymentTemplate": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}}, "title": "ResultCapDentalPaymentScheduleEntityRow"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity"}}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2Success"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalPaymentScheduleEntity, ResultCapDentalPaymentScheduleEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity"}, {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntityRow"}]}}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3Success"}, "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentScheduleEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalPaymentTemplateEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "originSource": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}, "state": {"type": "string"}}, "title": "ResultCapDentalPaymentTemplateEntityRow"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentTemplateEntity"}}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2Success"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalPaymentTemplateEntity, ResultCapDentalPaymentTemplateEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentTemplateEntity"}, {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntityRow"}]}}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3Success"}, "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalPaymentTemplateEntitySearchEntityResponseV3SuccessBody"}, "ResultCapDentalSettlementEntityRow": {"properties": {"_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "claimLossIdentification": {"type": "string"}, "registryTypeId": {"type": "array", "items": {"type": "string"}}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}, "settlementNumber": {"type": "string"}}, "title": "ResultCapDentalSettlementEntityRow"}, "ResultCapDentalSettlementEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntity"}}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV2"}, "ResultCapDentalSettlementEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV2"}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV2Success"}, "ResultCapDentalSettlementEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV2SuccessBody"}, "ResultCapDentalSettlementEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: CapDentalSettlementEntity, ResultCapDentalSettlementEntityRow", "x-oneof": [{"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntity"}, {"$ref": "#/definitions/ResultCapDentalSettlementEntityRow"}]}}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV3"}, "ResultCapDentalSettlementEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV3"}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV3Success"}, "ResultCapDentalSettlementEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultCapDentalSettlementEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultCapDentalSettlementEntitySearchEntityResponseV3SuccessBody"}, "ResultRootEntityRow": {"properties": {"DOSDate": {"type": "string", "format": "date-time"}, "_modelName": {"type": "string"}, "_originalTimestamp": {"type": "string", "format": "date-time"}, "_timestamp": {"type": "string", "format": "date-time"}, "_uri": {"type": "string"}, "claimNumber": {"type": "string"}, "patientNumber": {"type": "string"}, "planCategory": {"type": "string"}, "policyNumber": {"type": "string"}, "providerTIN": {"type": "string"}, "rootId": {"type": "string", "format": "uuid"}, "score": {"type": "object"}, "state": {"type": "string"}, "toothCodes": {"type": "array", "items": {"type": "string"}}}, "title": "ResultRootEntityRow"}, "ResultRootEntitySearchEntityResponseV2": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "x-oneof": [{"$ref": "#/definitions/OrganizationCustomerRegistryIntegration_OrganizationCustomerEntity"}, {"$ref": "#/definitions/CapDentalPaymentScheduleIndex_CapDentalPaymentScheduleIdxEntity"}, {"$ref": "#/definitions/CapMockModel_EntityMock"}, {"$ref": "#/definitions/CapDentalPaymentDefinition_CapDentalPaymentEntity"}, {"$ref": "#/definitions/CapDentalUnverifiedPolicy_CapDentalUnverifiedPolicy"}, {"$ref": "#/definitions/CapDentalSettlement_CapDentalSettlementEntity"}, {"$ref": "#/definitions/DentalLossRootIdentifierTransformationModel_CapRootIdentifierEntity"}, {"$ref": "#/definitions/CapDentalPaymentSchedule_CapDentalPaymentScheduleEntity"}, {"$ref": "#/definitions/CapDentalPaymentTemplateIndex_CapDentalPaymentTemplateIdxEntity"}, {"$ref": "#/definitions/CapDentalSettlementIndex_CapDentalSettlementIdx"}, {"$ref": "#/definitions/MockRootModel_MockRootEntity"}, {"$ref": "#/definitions/CapDentalBalance_CapDentalBalanceEntity"}, {"$ref": "#/definitions/CapDentalPaymentTemplate_CapDentalPaymentTemplateEntity"}, {"$ref": "#/definitions/CapDentalProcedure_CapDentalProcedureProjection"}, {"$ref": "#/definitions/IndividualCustomerRegistryIntegration_IndividualCustomerEntity"}, {"$ref": "#/definitions/CapDentalPaymentIndex_CapDentalPaymentIdxEntity"}, {"$ref": "#/definitions/SecurityContext_SecurityContext"}, {"$ref": "#/definitions/DentalSettlementRootIdentifierTransformationModel_CapRootIdentifierEntity"}, {"$ref": "#/definitions/CapDentalPatientHistory_CapDentalPatientHistoryEntity"}, {"$ref": "#/definitions/CapDentalLoss_CapDentalLossEntity"}, {"$ref": "#/definitions/CorrelationIdHolder_CorrelationIdHolderEntity"}]}}}, "title": "ResultRootEntitySearchEntityResponseV2"}, "ResultRootEntitySearchEntityResponseV2Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV2"}}, "title": "ResultRootEntitySearchEntityResponseV2Success"}, "ResultRootEntitySearchEntityResponseV2SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV2Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultRootEntitySearchEntityResponseV2SuccessBody"}, "ResultRootEntitySearchEntityResponseV3": {"properties": {"count": {"type": "integer", "format": "int64"}, "result": {"type": "array", "items": {"type": "object", "description": "Actual return types are one of: ResultRootEntityRow, RootEntity", "x-oneof": [{"$ref": "#/definitions/ResultRootEntityRow"}]}}}, "title": "ResultRootEntitySearchEntityResponseV3"}, "ResultRootEntitySearchEntityResponseV3Success": {"properties": {"response": {"type": "string"}, "success": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV3"}}, "title": "ResultRootEntitySearchEntityResponseV3Success"}, "ResultRootEntitySearchEntityResponseV3SuccessBody": {"properties": {"body": {"$ref": "#/definitions/ResultRootEntitySearchEntityResponseV3Success"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "ResultRootEntitySearchEntityResponseV3SuccessBody"}, "RootEntityKey": {"properties": {"revisionNo": {"type": "integer", "format": "int64"}, "rootId": {"type": "string", "format": "uuid"}}, "title": "RootEntityKey"}, "SearchCapDentalBalanceEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySortClause"}}, "title": "SearchCapDentalBalanceEntitySearchEntityRequestV2"}, "SearchCapDentalBalanceEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalBalanceEntitySearchEntityRequestV2Body"}, "SearchCapDentalBalanceEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySortClause"}}}, "title": "SearchCapDentalBalanceEntitySearchEntityRequestV3"}, "SearchCapDentalBalanceEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalBalanceEntitySearchEntityRequestV3Body"}, "SearchCapDentalBalanceEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalBalanceEntitySearchMatcher"}, "SearchCapDentalBalanceEntitySearchQuery": {"properties": {"creationDate": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchValueMatcher"}, "originSource": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchValueMatcher"}, "payee": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchValueMatcher"}}, "title": "SearchCapDentalBalanceEntitySearchQuery"}, "SearchCapDentalBalanceEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalBalanceEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalBalanceEntitySearchValueMatcher"}, "SearchCapDentalBalanceEntitySortClause": {"properties": {"creationDate": {"type": "string"}, "originSource": {"type": "string"}, "payee": {"type": "string"}, "rootId": {"type": "string"}}, "title": "SearchCapDentalBalanceEntitySortClause"}, "SearchCapDentalLossEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalLossEntitySortClause"}}, "title": "SearchCapDentalLossEntitySearchEntityRequestV2"}, "SearchCapDentalLossEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalLossEntitySearchEntityRequestV2Body"}, "SearchCapDentalLossEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalLossEntitySortClause"}}}, "title": "SearchCapDentalLossEntitySearchEntityRequestV3"}, "SearchCapDentalLossEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalLossEntitySearchEntityRequestV3Body"}, "SearchCapDentalLossEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalLossEntitySearchMatcher"}, "SearchCapDentalLossEntitySearchQuery": {"properties": {"lossNumber": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchValueMatcher"}, "registryTypeId": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchValueMatcher"}}, "title": "SearchCapDentalLossEntitySearchQuery"}, "SearchCapDentalLossEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalLossEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalLossEntitySearchValueMatcher"}, "SearchCapDentalLossEntitySortClause": {"properties": {"lossNumber": {"type": "string"}, "rootId": {"type": "string"}}, "title": "SearchCapDentalLossEntitySortClause"}, "SearchCapDentalPatientHistoryEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySortClause"}}, "title": "SearchCapDentalPatientHistoryEntitySearchEntityRequestV2"}, "SearchCapDentalPatientHistoryEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPatientHistoryEntitySearchEntityRequestV2Body"}, "SearchCapDentalPatientHistoryEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySortClause"}}}, "title": "SearchCapDentalPatientHistoryEntitySearchEntityRequestV3"}, "SearchCapDentalPatientHistoryEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPatientHistoryEntitySearchEntityRequestV3Body"}, "SearchCapDentalPatientHistoryEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalPatientHistoryEntitySearchMatcher"}, "SearchCapDentalPatientHistoryEntitySearchQuery": {"properties": {"rootId": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchValueMatcher"}}, "title": "SearchCapDentalPatientHistoryEntitySearchQuery"}, "SearchCapDentalPatientHistoryEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalPatientHistoryEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalPatientHistoryEntitySearchValueMatcher"}, "SearchCapDentalPatientHistoryEntitySortClause": {"properties": {"rootId": {"type": "string"}}, "title": "SearchCapDentalPatientHistoryEntitySortClause"}, "SearchCapDentalPaymentEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySortClause"}}, "title": "SearchCapDentalPaymentEntitySearchEntityRequestV2"}, "SearchCapDentalPaymentEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentEntitySearchEntityRequestV2Body"}, "SearchCapDentalPaymentEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySortClause"}}}, "title": "SearchCapDentalPaymentEntitySearchEntityRequestV3"}, "SearchCapDentalPaymentEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentEntitySearchEntityRequestV3Body"}, "SearchCapDentalPaymentEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentEntitySearchMatcher"}, "SearchCapDentalPaymentEntitySearchQuery": {"properties": {"allocationSource": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchValueMatcher"}, "originSource": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchValueMatcher"}, "paymentNumber": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchValueMatcher"}, "state": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchValueMatcher"}}, "title": "SearchCapDentalPaymentEntitySearchQuery"}, "SearchCapDentalPaymentEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalPaymentEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentEntitySearchValueMatcher"}, "SearchCapDentalPaymentEntitySortClause": {"properties": {"originSource": {"type": "string"}, "paymentNumber": {"type": "string"}, "rootId": {"type": "string"}, "state": {"type": "string"}}, "title": "SearchCapDentalPaymentEntitySortClause"}, "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySortClause"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2"}, "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV2Body"}, "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySortClause"}}}, "title": "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3"}, "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchEntityRequestV3Body"}, "SearchCapDentalPaymentScheduleEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchMatcher"}, "SearchCapDentalPaymentScheduleEntitySearchQuery": {"properties": {"allocationSource": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}, "creationDate": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}, "originSource": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}, "paymentTemplate": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchQuery"}, "SearchCapDentalPaymentScheduleEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalPaymentScheduleEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentScheduleEntitySearchValueMatcher"}, "SearchCapDentalPaymentScheduleEntitySortClause": {"properties": {"creationDate": {"type": "string"}, "originSource": {"type": "string"}, "paymentTemplate": {"type": "string"}, "rootId": {"type": "string"}}, "title": "SearchCapDentalPaymentScheduleEntitySortClause"}, "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySortClause"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2"}, "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV2Body"}, "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySortClause"}}}, "title": "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3"}, "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchEntityRequestV3Body"}, "SearchCapDentalPaymentTemplateEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchMatcher"}, "SearchCapDentalPaymentTemplateEntitySearchQuery": {"properties": {"originSource": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchValueMatcher"}, "state": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchValueMatcher"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchQuery"}, "SearchCapDentalPaymentTemplateEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalPaymentTemplateEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalPaymentTemplateEntitySearchValueMatcher"}, "SearchCapDentalPaymentTemplateEntitySortClause": {"properties": {"originSource": {"type": "string"}, "rootId": {"type": "string"}, "state": {"type": "string"}}, "title": "SearchCapDentalPaymentTemplateEntitySortClause"}, "SearchCapDentalProcedureProjectionSearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSortClause"}}, "title": "SearchCapDentalProcedureProjectionSearchEntityRequestV2"}, "SearchCapDentalProcedureProjectionSearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalProcedureProjectionSearchEntityRequestV2Body"}, "SearchCapDentalProcedureProjectionSearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSortClause"}}}, "title": "SearchCapDentalProcedureProjectionSearchEntityRequestV3"}, "SearchCapDentalProcedureProjectionSearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalProcedureProjectionSearchEntityRequestV3Body"}, "SearchCapDentalProcedureProjectionSearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalProcedureProjectionSearchMatcher"}, "SearchCapDentalProcedureProjectionSearchQuery": {"properties": {"DOSDate": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "claimNumber": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "patientNumber": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "planCategory": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "policyNumber": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "providerTIN": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "state": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}, "toothCodes": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchValueMatcher"}}, "title": "SearchCapDentalProcedureProjectionSearchQuery"}, "SearchCapDentalProcedureProjectionSearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalProcedureProjectionSearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalProcedureProjectionSearchValueMatcher"}, "SearchCapDentalProcedureProjectionSortClause": {"properties": {"DOSDate": {"type": "string"}, "claimNumber": {"type": "string"}, "patientNumber": {"type": "string"}, "planCategory": {"type": "string"}, "policyNumber": {"type": "string"}, "providerTIN": {"type": "string"}, "rootId": {"type": "string"}, "state": {"type": "string"}}, "title": "SearchCapDentalProcedureProjectionSortClause"}, "SearchCapDentalSettlementEntitySearchEntityRequestV2": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchQuery"}, "sort": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySortClause"}}, "title": "SearchCapDentalSettlementEntitySearchEntityRequestV2"}, "SearchCapDentalSettlementEntitySearchEntityRequestV2Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchEntityRequestV2"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalSettlementEntitySearchEntityRequestV2Body"}, "SearchCapDentalSettlementEntitySearchEntityRequestV3": {"properties": {"embed": {"type": "array", "items": {"type": "string"}}, "fields": {"type": "array", "items": {"type": "string"}}, "limit": {"type": "integer", "format": "int64"}, "offset": {"type": "integer", "format": "int64"}, "query": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchQuery"}, "resolveEntity": {"type": "boolean"}, "singleLineQuery": {"type": "string", "description": "Single line search attribute. Can contain any indexable values of entity. Multi values should be white space separated."}, "sorting": {"type": "array", "items": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySortClause"}}}, "title": "SearchCapDentalSettlementEntitySearchEntityRequestV3"}, "SearchCapDentalSettlementEntitySearchEntityRequestV3Body": {"required": ["body"], "properties": {"body": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchEntityRequestV3"}, "finalResponse": {"type": "boolean"}, "requestId": {"type": "string"}}, "title": "SearchCapDentalSettlementEntitySearchEntityRequestV3Body"}, "SearchCapDentalSettlementEntitySearchMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "to": {"type": "string"}}, "title": "SearchCapDentalSettlementEntitySearchMatcher"}, "SearchCapDentalSettlementEntitySearchQuery": {"properties": {"claimLossIdentification": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchValueMatcher"}, "registryTypeId": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchValueMatcher"}, "rootId": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchValueMatcher"}, "settlementNumber": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchValueMatcher"}}, "title": "SearchCapDentalSettlementEntitySearchQuery"}, "SearchCapDentalSettlementEntitySearchValueMatcher": {"properties": {"from": {"type": "string"}, "matches": {"type": "array", "items": {"type": "string"}}, "not": {"$ref": "#/definitions/SearchCapDentalSettlementEntitySearchMatcher"}, "notEqual": {"type": "string"}, "to": {"type": "string"}}, "title": "SearchCapDentalSettlementEntitySearchValueMatcher"}, "SearchCapDentalSettlementEntitySortClause": {"properties": {"claimLossIdentification": {"type": "string"}, "rootId": {"type": "string"}, "settlementNumber": {"type": "string"}}, "title": "SearchCapDentalSettlementEntitySortClause"}, "SecurityContext_SecurityContext": {"required": ["_modelName", "_type"], "properties": {"_archived": {"type": "boolean", "example": false}, "_key": {"$ref": "#/definitions/RootEntityKey"}, "_modelName": {"type": "string", "example": "SecurityContext"}, "_modelType": {"type": "string", "example": "SecurityContext"}, "_modelVersion": {"type": "string", "example": "1"}, "_timestamp": {"type": "string", "example": "2024-01-01T00:42:42.173+02:00"}, "_type": {"type": "string", "example": "SecurityContext"}, "_version": {"type": "string", "example": "0_c0f3a3ed-a9bc-4c30-b806-71139d564075"}, "authorities": {"type": "array", "items": {"type": "string"}}, "authorityLevel": {"type": "integer", "format": "int64"}, "communicationChannel": {"type": "string"}, "dtRole": {"type": "string"}, "roles": {"type": "array", "items": {"type": "string"}}}, "title": "SecurityContext SecurityContext"}}}