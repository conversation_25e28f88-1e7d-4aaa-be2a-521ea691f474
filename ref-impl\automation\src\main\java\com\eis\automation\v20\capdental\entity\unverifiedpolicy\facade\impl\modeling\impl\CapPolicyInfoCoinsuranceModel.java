/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapPolicyInfoCoinsuranceModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapPolicyInfoCoinsuranceModel extends TypeModel implements ICapPolicyInfoCoinsuranceModel {

    private Integer coinsuranceOONPct;
    private Integer coinsuranceINPct;
    private String coinsuranceServiceType;

    public Integer getCoinsuranceOONPct() {
        return coinsuranceOONPct;
    }

    public void setCoinsuranceOONPct(Integer coinsuranceOONPct) {
        this.coinsuranceOONPct = coinsuranceOONPct;
    }

    public Integer getCoinsuranceINPct() {
        return coinsuranceINPct;
    }

    public void setCoinsuranceINPct(Integer coinsuranceINPct) {
        this.coinsuranceINPct = coinsuranceINPct;
    }

    public String getCoinsuranceServiceType() {
        return coinsuranceServiceType;
    }

    public void setCoinsuranceServiceType(String coinsuranceServiceType) {
        this.coinsuranceServiceType = coinsuranceServiceType;
    }
}
