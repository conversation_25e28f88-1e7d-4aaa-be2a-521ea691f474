@Passthrough("payment")
@KeyStrategy("PASSTHROUGH")
Transformation InitCapDentalPayment {
    Input {
        ? as input
        CapDentalPaymentDefinition.CapDentalPaymentEntity as payment
    }
    Output {
        CapDentalPaymentDefinition.CapDentalPaymentEntity
    }

    Attr originSource is input.originSource
    Attr direction is "outgoing"
    Attr creationDate is Now()
    Attr paymentDetails is input.entity
    Attr paymentSchedule is input.paymentSchedule
    Attr paymentNetAmount is input.paymentNetAmount
}