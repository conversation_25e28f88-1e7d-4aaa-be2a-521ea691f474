/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalBypassClaimLogicModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalClaimOverrideModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalOverrideClaimValueModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalWaiveClaimValueModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalClaimOverrideModel extends TypeModel implements ICapDentalClaimOverrideModel {

    @JsonProperty("isAllowed")
    private Boolean isAllowed;
    @JsonProperty("isDenied")
    private Boolean isDenied;
    @JsonProperty("suppressMemberEob")
    private Boolean suppressMemberEob;
    @JsonProperty("suppressProviderEob")
    private Boolean suppressProviderEob;
    private ICapDentalWaiveClaimValueModel waiveClaimValue;
    private ICapDentalOverrideClaimValueModel overrideClaimValue;
    private List<ICapDentalBypassClaimLogicModel> bypassClaimLogic;

    public Boolean getIsAllowed() {
        return isAllowed;
    }

    public void setIsAllowed(Boolean isAllowed) {
        this.isAllowed = isAllowed;
    }

    public Boolean getIsDenied() {
        return isDenied;
    }

    public void setIsDenied(Boolean isDenied) {
        this.isDenied = isDenied;
    }

    public Boolean getSuppressMemberEob() {
        return suppressMemberEob;
    }

    public void setSuppressMemberEob(Boolean suppressMemberEob) {
        this.suppressMemberEob = suppressMemberEob;
    }

    public Boolean getSuppressProviderEob() {
        return suppressProviderEob;
    }

    public void setSuppressProviderEob(Boolean suppressProviderEob) {
        this.suppressProviderEob = suppressProviderEob;
    }

    @JsonSerialize(as = CapDentalWaiveClaimValueModel.class)
    public ICapDentalWaiveClaimValueModel getWaiveClaimValue() {
        return waiveClaimValue;
    }

    @JsonDeserialize(as = CapDentalWaiveClaimValueModel.class)
    public void setWaiveClaimValue(ICapDentalWaiveClaimValueModel waiveClaimValue) {
        this.waiveClaimValue = waiveClaimValue;
    }

    @JsonSerialize(as = CapDentalOverrideClaimValueModel.class)
    public ICapDentalOverrideClaimValueModel getOverrideClaimValue() {
        return overrideClaimValue;
    }

    @JsonDeserialize(as = CapDentalOverrideClaimValueModel.class)
    public void setOverrideClaimValue(ICapDentalOverrideClaimValueModel overrideClaimValue) {
        this.overrideClaimValue = overrideClaimValue;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalBypassClaimLogicModel.class)
    public List<ICapDentalBypassClaimLogicModel> getBypassClaimLogic() {
        return bypassClaimLogic;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalBypassClaimLogicModel.class)
    public void setBypassClaimLogic(List<ICapDentalBypassClaimLogicModel> bypassClaimLogic) {
        this.bypassClaimLogic = bypassClaimLogic;
    }
}