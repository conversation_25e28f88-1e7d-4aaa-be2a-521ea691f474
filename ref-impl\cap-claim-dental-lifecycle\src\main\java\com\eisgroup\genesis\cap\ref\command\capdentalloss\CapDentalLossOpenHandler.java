/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;


import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.loss.repository.ClaimLossRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.model.ModelResolver;

import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Open handler
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalLossOpenHandler<I extends IdentifierRequest, O extends CapLoss>  implements ProductCommandHandler<I, O> {

    @Autowired
    private ClaimLossRepository claimLossRepository;

    @Autowired
    private ModelResolver modelResolver;


    @Nonnull
    @Override
    public O load(@Nonnull I input) {
        return (O) claimLossRepository.load(input.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public O execute(@Nonnull I input, @Nonnull O entity) {
        return Lazy.of(entity).get();
    }

    @Nonnull
    @Override
    public O save(@Nonnull I input, @Nonnull O entity) {
        return (O) claimLossRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return CapDentalLossCommands.OPEN_LOSS;
    }

}
