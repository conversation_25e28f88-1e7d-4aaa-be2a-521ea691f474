<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="http://www.flowable.org/processdef" exporter="Flowable Open Source Modeler" exporterVersion="6.7.2.11-e3">
  <process id="dentalGeneratePayment" name="dentalGeneratePayment" isExecutable="true">
    <startEvent id="generatePaymentsEntryPoint">
      <extensionElements>
        <flowable:eventType xmlns:flowable="http://flowable.org/bpmn"><![CDATA[CapDentalPaymentSchedule_generatePayments]]></flowable:eventType>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_key" sourceType="string" target="_key"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_variation" sourceType="string" target="_variation"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelName" sourceType="string" target="_modelName"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_modelVersion" sourceType="string" target="_modelVersion"></flowable:eventOutParameter>
        <flowable:eventOutParameter xmlns:flowable="http://flowable.org/bpmn" source="_uri" sourceType="string" target="_uri"></flowable:eventOutParameter>
        <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_inbound_kafka_event]]></flowable:channelKey>
      </extensionElements>
    </startEvent>
    <serviceTask id="generateSchedulePayments" name="Generate Schedule Payments" flowable:async="true" flowable:parallelInSameTransaction="true" flowable:type="http">
      <extensionElements>
        <flowable:field name="requestMethod">
          <flowable:string><![CDATA[POST]]></flowable:string>
        </flowable:field>
        <flowable:field name="requestUrl">
          <flowable:expression><![CDATA[${HTTPHelper.serviceDiscoveryQuery(execution).serviceName('generatePayment').query()}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestHeaders">
          <flowable:expression><![CDATA[Authorization: ${HTTPHelper.systemUserToken()}
Accept-Encoding: identity]]></flowable:expression>
        </flowable:field>
        <flowable:field name="requestBody">
          <flowable:expression><![CDATA[{body:{"_key" : ${execution.getVariable('_key')}}}]]></flowable:expression>
        </flowable:field>
        <flowable:field name="responseVariableName">
          <flowable:string><![CDATA[payments]]></flowable:string>
        </flowable:field>
        <flowable:field name="saveResponseVariableAsJson">
          <flowable:expression><![CDATA[${true}]]></flowable:expression>
        </flowable:field>
      </extensionElements>
    </serviceTask>
    <subProcess id="initPayments" name="Init Payments">
      <multiInstanceLoopCharacteristics isSequential="false" flowable:collection="${payments.body.success.payments}" flowable:elementVariable="payload">
        <extensionElements></extensionElements>
      </multiInstanceLoopCharacteristics>
      <serviceTask id="initPayment" name="Init Payment" flowable:async="true" flowable:type="send-event" flowable:triggerable="true">
        <extensionElements>
          <flowable:eventType><![CDATA[V20_command]]></flowable:eventType>
          <flowable:triggerEventType><![CDATA[V20_response]]></flowable:triggerEventType>
          <flowable:eventInParameter sourceExpression="${requestId}" target="requestId" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter sourceExpression="{'entity': ${payload.paymentDetails}, 'originSource': ${payload.originSource}, 'paymentSchedule': ${payload.paymentSchedule}, 'paymentNetAmount': ${payload.paymentNetAmount}}" target="payload" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="1" target="_modelVersion" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="CapDentalPaymentDefinition" target="_modelName" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="initPayment" target="commandName" targetType="string"></flowable:eventInParameter>
          <flowable:eventInParameter source="payment" target="_variation" targetType="string"></flowable:eventInParameter>
          <flowable:channelKey xmlns:flowable="http://flowable.org/bpmn"><![CDATA[V20_outbound_kafka_command]]></flowable:channelKey>
          <flowable:triggerEventCorrelationParameter xmlns:flowable="http://flowable.org/bpmn" name="requestId" type="null" value="${requestId}"></flowable:triggerEventCorrelationParameter>
        </extensionElements>
      </serviceTask>
      <endEvent id="sid-6393FAD7-DC1A-4183-B5A1-3D90F45DB4F4"></endEvent>
      <startEvent id="sid-F977E652-A111-482D-9753-9D980BCDCEB3" flowable:formFieldValidation="true"></startEvent>
      <sequenceFlow id="sid-B124257B-B07F-43F6-BE19-754BDEC0163D" sourceRef="initPayment" targetRef="sid-6393FAD7-DC1A-4183-B5A1-3D90F45DB4F4"></sequenceFlow>
      <sequenceFlow id="sid-6B7F6106-6667-41F7-BB1A-C0A854C8CE1C" sourceRef="sid-F977E652-A111-482D-9753-9D980BCDCEB3" targetRef="initPayment"></sequenceFlow>
    </subProcess>
    <exclusiveGateway id="sid-6D5AD519-5475-4D53-AC34-9D68027842B7"></exclusiveGateway>
    <sequenceFlow id="sid-EDF9594C-9787-42A1-8B19-917AEA7A44D4" sourceRef="generateSchedulePayments" targetRef="sid-6D5AD519-5475-4D53-AC34-9D68027842B7"></sequenceFlow>
    <endEvent id="sid-16AF142D-21E3-4670-9E31-2A1AD8979466"></endEvent>
    <sequenceFlow id="sid-04429897-3994-4782-8CA5-853B8C1312A3" sourceRef="sid-6D5AD519-5475-4D53-AC34-9D68027842B7" targetRef="initPayments">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${payments.body.success.payments != null && payments.body.success.payments.size() > 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-FE40A105-5F12-43EF-AF3D-E5076A58BB5D" sourceRef="generatePaymentsEntryPoint" targetRef="generateSchedulePayments"></sequenceFlow>
    <sequenceFlow id="sid-6EB16CEC-C21F-4906-B34F-25D20A7E5CCA" sourceRef="sid-6D5AD519-5475-4D53-AC34-9D68027842B7" targetRef="sid-16AF142D-21E3-4670-9E31-2A1AD8979466">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${payments.body.success.payments == null || payments.body.success.payments.size() == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sid-08EF8D61-F796-4D4D-B360-425F101DB9EB" sourceRef="initPayments" targetRef="sid-16AF142D-21E3-4670-9E31-2A1AD8979466"></sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_generatePayment">
    <bpmndi:BPMNPlane bpmnElement="generatePayment" id="BPMNPlane_generatePayment">
      <bpmndi:BPMNShape bpmnElement="generatePaymentsEntryPoint" id="BPMNShape_generatePaymentsEntryPoint">
        <omgdc:Bounds height="30.0" width="30.499999999999993" x="45.00000000000001" y="265.00000000000006"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="generateSchedulePayments" id="BPMNShape_generateSchedulePayments">
        <omgdc:Bounds height="79.99999999999997" width="100.00000000000003" x="195.00000000000003" y="240.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initPayments" id="BPMNShape_initPayments">
        <omgdc:Bounds height="175.99999999999997" width="394.77776169188195" x="495.00000000000006" y="195.00000000000003"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="initPayment" id="BPMNShape_initPayment">
        <omgdc:Bounds height="80.0" width="99.99999999999994" x="642.3888808459411" y="232.9999973509049"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6393FAD7-DC1A-4183-B5A1-3D90F45DB4F4" id="BPMNShape_sid-6393FAD7-DC1A-4183-B5A1-3D90F45DB4F4">
        <omgdc:Bounds height="28.0" width="28.0" x="810.0000000000002" y="258.9999973509049"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-F977E652-A111-482D-9753-9D980BCDCEB3" id="BPMNShape_sid-F977E652-A111-482D-9753-9D980BCDCEB3">
        <omgdc:Bounds height="30.0" width="30.0" x="540.0000000000002" y="257.99999735090495"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-6D5AD519-5475-4D53-AC34-9D68027842B7" id="BPMNShape_sid-6D5AD519-5475-4D53-AC34-9D68027842B7">
        <omgdc:Bounds height="40.0" width="40.0" x="375.00000000000006" y="261.9999973509049"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sid-16AF142D-21E3-4670-9E31-2A1AD8979466" id="BPMNShape_sid-16AF142D-21E3-4670-9E31-2A1AD8979466">
        <omgdc:Bounds height="28.0" width="27.999999999999886" x="1005.0000000000001" y="269.00000000000006"></omgdc:Bounds>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="sid-B124257B-B07F-43F6-BE19-754BDEC0163D" id="BPMNEdge_sid-B124257B-B07F-43F6-BE19-754BDEC0163D" flowable:sourceDockerX="49.99999999999997" flowable:sourceDockerY="40.0" flowable:targetDockerX="14.0" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="742.3388808458849" y="272.9999973509049"></omgdi:waypoint>
        <omgdi:waypoint x="810.0000000000002" y="272.9999973509049"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6EB16CEC-C21F-4906-B34F-25D20A7E5CCA" id="BPMNEdge_sid-6EB16CEC-C21F-4906-B34F-25D20A7E5CCA" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="13.999999999999943" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="395.00000000000006" y="301.9454858177161"></omgdi:waypoint>
        <omgdi:waypoint x="395.0" y="503.0"></omgdi:waypoint>
        <omgdi:waypoint x="1019.0" y="503.0"></omgdi:waypoint>
        <omgdi:waypoint x="1019.0" y="296.94992302932593"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-6B7F6106-6667-41F7-BB1A-C0A854C8CE1C" id="BPMNEdge_sid-6B7F6106-6667-41F7-BB1A-C0A854C8CE1C" flowable:sourceDockerX="15.0" flowable:sourceDockerY="15.0" flowable:targetDockerX="49.99999999999997" flowable:targetDockerY="40.0">
        <omgdi:waypoint x="569.9499990318301" y="272.99999735090495"></omgdi:waypoint>
        <omgdi:waypoint x="642.388880845881" y="272.9999973509049"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-08EF8D61-F796-4D4D-B360-425F101DB9EB" id="BPMNEdge_sid-08EF8D61-F796-4D4D-B360-425F101DB9EB" flowable:sourceDockerX="197.38888084594097" flowable:sourceDockerY="87.99999999999999" flowable:targetDockerX="13.999999999999943" flowable:targetDockerY="14.0">
        <omgdi:waypoint x="889.727761691882" y="283.00000000000006"></omgdi:waypoint>
        <omgdi:waypoint x="1005.0000000000001" y="283.00000000000006"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-EDF9594C-9787-42A1-8B19-917AEA7A44D4" id="BPMNEdge_sid-EDF9594C-9787-42A1-8B19-917AEA7A44D4" flowable:sourceDockerX="50.000000000000014" flowable:sourceDockerY="39.999999999999986" flowable:targetDockerX="20.0" flowable:targetDockerY="20.0">
        <omgdi:waypoint x="294.95000000000005" y="280.66599911785136"></omgdi:waypoint>
        <omgdi:waypoint x="375.220906952471" y="281.7368398001468"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-FE40A105-5F12-43EF-AF3D-E5076A58BB5D" id="BPMNEdge_sid-FE40A105-5F12-43EF-AF3D-E5076A58BB5D" flowable:sourceDockerX="15.249999999999996" flowable:sourceDockerY="15.0" flowable:targetDockerX="50.000000000000014" flowable:targetDockerY="39.999999999999986">
        <omgdi:waypoint x="75.44999943717204" y="280.00000000000006"></omgdi:waypoint>
        <omgdi:waypoint x="195.00000000000003" y="280.0"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sid-04429897-3994-4782-8CA5-853B8C1312A3" id="BPMNEdge_sid-04429897-3994-4782-8CA5-853B8C1312A3" flowable:sourceDockerX="20.0" flowable:sourceDockerY="20.0" flowable:targetDockerX="197.38888084594097" flowable:targetDockerY="87.99999999999999">
        <omgdi:waypoint x="414.8798213300135" y="282.06685658713303"></omgdi:waypoint>
        <omgdi:waypoint x="494.9999999999976" y="282.3362582918824"></omgdi:waypoint>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>