@CapEndpoint("generatePayment")
Transformation CapDentalPaymentGeneration {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        *CapDentalPaymentDefinition.CapDentalPaymentEntity as payments
    }

    Mapping payments is Transform("CapDentalPaymentGenerationMapping", schedule).selectedPayments {
        Attr paymentDetails is paymentDetails
        Attr originSource is Root().schedule.originSource
        Attr paymentSchedule is ToExtLink(Root().schedule)
        Attr paymentNetAmount is paymentNetAmount
    }

}