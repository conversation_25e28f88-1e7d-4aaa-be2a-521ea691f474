#!/bin/bash

prefix="abc"
version=$component_build_artifacts_publish_version

folder_array=()
folder_with_prefix_array=()

project_core_module_name="dxp-core"
project_core_configuration_file_prefix="dxp.core"

file_build_sbt="build.sbt"
file_module_conf=".module.conf"
file_routes=".routes"
file_java=".java"
file_json=".json"


## Functions declaration

# Rename build parameter variables (2 parameters: 1 = file, 2 = parameter name)
function renameBuildParameterVariables() {
    local file_content="$(cat $1 | tr '\n' '@')"
    local variables="$(echo $file_content | awk -F ''$2'\\(' '{print $2}' | awk -F '\\)' '{print $1}')"
    local variables_with_prefix="$(echo $variables | sed 's/\s//g' | sed 's/,/,'${prefix}'/g' | tr -d '@')"
    variables_with_prefix="$(echo ${variables_with_prefix^} | sed -r 's/'${prefix}'([A-Za-z0-9])/'${prefix}'\U\1/g')"
    local new_file="$(echo $file_content | sed -r 's/'$2'\([^)]*/'$2'\('${prefix}${variables_with_prefix}'/g')"
    echo $new_file | tr '@' '\n' > $1
}

# Update "build.sbt" file content (parameters: 1 = file)
function updateBuildSbtFileContent() {
    # Check "build.sbt" file location (main/modules-nested)
    if [[ $1 == *"/modules"* ]];
    then
        # Add prefix to <project.in(file("..."))> methods
        project_files=($(cat $1 | awk -F 'file\\(\"' '{print $2}' | awk -F '\"' '{print $1}'))
        for project_file in "${project_files[@]}"
        do
            if [[ ${project_file} != "." ]];
            then
                sed -i -r 's/file\(\"'${project_file}'\"\)/file\(\"'${prefix}'-'${project_file}'\"\)/g' $1
            fi
        done
    else
        # Add prefix to <project.in(file("..."))> methods
        project_files=($(cat $1 | awk -F 'file\\(\"modules/' '{print $2}' | awk -F '\"' '{print $1}'))
        for project_file in "${project_files[@]}"
        do
            if [[ ${project_file} != "." ]];
            then
                sed -i -r 's/file\(\"modules\/'${project_file}'\"\)/file\(\"modules\/'${prefix}'-'${project_file}'\"\)/g' $1
            fi
        done

        # Rename to default "core" module
        sed -i -r 's/'${project_core_module_name}'/core/g' $1
    fi

    # Add prefix to <LocalProject("...")> methods
    sed -i -r 's/LocalProject\(\"([A-Za-z0-9])/LocalProject\(\"'${prefix}'\U\1/g' $1

    # Add prefix to <lazy val ...> variables
    sed -i -r 's/lazy val ([A-Za-z0-9])/lazy val '${prefix}'\U\1/g' $1

    # Add prefix to <dependsOn(...)> variables
    renameBuildParameterVariables $1 "dependsOn"

    # Add prefix to <aggregate(...)> variables
    renameBuildParameterVariables $1 "aggregate"

    # Check if module is publishing (not skipping)
    publish_skip="$(grep -c 'publish / skip := true' $1)"
    if [[ ${publish_skip} -eq 0 ]]
    then
      # Add binary dependency on base module
      echo "libraryDependencies += \"com.eisgroup.dxp\" % \"$library_module_name\" % \"$version\"" >> $1
    fi
}


# Remove "target" folders from project
rm -rf `find . -name "target" -type d`

# Process Dockerfile for project
docker_file=./Dockerfile
sed -i -r 's/([^b].{1})(gateway)/\1'${prefix}'\2/g' ${docker_file}

# Create codegen defaults file to apply vendor prefix for generated routes
mkdir ./conf/codegen && echo '{
  "swagger": "2.0",
  "x-dxp-defaults": {
    "vendor-prefix" : "'${prefix}'"
  }
}' > ./conf/codegen/persona.defaults.json

# Process modules "build.sbt" files
for file in `find ./modules -type f -name ${file_build_sbt}`
do
    module_file_path="$(dirname $file)"
    module_path="$(dirname $module_file_path)"
    module_name="$(basename $module_file_path)"
    library_module_name="$(echo $module_name | tr -d '-')"

    # Check if it's core module then remove all stuff, otherwise - clean up and rename
    if [[ $module_name == *${project_core_module_name}* ]];
    then
        # Override "build.sbt" file to contain only binary dependency
        echo -e "Common.projectSettings\n\npublish / skip := true" > $file

        # Remove all files and folders from core modules to support only binary dependencies (except Version Service)
        find $module_file_path -type f ! \( -name "*BindingModule.java" -o -name "*VersionService.java" -o -name "*${file_module_conf}" -o -name "*${file_routes}" -o -name ${file_build_sbt} \) -delete
        find $module_file_path -type d -empty -delete

        # Add folder name for further renaming
        folder_array+=("${module_path}/${module_name}")
        module_name="core"
        folder_with_prefix_array+=("${module_path}/${prefix}-${module_name}")
    else
        updateBuildSbtFileContent $file

        # Add folder name for further renaming
        folder_array+=("${module_path}/${module_name}")
        folder_with_prefix_array+=("${module_path}/${prefix}-${module_name}")
    fi
done

# Process main "build.sbt" file
updateBuildSbtFileContent ./${file_build_sbt}

# Update configuration file to use specific core module configuration
common_conf_file=./conf/common.conf
sed -i 's/'${project_core_configuration_file_prefix}'/'${prefix}'.'${project_core_configuration_file_prefix}'/g' ${common_conf_file}

# Update main routes file to use specific core module routes
gateway_routes_file=./conf/gateway.routes
sed -i 's/'${project_core_configuration_file_prefix}'/'${prefix}'.'${project_core_configuration_file_prefix}'/g' ${gateway_routes_file}

# Update BuildInfo package section for Version service
build_info_file=./project/BuildPluginsSettings.scala
build_info_file_content="$(cat ${build_info_file})"
build_info_package="$(echo ${build_info_file_content} | awk -F 'buildInfoPackage := \"' '{print $2}' | awk -F '\"' '{print $1}')"
sed -i 's/'${build_info_package}'/'${build_info_package}'.'${prefix}'/g' ${build_info_file}
find ./modules -type f -name "*VersionService.java" -print0 | xargs -0 sed -i 's/'${build_info_package}'/'${build_info_package}'.'${prefix}'/g'

# Add prefix to modules folders
SORTED_KEYS=$(
  for INDEX in ${!folder_array[@]}
  do
    echo "${folder_array[$INDEX]}:::$INDEX"
  done | sort | awk -F::: '{print $2}'
)

for KEY in $SORTED_KEYS
do
  mv ${folder_array[KEY]} ${folder_with_prefix_array[KEY]}
done

# Add prefix folder in source code </app> folder
for folder in `find ./modules -type d -name "app"`
do
    folder_items=($(ls $folder))
    for item in ${!folder_items[@]}
    do
       updated="$(echo ${folder_items[$item]} | sed 's/\///g')"
       if [ ! -d "${folder}/${prefix}" ]
       then
          mkdir "${folder}/${prefix}"
       fi

       mv "${folder}/${updated}" "${folder}/${prefix}/${updated}"
    done
done

# Add prefix to ".routes" files
for file in `find ./modules -type f -name "*${file_routes}"`
do
    file_module_path="$(dirname $file)"
    file_name="$(basename $file)"
    file_name_without_extension="${file_name%${file_routes}}"

    # Avoid "agent" and "core" modules changes as they use binary links
    if [[ ${file_module_path} != *"/${prefix}-agent/"* && ${file_module_path} != *"/${prefix}-core/"* ]]
    then
        # Add prefix to source code API routes lines (with Controller)
        sed -i -r 's/[ \t]([A-Za-z0-9\.]*Controller)/ '${prefix}'.\1/g' $file

        # Add prefix to generated code API routes lines
        sed -i -r 's/[ \t]([A-Za-z0-9\.]*\.generated\.Routes)/ '${prefix}'.\1/g' $file
    fi

    # Find and replace file links
    for fileinside in `find ./modules -type f -name "*${file_routes}" -print0 | xargs -0 grep -l "${file_name_without_extension}"`
    do
        sed -i -r 's/[ \t]'${file_name_without_extension}'/ '${prefix}'.'${file_name_without_extension}'/g' ${fileinside}
    done

    # Rename file with prefix
    mv "${file_module_path}/${file_name}" "${file_module_path}/${prefix}.${file_name}"
done

# Add prefix to ".module.conf" files
for file in `find ./modules -type f -name "*${file_module_conf}"`
do
    file_module_path="$(dirname $file)"
    file_name="$(basename $file)"

    # Add prefix to "play.modules.enabled" lines
    sed -i -r 's/(play.modules.enabled[ ]*\+\=[ ]*\")/\1'${prefix}'./g' $file

    # Find and replace file links
    for fileinside in `find ./modules -type f -name "*${file_module_conf}" -print0 | xargs -0 grep -l "${file_name}"`
    do
        sed -i -r 's/'${file_name}'/'${prefix}'.'${file_name}'/g' ${fileinside}
    done

    # Rename file with prefix
    mv "${file_module_path}/${file_name}" "${file_module_path}/${prefix}.${file_name}"
done

# Add prefix to ".json" files
for file in `find ./modules -type f -name "*${file_json}"`
do
    file_folder="$(dirname $file)"
    file_name="$(basename $file)"
    folder_path="$(dirname $file_folder)"
    folder_name="$(basename $file_folder)"

    # Add prefix to each schema mapping in persona schemas
    schema_mapping="$(cat $file | grep -o -P '(?<=schema\"\:).*(?=\'${file_json}')' | sed 's/"//g' | xargs)"
    if [ -n "$schema_mapping" ];
    then
        sed -i 's/'${schema_mapping}'/'${prefix}'.'${schema_mapping}'/g' $file
    fi

    # Rename file with prefix
    mv "${file_folder}/${file_name}" "${file_folder}/${prefix}.${file_name}"
done

# Add prefix to ".java" files
for file in `find ./modules -type f -name "*${file_java}"`
do
    file_path="$(dirname $file)"
    file_name="$(basename $file)"
    file_name_without_extension="${file_name%${file_java}}"

    # Add prefix to "package" line
    sed -i -r 's/(^package )/\1'${prefix}'./g' $file

    # Find and replace lines in other files
    for fileinside in `find ./modules -type f -name "*${file_java}" -print0 | xargs -0 grep -l "${file_name_without_extension}[^A-Za-z0-9]*"`
    do
        # Add prefix to import lines
        sed -i -r 's/((import static|import) )(.*\b'${file_name_without_extension}'\b)/\1'${prefix}'.\3/g' $fileinside

        # Add prefix to dataType lines (request models)
        sed -i -r 's/(dataType[ ]*=[ ]*\")(.*\b'${file_name_without_extension}'\b)/\1'${prefix}'.\2/g' $fileinside

        # Remove duplicated prefix (in case with same java class names in project)
        sed -i -r 's/('${prefix}'.){2,}/'${prefix}'./g' $fileinside
    done
done
