<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
        <artifactId>ms-claim-dental-app-pom</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ms-claim-dental-app</artifactId>
    <packaging>jar</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>dependency-list</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>list</goal>
                        </goals>
                        <configuration>
                            <outputFile>target/classes/dependency.list</outputFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <classifier>fat</classifier>
                    <attach>false</attach>
                    <mainClass>com.eisgroup.genesis.boot.Bootstrap</mainClass>
                </configuration>
            </plugin>
            <!-- redefined for ordering purposes,
                have to be executed after spring boot package plugin -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <version>${dockerfile-maven-plugin.version}</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.eisgroup.genesis.tools</groupId>
                <artifactId>fintrospector-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>com.eisgroup.genesis.tools</groupId>
                <artifactId>fgenerator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <configuration>
                            <inputDirectory>${project.build.directory}/classes</inputDirectory>
                            <outputDirectory>${project.build.directory}/classes/facade-schema</outputDirectory>
                            <generators>
                                <generator>
                                    <groupId>com.eisgroup.genesis.facade</groupId>
                                    <artifactId>facade-fgenerator</artifactId>
                                    <version>${facade.framework.version}</version>
                                </generator>
                            </generators>
                            <includes>
                                <include>SwaggerGenerator</include>
                            </includes>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>com.eisgroup.genesis.tools</groupId>
                <artifactId>fgenerator-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>prepare-package</phase>
                        <configuration>
                            <inputDirectory>${project.build.directory}/classes</inputDirectory>
                            <outputDirectory>${project.build.directory}/classes/security</outputDirectory>
                            <generators>
                                <generator>
                                    <groupId>com.eisgroup.genesis.security</groupId>
                                    <artifactId>security-fgenerator</artifactId>
                                    <version>${security.version}</version>
                                </generator>
                            </generators>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/banner.txt</include>
                </includes>
            </resource>
        </resources>
    </build>

    <profiles>
        <profile>
            <id>generate-localization</id>
            <activation>
                <property>
                    <name>localization</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>com.eisgroup.genesis.tools</groupId>
                        <artifactId>fgenerator-maven-plugin</artifactId>
                        <version>${fgenerator.maven.plugin.version}</version>
                        <executions>
                            <execution>
                                <id>generate-localization</id>
                                <phase>prepare-package</phase>
                                <goals>
                                    <goal>generate</goal>
                                </goals>
                                <configuration>
                                    <!-- Can be omitted -->
                                    <properties>
                                        <!-- Whether to generate MT bundle for testing, true by default -->
                                        <generateMtBundle>true</generateMtBundle>
                                        <!-- Whether to set the default value in the output, true by default -->
                                        <writeDefault>true</writeDefault>
                                    </properties>
                                    <outputDirectory>${project.build.sourceDirectory}/../resources/dental</outputDirectory>
                                    <generators>
                                        <generator>
                                            <groupId>com.eisgroup.genesis.platform</groupId>
                                            <artifactId>localization-fgenerator</artifactId>
                                            <version>${localization.fgenerator.version}</version>
                                        </generator>
                                    </generators>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <!-- Fat-jar assembly -->
        <dependency>
            <groupId>com.eisgroup.genesis.apps</groupId>
            <artifactId>spring-app-bundle</artifactId>
            <classifier>app</classifier>
            <type>tile</type>
        </dependency>

        <!-- Loss Applications -->
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-claim-dental-commands-app</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-claim-dental-batch-commands-app</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-claim-dental-events-app</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
                    <artifactId>cap-claim-dental-lifecycle-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-claim-dental-facade-app</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eisgroup.genesis.ref.cap.dental</groupId>
                    <artifactId>cap-claim-dental-lifecycle-model</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- Dental unverified policy -->
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-dental-up-commands-app</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-dental-up-facade-app</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- Service discovery -->
        <dependency>
            <groupId>com.eisgroup.genesis.discovery</groupId>
            <artifactId>service-discovery-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.discovery</groupId>
            <artifactId>service-discovery-bundle</artifactId>
            <type>tile</type>
            <classifier>facade</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.discovery</groupId>
            <artifactId>service-discovery-bundle</artifactId>
            <type>tile</type>
            <classifier>integration</classifier>
        </dependency>

        <!-- Dependency to decorate lifecycle events with absence identifier data -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-stream-consumer</artifactId>
        </dependency>

        <!-- Versioning. Dependency to decorate lifecycle events with entity difference data -->
        <dependency>
            <groupId>com.eisgroup.genesis.domain</groupId>
            <artifactId>entity-comparison-lifecycle-integration</artifactId>
        </dependency>

        <!-- Should be removed, when GENESIS-51600 is fixed -->
        <dependency>
            <groupId>com.eisgroup.genesis.search</groupId>
            <artifactId>search-integration</artifactId>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-rx-debug</artifactId>
        </dependency>

        <!-- External models -->
        <dependency>
            <groupId>com.eisgroup.genesis.model</groupId>
            <artifactId>external-models-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.facade</groupId>
            <artifactId>facade-bundle</artifactId>
            <type>tile</type>
        </dependency>


        <!-- Decision tiles -->
        <dependency>
            <groupId>com.eisgroup.genesis.decision</groupId>
            <artifactId>rules.decision-bundle</artifactId>
            <classifier>commands</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.decision</groupId>
            <artifactId>rules.decision-bundle</artifactId>
            <classifier>facade</classifier>
            <type>tile</type>
        </dependency>
        <dependency>
            <groupId>com.eisgroup.genesis.decision</groupId>
            <artifactId>rules.decision-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-search-request-reindex-events</artifactId>
        </dependency>

        <!-- Deployer dependencies -->

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-claim-dental-deployer-app</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eisgroup.genesis.facade</groupId>
                    <artifactId>entity-facade</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.ref.cap.applications</groupId>
            <artifactId>cap-dental-up-deployer-app</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-deployer</artifactId>
        </dependency>

        <!-- Metadata -->
        <dependency>
            <groupId>com.eisgroup.genesis.application.metadata</groupId>
            <artifactId>app-metadata-bundle</artifactId>
            <type>tile</type>
        </dependency>

        <!-- External models deployment -->
        <dependency>
            <groupId>com.eisgroup.genesis.model</groupId>
            <artifactId>external-models-bundle</artifactId>
            <type>tile</type>
            <classifier>deployer</classifier>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.decision</groupId>
            <artifactId>rules.decision-bundle</artifactId>
            <classifier>deployer</classifier>
            <type>tile</type>
        </dependency>

        <!-- Documentation -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-documentation</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.mock</groupId>
            <artifactId>cap-mock-resolver</artifactId>
        </dependency>

        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-mock</artifactId>
        </dependency>

        <!-- Transformation endpoint service discovery -->
        <dependency>
            <groupId>com.eisgroup.genesis.cap.common</groupId>
            <artifactId>cap-common-transformation-discovery</artifactId>
            <version>${cap.core.version}</version>
        </dependency>
    </dependencies>

</project>
