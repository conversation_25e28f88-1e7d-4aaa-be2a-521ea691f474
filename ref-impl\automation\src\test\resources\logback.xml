<?xml version="1.0" encoding="UTF-8" ?>
<!--
  ~ Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
  ~ CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
  -->

<configuration>

    <define name="rootLevel" class="com.exigen.istf.utils.logging.CustomLoggerPropertyProvider">
        <name>tzappa-v20.loglevel</name>
        <defaultValue>INFO</defaultValue>
    </define>


    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{ISO8601} %t %5p %t %c{1}:%M:%L %m%n</pattern>
        </encoder>
    </appender>
    <appender name="testng" class="com.exigen.istf.utils.logging.TestNGReporterAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <pattern>%d{ISO8601} %t %5p %t %c{1}:%M:%L %m%n</pattern>
    </appender>


    <appender name="testLog" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator class="com.exigen.istf.utils.logging.CustomLoggerDiscriminator">
            <key>ISTF-LogFileName</key>
        </discriminator>
        <sift>
            <appender name="TEST-${ISTF-LogFileName}" class="ch.qos.logback.core.FileAppender">
                <file>${ISTF-LogFileName}</file>
                <append>true</append>
                <encoder>
                    <pattern>%d{ISO8601} %t %5p %t %c{1}:%M:%L %m%n</pattern>
                </encoder>
            </appender>
        </sift>
    </appender>

    <logger name="org.openqa" level="OFF"/>
    <logger name="com.atlassian.jira.rest.client.internal.async" level="OFF"/>
    <logger name="com.exigen.istf.verification.StackTraceUtils" level="OFF"/>
    <logger name="com.exigen.istf.exec" level="${rootLevel}"/>
    <logger name="com.exigen.istf.sync" level="${rootLevel}"/>
    <!--<logger name="com.datastax.driver.core" level="OFF" />-->
    <root level="${rootLevel}">
        <appender-ref ref="console"/>
        <appender-ref ref="testLog"/>
        <appender-ref ref="testng"/>
    </root>
</configuration>