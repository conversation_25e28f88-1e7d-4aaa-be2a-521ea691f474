#!/bin/bash

# Existing vendor prefix in "ref-impl" project
existing_vendor_prefix="abc"

# Enter new vendor prefix to be applied in the project
new_vendor_prefix="eis"


folders_with_old_prefix=()
folders_with_new_prefix=()

# Rename vendor prefix (0 parameters)
function renameVendorPrefix() {
    sed "s#"${existing_vendor_prefix}"#"${new_vendor_prefix}"#g"
}

# Rename vendor prefix in file (1 parameter: 1 = file)
function renameVendorPrefixInFile() {
    sed -i '' "s#"${existing_vendor_prefix}"#"${new_vendor_prefix}"#g" $1
}

# Rename vendor prefix in name (1 parameter: 1 = folder, file, etc.)
function renameVendorPrefixInName() {
    entity_path="$(dirname $1)"
    entity_name="$(basename $1)"
    new_entity_name="$(echo ${entity_name} | renameVendorPrefix)"

    if [[ "$new_entity_name" != "$entity_name" ]];
    then
        mv "${entity_path}/${entity_name}" "${entity_path}/${new_entity_name}"
    fi
}


# Remove "target" folders from project
rm -rf `find . -name "target" -type d`

# Rename vendor prefix in Dockerfile
renameVendorPrefixInFile ./Dockerfile

# Rename vendor prefix in codegen defaults file
renameVendorPrefixInFile ./conf/codegen/persona.defaults.json

# Rename vendor prefix in all project files
for file in `find ./modules -type f`
do
    renameVendorPrefixInFile ${file}
    renameVendorPrefixInName ${file}
done

# Rename vendor prefix in all project modules name
for file in `find ./modules -type f -name "build.sbt"`
do
    module_file_path="$(dirname $file)"
    module_path="$(dirname $module_file_path)"
    module_name="$(basename $module_file_path)"
    new_module_name="$(echo $module_name | renameVendorPrefix)"

    # Add folder name for further renaming
    folders_with_old_prefix+=("${module_path}/${module_name}")
    folders_with_new_prefix+=("${module_path}/${new_module_name}")
done

SORTED_KEYS=$(
  for INDEX in ${!folders_with_old_prefix[@]}
  do
    echo "${folders_with_old_prefix[$INDEX]}:::$INDEX"
  done | sort | awk -F::: '{print $2}'
)

for KEY in $SORTED_KEYS
do
  mv ${folders_with_old_prefix[KEY]} ${folders_with_new_prefix[KEY]}
done

# Rename vendor prefix in all project folders name
for folder in `find ./modules -type d`
do
    renameVendorPrefixInName ${folder}
done

# Rename vendor prefix in main "build.sbt" file
renameVendorPrefixInFile ./build.sbt

# Rename vendor prefix in configuration file
renameVendorPrefixInFile ./conf/common.conf

# Rename vendor prefix in main routes file
renameVendorPrefixInFile ./conf/gateway.routes

# Rename vendor prefix in BuildInfo package section and Version service
renameVendorPrefixInFile ./project/BuildPluginsSettings.scala
for file in `find ./modules -type f -name "*VersionService.java"`
do
    renameVendorPrefixInFile $file
done
