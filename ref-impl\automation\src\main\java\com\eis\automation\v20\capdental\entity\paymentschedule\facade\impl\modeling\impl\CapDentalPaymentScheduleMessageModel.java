/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.claimsettlement.common.facade.impl.modeling.impl.MessageTypeModel;
import com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf.ICapDentalPaymentScheduleMessageModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentScheduleMessageModel extends MessageTypeModel implements ICapDentalPaymentScheduleMessageModel {
}
