/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.financial.command.config.CapPaymentBaseLifecycleConfig;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateCloseHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.CapDentalPaymentTemplateUpdateHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentTemplateLifecycleConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;
import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.Collection;

public class CapDentalPaymentTemplateLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        Collection<CommandHandler<?, ?>> handlers = new ArrayList<>();

        handlers.add(new CapDentalPaymentTemplateInitHandler());
        handlers.add(new CapDentalPaymentTemplateUpdateHandler());
        handlers.add(new CapDentalPaymentTemplateCloseHandler());

        return handlers;
    }

    @Override
    public String getModelName() {
        return "CapDentalPaymentTemplate";
    }

    public String getModelVersion() {
        return "1";
    }

    public String getModelType() {
        return "CapPaymentTemplate";
    }

    public String getStateMachineName() {
        return getModelName();
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[] {
                CapDentalPaymentTemplateLifecycleConfig.class,
                CapPaymentBaseLifecycleConfig.class,
                CapBaseCommandConfig.class
        };
    }
}
