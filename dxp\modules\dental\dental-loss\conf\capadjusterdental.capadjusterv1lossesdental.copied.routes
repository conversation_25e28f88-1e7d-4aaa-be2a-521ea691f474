POST        /losses-dental        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1CommandCreateLossPost()
PUT        /losses-dental        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1CommandUpdateLossPost()
POST        /losses-dental/draft        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1CommandInitLossPost()
POST        /losses-dental/rules/:entryPoint        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1RulesEntryPointPost(entryPoint:String,delta:java.lang.Boolean ?= null)
GET        /losses-dental/:rootId/:revisionNo        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(rootId: java.util.UUID,revisionNo:Integer,embed:java.lang.String ?= null,fields:java.lang.String ?= null)
POST        /losses-dental/:rootId/:revisionNo/submit        com.eisgroup.dxp.controllers.capadjusterdental.CapAdjusterDentalCapAdjusterv1lossesDentalApiController.apiCaplossCapDentalLossV1CommandSubmitLossPost(rootId:java.util.UUID,revisionNo:Long)







