/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf;

import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;

import java.time.LocalDateTime;
import java.util.List;

public interface ICapDentalServiceDataModel extends ITypeModel {

    String getProcedureType();

    String getDecision();

    void setDecision(String decision);

    String getCdtSubmittedCd();

    String getCdtCoveredCd();

    void setCdtCoveredCd(String cdtCoveredCd);

    String getToothArea();

    List<String> getSurfaces();

    List<String> getToothCodes();

    Integer getQuantity();

    Boolean getIsProcedureAuthorized();

    Boolean getIsPredet();

    LocalDateTime getDOSDate();

}
