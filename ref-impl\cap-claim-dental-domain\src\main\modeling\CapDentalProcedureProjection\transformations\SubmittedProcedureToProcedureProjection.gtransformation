@KeyStrategy("FRESH_KEYS")
@Indexing
Transformation SubmittedProcedureToProcedureProjection {
    Input {
        CapDentalSettlement.CapDentalSettlementEntity as settlement
        CapDentalSettlement.CapDentalProcedureEntity as procedure
    }
    Output {
        CapDentalProcedure.CapDentalProcedureProjection
    }

    Var claimAttributes is resolveAttributes(settlement)
    Attr uri is ToExtLink(procedure)
    Attr claimNumber is claimAttributes.claimNumber
    Attr policyNumber is claimAttributes.policyNumber
    Attr patientNumber is claimAttributes.patientNumber
    Attr planCategory is claimAttributes.planCategory
    Attr DOSDate is AsTime(procedure.dateOfService)
    Attr toothCodes is procedure.toothCodes
    Attr providerTIN is procedure.dentist.providerTIN
    Attr state is "Active"

    Producer resolveAttributes(settlement) {
        Var capLoss is ExtLink(settlement.claimLossIdentification)
        Attr claimNumber is capLoss.lossNumber
        Attr policyNumber is capLoss.policyId

        //GENESIS-366362
        Attr patientNumber is ExtLink(AsExtLink(settlement.settlementLossInfo.claimData.patientRole.registryId)).customerNumber
         //TODO we are supposed to take it from masterPolicyInfo but the mapping from Policy is not fully done, CapDentalPolicyInfoEntity	planCategory
        Attr planCategory is "PPO"
    }
}