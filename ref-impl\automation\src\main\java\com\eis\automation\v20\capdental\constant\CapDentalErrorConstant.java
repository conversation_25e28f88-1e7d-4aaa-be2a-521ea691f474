/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.constant;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.ErrorConstant;

public class CapDentalErrorConstant {

    public static final ErrorConstant RECEIVED_DATE_IS_MANDATORY = new ErrorConstant("MandatoryClaimReceivedDate", "receivedDate is mandatory.");

    public static final ErrorConstant PAYEE_SOURCE_IS_MANDATORY = new ErrorConstant("MandatoryClaimSource", "payee source is mandatory.");

    public static final ErrorConstant SOURCE_FOR_CLAIM_IS_MANDATORY = new ErrorConstant("MandatoryClaimSource", "source for the claim is mandatory.");

    public static final ErrorConstant PAYEE_TYPE_IS_MANDATORY = new ErrorConstant("MandatoryClaimPayeeType", "payeeType is mandatory.");

    public static final ErrorConstant TRANSACTION_TYPE_IS_MANDATORY = new ErrorConstant("MandatoryClaimTransactionType", "transactionType is mandatory.");

    public static final ErrorConstant PATIENT_IS_MANDATORY = new ErrorConstant("MandatoryClaimPatient", "patient is mandatory.");

    public static final ErrorConstant POLICY_HOLDER_IS_MANDATORY = new ErrorConstant("MandatoryClaimPolicyHolder", "policyHolder is mandatory.");

    public static final ErrorConstant PROVIDER_IS_MANDATORY = new ErrorConstant("MandatoryClaimProvider", "provider is mandatory when the isUnknownOrIntProvider flag is not true.");

    public static final ErrorConstant PROVIDER_FEE_TYPE_IS_MANDATORY = new ErrorConstant("MandatoryClaimProviderFeeType", "providerFee.type is mandatory.");

    public static final ErrorConstant PROVIDER_FEE_FEE_IS_MANDATORY = new ErrorConstant("MandatoryClaimProviderFee", "providerFee.fee is mandatory.");

    public static final ErrorConstant DISCOUNT_TYPE_IS_MANDATORY = new ErrorConstant("MandatoryClaimDiscountType", "discountType is mandatory.");

    public static final ErrorConstant DISCOUNT_NAME_IS_MANDATORY = new ErrorConstant("MandatoryClaimDiscountName", "discountName is mandatory.");

    public static final ErrorConstant SUBMITTED_FEE_FOR_PROCEDURE_IS_MANDATORY = new ErrorConstant("MandatorySubmittedFee", "submittedFee for procedure is mandatory.");

    public static final ErrorConstant PROCEDURE_CODE_IS_MANDATORY = new ErrorConstant("MandatoryProcedureCode", "procedureCode is mandatory.");

    public static final ErrorConstant ALTERNATE_PAYEE_IS_MANDATORY = new ErrorConstant("MandatoryAlternatePayee", "alternatePayee is mandatory.");

    // Rule deleted cause GENESIS-165841
    public static final ErrorConstant PAYMENT_FREQUENCY_FOR_ORTHODONTIC_SERVICE_IS_MANDATORY = new ErrorConstant("MandatoryOrthodonticFrequency", "Payment frequency for Orthodondic service is mandatory.");

    public static final ErrorConstant DISCOUNT_PERCENTAGE_IS_MANDATORY = new ErrorConstant("MandatoryClaimDiscountPercentage", "discountPercentage is mandatory.");

    public static final ErrorConstant DISCOUNT_AMOUNT_IS_MANDATORY = new ErrorConstant("MandatoryClaimDiscountAmount", "discountAmount is mandatory.");

    public static final ErrorConstant ORTHO_DETAILS_ARE_REQIRED_AND_ONLY_ONE_ORTHO_SERVICE_CAN_BE_PROVIDED = new ErrorConstant("MandatoryOneOrthoServiceForClaim", "ortho details are required and only one Ortho service can be provided.");

    public static final ErrorConstant CLAIM_LOSS_IDENTIFICATION_LINK_TO_CLAIM_IS_MANDATORY = new ErrorConstant("MandatoryClaimURI", "A claimLossIdentification link to Claim is mandatory.");

    public static final ErrorConstant THIS_COMMAND_DOES_NOT_ACCEPT_REASONCD_ATTRIBUTE = new ErrorConstant("clri002", "This command does not accept reasonCd attribute.");

    public static final ErrorConstant THIS_COMMAND_DOES_NOT_ACCEPT_REASON_DESCRIPTION_ATTRIBUTE = new ErrorConstant("clri001", "This command does not accept reasonDescription attribute.");

    public static final ErrorConstant THIS_COMMAND_DOES_NOT_ACCEPT_REASON_DESCRIPTION_ATTRIBUTE_CLOSE = new ErrorConstant("clci001", "This command does not accept reasonDescription attribute.");

    public static final ErrorConstant THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_SETTLEMENT_IS_IN_APPROVED_STATE = new ErrorConstant("clc001", "This command cannot be performed when Settlement is in Approved state.");

    public static final ErrorConstant CAPDNREASON_LOOKUP_CODE_MUST_BE_ONE_OF_THEESE_VALUES_DENIED_PAID_CANCELED_PREDETERMINED = new ErrorConstant("lcv0001", "CapDNReason lookup code must be one of these values: [Denied, Paid, Canceled, Predetermined]");

    public static final ErrorConstant THIS_COMMAND_CANNOT_BE_PERFORMED_WHEN_LOSS_IS_IN_CLOSED_STATE = new ErrorConstant("dsed-001", "This command cannot be performed when Loss is in Closed state.");

    public static final ErrorConstant CLAIM_IS_DENIED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_ALLOWED_OVERRIDE = new ErrorConstant("AssertClaimOverideDenied", "Claim isDenied cannot be used at the same time with isAllowed override.");

    public static final ErrorConstant CLAIM_OVERRIDE_IS_ALLOWED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_DENIED_OVERRIDE = new ErrorConstant("AssertClaimOverideAllowed", "Claim override isAllowed cannot be used and the same time with isDenied override.");

    public static final ErrorConstant SERVICE_OVERRIDE_IS_ALLOWED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_DENIED_OVERRIDE = new ErrorConstant("AssertServiceOverideAllowed", "Service override isAllowed cannot be used and the same time with isDenied override.");

    public static final ErrorConstant SERVICE_OVERRIDE_IS_DENIED_CANNOT_BE_USED_AT_THE_SAME_TIME_WITH_IS_ALLOWED_OVERRIDE = new ErrorConstant("AssertServiceOverideDenied", "Service override isDenied cannot be used at the same time with isAllowed override.");

    public static final ErrorConstant OVERRIDE_ORTHO_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ClaimOverrideOrthoWaitingPeriodHigherThanZero", "overrideOrthoWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant OVERRIDE_DEDUCTIBLE_CANNOT_BE_NEGATIVE = new ErrorConstant("ServiceOverrideDeductibleEqualOrHigherThanZero", "overrideDeductible cannot be negative.");

    public static final ErrorConstant OVERRIDE_MAXIMUM_AMOUNT_CANNOT_BE_NEGATIVE = new ErrorConstant("ServiceOverrideMaximumAmountEqualOrHigherThanZero", "overrideMaximumAmount cannot be negative.");

    public static final ErrorConstant SERVICE_OVERRIDE_GRACE_PERIOD_CANNOT_BE_NEGATIVE = new ErrorConstant("ServiceOverrideGracePeriodEqualOrHigherThanZero", "Service overrideGracePeriod cannot be negative.");

    public static final ErrorConstant SERVICE_OVERRIDE_PAYMENT_INTEREST_AMOUNT_CANNOT_BE_NEGATIVE = new ErrorConstant("ServiceOverridePaymentInterestAmountEqualOrHigherThanZero", "Service overridePaymentInterestAmount cannot be negative.");

    public static final ErrorConstant CLAIM_OVERRIDE_PAYMENT_INTEREST_DAYS_CANNOT_BE_NEGATIVE = new ErrorConstant("ClaimOverridePaymentInterestDaysEqualOrHigherThanZero", "Claim overridePaymentInterestDays cannot be negative.");

    public static final ErrorConstant OVERRIDE_PREVENTIVE_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ClaimOverridePreventiveWaitingPeriodHigherThanZero", "overridePreventiveWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant CLAIM_OVERRIDE_LATE_ENTRANT_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ClaimOverrideLateEntrantWaitingPeriodHigherThanZero", "Claim overrideLateEntrantWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant OVERRIDE_BASIC_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ClaimOverrideBasicWaitingPeriodHigherThanZero", "overrideBasicWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant OVERRIDE_MAJOR_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ClaimOverrideMajorWaitingPeriodHigherThanZero", "overrideMajorWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant SERVICE_OVERRIDE_LATE_ENTRANT_WAITING_PERIOD_HAS_TO_BE_GREATER_THAN_0 = new ErrorConstant("ServiceOverrideLateEntrantWaitingPeriodHigherThanZero", "Service overrideLateEntrantWaitingPeriod has to be greater than 0.");

    public static final ErrorConstant CLAIM_OVERRIDE_PAYMENT_INEREST_AMOUNT_CANNOT_BE_NEGATIVE = new ErrorConstant("ClaimOverridePaymentInterestAmountEqualOrHigherThanZero", "Claim overridePaymentInterestAmount cannot be negative.");

}
