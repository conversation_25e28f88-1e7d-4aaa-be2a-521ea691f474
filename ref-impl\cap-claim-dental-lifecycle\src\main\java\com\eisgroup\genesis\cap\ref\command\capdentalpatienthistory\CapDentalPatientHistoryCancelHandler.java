/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory;

import static com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryCommands.CANCEL_PATIENT_HISTORY;

import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.model.ModelResolver;


/**
 * Dental specific handler for cancelPatientHistory command.
 *
 * <AUTHOR>
 * @since 22.9
 */
@Modifying
public class CapDentalPatientHistoryCancelHandler implements ProductCommandHandler<IdentifierRequest, CapDentalPatientHistoryEntity> {

    @Autowired
    private CapDentalPatientHistoryRepository capDentalPatientHistoryRepository;

    @Autowired
    private ModelResolver modelResolver;

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity load(@Nonnull IdentifierRequest identifierRequest) {
        return capDentalPatientHistoryRepository.load(identifierRequest.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity execute(@Nonnull IdentifierRequest identifierRequest, @Nonnull CapDentalPatientHistoryEntity patientHistoryEntity) {
        return patientHistoryEntity;
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity save(@Nonnull IdentifierRequest identifierRequest, @Nonnull CapDentalPatientHistoryEntity patientHistoryEntity) {
        return capDentalPatientHistoryRepository.save(patientHistoryEntity).get();
    }

    @Override
    public String getName() {
        return CANCEL_PATIENT_HISTORY;
    }
}
