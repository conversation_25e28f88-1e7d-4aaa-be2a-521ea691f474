/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.financial.command.config.CapPaymentBaseLifecycleConfig;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.*;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryCancelHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalrecovery.CapDentalRecoveryUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentApproveHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentCancelHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentClearHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentDeclineHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentDisapproveHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentInitHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentIssueHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentRequestIssueHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentRequestStopHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentStopHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.underpayment.CapDentalUnderpaymentVoidHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalCancelOverpaymentWaiveHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalOverpaymentInitWaiveHandler;
import com.eisgroup.genesis.cap.ref.command.waive.CapDentalOverpaymentUpdateWaiveHandler;
import com.eisgroup.genesis.cap.ref.module.config.CapDentalPaymentDefinitionLifecycleConfig;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.lifecycle.CommandHandler;
import com.eisgroup.genesis.lifecycle.LifecycleModule;
import com.eisgroup.genesis.lifecycle.statemachine.StatefulLifecycle;
import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.Collection;

public class CapDentalPaymentDefinitionLifecycleModule implements LifecycleModule, StatefulLifecycle {

    @Nonnull
    @Override
    public Collection<CommandHandler<?, ?>> getCommands() {
        Collection<CommandHandler<?, ?>> handlers = new ArrayList<>();

        handlers.add(new CapDentalPaymentApproveHandler());
        handlers.add(new CapDentalPaymentCancelHandler());
        handlers.add(new CapDentalPaymentClearHandler());
        handlers.add(new CapDentalPaymentInitHandler());
        handlers.add(new CapDentalPaymentIssueHandler());
        handlers.add(new CapDentalPaymentRequestStopHandler());
        handlers.add(new CapDentalPaymentStopHandler());
        handlers.add(new CapDentalPaymentUpdateHandler());
        handlers.add(new CapDentalPaymentVoidHandler());
        handlers.add(new CapDentalPaymentRequestIssueFailHandler());
        handlers.add(new CapDentalPaymentRequestIssueHandler());
        handlers.add(new CapDentalProcessPaymentLifecycleHandler());
        handlers.add(new CapDentalPaymentDeclineHandler());

        handlers.add(new CapDentalRecoveryInitHandler());
        handlers.add(new CapDentalRecoveryUpdateHandler());
        handlers.add(new CapDentalRecoveryCancelHandler());

        handlers.add(new CapDentalOverpaymentInitWaiveHandler());
        handlers.add(new CapDentalOverpaymentUpdateWaiveHandler());
        handlers.add(new CapDentalCancelOverpaymentWaiveHandler());

        handlers.add(new CapDentalUnderpaymentApproveHandler());
        handlers.add(new CapDentalUnderpaymentCancelHandler());
        handlers.add(new CapDentalUnderpaymentClearHandler());
        handlers.add(new CapDentalUnderpaymentDeclineHandler());
        handlers.add(new CapDentalUnderpaymentDisapproveHandler());
        handlers.add(new CapDentalUnderpaymentInitHandler());
        handlers.add(new CapDentalUnderpaymentIssueHandler());
        handlers.add(new CapDentalUnderpaymentRequestIssueHandler());
        handlers.add(new CapDentalUnderpaymentRequestStopHandler());
        handlers.add(new CapDentalUnderpaymentStopHandler());
        handlers.add(new CapDentalUnderpaymentUpdateHandler());
        handlers.add(new CapDentalUnderpaymentVoidHandler());


        return handlers;
    }

    @Override
    public String getModelName() {
        return "CapDentalPaymentDefinition";
    }

    public String getModelVersion() {
        return "1";
    }

    public String getModelType() {
        return "CapPayment";
    }

    public String getStateMachineName() {
        return getModelName();
    }

    @Override
    public Object[] getConfigResources() {
        return new Object[] {
                CapBaseCommandConfig.class,
                CapDentalPaymentDefinitionLifecycleConfig.class,
                CapTransformationCommandsConfig.class,
                CapPaymentBaseLifecycleConfig.class
        };
    }
}
