# Default values for ms-claim-dental.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ""
  tag: ""
  pullPolicy: Always
      
  dxpClaimDentalApp:
    name: dxp-claim-benefits-dental-app
    ports:
      tcp-debug: 1045
      tcp-jmxrmi: 1099
      http-jmxjolokia: 8778
      http-metrics: 9090
      http-app: 9191
      tcp-jacoco: 6300
      tcp-liveness: 36988
      tcp-readiness: 36999
      
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

ingress:
  enabled: true
  istio: false
  useClusterGateway: true # If false namespace gateway will be used
  domain_prefix: ""
  domain: ""
  annotations: {}
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  hosts:
    - host: dxp-claim-benefits-dental-app # FQDN template: {{ .host }}-{{ $namespace }}.{{ $domain }}
      paths:
        - path: "/"
          serviceName: "dxp-claim-benefits-dental-app"
          servicePort: 9191 # service port which will be exposed

  tls:
    enabled: false
    secretName: ""
  tcp: {}

resources:
  dxpClaimDentalApp:
    limits:
      cpu: "2"
      memory: "1320Mi"
    requests:
      cpu: "1"
      memory: "1100Mi"

readinessProbe:
  dxpClaimDentalApp:
    tcpSocket:
      port: 9191
    failureThreshold: 3
    successThreshold: 1
    periodSeconds: 10

livenessProbe:
  dxpClaimDentalApp:
    tcpSocket:
      port: 9191
    failureThreshold: 3
    successThreshold: 1
    periodSeconds: 10

startupProbe:
  dxpClaimDentalApp:
    tcpSocket:
      port: 9191
    failureThreshold: 200
    periodSeconds: 5

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

nodeSelector: {}

tolerations: []

affinity: {}

extraEnvSecrets: ["common-secret"]

externalSecrets:
  enabled: false

secretsData: {}

jprofiler:
  enabled: false
  claimName: ""

secretBackend:
  backendType: ""
  vaultMountPoint: ""
  vaultRole: ""
secretPath: ""
timeshifter: true
envs:
  genesis_postgres_url: "******************************************/{{ .Release.Namespace }}_v20"
  genesis_postgres_username: "{{ .Release.Namespace }}_v20"
  genesis_postgres_password: "{{ .Release.Namespace }}_v20"
  SPRING_PROFILES_ACTIVE: basic-login
  MAX_MEMORY_BUFFER: 50M
  MAX_DISK_BUFFER: 100M

  JAVA_OPTS:
    genesis_logs_LogstashEncoder: -Dgenesis.logs.LogstashEncoder=true
    genesis_logging_level: -Dgenesis.logging.level={{ default "INFO" .Values.log_level }}
    genesis_stream_partitions: -Dgenesis.stream.partitions=1

    #GENESIS-63959
    genesis_unique_fields: -Dgenesis.unique.fields.use.common.schema=true
    genesis_sequence_generator: -Dgenesis.sequence.generator.use.common.schema=true

    #GENESIS-78456
    com_eisgroup_reactiveConsumption: -Dcom.eisgroup.genesis.streams.consumer.StreamMessageHandler.reactiveConsumption=true
    #GENESIS-219486
    rxjava_io_scheduler: -Drxjava.io.scheduler.async=false
    com_eisgroup_parallelFactor: -Dcom.eisgroup.genesis.streams.consumer.StreamMessageHandler.parallelFactor=5

    #GENESIS-110105
    genesis_response_stream: -Dgenesis.global.response.stream=true
    genesis_external_model: -Dgenesis.external.model.entities.expose=false -Dgenesis.external.model.entities.listen=false

    #GENESIS-96486
    external_models: -Dexternal.models.store.url=http://infra-app:8484

    genesis_timeshifter: "{{if .Values.timeshifter}}-Dgenesis.timeshifter.enabled=true -javaagent:/opt/timeshiftagent/timeshifter-javaagent.jar -Dtimeshifter.zk.connect=$(genesis.zookeeper.url) -XX:CompileCommandFile=/opt/timeshiftagent/jit.excludes{{end}}"
    reactor_netty_ioWorkerCount: -Dreactor.netty.ioWorkerCount=16

    HeapDumpOnOutOfMemoryError: -XX:+HeapDumpOnOutOfMemoryError
    prometheusagent: -javaagent:/opt/prometheusagent/javaagent.jar=9090:/opt/prometheusagent/prometheus.yaml
    xdebug: -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=*:1045
    jmxrmi: -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.port=1099 -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false -Djava.rmi.server.hostname=127.0.0.1 -Dcom.sun.management.jmxremote.rmi.port=1099
    genesis_cors: "-Dgenesis.facade.cors.enabled=true -Dgenesis.facade.cors.allowed.origins=http{{if .Values.ingress.tls.enabled}}s{{end}}://admin-ui-{{.Release.Namespace}}.{{ .Values.ingress.domain }} -Dgenesis.facade.cors.allowed.methods=GET,POST,HEAD,OPTIONS,PUT,PATCH,DELETE -Dgenesis.facade.cors.allowed.headers=Origin,Accept,X-Requested-With,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,authorization,cache-control,pragma,Access-Control-Allow-Origin,X-Authorization"
    global_java_opts: $(global_java_opts)

ms_envs:
  dxpClaimDentalApp:
    JAVA_OPTS:
      MEM: -Xms768M -Xmx768M -XX:MaxDirectMemorySize=100M