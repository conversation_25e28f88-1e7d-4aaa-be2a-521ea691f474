// tslint:disable
import * as MAPI from '@eisgroup/models-api'
import { BusinessTypes } from "../index"
import {Money} from '@eisgroup/models-api'

let modelDefinition: MAPI.ModelDefinition.DomainModel = {
  "features":{
  },
  "importedFeatures":[
  ],
  "indexDeterminants":[
    "None"
  ],
  "moduleType":"DentalInternal",
  "name":"DentalInternal",
  "namespaceIncludes":[
  ],
  "root":{
    "type":"CapDentalInternalEntity"
  },
  "storeDeterminants":[
    "None"
  ],
  "types":{
    "CancelActivePaymentTemplatesOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "cancelledTemplates":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"cancelledTemplates",
          "targetType":"RootEntity"
        },
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CancelActivePaymentTemplatesOutput",
      "references":{
      }
    },
    "CapDentalFinancialCalculateLossBalanceDataInput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalFinancialCalculateLossBalanceDataInput",
      "references":{
      }
    },
    "CapDentalFinancialDataInput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "settlements":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"settlements",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalFinancialDataInput",
      "references":{
      }
    },
    "CapDentalInternalEntity":{
      "attributes":{
      },
      "baseTypeInfo":[
        {
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
                "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                },
                "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
                  "constructorArgs":{
                    "@array":"java.lang.Object",
                    "values":[
                    ]
                  },
                  "inherited":false
                }
              }
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.BaseDomain":{
            },
            "class com.eisgroup.genesis.factory.model.types.base.features.RootType":{
              "modelTypes":[
              ]
            }
          },
          "parents":[
          ],
          "typeName":"RootEntity"
        }
      ],
      "baseTypes":[
        "RootEntity"
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalInternalEntity",
      "references":{
      }
    },
    "CapDentalPaymentGenerationOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "payments":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"payments",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapDentalPaymentGenerationOutput",
      "references":{
      }
    },
    "CapUserCarryingEntity":{
      "attributes":{
        "userId":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"userId",
          "type":{
            "type":"STRING"
          }
        }
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"CapUserCarryingEntity",
      "references":{
      }
    },
    "SuspendActivePaymentSchedulesOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "suspendedSchedules":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"suspendedSchedules",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"SuspendActivePaymentSchedulesOutput",
      "references":{
      }
    },
    "SuspendActivePaymentTemplatesOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "suspendedTemplates":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"suspendedTemplates",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"SuspendActivePaymentTemplatesOutput",
      "references":{
      }
    },
    "UnsuspendPaymentSchedulesOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "unsuspendedSchedules":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"unsuspendedSchedules",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"UnsuspendPaymentSchedulesOutput",
      "references":{
      }
    },
    "UnsuspendPaymentTemplatesOutput":{
      "attributes":{
      },
      "baseTypeInfo":[
      ],
      "baseTypes":[
      ],
      "extLinks":{
        "originSource":{
          "cardinality":"SINGLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"originSource",
          "targetType":"RootEntity"
        },
        "unsuspendedTemplates":{
          "cardinality":"MULTIPLE",
          "features":{
            "class com.eisgroup.genesis.factory.features.CompilerSignature":{
              "features":{
              }
            }
          },
          "name":"unsuspendedTemplates",
          "targetType":"RootEntity"
        }
      },
      "features":{
        "class com.eisgroup.genesis.factory.features.CompilerSignature":{
          "features":{
            "class com.eisgroup.genesis.factory.features.Exclude":{
              "constructorArgs":{
                "@array":"java.lang.Object",
                "values":[
                  [
                  ],
                  [
                  ],
                  [
                  ]
                ]
              },
              "inherited":false
            }
          }
        }
      },
      "links":{
      },
      "name":"UnsuspendPaymentTemplatesOutput",
      "references":{
      }
    }
  },
  "variations":[
  ],
  "version":"1"
}
export namespace DentalInternal {
    export type Variations = never


    export class CancelActivePaymentTemplatesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CancelActivePaymentTemplatesOutput.name) }
        readonly cancelledTemplates: MAPI.ExternalLink[] = []
        readonly originSource?: MAPI.ExternalLink
    }

    export class CapDentalFinancialCalculateLossBalanceDataInput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalFinancialCalculateLossBalanceDataInput.name) }
        readonly originSource?: MAPI.ExternalLink
    }

    export class CapDentalFinancialDataInput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalFinancialDataInput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly settlements: MAPI.ExternalLink[] = []
    }

    export class CapDentalInternalEntity extends MAPI.BusinessEntity implements BusinessTypes.RootEntity, MAPI.RootBusinessType {
    constructor() { super(CapDentalInternalEntity.name) }
        readonly _modelName: string = 'DentalInternal'
        readonly _modelType: string = 'DentalInternal'
        readonly _modelVersion?: string = '1'
    }

    export class CapDentalPaymentGenerationOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapDentalPaymentGenerationOutput.name) }
        readonly payments: MAPI.ExternalLink[] = []
    }

    export class CapUserCarryingEntity extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(CapUserCarryingEntity.name) }
        readonly userId?: string
    }

    export class SuspendActivePaymentSchedulesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(SuspendActivePaymentSchedulesOutput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly suspendedSchedules: MAPI.ExternalLink[] = []
    }

    export class SuspendActivePaymentTemplatesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(SuspendActivePaymentTemplatesOutput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly suspendedTemplates: MAPI.ExternalLink[] = []
    }

    export class UnsuspendPaymentSchedulesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(UnsuspendPaymentSchedulesOutput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly unsuspendedSchedules: MAPI.ExternalLink[] = []
    }

    export class UnsuspendPaymentTemplatesOutput extends MAPI.BusinessEntity implements MAPI.BusinessType {
    constructor() { super(UnsuspendPaymentTemplatesOutput.name) }
        readonly originSource?: MAPI.ExternalLink
        readonly unsuspendedTemplates: MAPI.ExternalLink[] = []
    }


    export const model: MAPI.ModelDefinition.DomainModel = Object.freeze(modelDefinition)
    MAPI.FactoryRepository.registerModel(model)
    export const factory: MAPI.EntityFactory = MAPI.FactoryRepository.getFactory(model)
    factory.registerEntity(CancelActivePaymentTemplatesOutput, ()=> new CancelActivePaymentTemplatesOutput)
    factory.registerEntity(CapDentalFinancialCalculateLossBalanceDataInput, ()=> new CapDentalFinancialCalculateLossBalanceDataInput)
    factory.registerEntity(CapDentalFinancialDataInput, ()=> new CapDentalFinancialDataInput)
    factory.registerEntity(CapDentalInternalEntity, ()=> new CapDentalInternalEntity)
    factory.registerEntity(CapDentalPaymentGenerationOutput, ()=> new CapDentalPaymentGenerationOutput)
    factory.registerEntity(CapUserCarryingEntity, ()=> new CapUserCarryingEntity)
    factory.registerEntity(SuspendActivePaymentSchedulesOutput, ()=> new SuspendActivePaymentSchedulesOutput)
    factory.registerEntity(SuspendActivePaymentTemplatesOutput, ()=> new SuspendActivePaymentTemplatesOutput)
    factory.registerEntity(UnsuspendPaymentSchedulesOutput, ()=> new UnsuspendPaymentSchedulesOutput)
    factory.registerEntity(UnsuspendPaymentTemplatesOutput, ()=> new UnsuspendPaymentTemplatesOutput)

    /**
     * Shortcut to EntityFactory.newByType()
     */
    export function _new<T extends MAPI.BusinessEntity>(entityType: typeof MAPI.BusinessEntity, init?: string[]): T {
        return factory.newByType(entityType, init)
    }
}