/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss;

import com.eisgroup.genesis.cap.loss.command.ClaimLossUpdateHandler;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossUpdateInput;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;

/**
 * Command handler for Dental Loss initiation with custom request supporting policy
 *
 * <AUTHOR>
 * @since 21.15
 */
public class CapDentalLossUpdateHandler extends <PERSON><PERSON><PERSON><PERSON><PERSON>UpdateHandler<ClaimLossUpdateInput, CapLoss> {



}
