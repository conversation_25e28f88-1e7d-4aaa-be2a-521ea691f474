{
  "data": [
    {
      "amount": 60,
      "transactionDate": ignore,
      "extension": {
        "_type": "JsonType",
        "networkType": "INN"
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 60,
      "transactionDate": ignore,
      "extension": {
        "_type": "JsonType",
        "networkType": "INN"
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 60,
      "transactionDate": ignore,
      "extension": {
        "_type": "JsonType",
        "networkType": "INN"
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 25,
      "transactionDate": ignore,
      "extension": {
        "_type": "JsonType",
        "networkType": "INN"
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "_type": "CapAccumulatorTransactionData"
    },
    {
      "amount": 25,
      "transactionDate": ignore,
      "extension": {
        "_type": "JsonType",
        "networkType": "INN"
      },
      "resource": {
        "_uri": "capMock://CapDentalUnverifiedPolicy/cf133357-061c-4913-9d0b-34a665c20137/1"
      },
      "party": {
        "_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"
      },
      "_type": "CapAccumulatorTransactionData"
    }
  ],
  "sourceURI": "gentity://CapPayment/CapDentalPaymentDefinition/payment/04fb2459-3777-4032-8d53-f5c5da4114ec/1",
  "policyURI": "capMock://CapDentalUnverifiedPolicy/cf133357-061c-4913-9d0b-34a665c20137/1",
  "transactionTimestamp": ignore,
  "customerURI": "capMock://INDIVIDUALCUSTOMER/cf133357-061c-4913-9d0b-34a665c20111",
  "_modelName": "CapAccumulatorTransaction",
  "_modelVersion": "1",
  "_modelType": "CapAccumulatorTransactionEntry",
  "_type": "CapAccumulatorTransactionEntity"
}