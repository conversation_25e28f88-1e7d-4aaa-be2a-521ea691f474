/* Copyright © 2021 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.module.config;

import com.eisgroup.genesis.cap.common.command.config.CapBaseCommandConfig;
import com.eisgroup.genesis.cap.loss.command.action.config.ClaimLossActionConfig;
import com.eisgroup.genesis.cap.policy.api.ModelNamePolicyProjectionTypeResolver;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalClaimLossCloseValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossUpdateInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.ClaimLossCloseInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.ClaimLossReopenInputValidator;
import com.eisgroup.genesis.cap.ref.command.capdentalloss.validator.CapDentalLossValidator;
import com.eisgroup.genesis.cap.ref.command.common.validator.CapDentalLinkValidator;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.cap.transformation.command.config.CapTransformationCommandsConfig;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.generator.SequenceGenerator;
import com.eisgroup.genesis.generator.impl.SimpleNumberGenerator;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

/**
 * Claim Dental Loss lifecycle services configuration.
 *
 * <AUTHOR>
 * @since 21.15
 */
@Import({ CapBaseCommandConfig.class, CapTransformationCommandsConfig.class, ClaimLossActionConfig.class})
public class CapDentalLossLifecycleConfig {

    @Bean
    public EntityNumberGenerator lossNumberGenerator(SequenceGenerator sequenceGenerator) {
        return new SimpleNumberGenerator("loss_number", "DN%d", sequenceGenerator);
    }

    @Bean
    public ModelNamePolicyProjectionTypeResolver lossPolicyProjectionTypeResolver() {
        return new ModelNamePolicyProjectionTypeResolver() {
            @Override
            public String resolve(String modelName) {
                return "header";
            }
        };
    }

    @Bean
    public ClaimLossCloseInputValidator claimLossCloseInputValidator() {
        return new ClaimLossCloseInputValidator();
    }

    @Bean
    public ClaimLossReopenInputValidator claimLossReopenInputValidator() {
        return new ClaimLossReopenInputValidator();
    }

    @Bean
    public CapDentalClaimLossCloseValidator capDentalClaimLossCloseValidator(EntityLinkResolverRegistry entityLinkResolverRegistry, CapDentalSettlementIndexResolver capDentalSettlementIndexResolver) {
        return new CapDentalClaimLossCloseValidator(entityLinkResolverRegistry, capDentalSettlementIndexResolver);
    }

    @Bean
    public CapDentalLinkValidator capDentalLinkValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        return new CapDentalLinkValidator(entityLinkResolverRegistry);
    }

    @Bean
    public CapDentalLossValidator capDentalLossValidator(CapDentalLinkValidator capDentalLinkValidator) {
        return new CapDentalLossValidator(capDentalLinkValidator);
    }

    @Bean
    public CapDentalLossUpdateInputValidator capDentalLossUpdateInputValidator(CapDentalLossValidator capDentalLossValidator) {
        return new CapDentalLossUpdateInputValidator(capDentalLossValidator);
    }
}
