Transformation CapDentalPaymentGenerationMapping {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        CapDentalPaymentSchedule.CapDentalPaymentGenerationRulesOutput
    }

    Var originSource is createOriginSource(schedule)
    Var actualPaymentIndexes is Load(Root().originSource, "CapDentalPaymentIndex", "CapDentalPaymentIdxEntity")

    Var request is createRequest(schedule)
    Var response is ExecuteRules("claim-dental-financial", "_api_payment_generation", request)

    Attr selectedPayments is response.selectedPayments

    Producer createRequest(schedule) {
        Attr paymentSchedule is schedule
        Attr currentDateTime is Now()
        Attr lastGeneratedPaymentDate is Max(actualPaymentIndexes.paymentDate)
    }

    Producer createOriginSource(schedule) {
        Attr originSource is schedule.originSource._uri
    }

}