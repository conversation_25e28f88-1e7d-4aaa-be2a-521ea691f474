Model CapDentalLoss

Common {
    Number is output.lossNumber
    Attribute lossNumber is output.lossNumber
    Attribute lossType is output.claimType
}

Command updateLoss {
  Activity CapDentalLoss_updateLoss

  Activity CapDentalLoss_updateLoss with {
    Identified using ["CapDentalOverview", output._key.rootId]
  }
}

Command closeLoss {
  Activity CapDentalLoss_closeLoss

  Activity CapDentalLoss_closeLoss with {
    Identified using ["CapDentalOverview", output._key.rootId]
  }
}