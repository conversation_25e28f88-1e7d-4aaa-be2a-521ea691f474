<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.eis.automation.v20</groupId>
        <artifactId>pepelatz-v20-pom</artifactId>
        <version>23.4</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>claim-dental</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>

    <properties>
        <runTag>CLAIM DENTAL</runTag>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.eis.automation.v20</groupId>
            <artifactId>platform</artifactId>
            <version>23.4</version>
        </dependency>
        <dependency>
            <groupId>com.eis.automation.v20</groupId>
            <artifactId>registry</artifactId>
            <version>23.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>platform</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.eis.automation.v20</groupId>
            <artifactId>cem</artifactId>
            <version>23.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>platform</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>registry</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.eis.automation.v20</groupId>
            <artifactId>cap</artifactId>
            <version>23.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>platform</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>cem</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>registry</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>security</artifactId>
                </exclusion>
                <!--exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>policy-benefits-std</artifactId>
                </exclusion-->
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>policy-benefits-smp</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>policy-benefits-hi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>policy-benefits-ci</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.eis.automation.v20</groupId>
                    <artifactId>policy-life-gtl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>unpack-class-configurator</id>
                        <phase>process-test-classes</phase>
                        <goals>
                            <goal>unpack</goal>
                        </goals>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.eis.automation.v20</groupId>
                                    <artifactId>platform</artifactId>
                                    <excludes>**/*.class,**/*.properties</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>com.eis.automation.v20</groupId>
                                    <artifactId>registry</artifactId>
                                    <type>test-jar</type>
                                    <excludes>**/*.class,**/*.properties</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>com.eis.automation.v20</groupId>
                                    <artifactId>cem</artifactId>
                                    <type>test-jar</type>
                                    <excludes>**/*.class,**/*.properties</excludes>
                                </artifactItem>
                                <artifactItem>
                                    <groupId>com.eis.automation.v20</groupId>
                                    <artifactId>cap</artifactId>
                                    <type>test-jar</type>
                                    <excludes>**/*.class,**/*.properties</excludes>
                                </artifactItem>
                            </artifactItems>
                            <outputDirectory>${project.build.directory}\test-classes</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>GDS</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>REGRESSION</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>E2E</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <configuration combine.self="override">
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


</project>