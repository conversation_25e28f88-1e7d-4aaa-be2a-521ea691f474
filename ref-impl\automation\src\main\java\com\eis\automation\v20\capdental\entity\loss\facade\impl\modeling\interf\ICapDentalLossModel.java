/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf;

import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.interf.ICapPolicyInfoModel;
import com.eis.automation.v20.platform.common.modeling.domain.interf.ITypeModel;
import com.eis.automation.v20.platform.common.modeling.generic.external.IRootEntityUri;

import java.time.LocalDateTime;

public interface ICapDentalLossModel extends ITypeModel, IRootEntityUri {

    String getPolicyId();

    void setPolicyId(String policyId);

    ICapDentalDetailModel getEntity();

    void setEntity(ICapDentalDetailModel entity);

    ICapDentalDetailModel getLossDetail();

    String getState();

    String getLossNumber();

    String getLossSubStatusCd();

    String getReasonCd();

    String getReasonDescription();

    ICapPolicyInfoModel getPolicy();

    LocalDateTime getTimestamp();

    void setTimestamp(LocalDateTime timestamp);

}
