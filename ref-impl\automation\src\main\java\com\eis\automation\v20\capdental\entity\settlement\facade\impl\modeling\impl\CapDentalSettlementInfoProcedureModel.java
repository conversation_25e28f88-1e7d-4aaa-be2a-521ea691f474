/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.impl.CapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.*;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalSettlementInfoProcedureModel extends CapDentalProcedureModel implements ICapDentalSettlementInfoProcedureModel {

    private String procedureStatus;
    private List<ICapDentalFeeRateModel> feeUCRME;
    private List<ICapDentalFeeRateModel> feeSchedule;
    private Integer count;
    private ICapDentalConsultantReviewModel consultantReview;
    private ICapDentalDentistModel dentist;
    private Money submittedFeeUCR;
    private List<ICapDentalFeeRateModel> feeUCR;
    private Money coveredFeeUCR;
    private Money submittedFeeSchedule;
    //TODO
    //public policyInfo;
    //public masterPolicyInfo;
    //public certPolicyInfo;
    private Money submittedFeeUCRME;
    private Money coveredFeeUCRME;
    private Money coveredFeeSchedule;
    private ICapDentalDHMOFrequencyModel frequencyDHMO;
    private ICapDentalPPOFrequencyModel frequencyPPO;
    private Money paidAmount;
    private ICapDentalCalculationStatusModel status;

    public String getProcedureStatus() {
        return procedureStatus;
    }

    public void setProcedureStatus(String procedureStatus) {
        this.procedureStatus = procedureStatus;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public List<ICapDentalFeeRateModel> getFeeUCRME() {
        return feeUCRME;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public void setFeeUCRME(List<ICapDentalFeeRateModel> feeUCRME) {
        this.feeUCRME = feeUCRME;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public List<ICapDentalFeeRateModel> getFeeSchedule() {
        return feeSchedule;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public void setFeeSchedule(List<ICapDentalFeeRateModel> feeSchedule) {
        this.feeSchedule = feeSchedule;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @JsonSerialize(as = CapDentalConsultantReviewModel.class)
    public ICapDentalConsultantReviewModel getConsultantReview() {
        return consultantReview;
    }

    @JsonDeserialize(as = CapDentalConsultantReviewModel.class)
    public void setConsultantReview(ICapDentalConsultantReviewModel consultantReview) {
        this.consultantReview = consultantReview;
    }

    @JsonSerialize(as = CapDentalDentistModel.class)
    public ICapDentalDentistModel getDentist() {
        return dentist;
    }

    @JsonDeserialize(as = CapDentalDentistModel.class)
    public void setDentist(ICapDentalDentistModel dentist) {
        this.dentist = dentist;
    }

    public Money getSubmittedFeeUCR() {
        return submittedFeeUCR;
    }

    public void setSubmittedFeeUCR(Money submittedFeeUCR) {
        this.submittedFeeUCR = submittedFeeUCR;
    }

    @JsonSerialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public List<ICapDentalFeeRateModel> getFeeUCR() {
        return feeUCR;
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalFeeRateModel.class)
    public void setFeeUCR(List<ICapDentalFeeRateModel> feeUCR) {
        this.feeUCR = feeUCR;
    }

    public Money getCoveredFeeUCR() {
        return coveredFeeUCR;
    }

    public void setCoveredFeeUCR(Money coveredFeeUCR) {
        this.coveredFeeUCR = coveredFeeUCR;
    }

    public Money getSubmittedFeeSchedule() {
        return submittedFeeSchedule;
    }

    public void setSubmittedFeeSchedule(Money submittedFeeSchedule) {
        this.submittedFeeSchedule = submittedFeeSchedule;
    }

    public Money getSubmittedFeeUCRME() {
        return submittedFeeUCRME;
    }

    public void setSubmittedFeeUCRME(Money submittedFeeUCRME) {
        this.submittedFeeUCRME = submittedFeeUCRME;
    }

    public Money getCoveredFeeUCRME() {
        return coveredFeeUCRME;
    }

    public void setCoveredFeeUCRME(Money coveredFeeUCRME) {
        this.coveredFeeUCRME = coveredFeeUCRME;
    }

    public Money getCoveredFeeSchedule() {
        return coveredFeeSchedule;
    }

    public void setCoveredFeeSchedule(Money coveredFeeSchedule) {
        this.coveredFeeSchedule = coveredFeeSchedule;
    }

    @JsonSerialize(as = CapDentalDHMOFrequencyModel.class)
    public ICapDentalDHMOFrequencyModel getFrequencyDHMO() {
        return frequencyDHMO;
    }

    @JsonDeserialize(as = CapDentalDHMOFrequencyModel.class)
    public void setFrequencyDHMO(ICapDentalDHMOFrequencyModel frequencyDHMO) {
        this.frequencyDHMO = frequencyDHMO;
    }

    @JsonSerialize(as = CapDentalPPOFrequencyModel.class)
    public ICapDentalPPOFrequencyModel getFrequencyPPO() {
        return frequencyPPO;
    }

    @JsonDeserialize(as = CapDentalPPOFrequencyModel.class)
    public void setFrequencyPPO(ICapDentalPPOFrequencyModel frequencyPPO) {
        this.frequencyPPO = frequencyPPO;
    }

    public Money getPaidAmount() {
        return paidAmount;
    }

    public void setPaidAmount(Money paidAmount) {
        this.paidAmount = paidAmount;
    }

    @JsonSerialize(as = CapDentalCalculationStatusModel.class)
    public ICapDentalCalculationStatusModel getStatus() {
        return status;
    }

    @JsonDeserialize(as = CapDentalCalculationStatusModel.class)
    public void setStatus(ICapDentalCalculationStatusModel status) {
        this.status = status;
    }
}
