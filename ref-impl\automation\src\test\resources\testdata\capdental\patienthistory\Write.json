{"TestData": {"entity": {"patientHistoryData": {"serviceData": {"decision": "Allowed", "cdtCoveredCd": "D0110", "isProcedureAuthorized": true, "isPredet": false, "DOSDate": "$<today-1d:yyyy-MM-dd'T'HH:mm:ss.SSS'Z'>", "_type": "CapDentalServiceDataEntity"}, "claimData": {"patient": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//{{IndividualCustomer_rootId}}"}, "_type": "CapDentalClaimDataEntity"}, "_type": "CapDentalPatientHistoryDataEntity"}, "_modelName": "CapDentalPatientHistory", "_modelVersion": "1", "_modelType": "CapDentalPatientHistoryModelType", "_type": "CapDentalPatientHistoryEntity"}, "ModelImplClassPath": "com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl.CapDentalPatientHistoryWrapperModel"}}