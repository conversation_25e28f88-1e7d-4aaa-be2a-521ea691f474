/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.impl.CapBasePaymentDetailsModel;
import com.eis.automation.v20.cap.entity.payment.common.facade.impl.modeling.interf.ICapBasePaymentAllocationModel;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentDetailsModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentDetailsModel extends CapBasePaymentDetailsModel implements ICapDentalPaymentDetailsModel {

    @JsonSerialize(as = List.class, contentAs = CapDentalPaymentAllocationModel.class)
    public List<ICapBasePaymentAllocationModel> getPaymentAllocations() {
        return super.getPaymentAllocations();
    }

    @JsonDeserialize(as = List.class, contentAs = CapDentalPaymentAllocationModel.class)
    public void setPaymentAllocations(List<ICapBasePaymentAllocationModel> paymentAllocations) {
        super.setPaymentAllocations(paymentAllocations);
    }

}
