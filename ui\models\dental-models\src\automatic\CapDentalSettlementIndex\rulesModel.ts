// tslint:disable
import * as model from "@eisgroup/kraken-integration-model"
import { ContextModelTree } from "kraken-typescript-engine"
import { KRAKEN_MODEL_TREE_CAPDENTALSETTLEMENTINDEX } from "./kraken_model_tree_CapDentalSettlementIndex"

let name = "CapDentalSettlementIndex"

let namespace = "CapDentalSettlementIndex"

let currencyCd = "USD"

export type CapDentalSettlementIndexEntryPointName = never

let entryPointNames = [
] as CapDentalSettlementIndexEntryPointName[]

let modelTree = KRAKEN_MODEL_TREE_CAPDENTALSETTLEMENTINDEX as ContextModelTree.ContextModelTree
Object.freeze(model.__markGenerated(modelTree, 'model-tree'))

const typeNameOverrides: model.TypeNameOverride[] = [
]

export const rulesModel_CapDentalSettlementIndex = new model.RulesModel(Object.freeze(model.__markGenerated({
    name,
    namespace,
    entryPointNames,
    typeNameOverrides,
    modelTree,
    currencyCd}, 'rules-model')));
