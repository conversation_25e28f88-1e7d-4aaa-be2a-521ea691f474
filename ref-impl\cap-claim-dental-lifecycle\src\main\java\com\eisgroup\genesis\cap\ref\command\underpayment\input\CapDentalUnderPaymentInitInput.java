/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.underpayment.input;

import com.eisgroup.genesis.cap.financial.command.underpayment.input.CapUnderpaymentInitInput;
import com.google.gson.JsonObject;

/**
 * Input for UnderpaymentInitHandler
 *
 * <AUTHOR>
 * @since 22.15
 */
public class CapDentalUnderPaymentInitInput extends CapUnderpaymentInitInput {


    public CapDentalUnderPaymentInitInput(JsonObject original) {
        super(original);
    }



}
