Transformation CapDentalPaymentScheduleActivationInput {
    Input {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleEntity as schedule
    }
    Output {
        CapDentalPaymentSchedule.CapDentalPaymentScheduleActivationInput
    }

    Attr paymentSchedule is schedule

    Var userRef is CurrentUser()
    Var user is ExtLink(AsExtLink("geroot://User/User//" + userRef.userId))
    Var authorityLevel is FlatMap(user.userProfiles[currentUserProfile].authorityLevels[activeAuthority]<levelDesc>)

    Attr userAuthority is FlatMap(toClaimAuthority(authorityLevel))

    Filter currentUserProfile {
        _key.id == CurrentUser().profileId
    }

    Filter activeAuthority {
        PeriodOverlaps(nowPeriod(), Super())
    }

    Producer nowPeriod() {
        Attr startDate is Now()
        Attr endDate is IncreaseByDuration(Now(),"PT1S")
    }

    Producer toClaimAuthority(authority) {
        Attr authorityLevel is authority.level
        Attr typeCd is authority.typeCd
        Attr subTypeCd is authority.subTypeCd
    }

    Sort levelDesc {
        "level" -> "DESC"
    }
}
