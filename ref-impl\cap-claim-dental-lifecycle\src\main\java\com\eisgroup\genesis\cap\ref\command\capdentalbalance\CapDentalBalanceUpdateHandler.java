/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.input.CapDentalBalanceUpdateInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceEntity;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import static com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceCommands.UPDATE_DENTAL_BALANCE;

/**
 * Handler for dental balance update.
 *
 * <AUTHOR>
 * @since 22.13
 */
@Modifying
public class CapDentalBalanceUpdateHandler implements ProductCommandHandler<CapDentalBalanceUpdateInput, CapDentalBalanceEntity> {

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private CapDentalBalanceRepository capDentalBalanceRepository;

    @Nonnull
    @Override
    public CapDentalBalanceEntity load(@Nonnull CapDentalBalanceUpdateInput capDentalBalanceUpdateInput) {
        return capDentalBalanceRepository.load(capDentalBalanceUpdateInput.getKey(), modelResolver.getModelName()).get();
    }

    @Nonnull
    @Override
    public CapDentalBalanceEntity execute(@Nonnull CapDentalBalanceUpdateInput capDentalBalanceUpdateInput, @Nonnull CapDentalBalanceEntity entity) {
        return populateAttributes(capDentalBalanceUpdateInput, entity).get();
    }

    public Lazy<CapDentalBalanceEntity> populateAttributes(CapDentalBalanceUpdateInput input, CapDentalBalanceEntity entity) {
        return Lazy.of(entity)
                .map(populatedEntity -> {
                    populatedEntity.setTotalBalanceAmount(input.getTotalBalanceAmount());
                    populatedEntity.setBalanceItems(input.getBalanceItems());
                    return populatedEntity;
                });
    }

    @Nonnull
    @Override
    public CapDentalBalanceEntity save(@Nonnull CapDentalBalanceUpdateInput capDentalBalanceUpdateInput, @Nonnull CapDentalBalanceEntity entity) {
        return capDentalBalanceRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return UPDATE_DENTAL_BALANCE;
    }
}
