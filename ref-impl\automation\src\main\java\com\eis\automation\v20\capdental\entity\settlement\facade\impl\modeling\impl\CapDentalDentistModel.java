/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.common.modeling.impl.TermModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ITermModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalDentistModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.util.List;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalDentistModel extends TypeModel implements ICapDentalDentistModel {

    private String dentistID;
    private String inOutNetwork;
    private String pcdID;
    private String providerTIN;
    private String feeScheduleType;
    private ITermModel dentistTerm;
    private List<String> dentistSpecialties;
    private String practiceType;
    private ITermModel practiceTerm;

    public String getDentistID() {
        return dentistID;
    }

    public void setDentistID(String dentistID) {
        this.dentistID = dentistID;
    }

    public String getInOutNetwork() {
        return inOutNetwork;
    }

    public void setInOutNetwork(String inOutNetwork) {
        this.inOutNetwork = inOutNetwork;
    }

    public String getPcdID() {
        return pcdID;
    }

    public void setPcdID(String pcdID) {
        this.pcdID = pcdID;
    }

    public String getProviderTIN() {
        return providerTIN;
    }

    public void setProviderTIN(String providerTIN) {
        this.providerTIN = providerTIN;
    }

    public String getFeeScheduleType() {
        return feeScheduleType;
    }

    public void setFeeScheduleType(String feeScheduleType) {
        this.feeScheduleType = feeScheduleType;
    }

    @JsonSerialize(as = TermModel.class)
    public ITermModel getDentistTerm() {
        return dentistTerm;
    }

    @JsonDeserialize(as = TermModel.class)
    public void setDentistTerm(ITermModel dentistTerm) {
        this.dentistTerm = dentistTerm;
    }

    public List<String> getDentistSpecialties() {
        return dentistSpecialties;
    }

    public void setDentistSpecialties(List<String> dentistSpecialties) {
        this.dentistSpecialties = dentistSpecialties;
    }

    public String getPracticeType() {
        return practiceType;
    }

    public void setPracticeType(String practiceType) {
        this.practiceType = practiceType;
    }

    @JsonSerialize(as = TermModel.class)
    public ITermModel getPracticeTerm() {
        return practiceTerm;
    }

    @JsonDeserialize(as = TermModel.class)
    public void setPracticeTerm(ITermModel practiceTerm) {
        this.practiceTerm = practiceTerm;
    }
}
