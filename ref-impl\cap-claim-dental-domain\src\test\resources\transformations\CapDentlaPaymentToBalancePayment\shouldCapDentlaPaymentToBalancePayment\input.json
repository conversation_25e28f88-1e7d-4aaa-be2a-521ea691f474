{"_key": {"rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1}, "_type": "CapDentalPaymentEntity", "_modelName": "CapDentalPaymentDefinition", "_modelVersion": "1", "_modelType": "CapPayment", "_timestamp": "2025-06-26T09:09:53.585Z", "_variation": "payment", "state": "Approved", "paymentNumber": "P1", "paymentNetAmount": {"currency": "USD", "amount": 60}, "creationDate": "2025-06-26T09:09:53.498Z", "direction": "outgoing", "originSource": {"_uri": "capMock://CapDentalLoss/067c0c29-f9ca-465f-8303-02653330336d/1"}, "paymentSchedule": {"_uri": "gentity://CapPaymentSchedule/CapDentalPaymentSchedule//34a7590b-3d61-463e-8f43-25e354d85e5b/1"}, "paymentDetails": {"_key": {"rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "id": "71e21630-3808-3b10-adba-6fd63c641a23"}, "_type": "CapDentalPaymentDetailsEntity", "_modelName": "CapDentalPaymentDefinition", "_modelVersion": "1", "_modelType": "CapPayment", "_timestamp": "2025-06-26T09:09:53.572Z", "payeeDetails": {"payee": {"_uri": "geroot://Provider/IndividualProvider//05f08830-ad3c-39c4-ab5f-13378bd3d41c"}, "_type": "CapDentalPaymentPayeeDetailsEntity", "_key": {"id": "b17f3dfb-f9e2-3937-b8a4-01624f7d8da1", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "71e21630-3808-3b10-adba-6fd63c641a23"}}, "paymentAllocations": [{"allocationLossInfo": {"lossSource": {"_uri": "capMock://CapDentalLoss/067c0c29-f9ca-465f-8303-02653330336d/1"}, "_type": "CapDentalPaymentAllocationLossInfoEntity", "_key": {"id": "5232ab4f-6027-3ca5-8bf9-b06df9c0b256", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "ebde0dba-8417-331a-afd1-af1e05d7db46"}}, "allocationPayableItem": {"procedureID": "29e3d481-281f-44a9-a9d0-748afb403e49", "claimSource": {"_uri": "capMock://CapDentalLoss/067c0c29-f9ca-465f-8303-02653330336d/1"}, "_type": "CapDentalPaymentAllocationPayableItemEntity", "_key": {"id": "e0a5b96c-3d42-3fa3-849c-1651168473f9", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "ebde0dba-8417-331a-afd1-af1e05d7db46"}}, "allocationDentalDetails": {"accumulatorDetails": [{"networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 60}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "0dc706a9-75af-3afa-86a1-b3fdb048279d", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "27674ab7-ba83-364b-b026-151ff26ab586"}}, {"networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 60}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "bf86d846-7ac5-3877-a9e3-918e4eea2339", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "27674ab7-ba83-364b-b026-151ff26ab586"}}, {"networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 60}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "233be62d-8a29-3207-9b18-4a732544128f", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "27674ab7-ba83-364b-b026-151ff26ab586"}}, {"accumulatorType": "IndividualDeductible", "renewalType": "Annual", "networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 25}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "f141d78d-56da-31e6-ab78-6343a00b504a", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "27674ab7-ba83-364b-b026-151ff26ab586"}}, {"accumulatorType": "FamilyDeductible", "renewalType": "Annual", "networkType": "INN", "accumulatorAmount": {"currency": "USD", "amount": 25}, "_type": "CapDentalPaymentAllocationAccumulatorDetailsEntity", "_key": {"id": "86669acc-30a2-31c2-8c31-ee95a5fb3b80", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "27674ab7-ba83-364b-b026-151ff26ab586"}}], "transactionTypeCd": "ActualServices", "patient": {"_uri": "geroot://Customer/INDIVIDUALCUSTOMER//b0a2453b-cea9-4a32-823a-6ddae86e27c2"}, "_type": "CapDentalPaymentAllocationDentalDetailsEntity", "_key": {"id": "27674ab7-ba83-364b-b026-151ff26ab586", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "ebde0dba-8417-331a-afd1-af1e05d7db46"}}, "allocationGrossAmount": {"currency": "USD", "amount": 60}, "allocationNetAmount": {"currency": "USD", "amount": 60}, "allocationSource": {"_uri": "gentity://CapSettlement/CapDentalSettlement//76cc9985-fd0e-4d75-9242-7b0a2ae357fe/1"}, "_type": "CapDentalPaymentAllocationEntity", "_key": {"id": "ebde0dba-8417-331a-afd1-af1e05d7db46", "rootId": "04fb2459-3777-4032-8d53-f5c5da4114ec", "revisionNo": 1, "parentId": "71e21630-3808-3b10-adba-6fd63c641a23"}}], "paymentDate": "2025-06-26T09:08:19.098Z"}}