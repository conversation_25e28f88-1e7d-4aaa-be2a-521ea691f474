/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.spi.CapInputValidator;
import com.eisgroup.genesis.cap.loss.command.input.ClaimLossReopenInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;

import java.util.ArrayList;
import java.util.List;

/**
 * Validates {@link ClaimLossReopenInput} request
 *
 * <AUTHOR>
 * @since 22.6
 */
public class ClaimLossReopenInputValidator extends CapInputValidator<ClaimLossReopenInput> {

    public ClaimLossReopenInputValidator() {
        super(ClaimLossReopenInput.class);
    }

    @Override
    public Streamable<ErrorHolder> validate(ClaimLossReopenInput input) {
        List<ErrorHolder> errorHolders = new ArrayList<>();
        if (input.getReasonCd() != null) {
            errorHolders.add(ClaimLossReopenInputErrorDefinition.REASON_CD_PROVIDED.builder().build());
        }
        if (input.getReasonDescription() != null) {
            errorHolders.add(ClaimLossReopenInputErrorDefinition.REASON_DESCRIPTION_PROVIDED.builder().build());
        }
        return Streamable.from(errorHolders);
    }

    public static class ClaimLossReopenInputErrorDefinition extends BaseErrorDefinition {
        public static final ClaimLossReopenInputErrorDefinition REASON_DESCRIPTION_PROVIDED = new ClaimLossReopenInputErrorDefinition("clri001", "This command does not accept reasonDescription attribute.");
        public static final ClaimLossReopenInputErrorDefinition REASON_CD_PROVIDED = new ClaimLossReopenInputErrorDefinition("clri002", "This command does not accept reasonCd attribute.");

        private ClaimLossReopenInputErrorDefinition(String code, String message) {
            super(code, message);
        }
    }

}
