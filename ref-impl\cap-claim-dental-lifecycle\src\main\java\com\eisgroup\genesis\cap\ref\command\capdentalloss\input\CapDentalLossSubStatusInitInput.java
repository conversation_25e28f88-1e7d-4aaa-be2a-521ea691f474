/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalloss.input;

import com.eisgroup.genesis.cap.loss.command.input.ClaimLossSubStatusInput;
import com.eisgroup.genesis.factory.modeling.types.CapLoss;
import com.eisgroup.genesis.lookups.Lookup;
import com.google.gson.JsonObject;

public class CapDentalLossSubStatusInitInput extends ClaimLossSubStatusInput {

    public CapDentalLossSubStatusInitInput(JsonObject original) {
        super(original);
    }

    public CapDentalLossSubStatusInitInput(CapLoss claimLoss) {
        super(claimLoss.toJson());
    }

    @Override
    @Lookup("CapDNSubStatus")
    public String getLossSubStatusCd() {
        return super.getLossSubStatusCd();
    }
}