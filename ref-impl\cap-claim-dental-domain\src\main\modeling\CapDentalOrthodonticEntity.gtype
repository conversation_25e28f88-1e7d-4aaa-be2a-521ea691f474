BaseType CapDentalOrthodonticEntity {

    @Description("Date Appliance Placed.")
    Attr appliancePlacedDate: Date

    @Description("Down payment amount.")
    Attr downPayment: Money

    @Deprecated
    @Description("Deprecated")
    @Lookup("CapDNFrequency")
    Attr frequency: String

    @Deprecated
    @Description("Deprecated")
    Attr months: Integer

    @Description("Orthodontic Payment Frequency.")
    @Lookup("CapDNFrequency")
    Attr orthoFrequencyCd: String

    @Description("Number of Months of Treatment.")
    Attr orthoMonthQuantity: Integer
}