/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.loss;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossCloseModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.ICapLossReopenModel;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.eis.automation.v20.platform.entity.activity.facade.IActivity;
import com.eis.automation.v20.platform.entity.activity.facade.impl.modeling.ActivityModel;
import com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended;
import com.eis.automation.v20.platform.utils.assertion.bam.BamModel;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.testng.annotations.Test;

import java.util.List;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.BamConstant.BamStatus.FINISHED;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.CLOSED;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.BamMessage.CAP_DENTAL_LOSS_REOPEN;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimReasonCd.DENIED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.D0110;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.*;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.PlatformPredicates.bam;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalClaimLifecycle extends CapDentalBaseTest {

    @Autowired
    private IProviderService provider;
    @Lazy
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    @Autowired
    private IActivity activity;

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-162386", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_Close_Reopen() {
        // Preconditions
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setProcedureCode(D0110);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setPredetInd(false);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        ICapDentalLossModel closedDentalClaim = capDentalLoss.loadDentalLoss(submitDentalClaim, CLOSED);

        // Step 1
        ICapLossReopenModel dentalClaimReopenModel = modelUtils.create(capDentalLoss.getTestData("Reopen", "TestData"));
        dentalClaimReopenModel.setKey((closedDentalClaim.getKey()));
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().reopen().perform(b -> b.setModel(dentalClaimReopenModel)));
        FailureAssertion.assertThat(failure).hasError(THIS_COMMAND_DOES_NOT_ACCEPT_REASONCD_ATTRIBUTE)
                .hasError(THIS_COMMAND_DOES_NOT_ACCEPT_REASON_DESCRIPTION_ATTRIBUTE);

        // Step 2.1
        ICapLossReopenModel dentalClaimReopenModel2 = modelUtils.create(capDentalLoss.getTestData("Reopen", "TestData"));
        dentalClaimReopenModel2.setKey((closedDentalClaim.getKey()));
        dentalClaimReopenModel2.setReasonCd(null);
        dentalClaimReopenModel2.setReasonDescription(null);
        ICapDentalLossModel reopenDentalClaim2 = capDentalLoss.getFacade().reopen().perform(b -> b.setModel(dentalClaimReopenModel2));
        assertThat(reopenDentalClaim2.getState()).isEqualTo(INCOMPLETE);
        assertThat(reopenDentalClaim2.getReasonCd()).isEqualTo(null);

        // Step 2.2
        List<ActivityModel> paymentActivities = activity.fetch().performNoSuccessAnalysis(b -> b
                .setModel(reopenDentalClaim2), bam(CAP_DENTAL_LOSS_REOPEN)).safeGetResponseBody();
        CustomAssertionsExtended.assertThat(BamModel.of(paymentActivities)).hasMessage(CAP_DENTAL_LOSS_REOPEN).withStatus(FINISHED);

        // Step 6
        ICapLossCloseModel dentalClaimCloseModel = modelUtils.create(capDentalLoss.getTestData("Close", "TestData"));
        dentalClaimCloseModel.setKey(closedDentalClaim.getKey());
        dentalClaimCloseModel.setReasonCd(DENIED);
        dentalClaimCloseModel.setReasonDescription("TestDescription");
        IFailureResponse failure2 = getError(() -> capDentalLoss.getFacade().close().perform(b -> b.setModel(dentalClaimCloseModel)));
        FailureAssertion.assertThat(failure2).hasError(THIS_COMMAND_DOES_NOT_ACCEPT_REASON_DESCRIPTION_ATTRIBUTE_CLOSE);
    }

}
