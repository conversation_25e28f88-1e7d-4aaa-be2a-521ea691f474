/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.config;

import static org.hamcrest.CoreMatchers.notNullValue;
import static org.hamcrest.MatcherAssert.assertThat;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import com.eisgroup.genesis.cap.common.versioning.events.handlers.CapChangeTrackingEventHandler;
import com.eisgroup.genesis.cap.common.versioning.repository.CapChangeHistoryRepository;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.streams.consumer.ErrorHandlingStrategy;

/**
 *
 */
@RunWith(MockitoJUnitRunner.class)
public class DentalVersioningEventsConfigTest {

    @InjectMocks
    private DentalVersioningEventsConfig config;

    @Mock
    private CapChangeHistoryRepository repository;
    @Mock
    private EntityLinkBuilderRegistry linkBuilderRegistry;
    @Mock
    private ErrorHandlingStrategy deadLetterQueueStrategy;
    @Mock
    private CapDentalSettlementIndexResolver capDentalSettlementIndexResolver;

    @Test
    public void shouldReturnCapChangeTrackingEventHandler() {

        //when
        CapChangeTrackingEventHandler result = config.capChangeTrackingEventHandler(repository, linkBuilderRegistry, deadLetterQueueStrategy, null, null, null, capDentalSettlementIndexResolver);

        //then
        assertThat(result, notNullValue());
    }




}
