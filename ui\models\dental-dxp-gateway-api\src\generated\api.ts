// tslint:disable
/**
 * Dental DXP API
 * Dental domain related DXP APIs
 *
 * OpenAPI spec version: 1.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */


import * as url from "url";
import * as portableFetch from "portable-fetch";
import { Configuration } from "./configuration";

const BASE_PATH = "https://localhost:9095".replace(/\/+$/, "");

/**
 *
 * @export
 */
export const COLLECTION_FORMATS = {
    csv: ",",
    ssv: " ",
    tsv: "\t",
    pipes: "|",
};

/**
 *
 * @export
 * @interface FetchAPI
 */
export interface FetchAPI {
    (url: string, init?: any): Promise<Response>;
}

/**
 *  
 * @export
 * @interface FetchArgs
 */
export interface FetchArgs {
    url: string;
    options: any;
}

/**
 * 
 * @export
 * @class BaseAPI
 */
export class BaseAPI {
    protected configuration: Configuration;

    constructor(configuration?: Configuration, protected basePath: string = BASE_PATH, protected fetch: FetchAPI = portableFetch) {
        if (configuration) {
            this.configuration = configuration;
            this.basePath = configuration.basePath || this.basePath;
        }
    }
};

/**
 * 
 * @export
 * @class RequiredError
 * @extends {Error}
 */
export class RequiredError extends Error {
    name: "RequiredError"
    constructor(public field: string, msg?: string) {
        super(msg);
    }
}

/**
 * DXP application information
 * @export
 * @interface AppVersion
 */
export interface AppVersion {
    /**
     * Build number
     * @type {string}
     * @memberof AppVersion
     */
    buildNumber?: string;
    /**
     * Build time
     * @type {string}
     * @memberof AppVersion
     */
    buildTime?: string;
    /**
     * Additional details
     * @type {{ [key: string]: any; }}
     * @memberof AppVersion
     */
    extentions?: { [key: string]: any; };
    /**
     * DXP Core version
     * @type {string}
     * @memberof AppVersion
     */
    gatewayVersion?: string;
    /**
     * Revision
     * @type {string}
     * @memberof AppVersion
     */
    revision?: string;
    /**
     * DXP App version
     * @type {string}
     * @memberof AppVersion
     */
    version?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity {
    /**
     * Policy Number of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    PolicyNumber?: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * Policyholder Address of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    address?: string;
    /**
     * Other Coverage Type of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherCoverageType?: string;
    /**
     * Other Insurance Company Name of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherInsuranceCompany?: string;
    /**
     * Other Policy Type of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherPolicyType?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossTerm}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    period?: CapAdjusterDentalCapDentalLossTerm;
    /**
     * Plan or Group Number of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    plan?: string;
    /**
     * Policyholder Date of Birth of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderDateOfBirth?: string;
    /**
     * First Name of Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderFirstName?: string;
    /**
     * Policyholder Gender of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderGender?: string;
    /**
     * Last Name of Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderLastName?: string;
    /**
     * Patient Relationship to Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderRelationshipToPatient?: string;
    /**
     * Type of Coordination of Benefits.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity
     */
    typeOfCob?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    alternatePayee?: CapAdjusterDentalEntityLink;
    /**
     * Clean Claim Date.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    cleanClaimDate?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    cob?: Array<CapAdjusterDentalCapDentalLossCapDentalClaimCoordinationOfBenefitsEntity>;
    /**
     * Deprected this should come from customer.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    dateOfBirth?: string;
    /**
     * Digital Image Numbers.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    digitalImageNumbers?: Array<string>;
    /**
     * Flag to identify if the provider is unknown or international.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    isUnknownOrIntProvider?: boolean;
    /**
     * Missing Tooth.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    missingTooths?: Array<string>;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    patient?: CapAdjusterDentalEntityLink;
    /**
     * Payee Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    payeeType?: string;
    /**
     * Place of Treatment.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    placeOfTreatment?: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    policyholder?: CapAdjusterDentalEntityLink;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    provider?: CapAdjusterDentalEntityLink;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    providerDiscount?: CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    providerFees?: Array<CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity>;
    /**
     * Received Date.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    receivedDate?: string;
    /**
     * Comments and Remarks.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    remark?: string;
    /**
     * Source EDI NONEDI.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    source?: string;
    /**
     * Type of Claim.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity
     */
    transactionType?: string;
}

/**
 * Defines what loss it is and what happened.
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalDetailEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalDetailEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    _version?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    claimData?: CapAdjusterDentalCapDentalLossCapDentalClaimDataEntity;
    /**
     * Description of loss itself
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    lossDesc?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalLossCapDentalProcedureEntity>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDetailEntity
     */
    submittedProcedures?: Array<CapAdjusterDentalCapDentalLossCapDentalProcedureEntity>;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity
     */
    _type: string;
    /**
     * Diagnosis Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity
     */
    code?: string;
    /**
     * Diagnosis Code List Qualifier.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity
     */
    qualifier?: string;
}

/**
 * Main object for the CAP Loss Domain.
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalLossEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalLossEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalRootEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _key?: CapAdjusterDentalRootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    _version?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalDetailEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    lossDetail?: CapAdjusterDentalCapDentalLossCapDentalDetailEntity;
    /**
     * A unique loss number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    lossNumber?: string;
    /**
     * Loss Sub Status.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    lossSubStatusCd?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    policy?: CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    policyId?: string;
    /**
     * Loss Sub Status Reason.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    reasonCd?: string;
    /**
     * This attribute allows to enter the description of close/reopen reason.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    reasonDescription?: string;
    /**
     * Current status of the Loss. Updated each time a new status is gained through state machine.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossEntity
     */
    state?: string;
}

/**
 * An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.).
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    _type: string;
    /**
     * CAP policy identifier.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    capPolicyId?: string;
    /**
     * Defines policy version stored on CAP side.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    capPolicyVersionId?: string;
    /**
     * Currency Code
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    currencyCd?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalLossCapInsuredInfo>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    insureds?: Array<CapAdjusterDentalCapDentalLossCapInsuredInfo>;
    /**
     * Indicates if policy is verified.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    isVerified?: boolean;
    /**
     * Indicates policy number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    policyNumber?: string;
    /**
     * Policy version status
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    policyStatus?: string;
    /**
     * Indicates whether policy type is master or certificate (individual).
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    policyType?: string;
    /**
     * The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    productCd?: string;
    /**
     * Situs state
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    riskStateCd?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossTerm}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    term?: CapAdjusterDentalCapDentalLossTerm;
    /**
     * The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalLossPolicyEntity
     */
    txEffectiveDate?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    _type: string;
    /**
     * Date Appliance Placed.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    appliancePlacedDate?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    downPayment?: CapAdjusterDentalMoney;
    /**
     * Deprecated
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    frequency?: string;
    /**
     * Deprecated
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    months?: number;
    /**
     * Orthodontic Payment Frequency.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    orthoFrequencyCd?: string;
    /**
     * Number of Months of Treatment.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity
     */
    orthoMonthQuantity?: number;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossPeriod}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
     */
    authorizationPeriod?: CapAdjusterDentalCapDentalLossPeriod;
    /**
     * Name of the person who authorized the procedure.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
     */
    authorizedBy?: string;
    /**
     * Is procedure authorized?
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity
     */
    isProcedureAuthorized?: boolean;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    allowed?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    considered?: CapAdjusterDentalMoney;
    /**
     * Type of Coverage.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    coverageType?: string;
    /**
     * Paid INN/Paid OON.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    innOnn?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    paid?: CapAdjusterDentalMoney;
    /**
     * Primary Coverage Status.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity
     */
    primaryCoverageStatus?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalProcedureEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    cob?: CapAdjusterDentalCapDentalLossCapDentalProcedureCoordinationOfBenefitsEntity;
    /**
     * Date of Service.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    dateOfService?: string;
    /**
     * Procedure Description.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    description?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    diagnosisCodes?: Array<CapAdjusterDentalCapDentalLossCapDentalDiagnosisCodeEntity>;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    ortho?: CapAdjusterDentalCapDentalLossCapDentalOrthodonticEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    preauthorization?: CapAdjusterDentalCapDentalLossCapDentalPreauthorizationEntity;
    /**
     * Preauthorization Number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    preauthorizationNumber?: string;
    /**
     * Is this procedure part of predetermination or real service?
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    predetInd?: boolean;
    /**
     * Date of Prior Prosthesis Placement.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    priorProsthesisPlacementDate?: string;
    /**
     * CDT Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    procedureCode?: string;
    /**
     * Procedure Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    procedureType?: string;
    /**
     * Number of services.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    quantity?: number;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    submittedFee?: CapAdjusterDentalMoney;
    /**
     * Tooth Surface.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    surfaces?: Array<string>;
    /**
     * Area of Oral Cavity.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    toothArea?: string;
    /**
     * Tooth Numbers/Letters.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    toothCodes?: Array<string>;
    /**
     * Tooth System.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    toothSystem?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProcedureEntity
     */
    treatmentReason?: CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    discountAmount?: CapAdjusterDentalMoney;
    /**
     * Concession Name.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    discountName?: string;
    /**
     * Concession %.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    discountPercentage?: number;
    /**
     * Concession Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderDiscountEntity
     */
    discountType?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity
     */
    fee?: CapAdjusterDentalMoney;
    /**
     * Provicer Fee Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalProviderFeesEntity
     */
    type?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
 */
export interface CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
     */
    _type: string;
    /**
     * Auto Accident State.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
     */
    autoAccidentState?: string;
    /**
     * Date of Accident.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
     */
    dateOfAccident?: string;
    /**
     * Treatment Resulting From.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapDentalTreatmentReasonEntity
     */
    treatmentResultingFrom?: string;
}

/**
 * An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.
 * @export
 * @interface CapAdjusterDentalCapDentalLossCapInsuredInfo
 */
export interface CapAdjusterDentalCapDentalLossCapInsuredInfo {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossCapInsuredInfo
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapInsuredInfo
     */
    _type: string;
    /**
     * Indicates if a party is a primary insured.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalLossCapInsuredInfo
     */
    isMain?: boolean;
    /**
     * Searchable unique registry ID that identifies the subject of the claim.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossCapInsuredInfo
     */
    registryTypeId?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossInitInput
 */
export interface CapAdjusterDentalCapDentalLossInitInput {
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalDetailEntity}
     * @memberof CapAdjusterDentalCapDentalLossInitInput
     */
    entity: CapAdjusterDentalCapDentalLossCapDentalDetailEntity;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossInitInput
     */
    policyId?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest
 */
export interface CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest {
    /**
     * 
     * @type {any}
     * @memberof CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest
     */
    dimensions?: any;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossPeriod
 */
export interface CapAdjusterDentalCapDentalLossPeriod {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossPeriod
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossPeriod
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalLossPeriod
     */
    endDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalLossPeriod
     */
    startDate?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalLossTerm
 */
export interface CapAdjusterDentalCapDentalLossTerm {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalLossTerm
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalLossTerm
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalLossTerm
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalLossTerm
     */
    expirationDate?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementAccessTrackInfo
 */
export interface CapAdjusterDentalCapDentalSettlementAccessTrackInfo {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    createdBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    createdOn?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    updatedBy?: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementAccessTrackInfo
     */
    updatedOn?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
     */
    individualBasicINNAnnualDeductible?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
     */
    individualMajorINNAnnualDeductible?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity
     */
    individualPreventiveINNAnnualDeductible?: CapAdjusterDentalMoney;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    _type: string;
    /**
     * Type for the Maximum.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    accumulatorType?: string;
    /**
     * Defines to which procedure category maximum applies to.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    appliesToProcedureCategory?: string;
    /**
     * Network type for maximum.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    networkType?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    remainingAmount?: CapAdjusterDentalMoney;
    /**
     * Renewal type for maximum.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    renewalType?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity
     */
    reservedAmount?: CapAdjusterDentalMoney;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    _type: string;
    /**
     * Bypass All Rules and Dental Review Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassAllRules?: boolean;
    /**
     * Bypass Clinical/Consultant Review.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassClinicalReview?: boolean;
    /**
     * Bypass COB Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassCobLogic?: boolean;
    /**
     * Bypass Duplicate Service Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassDuplicateServiceLogic?: boolean;
    /**
     * Bypass Grace Period Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassGracePeriodLogic?: boolean;
    /**
     * Bypass Interest Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassInterestLogic?: boolean;
    /**
     * Bypass Missing Tooth Exclusion Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassMissingToothLogic?: boolean;
    /**
     * Bypass Overpayment Recoupment Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassOverpaymentLogic?: boolean;
    /**
     * Bypass Tooth Extraction Rules.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity
     */
    bypassToothExtractionRules?: boolean;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    _type: string;
    /**
     * Bypass All Rules and Dental Review Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassAllRules?: boolean;
    /**
     * Bypass Clinical/Consultant Review.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassClinicalReview?: boolean;
    /**
     * Bypass COB Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassCobLogic?: boolean;
    /**
     * Bypass Duplicate Service Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassDuplicateServiceLogic?: boolean;
    /**
     * Bypass Grace Period Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassGracePeriodLogic?: boolean;
    /**
     * Bypass Interest Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassInterestLogic?: boolean;
    /**
     * Bypass Missing Tooth Exclusion Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassMissingToothLogic?: boolean;
    /**
     * Bypass Overpayment Recoupment Logic.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassOverpaymentLogic?: boolean;
    /**
     * Bypass Tooth Extraction Rules.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity
     */
    bypassToothExtractionRules?: boolean;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    allowedFee?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    charge?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    coinsuranceAmt?: CapAdjusterDentalMoney;
    /**
     * Coinsurance percentage if exists.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    coinsurancePercentage?: number;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    consideredFee?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    contributionToMOOP?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    copay?: CapAdjusterDentalMoney;
    /**
     * Procedure code received after adjudication
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    coveredCode?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    coveredFee?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    netBenefitAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    patientResponsibility?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    payableDeductible?: CapAdjusterDentalMoney;
    /**
     * Calculation Result Procedure ID.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    procedureID?: string;
    /**
     * Covered Procedure area calculated after adjudication..
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    procedureType?: string;
    /**
     * Calculation Result Submitted Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity
     */
    submittedCode?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    _type: string;
    /**
     * Calculation code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    code?: string;
    /**
     * Calculation Status Covered code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    coveredCode?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    fee?: CapAdjusterDentalMoney;
    /**
     * Status flag - Denied, Allowed, Review Required...
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    flag?: string;
    /**
     * Calculation percentage.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    percentage?: number;
    /**
     * Claim number of the history procedure if the service is predet-preauth
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    preauthorizationNumber?: string;
    /**
     * Predentermined indicator.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    predetInd?: boolean;
    /**
     * Calculation Status Procedure ID.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    procedureID?: string;
    /**
     * Question for the procedure.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    questions?: Array<string>;
    /**
     * Calculation Status Reason Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    reasonCode?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    remarkMessages?: Array<CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity>;
    /**
     * Calculation Status Reason.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    statusReason?: string;
    /**
     * Calculation Status Submitted procedure code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity
     */
    submittedCode?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity {
    /**
     * Policy Number of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    PolicyNumber?: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * Policyholder Address of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    address?: string;
    /**
     * Other Coverage Type of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherCoverageType?: string;
    /**
     * Other Insurance Company Name of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherInsuranceCompany?: string;
    /**
     * Other Policy Type of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    otherPolicyType?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementTerm}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    period?: CapAdjusterDentalCapDentalSettlementTerm;
    /**
     * Plan or Group Number of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    plan?: string;
    /**
     * Policyholder Date of Birth of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderDateOfBirth?: string;
    /**
     * First Name of Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderFirstName?: string;
    /**
     * Policyholder Gender of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderGender?: string;
    /**
     * Last Name of Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderLastName?: string;
    /**
     * Patient Relationship to Policyholder of COB.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    policyholderRelationshipToPatient?: string;
    /**
     * Type of Coordination of Benefits.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity
     */
    typeOfCob?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    alternatePayee?: CapAdjusterDentalEntityLink;
    /**
     * Clean Claim Date.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    cleanClaimDate?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    cob?: Array<CapAdjusterDentalCapDentalSettlementCapDentalClaimCoordinationOfBenefitsEntity>;
    /**
     * Deprected this should come from customer.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    dateOfBirth?: string;
    /**
     * Digital Image Numbers.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    digitalImageNumbers?: Array<string>;
    /**
     * Flag to identify if the provider is unknown or international.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    isUnknownOrIntProvider?: boolean;
    /**
     * Missing Tooth.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    missingTooths?: Array<string>;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    patient?: CapAdjusterDentalEntityLink;
    /**
     * Payee Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    payeeType?: string;
    /**
     * Place of Treatment.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    placeOfTreatment?: string;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    policyholder?: CapAdjusterDentalEntityLink;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    provider?: CapAdjusterDentalEntityLink;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    providerDiscount?: CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    providerFees?: Array<CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity>;
    /**
     * Received Date.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    receivedDate?: string;
    /**
     * Comments and Remarks.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    remark?: string;
    /**
     * Source EDI NONEDI.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    source?: string;
    /**
     * Type of Claim.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity
     */
    transactionType?: string;
}

/**
 * Business entity that houses the information from loss to use in settlement.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    claimData?: CapAdjusterDentalCapDentalSettlementCapDentalClaimDataEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    patient?: CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity;
    /**
     * Claim received date.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    receivedDate?: string;
    /**
     * Source type EDI or NONEDI.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    source?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity
     */
    submittedProcedures?: Array<CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity>;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    bypassClaimLogic?: CapAdjusterDentalCapDentalSettlementCapDentalBypassClaimLogicEntity;
    /**
     * Allow Service.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    isAllowed?: boolean;
    /**
     * Deny Service.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    isDenied?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    overrideClaimValue?: CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity;
    /**
     * Suppress EOB for Member.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    suppressMemberEob?: boolean;
    /**
     * Suppress EOB for Provider.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    suppressProviderEob?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity
     */
    waiveClaimValue?: CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    _type: string;
    /**
     * Consultant review alternate CDT code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    alternateCDTCode?: string;
    /**
     * Consultant review reply.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    consultantReply?: string;
    /**
     * Consultant review reply letter.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    consultantReplyLetter?: string;
    /**
     * Surface which had consultant review.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity
     */
    surface?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    _type: string;
    /**
     * DHMO number of times procedure allowed.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    frequency?: string;
    /**
     * DHMO frequency period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    frequencyPeriod?: number;
    /**
     * DHMO frequency period type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    frequencyPeriodType?: string;
    /**
     * DHMO Range of the frequency.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    frequencyRange?: string;
    /**
     * DHMO applied toward the Maximum Out of Pocket value.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    isAppliedTowardsMOOP?: string;
    /**
     * If the DHMO covered or not.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity
     */
    isCovered?: boolean;
}

/**
 * Business entity defines settlement result.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    _type: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    entries?: Array<CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementMessageType>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    messages?: Array<CapAdjusterDentalCapDentalSettlementMessageType>;
    /**
     * Provider or insured or alternate payee reference for payment generation.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    payeeRef?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    paymentAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    preprocessMessages?: Array<CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity>;
    /**
     * Proposal code which identifies next steps for system and/or user: Additional Reviewer or generate payment.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    proposal?: string;
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    reserve?: number;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity
     */
    reservedAccumulators?: Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    _type: string;
    /**
     * Dentist National Provider ID.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    dentistID?: string;
    /**
     * Dentist specialities.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    dentistSpecialties?: Array<string>;
    /**
     * Dentist fee schedule type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    feeScheduleType?: string;
    /**
     * If the dentist In or Out of Network.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    inOutNetwork?: string;
    /**
     * Primary care dentist in DHMO.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    pcdID?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementTerm}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    practiceTerm?: CapAdjusterDentalCapDentalSettlementTerm;
    /**
     * Dentist practice type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    practiceType?: string;
    /**
     * Provider tax identification number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity
     */
    providerTIN?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity
     */
    _type: string;
    /**
     * Diagnosis Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity
     */
    code?: string;
    /**
     * Diagnosis Code List Qualifier.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity
     */
    qualifier?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity
     */
    feeAmount?: CapAdjusterDentalMoney;
    /**
     * Code that fee applied to.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity
     */
    feeCode?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    implantsINNAnnualMaximum?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    individualBasicINNAnnualMaximum?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    individualMajorINNAnnualMaximum?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    individualPreventiveINNAnnualMaximum?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity
     */
    orthoINNAnnualMaximum?: CapAdjusterDentalMoney;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    _type: string;
    /**
     * Date Appliance Placed.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    appliancePlacedDate?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    downPayment?: CapAdjusterDentalMoney;
    /**
     * Deprecated
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    frequency?: string;
    /**
     * Deprecated
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    months?: number;
    /**
     * Orthodontic Payment Frequency.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    orthoFrequencyCd?: string;
    /**
     * Number of Months of Treatment.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity
     */
    orthoMonthQuantity?: number;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    _type: string;
    /**
     * Override Basic Services Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideBasicWaitingPeriod?: number;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementPeriod}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideEligibilityPeriod?: CapAdjusterDentalCapDentalSettlementPeriod;
    /**
     * Override Fee Schedule.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideFeeSchedule?: string;
    /**
     * Override Grace Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideGracePeriod?: number;
    /**
     * Override Late Entrant Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideLateEntrantWaitingPeriod?: number;
    /**
     * Override Major Services Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideMajorWaitingPeriod?: number;
    /**
     * Override Ortho Services Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideOrthoWaitingPeriod?: number;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overridePaymentInterestAmount?: CapAdjusterDentalMoney;
    /**
     * Override Payment Interest Number of Days.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overridePaymentInterestDays?: number;
    /**
     * Override Payment Interest State.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overridePaymentInterestState?: string;
    /**
     * Override Preventive Services Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overridePreventiveWaitingPeriod?: number;
    /**
     * Override Student Dependent Status.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideClaimValueEntity
     */
    overrideStudentDependentStatus?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideAllowedAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideCobApplied?: CapAdjusterDentalMoney;
    /**
     * Override Coinsurance %
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideCoinsurancePct?: number;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideConsideredAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideCopayAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideCoveredAmount?: CapAdjusterDentalMoney;
    /**
     * Override Covered CDT Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideCoveredCdtCode?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideDeductible?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementPeriod}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideEligibilityPeriod?: CapAdjusterDentalCapDentalSettlementPeriod;
    /**
     * Override Essential Health Benefit.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideEssentialHealthBenefit?: string;
    /**
     * Override Fee Schedule.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideFeeSchedule?: string;
    /**
     * Override Grace Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideGracePeriod?: number;
    /**
     * Override Late Entrant Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideLateEntrantWaitingPeriod?: number;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideMaximumAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overridePatientResponsibility?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overridePaymentInterestAmount?: CapAdjusterDentalMoney;
    /**
     * Override Payment Interest Number of Days.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overridePaymentInterestDays?: number;
    /**
     * Override Payment Interest State.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overridePaymentInterestState?: string;
    /**
     * Override Replacement Limit.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideReplacementLimit?: number;
    /**
     * Override Category of Service.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideServiceCategory?: string;
    /**
     * Override Service Frequency Limit.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideServiceFrequencyLimit?: number;
    /**
     * Override Service Category Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideServiceWaitingPeriod?: number;
    /**
     * Override Student Dependent Status.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity
     */
    overrideStudentDependentStatus?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    _type: string;
    /**
     * PPO number of times procedure allowed.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequency?: string;
    /**
     * PPO frequency comment.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequencyComment?: string;
    /**
     * PPO frequency period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequencyPeriod?: number;
    /**
     * PPO frequency period type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequencyPeriodType?: string;
    /**
     * PPO Range of the frequency.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequencyRange?: string;
    /**
     * PPO rule to determine if procedure allowed.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    frequencyRule?: string;
    /**
     * PPO procedure type for the frequency.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity
     */
    procedureType?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    _type: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    accumulators?: Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>;
    /**
     * Patient's date of birth.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    birthDate?: string;
    /**
     * NOT USED
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    dateOfBirth?: string;
    /**
     * Patient's disabilities.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    disabilities?: Array<string>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    historyProcedures?: Array<CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity>;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    isDependentChild?: boolean;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    isDisabledChild?: boolean;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    isFullTimeStudent?: boolean;
    /**
     * Patient's ID.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPatientEntity
     */
    patientID?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
     */
    _type: string;
    /**
     * Coinsurance percentage in network.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceINPct?: number;
    /**
     * Coinsurance percentage outside network.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceOONPct?: number;
    /**
     * Coinsurance Service Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity
     */
    coinsuranceServiceType?: string;
}

/**
 * An object that stores policy information. Available for Absence Case, Claims (STD, SMP, etc.) & Settlement (Absence, STD, SMP, etc.).
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    _type: string;
    /**
     * Is late entrant benefit period waiting applied.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    applyLateEntrantBenefitWaitingPeriods?: boolean;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    basicINNCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    basicOONCoinsurancePercent?: number;
    /**
     * Basic waiting period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    basicWaitingPeriod?: string;
    /**
     * CAP policy identifier.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    capPolicyId?: string;
    /**
     * Defines policy version stored on CAP side.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    capPolicyVersionId?: string;
    /**
     * Child Max Age limit.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    childMaxAgeCd?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    coinsurances?: Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoCoinsuranceEntity>;
    /**
     * Currency Code
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    currencyCd?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    deductibleDetails?: Array<CapAdjusterDentalCapDentalSettlementCapDeductibleDetailEntity>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    dentalMaximums?: Array<CapAdjusterDentalCapDentalSettlementCapDentalMaximumEntity>;
    /**
     * Full time student Age limit.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    fullTimeStudentAgeCd?: string;
    /**
     * Implants waiting period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    implantsWaitingPeriod?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    insureds?: Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity>;
    /**
     * Implants Maximum Applies toward Plan Maximum
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    isImplantsMaximumAppliedTowardPlanMaximum?: boolean;
    /**
     * Indicates if policy is verified.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    isVerified?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    isWaitingPeriodWaived?: boolean;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    lateEntrantWaitingPeriodsDetails?: Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity>;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    majorINNCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    majorOONCoinsurancePercent?: number;
    /**
     * Major waiting period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    majorWaitingPeriod?: string;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    nonStandardChildAgeLimit?: number;
    /**
     * Ortho In Network Coinsurance Percentage.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    orthoINNCoinsurancePercent?: number;
    /**
     * Ortho Out of Network Coinsurance Percentage.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    orthoOONCoinsurancePercent?: number;
    /**
     * Ortho waiting period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    orthoWaitingPeriod?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalTermEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    patientTerm?: CapAdjusterDentalCapDentalSettlementCapDentalTermEntity;
    /**
     * Primary Care Dentist ID.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    pcdId?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalTermEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    pcdTerm?: CapAdjusterDentalCapDentalSettlementCapDentalTermEntity;
    /**
     * Plan type for the policy.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    plan?: string;
    /**
     * PPO/DHMO product.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    planCategory?: string;
    /**
     * Plan Name.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    planName?: string;
    /**
     * Indicates policy number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    policyNumber?: string;
    /**
     * Date which the Policy is paid up to.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    policyPaidToDate?: string;
    /**
     * Date which the Policy is paid up to with Grace period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    policyPaidToDateWithGracePeriod?: string;
    /**
     * Policy version status
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    policyStatus?: string;
    /**
     * Indicates whether policy type is master or certificate (individual).
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    policyType?: string;
    /**
     * Preventetive waiting period.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    preventWaitingPeriod?: string;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    preventiveINNCoinsurancePercent?: number;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    preventiveOONCoinsurancePercent?: number;
    /**
     * The attribute that represents the Policy Product (STDMaster, STDIndividual, SMPMaster).
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    productCd?: string;
    /**
     * Situs state
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    riskStateCd?: string;
    /**
     * NOT USED
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    standardChildAgeLimit?: number;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementTerm}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    term?: CapAdjusterDentalCapDentalSettlementTerm;
    /**
     * The date on which a policy action is applied. Represents the date on which a CapPolicyInfo>policyStatus became effective.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    txEffectiveDate?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    unverifiedInfo?: CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    waiveINNInd?: boolean;
    /**
     * NOT USED
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity
     */
    waiveOONInd?: boolean;
}

/**
 * An entity that stores insureds (sponsor - from Master Policy, insured - from Certificate Policy) information.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    _type: string;
    /**
     * Insured's role.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    insuredRoleNameCd?: string;
    /**
     * Is patient full time student?
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    isFullTimeStudent?: boolean;
    /**
     * Indicates if a party is a primary insured.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    isMain?: boolean;
    /**
     * Searchable unique registry ID that identifies the subject of the claim.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    registryTypeId?: string;
    /**
     * Patient's relationship to primary insured.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoInsuredDetailsEntity
     */
    relationshipToPrimaryInsuredCd?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    _type: string;
    /**
     * Late entrant basic Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    basicWaitingPeriod?: number;
    /**
     * Late entrant major Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    majorWaitingPeriod?: number;
    /**
     * Late entrant preventive Waiting Period.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoLateEntrantBenefitWaitingPeriodEntity
     */
    preventWaitingPeriod?: number;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementPeriod}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
     */
    authorizationPeriod?: CapAdjusterDentalCapDentalSettlementPeriod;
    /**
     * Name of the person who authorized the procedure.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
     */
    authorizedBy?: string;
    /**
     * Is procedure authorized?
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity
     */
    isProcedureAuthorized?: boolean;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
     */
    _type: string;
    /**
     * Preprocess Message Code if any issues with the intake.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
     */
    preprocessCode?: string;
    /**
     * Preprocess Message if any issues with the intake.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
     */
    preprocessMessage?: string;
    /**
     * Preprocess Message Severity if any issues with the intake - defaulted to Warning.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalPreprocessMessageEntity
     */
    preprocessSeverity?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    allowed?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    considered?: CapAdjusterDentalMoney;
    /**
     * Type of Coverage.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    coverageType?: string;
    /**
     * Paid INN/Paid OON.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    innOnn?: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    paid?: CapAdjusterDentalMoney;
    /**
     * Primary Coverage Status.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity
     */
    primaryCoverageStatus?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    certPolicyInfo?: CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    cob?: CapAdjusterDentalCapDentalSettlementCapDentalProcedureCoordinationOfBenefitsEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    consultantReview?: CapAdjusterDentalCapDentalSettlementCapDentalConsultantReviewEntity;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    coveredFeeSchedule?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    coveredFeeUCR?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    coveredFeeUCRME?: CapAdjusterDentalMoney;
    /**
     * Date of Service.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    dateOfService?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    dentist?: CapAdjusterDentalCapDentalSettlementCapDentalDentistEntity;
    /**
     * Procedure Description.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    description?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    diagnosisCodes?: Array<CapAdjusterDentalCapDentalSettlementCapDentalDiagnosisCodeEntity>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    feeSchedule?: Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    feeUCR?: Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    feeUCRME?: Array<CapAdjusterDentalCapDentalSettlementCapDentalFeeRateEntity>;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    frequencyDHMO?: CapAdjusterDentalCapDentalSettlementCapDentalDHMOFrequencyEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    frequencyPPO?: CapAdjusterDentalCapDentalSettlementCapDentalPPOFrequencyEntity;
    /**
     * Claim number of history procedure.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    lossNumber?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    masterPolicyInfo?: Array<CapAdjusterDentalCapDentalSettlementCapDentalPolicyInfoEntity>;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    ortho?: CapAdjusterDentalCapDentalSettlementCapDentalOrthodonticEntity;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    paidAmount?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    preauthorization?: CapAdjusterDentalCapDentalSettlementCapDentalPreauthorizationEntity;
    /**
     * Preauthorization Number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    preauthorizationNumber?: string;
    /**
     * Is this procedure part of predetermination or real service?
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    predetInd?: boolean;
    /**
     * Date of Prior Prosthesis Placement.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    priorProsthesisPlacementDate?: string;
    /**
     * CDT Code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    procedureCode?: string;
    /**
     * Historical Procedures.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    procedureStatus?: string;
    /**
     * Procedure Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    procedureType?: string;
    /**
     * Number of services.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    quantity?: number;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    status?: CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    submittedFee?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    submittedFeeSchedule?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    submittedFeeUCR?: CapAdjusterDentalMoney;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    submittedFeeUCRME?: CapAdjusterDentalMoney;
    /**
     * Tooth Surface.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    surfaces?: Array<string>;
    /**
     * Area of Oral Cavity.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    toothArea?: string;
    /**
     * Tooth Numbers/Letters.
     * @type {Array<string>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    toothCodes?: Array<string>;
    /**
     * Tooth System.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    toothSystem?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProcedureEntity
     */
    treatmentReason?: CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    discountAmount?: CapAdjusterDentalMoney;
    /**
     * Concession Name.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    discountName?: string;
    /**
     * Concession %.
     * @type {number}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    discountPercentage?: number;
    /**
     * Concession Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderDiscountEntity
     */
    discountType?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalMoney}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity
     */
    fee?: CapAdjusterDentalMoney;
    /**
     * Provicer Fee Type.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalProviderFeesEntity
     */
    type?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity
     */
    _type: string;
    /**
     * Additional multiple codes that explains how decision achieved.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity
     */
    remarkCode?: string;
    /**
     * Messages as per the remark codes.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalRemarkMessageEntity
     */
    remarkMessage?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    calculationResult?: CapAdjusterDentalCapDentalSettlementCapDentalCalculationResultEntity;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    reservedAccumulators?: Array<CapAdjusterDentalCapDentalSettlementCapDentalAccumulatorEntity>;
    /**
     * This link to Procedure from Intake. Defines which response is associated to which submited procedure.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    serviceSource?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalResultEntryEntity
     */
    status?: CapAdjusterDentalCapDentalSettlementCapDentalCalculationStatusEntity;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    _type: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    bypassServiceLogic?: CapAdjusterDentalCapDentalSettlementCapDentalBypassServiceLogicEntity;
    /**
     * Allow Service.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    isAllowed?: boolean;
    /**
     * Deny Service.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    isDenied?: boolean;
    /**
     * Override Remark.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    overrideRemark?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    overrideServiceValue?: CapAdjusterDentalCapDentalSettlementCapDentalOverrideServiceValueEntity;
    /**
     * Link to Service.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    serviceSource?: string;
    /**
     * Suppress EOB for Member.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    suppressMemberEob?: boolean;
    /**
     * Suppress EOB for Provider.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    suppressProviderEob?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity
     */
    waiveServiceValue?: CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity;
}

/**
 * The object that includes information taken from Absence case.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity
     */
    id?: string;
}

/**
 * This Business entity houses the detail of the settlement.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    _version?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    claimOverride?: CapAdjusterDentalCapDentalSettlementCapDentalClaimOverrideEntity;
    /**
     * EOB Free Form Message.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    eobFreeFormMessage?: string;
    /**
     * NOT USED
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    overrideCd?: string;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity>}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity
     */
    serviceOverrides?: Array<CapAdjusterDentalCapDentalSettlementCapDentalServiceOverrideEntity>;
}

/**
 * Main object for the CAP Settlement Domain.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity {
    /**
     * 
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _archived?: boolean;
    /**
     * 
     * @type {CapAdjusterDentalRootEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _key?: CapAdjusterDentalRootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _modelName: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _modelType?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _modelVersion?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _timestamp?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _type: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    _version?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementAccessTrackInfo}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    accessTrackInfo?: CapAdjusterDentalCapDentalSettlementAccessTrackInfo;
    /**
     * 
     * @type {CapAdjusterDentalEntityLink}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    claimLossIdentification?: CapAdjusterDentalEntityLink;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementAbsenceInfo?: CapAdjusterDentalCapDentalSettlementCapDentalSettlementAbsenceInfoEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementDetail?: CapAdjusterDentalCapDentalSettlementCapDentalSettlementDetailEntity;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementLossInfo?: CapAdjusterDentalCapDentalSettlementCapDentalClaimInfoEntity;
    /**
     * A unique settlement number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementNumber?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementResult?: CapAdjusterDentalCapDentalSettlementCapDentalDecisionResultEntity;
    /**
     * Defines settlement type
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    settlementType?: string;
    /**
     * Current status of the Settlement. Updated each time a new status is gained through state machine.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity
     */
    state?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalTermEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalTermEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTermEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTermEntity
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTermEntity
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTermEntity
     */
    expirationDate?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
     */
    _type: string;
    /**
     * Auto Accident State.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
     */
    autoAccidentState?: string;
    /**
     * Date of Accident.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
     */
    dateOfAccident?: string;
    /**
     * Treatment Resulting From.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalTreatmentReasonEntity
     */
    treatmentResultingFrom?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity
     */
    _type: string;
    /**
     * Claim Without Policy Employer Name.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity
     */
    employerName?: string;
    /**
     * Claim Without Policy Group Number.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalUnverifiedInfoEntity
     */
    groupNumber?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    _type: string;
    /**
     * Waive Basic Services Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveBasicWaitingPeriod?: boolean;
    /**
     * Waive Deductible.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveDeductible?: boolean;
    /**
     * Waive Eligibility After Coverage Start Date.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveEligibilityAfterStartDate?: boolean;
    /**
     * Waive Eligibility Prior to Coverage Start Date.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveEligibilityPriorStartDate?: boolean;
    /**
     * Waive Late Entrant Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveLateEntrantWaitingPeriod?: boolean;
    /**
     * Waive Major Services Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveMajorWaitingPeriod?: boolean;
    /**
     * Waive Maximum Amount Limits.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveMaximumLimitAmounts?: boolean;
    /**
     * Waive Ortho Services Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveOrthoWaitingPeriod?: boolean;
    /**
     * Waive Preventive Services Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waivePreventiveWaitingPeriod?: boolean;
    /**
     * Waive Replacement Limit.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveReplacementLimit?: boolean;
    /**
     * Waive Service Frequency Limit.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveClaimValueEntity
     */
    waiveServiceFrequencyLimit?: boolean;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
 */
export interface CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    _type: string;
    /**
     * Waive Deductible.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveDeductible?: boolean;
    /**
     * Waive Eligibility After Coverage Start Date.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveEligibilityAfterStartDate?: boolean;
    /**
     * Waive Eligibility Prior to Coverage Start Date.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveEligibilityPriorStartDate?: boolean;
    /**
     * Waive Late Entrant Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveLateEntrantWaitingPeriod?: boolean;
    /**
     * Waive Maximum Amount Limits.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveMaximumLimitAmounts?: boolean;
    /**
     * Waive Replacement Limit.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveReplacementLimit?: boolean;
    /**
     * Waive Service Frequency Limit.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveServiceFrequencyLimit?: boolean;
    /**
     * Waive Service Category Waiting Period.
     * @type {boolean}
     * @memberof CapAdjusterDentalCapDentalSettlementCapDentalWaiveServiceValueEntity
     */
    waiveServiceWaitingPeriod?: boolean;
}

/**
 * Defines a message that can be forwarded to the user on some exceptions.
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementMessageType
 */
export interface CapAdjusterDentalCapDentalSettlementMessageType {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    _type: string;
    /**
     * Message code.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    code?: string;
    /**
     * Message text.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    message?: string;
    /**
     * Message severity.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    severity?: string;
    /**
     * Message source.
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementMessageType
     */
    source?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementPeriod
 */
export interface CapAdjusterDentalCapDentalSettlementPeriod {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementPeriod
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementPeriod
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementPeriod
     */
    endDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementPeriod
     */
    startDate?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalCapDentalSettlementTerm
 */
export interface CapAdjusterDentalCapDentalSettlementTerm {
    /**
     * 
     * @type {CapAdjusterDentalEntityKey}
     * @memberof CapAdjusterDentalCapDentalSettlementTerm
     */
    _key?: CapAdjusterDentalEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalCapDentalSettlementTerm
     */
    _type: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementTerm
     */
    effectiveDate?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalCapDentalSettlementTerm
     */
    expirationDate?: Date;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalClaimLossUpdateInput
 */
export interface CapAdjusterDentalClaimLossUpdateInput {
    /**
     * 
     * @type {CapAdjusterDentalRootEntityKey}
     * @memberof CapAdjusterDentalClaimLossUpdateInput
     */
    _key: CapAdjusterDentalRootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalClaimLossUpdateInput
     */
    _updateStrategy?: string;
    /**
     * 
     * @type {CapAdjusterDentalCapDentalLossCapDentalDetailEntity}
     * @memberof CapAdjusterDentalClaimLossUpdateInput
     */
    entity: CapAdjusterDentalCapDentalLossCapDentalDetailEntity;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalEntityKey
 */
export interface CapAdjusterDentalEntityKey {
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalEntityKey
     */
    id?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalEntityKey
     */
    parentId?: string;
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalEntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalEntityKey
     */
    rootId?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalEntityLink
 */
export interface CapAdjusterDentalEntityLink {
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalEntityLink
     */
    _uri?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalIdentifierRequest
 */
export interface CapAdjusterDentalIdentifierRequest {
    /**
     * 
     * @type {CapAdjusterDentalRootEntityKey}
     * @memberof CapAdjusterDentalIdentifierRequest
     */
    _key: CapAdjusterDentalRootEntityKey;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalIdentifierRequest
     */
    _version?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalMoney
 */
export interface CapAdjusterDentalMoney {
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalMoney
     */
    amount: number;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalMoney
     */
    currency: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalResultCapDentalSettlementEntityRow
 */
export interface CapAdjusterDentalResultCapDentalSettlementEntityRow {
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    _modelName?: string;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    _originalTimestamp?: Date;
    /**
     * 
     * @type {Date}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    _timestamp?: Date;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    _uri?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    claimLossIdentification?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    registryTypeId?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    rootId?: string;
    /**
     * 
     * @type {any}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    score?: any;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntityRow
     */
    settlementNumber?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV2
 */
export interface CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV2 {
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV2
     */
    count?: number;
    /**
     * 
     * @type {Array<CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity>}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV2
     */
    result?: Array<CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity>;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV3
 */
export interface CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV3 {
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV3
     */
    count?: number;
    /**
     * Actual return types are one of: CapDentalSettlementEntity, ResultCapDentalSettlementEntityRow
     * @type {Array<any>}
     * @memberof CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV3
     */
    result?: Array<any>;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalRootEntityKey
 */
export interface CapAdjusterDentalRootEntityKey {
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalRootEntityKey
     */
    revisionNo?: number;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalRootEntityKey
     */
    rootId?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
 */
export interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2 {
    /**
     * 
     * @type {Array<string>}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    embed?: Array<string>;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    fields?: Array<string>;
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    limit?: number;
    /**
     * 
     * @type {number}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    offset?: number;
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    query?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery;
    /**
     * 
     * @type {{ [key: string]: any; }}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2
     */
    sort?: { [key: string]: any; };
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher
 */
export interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher {
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher
     */
    from?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher
     */
    matches?: Array<string>;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher
     */
    to?: string;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery
 */
export interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery {
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery
     */
    claimLossIdentification?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher;
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery
     */
    registryTypeId?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher;
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery
     */
    rootId?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher;
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchQuery
     */
    settlementNumber?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher;
}

/**
 * 
 * @export
 * @interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
 */
export interface CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher {
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
     */
    from?: string;
    /**
     * 
     * @type {Array<string>}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
     */
    matches?: Array<string>;
    /**
     * 
     * @type {CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
     */
    not?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchMatcher;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
     */
    notEqual?: string;
    /**
     * 
     * @type {string}
     * @memberof CapAdjusterDentalSearchCapDentalSettlementEntitySearchValueMatcher
     */
    to?: string;
}

/**
 * Common error details
 * @export
 * @interface GenesisCommonExceptionDTO
 */
export interface GenesisCommonExceptionDTO {
    /**
     * Error additional details
     * @type {{ [key: string]: any; }}
     * @memberof GenesisCommonExceptionDTO
     */
    details?: { [key: string]: any; };
    /**
     * Error code
     * @type {string}
     * @memberof GenesisCommonExceptionDTO
     */
    errorCode?: string;
    /**
     * Error nested information
     * @type {Array<GenesisValidationExceptionDTO>}
     * @memberof GenesisCommonExceptionDTO
     */
    errors?: Array<GenesisValidationExceptionDTO>;
    /**
     * Error field
     * @type {string}
     * @memberof GenesisCommonExceptionDTO
     */
    field?: string;
    /**
     * Error log reference
     * @type {string}
     * @memberof GenesisCommonExceptionDTO
     */
    logReference?: string;
    /**
     * Error message
     * @type {string}
     * @memberof GenesisCommonExceptionDTO
     */
    message?: string;
    /**
     * Error path
     * @type {string}
     * @memberof GenesisCommonExceptionDTO
     */
    path?: string;
}

/**
 * Exception details
 * @export
 * @interface GenesisValidationExceptionDTO
 */
export interface GenesisValidationExceptionDTO {
    /**
     * Error additional details
     * @type {{ [key: string]: any; }}
     * @memberof GenesisValidationExceptionDTO
     */
    details?: { [key: string]: any; };
    /**
     * Error code
     * @type {string}
     * @memberof GenesisValidationExceptionDTO
     */
    errorCode?: string;
    /**
     * Error nested information
     * @type {Array<GenesisValidationExceptionDTO>}
     * @memberof GenesisValidationExceptionDTO
     */
    errors?: Array<GenesisValidationExceptionDTO>;
    /**
     * Error field
     * @type {string}
     * @memberof GenesisValidationExceptionDTO
     */
    field?: string;
    /**
     * Error log reference
     * @type {string}
     * @memberof GenesisValidationExceptionDTO
     */
    logReference?: string;
    /**
     * Error message
     * @type {string}
     * @memberof GenesisValidationExceptionDTO
     */
    message?: string;
    /**
     * Error path
     * @type {string}
     * @memberof GenesisValidationExceptionDTO
     */
    path?: string;
}

/**
 * Log level information
 * @export
 * @interface LogLevel
 */
export interface LogLevel {
    /**
     * Log level
     * @type {string}
     * @memberof LogLevel
     */
    level?: string;
}


/**
 * CapAdjusterv1lossesDentalApi - fetch parameter creator
 * @export
 */
export const CapAdjusterv1lossesDentalApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalIdentifierRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: CapAdjusterDentalIdentifierRequest,  }, options: any = {}): FetchArgs {
            const localVarPath = `/cap-adjuster/v1/losses-dental`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapAdjusterDentalIdentifierRequest" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalCapDentalLossInitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapAdjusterDentalCapDentalLossInitInput,  }, options: any = {}): FetchArgs {
            const localVarPath = `/cap-adjuster/v1/losses-dental/draft`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapAdjusterDentalCapDentalLossInitInput" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params: { rootId: string, revisionNo: number,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost.');
            }
            const localVarPath = `/cap-adjuster/v1/losses-dental/{rootId}/{revisionNo}/submit`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalClaimLossUpdateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: CapAdjusterDentalClaimLossUpdateInput,  }, options: any = {}): FetchArgs {
            const localVarPath = `/cap-adjuster/v1/losses-dental`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'PUT' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapAdjusterDentalClaimLossUpdateInput" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet.');
            }
            const localVarPath = `/cap-adjuster/v1/losses-dental/{rootId}/{revisionNo}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine
         * @summary 
         * @param {string} entryPoint 
         * @param {boolean} [delta] 
         * @param {CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, delta?: boolean, body?: CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.entryPoint' is not null or undefined
            if (params.entryPoint === null || params.entryPoint === undefined) {
                throw new RequiredError('params.entryPoint','Required parameter params.entryPoint was null or undefined when calling capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost.');
            }
            const localVarPath = `/cap-adjuster/v1/losses-dental/rules/{entryPoint}`
                .replace(`{${"entryPoint"}}`, encodeURIComponent(String(params.entryPoint)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            if (params.delta !== undefined) {
                localVarQueryParameter['delta'] = params.delta;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalApi - functional programming interface
 * @export
 */
export const CapAdjusterv1lossesDentalApiFp = function(configuration?: Configuration) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalIdentifierRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: CapAdjusterDentalIdentifierRequest,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalLossCapDentalLossEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalCapDentalLossInitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapAdjusterDentalCapDentalLossInitInput,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalLossCapDentalLossEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params: { rootId: string, revisionNo: number,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalLossCapDentalLossEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalClaimLossUpdateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: CapAdjusterDentalClaimLossUpdateInput,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalLossCapDentalLossEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalLossCapDentalLossEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine
         * @summary 
         * @param {string} entryPoint 
         * @param {boolean} [delta] 
         * @param {CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, delta?: boolean, body?: CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<any> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalApiFetchParamCreator(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalApi - factory interface
 * @export
 */
export const CapAdjusterv1lossesDentalApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalIdentifierRequest} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: CapAdjusterDentalIdentifierRequest,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalCapDentalLossInitInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapAdjusterDentalCapDentalLossInitInput,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params: { rootId: string, revisionNo: number,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params, options)(fetch, basePath);
        },
        /**
         * Executes command for a given path parameters and request body data.
         * @summary 
         * @param {CapAdjusterDentalClaimLossUpdateInput} [body] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: CapAdjusterDentalClaimLossUpdateInput,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params, options)(fetch, basePath);
        },
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options)(fetch, basePath);
        },
        /**
         * This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine
         * @summary 
         * @param {string} entryPoint 
         * @param {boolean} [delta] 
         * @param {CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, delta?: boolean, body?: CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest,  }, options?: any) {
            return CapAdjusterv1lossesDentalApiFp(configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params, options)(fetch, basePath);
        },
    };
};

/**
 * CapAdjusterv1lossesDentalApi - object-oriented interface
 * @export
 * @class CapAdjusterv1lossesDentalApi
 * @extends {BaseAPI}
 */
export class CapAdjusterv1lossesDentalApi extends BaseAPI {
    /**
     * Executes command for a given path parameters and request body data.
     * @summary 
     * @param {CapAdjusterDentalIdentifierRequest} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params: { body?: CapAdjusterDentalIdentifierRequest,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandCreateLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @summary 
     * @param {CapAdjusterDentalCapDentalLossInitInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params: { body?: CapAdjusterDentalCapDentalLossInitInput,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandInitLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @summary 
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params: { rootId: string, revisionNo: number,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandSubmitLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Executes command for a given path parameters and request body data.
     * @summary 
     * @param {CapAdjusterDentalClaimLossUpdateInput} [body] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params: { body?: CapAdjusterDentalClaimLossUpdateInput,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1CommandUpdateLossPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns factory entity root record for a given path parameters.
     * @summary 
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1EntitiesRootIdRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

    /**
     * This endpoint is deprecated for removal. Migrate to an endpoint '/bundle' Endpoint for internal communication for Kraken UI engine
     * @summary 
     * @param {string} entryPoint 
     * @param {boolean} [delta] 
     * @param {CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest} [body] No description provided
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalApi
     */
    public capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params: { entryPoint: string, delta?: boolean, body?: CapAdjusterDentalCapDentalLossKrakenDeprecatedBundleRequest,  }, options?: any) {
        return CapAdjusterv1lossesDentalApiFp(this.configuration).capadjusterV1LossesdentalApiCaplossCapDentalLossV1RulesEntryPointPost(params, options)(this.fetch, this.basePath);
    }

}

/**
 * CapAdjusterv1lossesDentalSearchApi - fetch parameter creator
 * @export
 */
export const CapAdjusterv1lossesDentalSearchApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * No description provided
         * @summary 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params: { embed?: string, fields?: string, limit?: number, offset?: number, body?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2,  }, options: any = {}): FetchArgs {
            const localVarPath = `/cap-adjuster/v1/losses-dental-search/settlement`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'POST' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            if (params.limit !== undefined) {
                localVarQueryParameter['limit'] = params.limit;
            }

            if (params.offset !== undefined) {
                localVarQueryParameter['offset'] = params.offset;
            }

            localVarHeaderParameter['Content-Type'] = 'application/json';

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);
            const needsSerialization = (<any>"CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2" !== "string") || localVarRequestOptions.headers['Content-Type'] === 'application/json';
            localVarRequestOptions.body =  needsSerialization ? JSON.stringify(params.body || {}) : (params.body || "");

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * No description provided
         * @summary 
         * @param {string} rootId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params: { rootId: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost.');
            }
            const localVarPath = `/cap-adjuster/v1/losses-dental-search/loss/{rootId}/settlement`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalSearchApi - functional programming interface
 * @export
 */
export const CapAdjusterv1lossesDentalSearchApiFp = function(configuration?: Configuration) {
    return {
        /**
         * No description provided
         * @summary 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params: { embed?: string, fields?: string, limit?: number, offset?: number, body?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV2> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalSearchApiFetchParamCreator(configuration).capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * No description provided
         * @summary 
         * @param {string} rootId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params: { rootId: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalResultCapDentalSettlementEntitySearchEntityResponseV3> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalSearchApiFetchParamCreator(configuration).capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalSearchApi - factory interface
 * @export
 */
export const CapAdjusterv1lossesDentalSearchApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * No description provided
         * @summary 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {number} [limit] 
         * @param {number} [offset] 
         * @param {CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2} [body] No description provided
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params: { embed?: string, fields?: string, limit?: number, offset?: number, body?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2,  }, options?: any) {
            return CapAdjusterv1lossesDentalSearchApiFp(configuration).capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params, options)(fetch, basePath);
        },
        /**
         * No description provided
         * @summary 
         * @param {string} rootId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params: { rootId: string,  }, options?: any) {
            return CapAdjusterv1lossesDentalSearchApiFp(configuration).capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params, options)(fetch, basePath);
        },
    };
};

/**
 * CapAdjusterv1lossesDentalSearchApi - object-oriented interface
 * @export
 * @class CapAdjusterv1lossesDentalSearchApi
 * @extends {BaseAPI}
 */
export class CapAdjusterv1lossesDentalSearchApi extends BaseAPI {
    /**
     * No description provided
     * @summary 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {number} [limit] 
     * @param {number} [offset] 
     * @param {CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2} [body] No description provided
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalSearchApi
     */
    public capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params: { embed?: string, fields?: string, limit?: number, offset?: number, body?: CapAdjusterDentalSearchCapDentalSettlementEntitySearchEntityRequestV2,  }, options?: any) {
        return CapAdjusterv1lossesDentalSearchApiFp(this.configuration).capadjusterV1LossesdentalsearchApiCommonSearchV2CapDentalSettlementPost(params, options)(this.fetch, this.basePath);
    }

    /**
     * No description provided
     * @summary 
     * @param {string} rootId 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalSearchApi
     */
    public capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params: { rootId: string,  }, options?: any) {
        return CapAdjusterv1lossesDentalSearchApiFp(this.configuration).capadjusterV1LossesdentalsearchApiCommonSearchV3CapDentalSettlementPost(params, options)(this.fetch, this.basePath);
    }

}

/**
 * CapAdjusterv1lossesDentalSettlementsApi - fetch parameter creator
 * @export
 */
export const CapAdjusterv1lossesDentalSettlementsApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.rootId' is not null or undefined
            if (params.rootId === null || params.rootId === undefined) {
                throw new RequiredError('params.rootId','Required parameter params.rootId was null or undefined when calling capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet.');
            }
            // verify required parameter 'params.revisionNo' is not null or undefined
            if (params.revisionNo === null || params.revisionNo === undefined) {
                throw new RequiredError('params.revisionNo','Required parameter params.revisionNo was null or undefined when calling capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet.');
            }
            const localVarPath = `/cap-adjuster/v1/losses-dental-settlements/{rootId}/{revisionNo}`
                .replace(`{${"rootId"}}`, encodeURIComponent(String(params.rootId)))
                .replace(`{${"revisionNo"}}`, encodeURIComponent(String(params.revisionNo)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            // authentication persona_BasicAuthPassThroughLogonFilter required
            // http basic authentication required
            if (configuration && (configuration.username || configuration.password)) {
                localVarHeaderParameter["Authorization"] = "Basic " + btoa(configuration.username + ":" + configuration.password);
            }

            // authentication persona_GenesisAuthToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("X-Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["X-Authorization"] = localVarApiKeyValue;
            }

            // authentication persona_GenesisSSOToken required
            if (configuration && configuration.apiKey) {
                const localVarApiKeyValue = typeof configuration.apiKey === 'function'
					? configuration.apiKey("Authorization")
					: configuration.apiKey;
                localVarHeaderParameter["Authorization"] = localVarApiKeyValue;
            }

            if (params.embed !== undefined) {
                localVarQueryParameter['embed'] = params.embed;
            }

            if (params.fields !== undefined) {
                localVarQueryParameter['fields'] = params.fields;
            }

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalSettlementsApi - functional programming interface
 * @export
 */
export const CapAdjusterv1lossesDentalSettlementsApiFp = function(configuration?: Configuration) {
    return {
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<CapAdjusterDentalCapDentalSettlementCapDentalSettlementEntity> {
            const localVarFetchArgs = CapAdjusterv1lossesDentalSettlementsApiFetchParamCreator(configuration).capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * CapAdjusterv1lossesDentalSettlementsApi - factory interface
 * @export
 */
export const CapAdjusterv1lossesDentalSettlementsApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * Returns factory entity root record for a given path parameters.
         * @summary 
         * @param {string} rootId 
         * @param {number} revisionNo 
         * @param {string} [embed] 
         * @param {string} [fields] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
            return CapAdjusterv1lossesDentalSettlementsApiFp(configuration).capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params, options)(fetch, basePath);
        },
    };
};

/**
 * CapAdjusterv1lossesDentalSettlementsApi - object-oriented interface
 * @export
 * @class CapAdjusterv1lossesDentalSettlementsApi
 * @extends {BaseAPI}
 */
export class CapAdjusterv1lossesDentalSettlementsApi extends BaseAPI {
    /**
     * Returns factory entity root record for a given path parameters.
     * @summary 
     * @param {string} rootId 
     * @param {number} revisionNo 
     * @param {string} [embed] 
     * @param {string} [fields] 
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof CapAdjusterv1lossesDentalSettlementsApi
     */
    public capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params: { rootId: string, revisionNo: number, embed?: string, fields?: string,  }, options?: any) {
        return CapAdjusterv1lossesDentalSettlementsApiFp(this.configuration).capadjusterV1LossesdentalsettlementsApiCapsettlementCapDentalSettlementV1EntitiesRootIdRevisionNoGet(params, options)(this.fetch, this.basePath);
    }

}

/**
 * Corev1Api - fetch parameter creator
 * @export
 */
export const Corev1ApiFetchParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * Returns a list of Swagger groups for a current DXP service. The list items are available for selection on Swagger UI
         * @summary Retrieves Swagger groups
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1ConfigurationGetAllSwaggerGroupsNames(params: {  }, options: any = {}): FetchArgs {
            const localVarPath = `/core/v1/configuration/swagger/groups`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns DXP app current log level
         * @summary Retrieves DXP log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsGetLogLevel(params: {  }, options: any = {}): FetchArgs {
            const localVarPath = `/core/v1/logs/log-level`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Allows updating a current log level of a DXP app
         * @summary Updates DXP log Level
         * @param {'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL'} logLevel Log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsUpdateLogLevel(params: { logLevel: 'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL',  }, options: any = {}): FetchArgs {
            // verify required parameter 'params.logLevel' is not null or undefined
            if (params.logLevel === null || params.logLevel === undefined) {
                throw new RequiredError('params.logLevel','Required parameter params.logLevel was null or undefined when calling coreV1LogsUpdateLogLevel.');
            }
            const localVarPath = `/core/v1/logs/log-level/{logLevel}`
                .replace(`{${"logLevel"}}`, encodeURIComponent(String(params.logLevel)));
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'PUT' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * Returns DXP app version information
         * @summary Retrieves DXP version information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1VersionGetAppVersion(params: {  }, options: any = {}): FetchArgs {
            const localVarPath = `/core/v1/version`;
            const localVarUrlObj = url.parse(localVarPath, true);
            const localVarRequestOptions = Object.assign({ method: 'GET' }, options);
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            localVarUrlObj.query = Object.assign({}, localVarUrlObj.query, localVarQueryParameter, options.query);
            // fix override query string Detail: https://stackoverflow.com/a/7517673/1077943
            delete localVarUrlObj.search;
            localVarRequestOptions.headers = Object.assign({}, localVarHeaderParameter, options.headers);

            return {
                url: url.format(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * Corev1Api - functional programming interface
 * @export
 */
export const Corev1ApiFp = function(configuration?: Configuration) {
    return {
        /**
         * Returns a list of Swagger groups for a current DXP service. The list items are available for selection on Swagger UI
         * @summary Retrieves Swagger groups
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1ConfigurationGetAllSwaggerGroupsNames(params: {  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<Array<string>> {
            const localVarFetchArgs = Corev1ApiFetchParamCreator(configuration).coreV1ConfigurationGetAllSwaggerGroupsNames(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns DXP app current log level
         * @summary Retrieves DXP log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsGetLogLevel(params: {  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<LogLevel> {
            const localVarFetchArgs = Corev1ApiFetchParamCreator(configuration).coreV1LogsGetLogLevel(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Allows updating a current log level of a DXP app
         * @summary Updates DXP log Level
         * @param {'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL'} logLevel Log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsUpdateLogLevel(params: { logLevel: 'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL',  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<Response> {
            const localVarFetchArgs = Corev1ApiFetchParamCreator(configuration).coreV1LogsUpdateLogLevel(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response;
                    } else {
                        throw response;
                    }
                });
            };
        },
        /**
         * Returns DXP app version information
         * @summary Retrieves DXP version information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1VersionGetAppVersion(params: {  }, options?: any): (fetch?: FetchAPI, basePath?: string) => Promise<AppVersion> {
            const localVarFetchArgs = Corev1ApiFetchParamCreator(configuration).coreV1VersionGetAppVersion(params, options);
            return (fetch: FetchAPI = portableFetch, basePath: string = BASE_PATH) => {
                return fetch(basePath + localVarFetchArgs.url, localVarFetchArgs.options).then((response) => {
                    if (response.status >= 200 && response.status < 300) {
                        return response.json();
                    } else {
                        throw response;
                    }
                });
            };
        },
    }
};

/**
 * Corev1Api - factory interface
 * @export
 */
export const Corev1ApiFactory = function (configuration?: Configuration, fetch?: FetchAPI, basePath?: string) {
    return {
        /**
         * Returns a list of Swagger groups for a current DXP service. The list items are available for selection on Swagger UI
         * @summary Retrieves Swagger groups
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1ConfigurationGetAllSwaggerGroupsNames(params: {  }, options?: any) {
            return Corev1ApiFp(configuration).coreV1ConfigurationGetAllSwaggerGroupsNames(params, options)(fetch, basePath);
        },
        /**
         * Returns DXP app current log level
         * @summary Retrieves DXP log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsGetLogLevel(params: {  }, options?: any) {
            return Corev1ApiFp(configuration).coreV1LogsGetLogLevel(params, options)(fetch, basePath);
        },
        /**
         * Allows updating a current log level of a DXP app
         * @summary Updates DXP log Level
         * @param {'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL'} logLevel Log level
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1LogsUpdateLogLevel(params: { logLevel: 'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL',  }, options?: any) {
            return Corev1ApiFp(configuration).coreV1LogsUpdateLogLevel(params, options)(fetch, basePath);
        },
        /**
         * Returns DXP app version information
         * @summary Retrieves DXP version information
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        coreV1VersionGetAppVersion(params: {  }, options?: any) {
            return Corev1ApiFp(configuration).coreV1VersionGetAppVersion(params, options)(fetch, basePath);
        },
    };
};

/**
 * Corev1Api - object-oriented interface
 * @export
 * @class Corev1Api
 * @extends {BaseAPI}
 */
export class Corev1Api extends BaseAPI {
    /**
     * Returns a list of Swagger groups for a current DXP service. The list items are available for selection on Swagger UI
     * @summary Retrieves Swagger groups
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Corev1Api
     */
    public coreV1ConfigurationGetAllSwaggerGroupsNames(params: {  }, options?: any) {
        return Corev1ApiFp(this.configuration).coreV1ConfigurationGetAllSwaggerGroupsNames(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns DXP app current log level
     * @summary Retrieves DXP log level
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Corev1Api
     */
    public coreV1LogsGetLogLevel(params: {  }, options?: any) {
        return Corev1ApiFp(this.configuration).coreV1LogsGetLogLevel(params, options)(this.fetch, this.basePath);
    }

    /**
     * Allows updating a current log level of a DXP app
     * @summary Updates DXP log Level
     * @param {'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL'} logLevel Log level
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Corev1Api
     */
    public coreV1LogsUpdateLogLevel(params: { logLevel: 'OFF' | 'ERROR' | 'WARN' | 'INFO' | 'DEBUG' | 'TRACE' | 'ALL',  }, options?: any) {
        return Corev1ApiFp(this.configuration).coreV1LogsUpdateLogLevel(params, options)(this.fetch, this.basePath);
    }

    /**
     * Returns DXP app version information
     * @summary Retrieves DXP version information
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof Corev1Api
     */
    public coreV1VersionGetAppVersion(params: {  }, options?: any) {
        return Corev1ApiFp(this.configuration).coreV1VersionGetAppVersion(params, options)(this.fetch, this.basePath);
    }

}

