/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalCalculationResultModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalCalculationResultModel extends TypeModel implements ICapDentalCalculationResultModel {

    private String procedureType;
    private Money charge;
    private Money coveredFee;
    private Money payableDeductible;
    private String procedureID;
    private String submittedCode;
    private Money allowedFee;
    private Money coinsuranceAmt;
    private String coveredCode;
    private Money patientResponsibility;
    private Money consideredFee;
    private Money netBenefitAmount;
    private Money copay;
    private Money contributionToMOOP;
    private Integer coinsurancePercentage;

    public String getProcedureType() {
        return procedureType;
    }

    public void setProcedureType(String procedureType) {
        this.procedureType = procedureType;
    }

    public Money getCharge() {
        return charge;
    }

    public void setCharge(Money charge) {
        this.charge = charge;
    }

    public Money getCoveredFee() {
        return coveredFee;
    }

    public void setCoveredFee(Money coveredFee) {
        this.coveredFee = coveredFee;
    }

    public Money getPayableDeductible() {
        return payableDeductible;
    }

    public void setPayableDeductible(Money payableDeductible) {
        this.payableDeductible = payableDeductible;
    }

    public String getProcedureID() {
        return procedureID;
    }

    public void setProcedureID(String procedureID) {
        this.procedureID = procedureID;
    }

    public String getSubmittedCode() {
        return submittedCode;
    }

    public void setSubmittedCode(String submittedCode) {
        this.submittedCode = submittedCode;
    }

    public Money getAllowedFee() {
        return allowedFee;
    }

    public void setAllowedFee(Money allowedFee) {
        this.allowedFee = allowedFee;
    }

    public Money getCoinsuranceAmt() {
        return coinsuranceAmt;
    }

    public void setCoinsuranceAmt(Money coinsuranceAmt) {
        this.coinsuranceAmt = coinsuranceAmt;
    }

    public String getCoveredCode() {
        return coveredCode;
    }

    public void setCoveredCode(String coveredCode) {
        this.coveredCode = coveredCode;
    }

    public Money getPatientResponsibility() {
        return patientResponsibility;
    }

    public void setPatientResponsibility(Money patientResponsibility) {
        this.patientResponsibility = patientResponsibility;
    }

    public Money getConsideredFee() {
        return consideredFee;
    }

    public void setConsideredFee(Money consideredFee) {
        this.consideredFee = consideredFee;
    }

    public Money getNetBenefitAmount() {
        return netBenefitAmount;
    }

    public void setNetBenefitAmount(Money netBenefitAmount) {
        this.netBenefitAmount = netBenefitAmount;
    }

    public Money getCopay() {
        return copay;
    }

    public void setCopay(Money copay) {
        this.copay = copay;
    }

    public Money getContributionToMOOP() {
        return contributionToMOOP;
    }

    public void setContributionToMOOP(Money contributionToMOOP) {
        this.contributionToMOOP = contributionToMOOP;
    }

    public Integer getCoinsurancePercentage() {
        return coinsurancePercentage;
    }

    public void setCoinsurancePercentage(Integer coinsurancePercentage) {
        this.coinsurancePercentage = coinsurancePercentage;
    }
}
