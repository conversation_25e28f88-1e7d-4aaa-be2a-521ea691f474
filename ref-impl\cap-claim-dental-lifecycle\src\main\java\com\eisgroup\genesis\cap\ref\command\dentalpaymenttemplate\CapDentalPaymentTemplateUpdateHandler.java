/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.template.CapPaymentTemplateUpdateHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpaymenttemplate.input.CapDentalPaymentTemplateUpdateInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymenttemplate.CapDentalPaymentTemplateEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.model.ModelResolver;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

/**
 * Command handler for updating {@link CapDentalPaymentTemplateEntity}
 *
 * <AUTHOR>
 * @since 22.2
 */
public class CapDentalPaymentTemplateUpdateHandler extends CapPaymentTemplateUpdateHandler<CapDentalPaymentTemplateUpdateInput, CapDentalPaymentTemplateEntity> {

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPaymentTemplateUpdateInput input, @Nonnull CapDentalPaymentTemplateEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(input);
    }

    @Nonnull
    @Override
    public CapDentalPaymentTemplateEntity execute(@Nonnull CapDentalPaymentTemplateUpdateInput input, @Nonnull CapDentalPaymentTemplateEntity entity) {
        DomainModel model = modelResolver.resolveModel(DomainModel.class);
        populateTemplateAttributes(input, entity);
        KeyTraversalUtil.traverseRoot(entity.toJson(), model);
        return entity;
    }
}
