/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement.validator;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.entity.Stateful;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.json.link.EntityLinkResolverRegistry;
import com.eisgroup.genesis.repository.ReadContext;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Validator service for {@link com.eisgroup.genesis.factory.model.capdentalsettlement.CapDentalSettlementEntity} validations
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalSettlementValidator {
    private static final Logger logger = LoggerFactory.getLogger(CapDentalSettlementValidator.class);
    private static final String CLOSED_STATE = "Closed";
    private static final String SETTLEMENT_MODEL_NAME = "CapSettlement";
    private final EntityLinkResolverRegistry entityLinkResolverRegistry;

    public CapDentalSettlementValidator(EntityLinkResolverRegistry entityLinkResolverRegistry) {
        this.entityLinkResolverRegistry = entityLinkResolverRegistry;
    }



    private Streamable<ErrorHolder> validateLossEntity(EntityLink<RootEntity> reference) {
        return Optional.ofNullable(reference)
            .map(Streamable::of)
            .orElseGet(Streamable::empty)
            .flatMap(ref -> retrieveLossEntity(ref).filter(this::isLossClosed)
                .flatMapMany(this::validateModelType)
                .onErrorResume(e -> Streamable.of(DentalSettlementErrorDefinition.LOSS_CLOSED.builder().build())));
    }



    public Streamable<ErrorHolder> validateLossNotClosed(EntityLink<RootEntity> reference) {
        return Optional.ofNullable(reference)
            .map(Streamable::of)
            .orElseGet(Streamable::empty)
            .flatMap(ref -> retrieveLossEntity(ref).filter(this::isLossClosed)
                .flatMapMany(this::validateModelType)
                .onErrorResume(e -> Streamable.of(DentalSettlementErrorDefinition.LOSS_CLOSED.builder().build())));



    }
    protected Streamable<ErrorHolder> validateModelType(RootEntity entity) {
        return Streamable.of(entity)
            .filter(ent -> !SETTLEMENT_MODEL_NAME.equals(ent.getModelType()))
            .map(ent -> {
                logger.info("Settlement entity with rootId {} could not be found as _modelType is incorrect", entity.getKey().getRootId());
                return DentalSettlementErrorDefinition.LOSS_CLOSED.builder()
                    .params(SETTLEMENT_MODEL_NAME)
                    .build();
            });
    }
    private boolean isLossClosed(RootEntity loss) {
        if (loss instanceof Stateful) {
            return CLOSED_STATE.equals(((Stateful) loss).getState());
        }
        return false;
    }

    private Lazy<RootEntity> retrieveLossEntity(EntityLink<RootEntity> reference) {
        return entityLinkResolverRegistry.getByURIScheme(reference.getSchema())
            .resolve(reference, ReadContext.empty());
    }

    public static class DentalSettlementErrorDefinition extends BaseErrorDefinition {

        public static final DentalSettlementErrorDefinition LOSS_CLOSED = new DentalSettlementErrorDefinition("dsed-001",
            "This command cannot be performed when Loss is in Closed state.");

        private DentalSettlementErrorDefinition(String code, String message) {
            super(code, message);
        }
    }
}
