/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory;

/**
 * Command names of patient history.
 *
 * <AUTHOR>
 * @since 22.6
 */
public class CapDentalPatientHistoryCommands {

    public static final String INIT_PATIENT_HISTORY = "initPatientHistory";
    public static final String UPDATE_PATIENT_HISTORY = "updatePatientHistory";
    public static final String CANCEL_PATIENT_HISTORY = "cancelPatientHistory";

    private CapDentalPatientHistoryCommands() {}
}
