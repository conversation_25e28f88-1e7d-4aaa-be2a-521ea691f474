/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalsettlement;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.adjudication.command.CapRefreshSettlementHandler;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.modeling.types.CapSettlement;

/**
 * Lifecycle command used to support state transition in the Settlement state machine
 * <AUTHOR>
 * @since 25.11
 */
public class CapDentalSettlementRefreshHandler extends CapRefreshSettlementHandler<IdentifierRequest, CapSettlement> {

    @Override
    protected Lazy<CapSettlement> removePolicyInfo(CapSettlement entity) {
        return Lazy.of(entity);
    }
}
