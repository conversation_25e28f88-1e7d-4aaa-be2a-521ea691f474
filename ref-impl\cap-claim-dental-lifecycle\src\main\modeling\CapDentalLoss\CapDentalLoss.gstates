StateMachine CapDentalLoss {
    EntryState uninitialized {
        Using initLoss command transit to Incomplete
    }

    State Incomplete {
        Using closeLoss command transit to Closed
        Using setLossSubStatus command transit to Incomplete
        Using submitLoss command transit to Pending
        Using updateLossDraft command transit to Incomplete
        Using requestClose<PERSON><PERSON> command transit to Incomplete
    }

    State Pending {
        Using closeLoss command transit to Closed
        Using setLossSubStatus command transit to Pending
        Using updateLoss command transit to Pending
        Using openLoss command transit to Open
        Using requestCloseLoss command transit to Pending
    }

    State Open {
        Using suspendLoss command transit to Suspended
        Using closeLoss command transit to Closed
        Using updateLoss command transit to Open
        Using pendLoss command transit to Pending
        Using requestCloseLoss command transit to Open
    }

    State Suspended {
        Using unsuspendLoss command transit to Open
        Using updateLoss command transit to Suspended
        Using pendLoss command transit to Pending
    }

    State Closed {
        Using reopenLoss command transit to Incomplete
    }
}