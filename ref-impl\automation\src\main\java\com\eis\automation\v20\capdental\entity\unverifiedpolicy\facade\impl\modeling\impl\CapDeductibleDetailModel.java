/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDeductibleDetailModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalMaximumModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import org.javamoney.moneta.Money;

@JsonFilter(AbstractFilter.NAME)
public class CapDeductibleDetailModel extends TypeModel implements ICapDeductibleDetailModel {

    private Money individualPreventiveINNAnnualDeductible;
    private Money individualMajorINNAnnualDeductible;
    private Money individualBasicINNAnnualDeductible;

    public Money getIndividualPreventiveINNAnnualDeductible() {
        return individualPreventiveINNAnnualDeductible;
    }

    public void setIndividualPreventiveINNAnnualDeductible(Money individualPreventiveINNAnnualDeductible) {
        this.individualPreventiveINNAnnualDeductible = individualPreventiveINNAnnualDeductible;
    }

    public Money getIndividualMajorINNAnnualDeductible() {
        return individualMajorINNAnnualDeductible;
    }

    public void setIndividualMajorINNAnnualDeductible(Money individualMajorINNAnnualDeductible) {
        this.individualMajorINNAnnualDeductible = individualMajorINNAnnualDeductible;
    }

    public Money getIndividualBasicINNAnnualDeductible() {
        return individualBasicINNAnnualDeductible;
    }

    public void setIndividualBasicINNAnnualDeductible(Money individualBasicINNAnnualDeductible) {
        this.individualBasicINNAnnualDeductible = individualBasicINNAnnualDeductible;
    }
}
