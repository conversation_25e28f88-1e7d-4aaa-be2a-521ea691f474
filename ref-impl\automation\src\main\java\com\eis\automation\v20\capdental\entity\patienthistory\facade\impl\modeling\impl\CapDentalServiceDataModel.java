/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.cap.entity.common.modeling.impl.PeriodModel;
import com.eis.automation.v20.cap.entity.common.modeling.interf.IPeriodModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalServiceDataModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.javamoney.moneta.Money;

import java.time.LocalDateTime;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalServiceDataModel extends TypeModel implements ICapDentalServiceDataModel {

    private String procedureType;
    private String decision;
    private String cdtSubmittedCd;
    private String cdtCoveredCd;
    private String toothArea;
    private Money benefitAmount;
    private Money deductibleAmount;
    private Money coinsuranceAmount;
    private Money submittedAmount;
    private Money copayAmount;
    private Money consideredAmount;
    private Money coveredAmount;
    private List<String> remarkCodes;
    private List<String> surfaces;
    private List<String> toothCodes;
    private Integer quantity;
    private Boolean isProcedureAuthorized;
    private Boolean isPredet;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    private LocalDateTime paymentDate;
    @JsonProperty(value = "DOSDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = YYYY_MM_DD_T_HH_MM_SS_SSS_Z)
    private LocalDateTime DOSDate;
    private UriModel service;
    private IPeriodModel authorizationPeriod;

    public String getProcedureType() {
        return procedureType;
    }

    public void setProcedureType(String procedureType) {
        this.procedureType = procedureType;
    }

    public String getDecision() {
        return decision;
    }

    public void setDecision(String decision) {
        this.decision = decision;
    }

    public String getCdtSubmittedCd() {
        return cdtSubmittedCd;
    }

    public void setCdtSubmittedCd(String cdtSubmittedCd) {
        this.cdtSubmittedCd = cdtSubmittedCd;
    }

    public String getCdtCoveredCd() {
        return cdtCoveredCd;
    }

    public void setCdtCoveredCd(String cdtCoveredCd) {
        this.cdtCoveredCd = cdtCoveredCd;
    }

    public String getToothArea() {
        return toothArea;
    }

    public void setToothArea(String toothArea) {
        this.toothArea = toothArea;
    }

    public Money getBenefitAmount() {
        return benefitAmount;
    }

    public void setBenefitAmount(Money benefitAmount) {
        this.benefitAmount = benefitAmount;
    }

    public Money getDeductibleAmount() {
        return deductibleAmount;
    }

    public void setDeductibleAmount(Money deductibleAmount) {
        this.deductibleAmount = deductibleAmount;
    }

    public Money getCoinsuranceAmount() {
        return coinsuranceAmount;
    }

    public void setCoinsuranceAmount(Money coinsuranceAmount) {
        this.coinsuranceAmount = coinsuranceAmount;
    }

    public Money getSubmittedAmount() {
        return submittedAmount;
    }

    public void setSubmittedAmount(Money submittedAmount) {
        this.submittedAmount = submittedAmount;
    }

    public Money getCopayAmount() {
        return copayAmount;
    }

    public void setCopayAmount(Money copayAmount) {
        this.copayAmount = copayAmount;
    }

    public Money getConsideredAmount() {
        return consideredAmount;
    }

    public void setConsideredAmount(Money consideredAmount) {
        this.consideredAmount = consideredAmount;
    }

    public Money getCoveredAmount() {
        return coveredAmount;
    }

    public void setCoveredAmount(Money coveredAmount) {
        this.coveredAmount = coveredAmount;
    }

    public List<String> getRemarkCodes() {
        return remarkCodes;
    }

    public void setRemarkCodes(List<String> remarkCodes) {
        this.remarkCodes = remarkCodes;
    }

    public List<String> getSurfaces() {
        return surfaces;
    }

    public void setSurfaces(List<String> surfaces) {
        this.surfaces = surfaces;
    }

    public List<String> getToothCodes() {
        return toothCodes;
    }

    public void setToothCodes(List<String> toothCodes) {
        this.toothCodes = toothCodes;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Boolean getIsProcedureAuthorized() {
        return isProcedureAuthorized;
    }

    public void setIsProcedureAuthorized(Boolean isProcedureAuthorized) {
        this.isProcedureAuthorized = isProcedureAuthorized;
    }

    public Boolean getIsPredet() {
        return isPredet;
    }

    public void setIsPredet(Boolean isPredet) {
        this.isPredet = isPredet;
    }

    public LocalDateTime getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDateTime paymentDate) {
        this.paymentDate = paymentDate;
    }

    @JsonProperty(value = "DOSDate")
    public LocalDateTime getDOSDate() {
        return DOSDate;
    }

    @JsonProperty(value = "DOSDate")
    public void setDOSDate(LocalDateTime DOSDate) {
        this.DOSDate = DOSDate;
    }

    public UriModel getService() {
        return service;
    }

    public void setService(UriModel service) {
        this.service = service;
    }

    @JsonSerialize(as = PeriodModel.class)
    public IPeriodModel getAuthorizationPeriod() {
        return authorizationPeriod;
    }

    @JsonDeserialize(as = PeriodModel.class)
    public void setAuthorizationPeriod(IPeriodModel authorizationPeriod) {
        this.authorizationPeriod = authorizationPeriod;
    }
}
