/* Copyright © 2025 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.events.config;

import com.eisgroup.genesis.cap.common.versioning.events.handlers.CapChangeTrackingEventHandler;
import com.eisgroup.genesis.cap.common.versioning.repository.CapChangeHistoryRepository;
import com.eisgroup.genesis.cap.ref.events.handlers.CapDentalChangeTrackingEventHandler;
import com.eisgroup.genesis.cap.ref.repository.CapDentalSettlementIndexResolver;
import com.eisgroup.genesis.json.link.EntityLinkBuilderRegistry;
import com.eisgroup.genesis.streams.consumer.ErrorHandlingStrategy;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.List;

public class DentalVersioningEventsConfig {

    @Bean
    @Primary
    public CapChangeTrackingEventHandler capChangeTrackingEventHandler (CapChangeHistoryRepository repository,
                                                                        EntityLinkBuilderRegistry linkBuilderRegistry,
                                                                        @Qualifier("deadLetterQueueStrategy") ErrorHandlingStrategy deadLetterQueueStrategy,
                                                                        @Value("#{'${cap.versioning.supported.actions}'.split(',')}") List<String> applicableActions,
                                                                        @Value("#{'${cap.versioning.supported.model.names}'.split(',')}") List<String> applicableModelNames,
                                                                        @Value("${cap.versioning.group:Common}") String groupPrefix, CapDentalSettlementIndexResolver capDentalSettlementIndexResolver) {
        return new CapDentalChangeTrackingEventHandler(repository, linkBuilderRegistry, deadLetterQueueStrategy, applicableActions, applicableModelNames, groupPrefix, capDentalSettlementIndexResolver);
    }

}
