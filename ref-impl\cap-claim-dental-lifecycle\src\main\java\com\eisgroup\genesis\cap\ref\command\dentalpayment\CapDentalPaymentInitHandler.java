/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpayment;

import java.util.List;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.financial.command.payment.CapPaymentInitHandler;
import com.eisgroup.genesis.cap.ref.command.dentalpayment.input.CapDentalPaymentInitInput;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpaymentdefinition.CapDentalPaymentEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.repository.ModeledEntitySchemaResolver;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.generator.EntityNumberGenerator;
import com.eisgroup.genesis.model.repo.ModelRepository;
import com.eisgroup.genesis.model.repo.ModelRepositoryFactory;
import com.eisgroup.genesis.transformation.model.TransformationModel;
import com.eisgroup.genesis.transformation.service.ModeledTransformationService;
import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;;

/**
 * Command handler for initiating a {@link CapDentalPaymentEntity}
 *
 * <AUTHOR>
 * @since 22.3
 */
public class CapDentalPaymentInitHandler extends CapPaymentInitHandler<CapDentalPaymentInitInput, CapDentalPaymentEntity> {

    private static final ModelRepository<TransformationModel> MODEL_REPOSITORY = ModelRepositoryFactory.getRepositoryFor(TransformationModel.class);

    @Autowired
    private ModeledTransformationService transformationService;

    @Autowired
    private EntityNumberGenerator paymentNumberGenerator;

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPaymentInitInput request, @Nonnull CapDentalPaymentEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

    @Nonnull
    @Override
    public CapDentalPaymentEntity execute(@Nonnull CapDentalPaymentInitInput input, @Nonnull CapDentalPaymentEntity entity) {
        return populateCapPaymentAttributes(input, entity).get();
    }

    @Override
    protected Lazy<CapDentalPaymentEntity> populateCapPaymentAttributes(@Nonnull CapDentalPaymentInitInput input, @Nonnull CapDentalPaymentEntity capPayment) {
        return Lazy.of(transformationService.transform(MODEL_REPOSITORY.getActiveModel("InitCapDentalPayment"), input.toJson(), capPayment))
                .cast(CapDentalPaymentEntity.class)
                .flatMap(payment -> paymentNumberGenerator.generateAsync(
                        ModeledEntitySchemaResolver.getSchemaNameUsing(modelResolver.resolveModel(DomainModel.class), getVariation()))
                        .map(number -> {
                            payment.setPaymentNumber(number);
                            return payment;
                        }))
                .map(payment -> {
                    DomainModel model = modelResolver.resolveModel(DomainModel.class);
                    KeyTraversalUtil.traverseRoot(payment.toJson(), model);
                    return payment;
                });
    }
}
