/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.loss;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalClaimDataModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProviderDiscountModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.PENDING;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimProviderDiscountType.AMOUNT;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimProviderDiscountType.PERCENTAGE;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.*;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.*;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalClaimRules extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    private IndividualCustomerModel individualCustomer;
    private IIndividualProviderModel individualProvider;
    private ICapDentalPolicyInfoModel createdDentalPolicy;
    private final String TEST_DATA_1 = "TestData_Ortho1";
    private final String TEST_DATA_2 = "TestData_Ortho2";

    @BeforeClass(groups = {REGRESSION}, alwaysRun = true)
    public void createPreconditions() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer));
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_PredeterminationOrthodontics() {
        // Step 1.1
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_1);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);
        verifyDentalClaimAttributes(submitDentalClaim, dentalClaimModel);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaimMandatoryRules() {
        // Step 2.1
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_2);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        // Step 2.2
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().submit().perform(b -> b.setModel(initDentalClaim.getKey())));
        FailureAssertion.assertThat(failure).hasError(RECEIVED_DATE_IS_MANDATORY)
                .hasError(SOURCE_FOR_CLAIM_IS_MANDATORY)
                .hasError(PAYEE_TYPE_IS_MANDATORY)
                .hasError(TRANSACTION_TYPE_IS_MANDATORY)
                .hasError(PATIENT_IS_MANDATORY)
                .hasError(POLICY_HOLDER_IS_MANDATORY)
                .hasError(PROVIDER_IS_MANDATORY)
                .hasError(PROVIDER_FEE_TYPE_IS_MANDATORY)
                .hasError(PROVIDER_FEE_FEE_IS_MANDATORY)
                .hasError(DISCOUNT_TYPE_IS_MANDATORY)
                .hasError(DISCOUNT_NAME_IS_MANDATORY)
                .hasError(SUBMITTED_FEE_FOR_PROCEDURE_IS_MANDATORY)
                .hasError(PROCEDURE_CODE_IS_MANDATORY);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaimProviderDiscountMandatoryRules() {
        // Step 2.4
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_1);
        ICapDentalProviderDiscountModel providerDiscountModel = dentalClaimModel.getEntity().getClaimData().getProviderDiscount();
        providerDiscountModel.setDiscountType(PERCENTAGE);
        providerDiscountModel.setDiscountPercentage(null);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().submit().perform(b -> b.setModel(initDentalClaim.getKey())));
        FailureAssertion.assertThat(failure).hasError(DISCOUNT_PERCENTAGE_IS_MANDATORY);

        // Step 2.3
        providerDiscountModel.setDiscountType(AMOUNT);
        providerDiscountModel.setDiscountAmount(null);
        ICapDentalLossModel claimWithoutDiscountAmount = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(claimWithoutDiscountAmount.getState()).isEqualTo(INCOMPLETE);
        failure = getError(() -> capDentalLoss.getFacade().submit().perform(b -> b.setModel(claimWithoutDiscountAmount.getKey())));
        FailureAssertion.assertThat(failure).hasError(DISCOUNT_AMOUNT_IS_MANDATORY);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_OrthodonticServices() {
        // Step 2.5
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_1);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(ORTHODONTIC_SERVICES);
        dentalClaimModel.getEntity().getClaimData().setProviderFees(null);
        ICapDentalProcedureModel dentalProcedureModel = dentalClaimModel.getEntity().getSubmittedProcedures().get(0);
        dentalProcedureModel.getPreauthorization().setIsProcedureAuthorized(false);
        dentalProcedureModel.getPreauthorization().setAuthorizedBy(null);
        dentalProcedureModel.getPreauthorization().setAuthorizationPeriod(null);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);
        verifyDentalClaimAttributes(submitDentalClaim, dentalClaimModel);

        // Step 3.2
        ICapDentalLossModel updateDentalClaimModel = capDentalLoss.createUpdateDentalLossModel(submitDentalClaim);
        updateDentalClaimModel.getEntity().getSubmittedProcedures().get(0).setOrtho(null);
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().update().perform(b -> b.setModel(updateDentalClaimModel)));
        FailureAssertion.assertThat(failure).hasError(ORTHO_DETAILS_ARE_REQIRED_AND_ONLY_ONE_ORTHO_SERVICE_CAN_BE_PROVIDED);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_PredeterminationActualServices() {
        // Step 2.6
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_1);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(PREDETERMINATION_ACTUAL_SERVICES);
        ICapDentalProcedureModel dentalProcedureModel = dentalClaimModel.getEntity().getSubmittedProcedures().get(0);
        dentalProcedureModel.setOrtho(null);
        dentalProcedureModel.setPredetInd(null);
        dentalProcedureModel.getPreauthorization().setIsProcedureAuthorized(false);
        dentalProcedureModel.getPreauthorization().setAuthorizedBy(null);
        dentalProcedureModel.getPreauthorization().setAuthorizationPeriod(null);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getState()).isEqualTo(PENDING);
        verifyDentalClaimAttributes(submitDentalClaim, dentalClaimModel);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-153255", component = CAP_DENTAL_LOSS)
    public void testDentalClaim_PredeterminationOrthodontics_Error() {
        // Step 3.1
        ICapDentalLossModel dentalClaimModel = createDentalClaimModel(TEST_DATA_1);
        dentalClaimModel.getEntity().getClaimData().setTransactionType(PREDETERMINATION_ORTHODONTICS);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).setOrtho(null);
        ICapDentalLossModel initDentalClaim = capDentalLoss.initDentalLoss(dentalClaimModel);
        assertThat(initDentalClaim.getState()).isEqualTo(INCOMPLETE);
        IFailureResponse failure = getError(() -> capDentalLoss.getFacade().submit().perform(b -> b.setModel(initDentalClaim.getKey())));
        FailureAssertion.assertThat(failure).hasError(ORTHO_DETAILS_ARE_REQIRED_AND_ONLY_ONE_ORTHO_SERVICE_CAN_BE_PROVIDED);
    }

    private void verifyDentalClaimAttributes(ICapDentalLossModel actualClaimModel, ICapDentalLossModel expectedClaimModel) {
        ICapDentalClaimDataModel actualClaimDataModel = actualClaimModel.getLossDetail().getClaimData();
        ICapDentalClaimDataModel expectedClaimDataModel = expectedClaimModel.getEntity().getClaimData();
        ICapDentalProcedureModel actualProcedureModel = actualClaimModel.getLossDetail().getSubmittedProcedures().get(0);
        ICapDentalProcedureModel expectedProcedureModel = expectedClaimModel.getEntity().getSubmittedProcedures().get(0);
        assertSoftly(softly -> {
            // claimData attributes
            softly.assertThat(actualClaimDataModel.getTransactionType()).isEqualTo(expectedClaimDataModel.getTransactionType());
            softly.assertThat(actualClaimDataModel.getReceivedDate()).isEqualTo(expectedClaimDataModel.getReceivedDate());
            softly.assertThat(actualClaimDataModel.getSource()).isEqualTo(expectedClaimDataModel.getSource());
            softly.assertThat(actualClaimDataModel.getPayeeType()).isEqualTo(expectedClaimDataModel.getPayeeType());
            softly.assertThat(actualClaimDataModel.getPatientRole().getRegistryId()).isEqualTo(expectedClaimDataModel.getPatientRole().getRegistryId());
            softly.assertThat(actualClaimDataModel.getPolicyholderRole().getRegistryId()).isEqualTo(expectedClaimDataModel.getPolicyholderRole().getRegistryId());
            softly.assertThat(actualClaimDataModel.getProviderRole().getProviderLink()).isEqualTo(expectedClaimDataModel.getProviderRole().getProviderLink());
            if (expectedClaimDataModel.getProviderFees() != null) {
                softly.assertThat(actualClaimDataModel.getProviderFees().get(0).getFee())
                        .isEqualTo(expectedClaimDataModel.getProviderFees().get(0).getFee());
                softly.assertThat(actualClaimDataModel.getProviderFees().get(0).getType())
                        .isEqualTo(expectedClaimDataModel.getProviderFees().get(0).getType());
            } else {
                softly.assertThat(actualClaimDataModel.getProviderFees()).isNull();
            }
            if (expectedClaimDataModel.getProviderDiscount() != null) {
                softly.assertThat(actualClaimDataModel.getProviderDiscount().getDiscountType())
                        .isEqualTo(expectedClaimDataModel.getProviderDiscount().getDiscountType());
                softly.assertThat(actualClaimDataModel.getProviderDiscount().getDiscountName())
                        .isEqualTo(expectedClaimDataModel.getProviderDiscount().getDiscountName());
                softly.assertThat(actualClaimDataModel.getProviderDiscount().getDiscountAmount())
                        .isEqualTo(expectedClaimDataModel.getProviderDiscount().getDiscountAmount());
                softly.assertThat(actualClaimDataModel.getProviderDiscount().getDiscountPercentage())
                        .isEqualTo(expectedClaimDataModel.getProviderDiscount().getDiscountPercentage());
            }
            // submittedProcedures attributes
            if(expectedProcedureModel.getPredetInd() != null) {
                softly.assertThat(actualProcedureModel.getPredetInd()).isEqualTo(expectedProcedureModel.getPredetInd());
            } else {
                softly.assertThat(actualProcedureModel.getPredetInd()).isEqualTo(true);
            }
            softly.assertThat(actualProcedureModel.getDateOfService()).isEqualTo(expectedProcedureModel.getDateOfService());
            softly.assertThat(actualProcedureModel.getProcedureCode()).isEqualTo(expectedProcedureModel.getProcedureCode());
            softly.assertThat(actualProcedureModel.getSubmittedFee()).isEqualTo(expectedProcedureModel.getSubmittedFee());
            if (expectedProcedureModel.getPreauthorization() != null) {
                softly.assertThat(actualProcedureModel.getPreauthorization().getIsProcedureAuthorized())
                        .isEqualTo(expectedProcedureModel.getPreauthorization().getIsProcedureAuthorized());
                softly.assertThat(actualProcedureModel.getPreauthorization().getAuthorizedBy())
                        .isEqualTo(expectedProcedureModel.getPreauthorization().getAuthorizedBy());
                if (expectedProcedureModel.getPreauthorization().getAuthorizationPeriod() != null) {
                    softly.assertThat(actualProcedureModel.getPreauthorization().getAuthorizationPeriod().getStartDate())
                            .isEqualTo(expectedProcedureModel.getPreauthorization().getAuthorizationPeriod().getStartDate());
                    softly.assertThat(actualProcedureModel.getPreauthorization().getAuthorizationPeriod().getEndDate())
                            .isEqualTo(expectedProcedureModel.getPreauthorization().getAuthorizationPeriod().getEndDate());
                }
            }
            // ortho attributes
            if (expectedProcedureModel.getOrtho() != null) {
                softly.assertThat(actualProcedureModel.getOrtho().getOrthoFrequencyCd())
                        .isEqualTo(expectedProcedureModel.getOrtho().getOrthoFrequencyCd());
                softly.assertThat(actualProcedureModel.getOrtho().getOrthoMonthQuantity())
                        .isEqualTo(expectedProcedureModel.getOrtho().getOrthoMonthQuantity());
            } else {
                softly.assertThat(actualProcedureModel.getOrtho()).isNull();
            }
        });

    }

    private ICapDentalLossModel createDentalClaimModel(String testData) {
        return capDentalLoss.createDentalLossModel(capDentalLoss.getSpecificTestData("DentalClaimDataModelRules", testData),
                createdDentalPolicy.getCapPolicyId(), individualCustomer, individualProvider);
    }
}