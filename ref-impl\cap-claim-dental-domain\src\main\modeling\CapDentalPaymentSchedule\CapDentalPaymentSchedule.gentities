@Description("The Root Entity of CAP Payment Schedule Domain.")
@StateMachine
Entity CapDentalPaymentScheduleEntity is CapPaymentSchedule {

    //EISDEVTS-49203
    @Description("A date when the schedule was created.")
    @Searchable
    Attr creationDate: Datetime

    //EISDEVTS-45702
    @Description("Dental claim for which the schedule is created.")
    @Searchable
    ExtLink originSource: RootEntity

    //EISDEVTS-45702
    @Description("List of schedule payments.")
    Attr payments: *CapDentalScheduledPaymentEntity

    //EISDEVTS-45702
    @Description("Link to the template the schedule is created from.")
    @Searchable
    ExtLink paymentTemplate: RootEntity

    //EISDEVTS-46034
    @Description("Stores Payment Schedule messages")
    Attr scheduleMessages: *CapDentalPaymentScheduleMessageEntity

    @Label("Payment Schedule Activation Result")
    Attr paymentScheduleActivationResult: CapDentalPaymentScheduleActivationResult
}

@Description("Stores Payment Schedule messages")
Entity CapDentalPaymentScheduleMessageEntity is MessageType {

}

@Description("Defines payment transaction information.")
Entity CapDentalScheduledPaymentEntity is CapScheduledPayment {

    //EISDEVTS-45702
    @Description("Dental payment details.")
    Attr paymentDetails: CapDentalPaymentDetailsEntity
}

@Description("Rules input for Payment generation rules.")
Entity CapDentalPaymentGenerationRulesInput {

    //EISDEVTS-48811
    @Description("Current system date and time.")
    Attr currentDateTime: Datetime

    //EISDEVTS-48811
    @Description("Last date when payment was generated for the claim.")
    Attr lastGeneratedPaymentDate: Datetime

    //EISDEVTS-48811
    @Description("Rules input for Payment generation rules.")
    Attr paymentSchedule: CapDentalPaymentScheduleEntity
}

@Description("Rules output for Payment generation rules.")
Entity CapDentalPaymentGenerationRulesOutput {

    //EISDEVTS-48811
    @Description("List of payments.")
    Attr selectedPayments: *CapDentalScheduledPaymentEntity
}

Entity CapDentalPaymentScheduleActivationResult {
   @Description("Error and informational messages that appear during the Payment Schedule activation.")
   @Label("Activation Messages")
   Attr activationMessages: *CapDentalPaymentScheduleMessageEntity

   @Label("Activation Status")
   Attr activationStatus: String
}


Entity CapDentalPaymentScheduleActivationInput {
  @Label("Payment Schedule")
  Attr paymentSchedule: CapDentalPaymentScheduleEntity

  @Label("User Authority")
  Attr userAuthority: *CapDentalPaymentScheduleUserAuthorityLevel
}

Entity CapDentalPaymentScheduleUserAuthorityLevel {
  @Label("Authority Level")
  Attr authorityLevel: Integer

  @Label("Sub Type Cd")
  Attr subTypeCd: String

  @Label("Type Cd")
  Attr typeCd: String
}

Entity CancelActivePaymentSchedulesOutput {
    ExtLink originSource: RootEntity
    ExtLink cancelledSchedules: *RootEntity
}