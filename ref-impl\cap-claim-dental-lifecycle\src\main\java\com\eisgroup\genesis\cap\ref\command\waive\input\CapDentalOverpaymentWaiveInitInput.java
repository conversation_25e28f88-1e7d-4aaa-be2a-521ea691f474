/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.waive.input;

import com.eisgroup.genesis.cap.financial.command.waive.input.CapOverpaymentWaiveInitInput;
import com.google.gson.JsonObject;

/**
 * {@link } command input
 *
 * <AUTHOR>
 * @since 22.15
 */
public class CapDentalOverpaymentWaiveInitInput extends CapOverpaymentWaiveInitInput {

    public CapDentalOverpaymentWaiveInitInput(JsonObject original) {
        super(original);
    }
}
