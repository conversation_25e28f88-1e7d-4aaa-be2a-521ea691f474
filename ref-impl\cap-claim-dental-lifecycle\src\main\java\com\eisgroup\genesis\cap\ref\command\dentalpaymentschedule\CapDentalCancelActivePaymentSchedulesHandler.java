/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.bam.CustomActivityTracking;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalCancelActivePaymentSchedulesInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CancelActivePaymentSchedulesOutput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.key.EntityKey;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.Variation;
import com.google.gson.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.cap.financial.command.CapPaymentScheduleCommands.CANCEL_PAYMENT_SCHEDULE;

/**
 * Cancels all Active or Suspended {@link com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity}
 * related to specific {@link com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity}
 *
 * <AUTHOR>
 * @since 22.10
 */
@CustomActivityTracking
public class CapDentalCancelActivePaymentSchedulesHandler implements ProductCommandHandler<CapDentalCancelActivePaymentSchedulesInput, CancelActivePaymentSchedulesOutput> {

    private static final List<String> APPLICABLE_STATES = List.of("Open", "Active", "Suspended");
    private static final String DENTAL_PAYMENT_SCHEDULE = "CapDentalPaymentSchedule";

    @Autowired
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;

    @Autowired
    private CommandPublisher commandPublisher;

    @Nonnull
    @Override
    public CancelActivePaymentSchedulesOutput load(@Nonnull CapDentalCancelActivePaymentSchedulesInput request) {
        return Lazy.of(ModelInstanceFactory.createInstance(DENTAL_PAYMENT_SCHEDULE, "1", CancelActivePaymentSchedulesOutput.class.getSimpleName()))
                .cast(CancelActivePaymentSchedulesOutput.class).get();
    }

    @Nonnull
    @Override
    public CancelActivePaymentSchedulesOutput execute(@Nonnull CapDentalCancelActivePaymentSchedulesInput request, @Nonnull CancelActivePaymentSchedulesOutput entity) {
        return Lazy.from(() ->buildPaymentSchedulesLinks(request))
                .map(canceledPaymentSchedules -> {
                    entity.setOriginSource(request.getOriginSource());
                    entity.setCancelledSchedules(canceledPaymentSchedules.get());
                    //Workaround for Workflow because CancelActivePaymentSchedulesOutput is not persistable entity, need to set model name, uri and key
                    FactoryLink factoryLink = new FactoryLink(request.getOriginSource());
                    entity.toJson().addProperty("_uri", request.getOriginSource().getURIString());
                    entity.toJson().addProperty("_modelName", DENTAL_PAYMENT_SCHEDULE);
                    entity.toJson().add(BaseKey.ATTRIBUTE_NAME, new EntityKey(factoryLink.getRootId(), factoryLink.getRevisionNo(), factoryLink.getRootId(), factoryLink.getRootId()).toJson());
                    return entity;
                }).get();
    }

    private Lazy<List<EntityLink<RootEntity>>> buildPaymentSchedulesLinks(@Nonnull CapDentalCancelActivePaymentSchedulesInput request) {
        return Lazy.from(()->capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(request.getOriginSource(), getApplicableStates())
                .flatMap(this::cancelPaymentSchedule).collect(Collectors.toList()));
    }

    @Nonnull
    @Override
    public CancelActivePaymentSchedulesOutput save(@Nonnull CapDentalCancelActivePaymentSchedulesInput request, @Nonnull CancelActivePaymentSchedulesOutput entity) {
        return Lazy.of(entity).get();
    }

    protected List<String> getApplicableStates() {
        return APPLICABLE_STATES;
    }

    protected Lazy<EntityLink<RootEntity>> cancelPaymentSchedule(CapDentalPaymentScheduleIdxEntity indexEntity) {
        EntityLink<RootEntity> paymentScheduleLink = new EntityLink<>(RootEntity.class, indexEntity.getPaymentSchedule());
        FactoryLink link = new FactoryLink(paymentScheduleLink);
        Command command = new Command(Variation.INVARIANT.getName(), CANCEL_PAYMENT_SCHEDULE, createCommandInput(link));
        return commandPublisher.publishLocally(command, link.getModelName())
                .filter(commandResult -> !commandResult.isFailure())
                .map(commandResult -> paymentScheduleLink);
    }

    private JsonObject createCommandInput(FactoryLink link) {
        RootEntityKey key = new RootEntityKey(link.getRootId(), link.getRevisionNo());
        return new IdentifierRequest(key).toJson();
    }

    @Override
    public String getName() {
        return CapDentalPaymentScheduleCommands.CANCEL_LOSS_PAYMENT_SCHEDULES;
    }
}
