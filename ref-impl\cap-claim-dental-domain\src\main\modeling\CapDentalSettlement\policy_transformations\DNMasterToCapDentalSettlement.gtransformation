// Flattens dental master policy plans and according to that plans create dental settlement policy info for each plan
Transformation DNMasterToCapDentalSettlement {
    Input {
        Ext CapPolicyHolder.CapPolicyEntity as policy
        Ext CapDentalSettlement.CapDentalProcedureEntity as inProcedure
    }
    Output {
        *CapDentalSettlement.CapDentalPolicyInfoEntity as out
    }

    Mapping out is policy.benefitPackagingDetail.plans {
        Var waitingPeriod is covDef.waitingPeriods

    Var dateOfService is inProcedure.dateOfService
    Var policyEffectiveDate is policy.termDetails.termEffectiveDate

    Var diffYear is Ternary(CompareDates(dateOfService,policyEffectiveDate) < 0  ,0,
                               Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P365D")))<1, 1,
                                   Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P731D")))<1, 2,
                                           Ternary(CompareDates(AsDate(dateOfService), AsDate(IncreaseByDuration(policyEffectiveDate, "P1096D"))) <1, 3,
                                                  4
                                                  ))))


        Var filteredCoinsurancesByGradedYear is Ternary(Equals(covDef.coinsuranceDetails.coinsuranceNumberOfGradedYears, Null()) ,
                                             covDef.coinsuranceDetails.coinsurances,
                                             covDef.coinsuranceDetails.coinsurances[FilterByDiffYear])

        Attr planCategory is covDef.planCategory
        Attr planName is planName
        Attr term is produceTerm(Super().policy.termDetails)
        Attr riskStateCd is Super().policy.riskStateCd
        Attr plan is planCd
        Attr coinsurances is SafeInvoke(filterCoInsurances,
                                        produceCoinsurance(filterCoInsurances))
        Attr orthoINNCoinsurancePercent is SafeInvoke(covDef.orthodonticCoverage.orthoCoinsuranceIN, Mult(covDef.orthodonticCoverage.orthoCoinsuranceIN, 100))
        Attr orthoOONCoinsurancePercent is SafeInvoke(covDef.orthodonticCoverage.orthoCoinsuranceOON, Mult(covDef.orthodonticCoverage.orthoCoinsuranceOON, 100))
        Attr preventWaitingPeriod is "" + waitingPeriod.preventiveWaitingPeriod
        Attr basicWaitingPeriod is "" + waitingPeriod.basicWaitingPeriod
        Attr majorWaitingPeriod is "" + waitingPeriod.majorWaitingPeriod
        Attr lateEntrantWaitingPeriodsDetails is SafeInvoke(waitingPeriod.lateEntrantWaitingPeriodsDetails,
                FlatMap(produceLateEntrantWaitingPeriod(waitingPeriod.lateEntrantWaitingPeriodsDetails)))
        Attr applyLateEntrantBenefitWaitingPeriods is waitingPeriod.applyLateEntrantBenefitWaitingPeriods
        Attr orthoWaitingPeriod is "" + covDef.orthodonticCoverage.orthoWaitingPeriod
        Attr fullTimeStudentAgeCd is covDef.dependentEligibilityDetails.fullTimeStudentAgeCd
        Attr childMaxAgeCd is covDef.dependentEligibilityDetails.childMaxAgeCd

        Attr serviceCategory is covDef.serviceCategories {
            Attr scOralEvaluations is scOralEvaluations
            Attr scSurgicalPeriodontics is scSurgicalPeriodontics
            Attr scCrowns is scCrowns
            Attr scImplantServices is scImplantServices
            Attr scFillings is scFillings
            Attr scFluorides is scFluorides
            Attr scStainlessSteelCrowns is scStainlessSteelCrowns
            Attr scBitewingRadiographs is scBitewingRadiographs
            Attr scFullMouthRadiographs is scFullMouthRadiographs
            Attr scAllOtherRadiographs is scAllOtherRadiographs
            Attr scRootCanals is scRootCanals
        }

        Attr frequencyLimitations is produceFrequencyLimitations(covDef)

        Attr orthoAvailability is covDef.orthodonticCoverage.orthoAvailability
        Attr orthoChildAgeLimit is covDef.orthodonticCoverage.orthoChildAgeLimit

        Producer produceFrequencyLimitations(covDef) {
            Var preventiveLimitations is First(covDef.limitations[PreventiveLimitationsFilter])
            Var basicLimitations is First(covDef.limitations[BasicLimitationsFilter])
            Var majorLimitations is First(covDef.limitations[MajorLimitationsFilter])

            Attr preventiveLimitations is SafeInvoke(preventiveLimitations, producePreventiveLimitations(preventiveLimitations))
            Attr basicLimitations is SafeInvoke(basicLimitations, produceBasicLimitations(basicLimitations))
            Attr majorLimitations is SafeInvoke(majorLimitations, produceMajorLimitations(majorLimitations))
        }

        Producer producePreventiveLimitations(preventiveLimitation) {
            Attr preventiveOralEvaluations is preventiveLimitation.preventiveOralEvaluations
            Attr preventiveFluorideTreatment is preventiveLimitation.preventiveFluorideTreatment
            Attr preventiveFluorideTreatmentAgeLimit is preventiveLimitation.preventiveFluorideTreatmentAgeLimit
            Attr preventiveBitewingRadiographs is preventiveLimitation.preventiveBitewingRadiographs
        }

        Producer produceBasicLimitations(basicLimitation) {
            Attr basicPeriodontalSurgery is basicLimitation.basicPeriodontalSurgery
            Attr basicStainlessSteelCrowns is basicLimitation.basicStainlessSteelCrowns
            Attr basicStainlessSteelCrownsAgeLimit is basicLimitation.basicStainlessSteelCrownsAgeLimit
        }

        Producer produceMajorLimitations(majorLimitation) {
            Attr majorCrowns is majorLimitation.majorCrowns
            Attr majorImplants is majorLimitation.majorImplants
            Attr majorDentureAdjustments is majorLimitation.majorDentureAdjustments
        }

        Producer produceCoinsurance(coinsurance) {
            Attr coinsuranceServiceType is coinsurance.coinsuranceServiceType
            Attr coinsuranceINPct is SafeInvoke(coinsurance.coinsuranceINAmount, Mult(coinsurance.coinsuranceINAmount, 100))
            Attr coinsuranceOONPct is SafeInvoke(coinsurance.coinsuranceOONAmount, Mult(coinsurance.coinsuranceOONAmount, 100))
        }

        Producer produceLateEntrantWaitingPeriod(latePeriodDetails) {
            Attr preventWaitingPeriod is "" + latePeriodDetails.preventiveWaitingPeriod
            Attr basicWaitingPeriod is "" + latePeriodDetails.basicWaitingPeriod
            Attr majorWaitingPeriod is "" + latePeriodDetails.majorWaitingPeriod
        }

        Producer produceTerm(termDetails) {
            Attr effectiveDate is termDetails.termEffectiveDate
            Attr expirationDate is termDetails.termExpirationDate
        }

        Filter PreventiveLimitationsFilter {
            Equals(_type, "DNPreventiveDiagnostic")
        }

        Filter BasicLimitationsFilter {
            Equals(_type, "DNBasic")
        }

        Filter MajorLimitationsFilter {
            Equals(_type, "DNMajor")
        }
        Filter FilterByDiffYear {
            coinsuranceGradedYear == Root().diffYear
        }
    }

}