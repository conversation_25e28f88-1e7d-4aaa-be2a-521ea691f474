/*
 *  Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 *  CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.accumulator.service;

import com.eis.automation.v20.cap.entity.capunverifiedpolicy.common.facade.impl.modeling.interf.ICapPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorContainerModel;
import com.eis.automation.v20.capdental.entity.accumulator.facade.impl.modeling.interf.ICapAccumulatorLoadModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.ICustomerModel;

import java.util.List;

public interface ICapAccumulatorService {
    ICapAccumulatorLoadModel createCapAccumulatorLoadModel(ICustomerModel customerModel, ICapPolicyInfoModel capPolicyModel);

    List<ICapAccumulatorContainerModel> loadAccumulator(ICustomerModel customerModel, ICapPolicyInfoModel capPolicyModel);
}