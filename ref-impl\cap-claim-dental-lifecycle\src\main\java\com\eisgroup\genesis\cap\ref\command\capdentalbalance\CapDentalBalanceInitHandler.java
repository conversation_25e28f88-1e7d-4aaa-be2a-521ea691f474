/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalbalance;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.capdentalbalance.input.CapDentalBalanceInitInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalBalanceRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalbalance.CapDentalBalanceEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.model.ModelResolver;
import com.eisgroup.genesis.time.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;

import static com.eisgroup.genesis.cap.ref.command.capdentalbalance.CapDentalBalanceCommands.INIT_DENTAL_BALANCE;

/**
 * Handler for dental balance initiation.
 *
 * <AUTHOR>
 * @since 22.13
 */
@Modifying
public class CapDentalBalanceInitHandler implements ProductCommandHandler<CapDentalBalanceInitInput, CapDentalBalanceEntity> {

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private CapDentalBalanceRepository capDentalBalanceRepository;

    @Nonnull
    @Override
    public CapDentalBalanceEntity load(@Nonnull CapDentalBalanceInitInput entityInput) {
        return (CapDentalBalanceEntity) ModelInstanceFactory.createRootInstance(this.modelResolver.getModelName(), this.modelResolver.getModelVersion());
    }

    @Nonnull
    @Override
    public CapDentalBalanceEntity execute(@Nonnull CapDentalBalanceInitInput entityInput, @Nonnull CapDentalBalanceEntity entity) {
        DomainModel model = modelResolver.resolveModel(DomainModel.class);
        KeyTraversalUtil.traverseRoot(entity.toJson(), model);
        return populateAttributes(entityInput, entity).get();
    }

    public Lazy<CapDentalBalanceEntity> populateAttributes(CapDentalBalanceInitInput input, CapDentalBalanceEntity entity) {
        return Lazy.of(entity)
                .map(populatedEntity -> {
                    populatedEntity.setOriginSource(input.getOriginSource());
                    populatedEntity.setPayee(input.getPayee());
                    populatedEntity.setTotalBalanceAmount(input.getTotalBalanceAmount());
                    populatedEntity.setBalanceItems(input.getBalanceItems());
                    populatedEntity.setCreationDate(TimeUtils.currentTime());
                    return populatedEntity;
                });
    }

    @Nonnull
    @Override
    public CapDentalBalanceEntity save(@Nonnull CapDentalBalanceInitInput entityInput, @Nonnull CapDentalBalanceEntity entity) {
        return capDentalBalanceRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return INIT_DENTAL_BALANCE;
    }

}
