/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapPolicyInfoLateEntrantWaitingPeriodModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapPolicyInfoLateEntrantWaitingPeriodModel extends TypeModel implements ICapPolicyInfoLateEntrantWaitingPeriodModel {

    private Integer preventWaitingPeriod;
    private Integer basicWaitingPeriod;
    private Integer majorWaitingPeriod;

    public Integer getPreventWaitingPeriod() {
        return preventWaitingPeriod;
    }

    public void setPreventWaitingPeriod(Integer preventWaitingPeriod) {
        this.preventWaitingPeriod = preventWaitingPeriod;
    }

    public Integer getBasicWaitingPeriod() {
        return basicWaitingPeriod;
    }

    public void setBasicWaitingPeriod(Integer basicWaitingPeriod) {
        this.basicWaitingPeriod = basicWaitingPeriod;
    }

    public Integer getMajorWaitingPeriod() {
        return majorWaitingPeriod;
    }

    public void setMajorWaitingPeriod(Integer majorWaitingPeriod) {
        this.majorWaitingPeriod = majorWaitingPeriod;
    }
}
