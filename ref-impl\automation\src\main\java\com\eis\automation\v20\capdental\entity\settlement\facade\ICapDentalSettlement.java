/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.settlement.facade;

import com.eis.automation.v20.cap.entity.capsettlement.base.facade.ICapSettlement;

public interface ICapDentalSettlement extends ICapSettlement {
}
