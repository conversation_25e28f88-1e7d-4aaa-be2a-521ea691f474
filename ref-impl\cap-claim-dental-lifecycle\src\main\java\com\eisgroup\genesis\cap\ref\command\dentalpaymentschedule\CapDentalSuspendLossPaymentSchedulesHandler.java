/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.cap.ref.command.dentalpaymentschedule.input.CapDentalSuspendActivePaymentSchedulesInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPaymentScheduleIndexRepository;
import com.eisgroup.genesis.command.Command;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.commands.publisher.api.CommandPublisher;
import com.eisgroup.genesis.commands.request.IdentifierRequest;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpaymentscheduleindex.CapDentalPaymentScheduleIdxEntity;
import com.eisgroup.genesis.factory.model.dentalinternal.SuspendActivePaymentSchedulesOutput;
import com.eisgroup.genesis.factory.modeling.types.RootEntity;
import com.eisgroup.genesis.factory.repository.links.FactoryLink;
import com.eisgroup.genesis.json.key.BaseKey;
import com.eisgroup.genesis.json.key.RootEntityKey;
import com.eisgroup.genesis.json.link.EntityLink;
import com.eisgroup.genesis.model.Variation;
import com.google.gson.JsonObject;
import io.reactivex.Maybe;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Nonnull;
import java.util.List;
import java.util.stream.Collectors;

import static com.eisgroup.genesis.cap.financial.command.CapPaymentScheduleCommands.SUSPEND_PAYMENT_SCHEDULE;

/**
 * Suspends all Active {@link com.eisgroup.genesis.factory.model.capdentalpaymentschedule.CapDentalPaymentScheduleEntity}
 * related to specific {@link com.eisgroup.genesis.factory.model.capdentalloss.CapDentalLossEntity}
 *
 * <AUTHOR>
 * @since 22.10
 */
public class CapDentalSuspendLossPaymentSchedulesHandler implements ProductCommandHandler<CapDentalSuspendActivePaymentSchedulesInput, SuspendActivePaymentSchedulesOutput> {

    private static final List<String> APPLICABLE_STATES = List.of("Active");
    private static final String DENTAL_INTERNAL_MODEL = "DentalInternal";

    @Autowired
    private CapDentalPaymentScheduleIndexRepository capDentalPaymentScheduleIndexRepository;

    @Autowired
    private CommandPublisher commandPublisher;

    @Nonnull
    @Override
    public SuspendActivePaymentSchedulesOutput load(@Nonnull CapDentalSuspendActivePaymentSchedulesInput request) {
        return Lazy.of(ModelInstanceFactory.createInstance(DENTAL_INTERNAL_MODEL, "1", SuspendActivePaymentSchedulesOutput.class.getSimpleName()))
            .cast(SuspendActivePaymentSchedulesOutput.class).get();
    }

    @Nonnull
    @Override
    public SuspendActivePaymentSchedulesOutput execute(@Nonnull CapDentalSuspendActivePaymentSchedulesInput request, @Nonnull SuspendActivePaymentSchedulesOutput entity) {
        return Lazy.from(() ->buildPaymentSchedulesLinks(request))
            .map(suspendedPaymentSchedules -> {
                entity.toJson().remove(BaseKey.ATTRIBUTE_NAME);
                entity.setOriginSource(request.getOriginSource());
                entity.setSuspendedSchedules(suspendedPaymentSchedules.get());
                return entity;
            }).get();
    }

    public Lazy<List<EntityLink<RootEntity>>> buildPaymentSchedulesLinks(@Nonnull CapDentalSuspendActivePaymentSchedulesInput request) {
        return Lazy.from(()->capDentalPaymentScheduleIndexRepository.resolvePaymentScheduleIndex(request.getOriginSource(), getApplicableStates())
            .flatMap(this::suspendPaymentSchedule).collect(Collectors.toList()));
    }

    @Nonnull
    @Override
    public SuspendActivePaymentSchedulesOutput save(@Nonnull CapDentalSuspendActivePaymentSchedulesInput request, @Nonnull SuspendActivePaymentSchedulesOutput entity) {
        return Lazy.of(entity).get();
    }

    protected List<String> getApplicableStates() {
        return APPLICABLE_STATES;
    }

    protected Lazy<EntityLink<RootEntity>> suspendPaymentSchedule(CapDentalPaymentScheduleIdxEntity indexEntity) {
        EntityLink<RootEntity> paymentScheduleLink = new EntityLink<>(RootEntity.class, indexEntity.getPaymentSchedule());
        FactoryLink link = new FactoryLink(paymentScheduleLink);
        Command command = new Command(Variation.INVARIANT.getName(), SUSPEND_PAYMENT_SCHEDULE, createCommandInput(link));
        return commandPublisher.publishLocally(command, link.getModelName())
                .filter(commandResult -> !commandResult.isFailure())
                .map(commandResult -> paymentScheduleLink);
    }

    private JsonObject createCommandInput(FactoryLink link) {
        RootEntityKey key = new RootEntityKey(link.getRootId(), link.getRevisionNo());
        return new IdentifierRequest(key).toJson();
    }

    @Override
    public String getName() {
        return CapDentalPaymentScheduleCommands.SUSPEND_LOSS_PAYMENT_SCHEDULES;
    }
}
