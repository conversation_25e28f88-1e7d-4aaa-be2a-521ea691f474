/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.patienthistory.facade;

import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.platform.common.action.CommonLoadAction;
import com.eis.automation.v20.platform.common.action.PostModelAction;

public interface ICapDentalPatientHistory {

    PostModelAction<ICapDentalPatientHistoryWrapperModel, ICapDentalPatientHistoryModel> init();

    CommonLoadAction<ICapDentalPatientHistoryModel> entities();
}
