@Library("ci-config@master")
@Library("ci-library@master")
import PodTemplateGenerator

pipeline {
    parameters {
        choice(name: 'SONAR_MODE', description: 'component build sonar mode', choices: ['UT-IT', 'ETCS', 'UT-IT+ETCS'])
        gitParameter(name: 'BRANCH', type: 'PT_BRANCH', sortMode: 'ASCENDING_SMART', defaultValue: 'master', selectedValue: 'DEFAULT',
                branchFilter: 'origin/(.*)', listSize: '1', quickFilterEnabled: true, description: 'Branch name to build.')
    }

    environment {  
        PROJECT = "dxp-claim-benefits-dental"
        MS_LIST = "dxp-claim-benefits-dental-app"
    }

    options {
        skipStagesAfterUnstable()
        timestamps()
        quietPeriod(0)
        buildDiscarder(logRotator(numToKeepStr: '30', artifactNumToKeepStr: '30'))
        timeout(time: 4, unit: 'HOURS')
    }

    agent {
        kubernetes {
            defaultContainer 'sbt'
            yaml new PodTemplateGenerator(this)
                .kind(templateKind: 'sbt21-sonar')
                .setTemplateMetadata(namespace: "automation")
                .generate()
        }
    }

    stages {
        stage('Build app') {
            steps {
                script {
                    dir('dxp') {
                        currentBuild.displayName = "#${BUILD_NUMBER}"                 
                        sh "sbt -Djava.io.tmpdir=$JENKINS_AGENT_WORKDIR -Dsbt.log.noformat=true clean compile dist"
                    }
                }
            }
        }

        stage('Collect UT-IT Coverage and Sonar Analysis') {
            when {
                expression { SONAR_MODE == 'UT-IT' || SONAR_MODE == 'UT-IT+ETCS' }
            }
            steps {
                script {
                    dir('dxp') {
                        withCredentials([usernamePassword(credentialsId: config.GIT_CREDENTIALS_ID, usernameVariable: 'username', passwordVariable: 'password')]) {
                
                            jacocoFilePath = "$JENKINS_AGENT_WORKDIR"
                    
                            println '== STAGE: Collect UT-IT coverage =='
                            sh """
                                #!/bin/bash
                                set +e
                                java -javaagent:/bin/jacoco/lib/jacocoagent.jar=output=file,destfile=/home/<USER>/UT-IT-jacoco/UT-IT-jacoco.exec,append=true 2>/dev/null
                                set -e
                                cp -rf ~/sonar/JacocoPluginSettingsUTIT.scala $WORKSPACE/dxp/project/JacocoPluginSettings.scala
                            """
                    
                            println '== STAGE: Generating UT-IT Coverage Report =='
                            generateReport(jacocoFilePath)
                    
                            println '== STAGE: Performing Sonar Analysis for UT-IT =='
                            sendReport(env.PROJECT, 'UT-IT')
                        }
                    }
                }
            }
        }
        
        stage('Collect ETCS Coverage and Sonar Analysis') {
            when {
                expression { SONAR_MODE == 'ETCS' || SONAR_MODE == 'UT-IT+ETCS' }
            }
            steps {
                script {
                    dir('dxp') {
                        withCredentials([usernamePassword(credentialsId: config.GIT_CREDENTIALS_ID, usernameVariable: 'username', passwordVariable: 'password')]) {                        
                            jacocoFilePath = "$JENKINS_AGENT_WORKDIR"
            
                            println '== STAGE: Collect ETCS coverage =='
            
                            sh """
                                source ~/sonar/sonar_etcs_datafile_generator.sh
            
                                cp -rf ~/sonar/JacocoPluginSettings.scala $WORKSPACE/dxp/project/JacocoPluginSettings.scala
                            """
            
                            println '== STAGE: Generating ETCS Coverage Report =='
                            generateReport(jacocoFilePath)
            
                            println '== STAGE: Execute Sonar Scan ETCS =='
                            sendReport(env.PROJECT, 'ETCS')
                        }
                    }
                }
            }
        }

        stage('Merge UT-IT+ETCS Coverage and Sonar Analysis') {
            when {
                expression { SONAR_MODE == 'UT-IT+ETCS' }
            }
            steps {
                script {
                    dir('dxp') {
                        withCredentials([usernamePassword(credentialsId: config.GIT_CREDENTIALS_ID, usernameVariable: 'username', passwordVariable: 'password')]) {             
                            jacocoFilePath = "$JENKINS_AGENT_WORKDIR"
            
                            println '== STAGE: Merging UT-IT and ETCS Coverage Data =='
                            sh """
                                set -x
                                java -jar /bin/jacoco/lib/jacococli.jar merge /home/<USER>/etcs-jacoco/etcs-jacoco.exec /home/<USER>/UT-IT-jacoco/UT-IT-jacoco.exec --destfile ${JENKINS_AGENT_WORKDIR}/merged-jacoco.exec
                                set +x
                                cp -rf ~/sonar/JacocoPluginSettings.scala $WORKSPACE/dxp/project/JacocoPluginSettings.scala
                            """
            
                            println '== STAGE: Performing Final Sonar Analysis for Combined UT-IT and ETCS Coverage =='
                            generateReport(jacocoFilePath)
                            sendReport(env.PROJECT, 'UT-IT+ETCS')
                        }
                    } 
                }
            }
        }
    }
}

void generateReport(String workdir) {
    sh "sbt -Djava.io.tmpdir=$workdir jacoco"
}

void sendReport(String name, String mode) {
    String projectName = "$name $mode"
 
    if (SONAR_MODE == 'UT-IT+ETCS') {
        projectKey = projectName.replace(' ', '_').replace('+', '_')
    } else {
        projectKey = projectKey = projectName.replaceAll("[\\[\\]()]", "").replace(' ', '_')
    }
     
    sh """
        project_name="$projectName"
        project_key="$projectKey"
        report_paths=\$(find ~+ -type f -name "jacoco.xml" | tr "\n" ",")
        source ~/sonar/sonar_scanner.sh \
        -DsonarScanner.home=~/sonar/ \
        -Dsonar.projectName="\$project_name" \
        -Dsonar.projectKey=\$project_key \
        -Dsonar.exclusions="**/test/**,**/conf/**,project/**,Dockerfile,jenkins/**,ref-impl/**" \
        -Dsonar.coverage.jacoco.xmlReportPaths=\$report_paths \
        -Dsonar.projectBaseDir=. \
        -Dsonar.java.binaries=. \
        -Dsonar.host.url="$config.SONAR_URL" \
        -Dsonar.login=\$SONAR_KEY
    """
}
