/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.validator;

import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.input.CapDentalPatientHistoryUpdateInput;
import com.eisgroup.genesis.exception.BaseErrorDefinition;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalClaimDataEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryDataEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.json.link.EntityLink;

import java.util.Optional;

/**
 * Validator that is used in {@link com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryUpdateHandler}
 *
 * <AUTHOR>
 * @since 22.12
 */
public class CapDentalPatientHistoryUpdateValidator {

    public Streamable<ErrorHolder> validatePatientUri(CapDentalPatientHistoryUpdateInput input, CapDentalPatientHistoryEntity entity) {
        String inputPatient = getPatient(input.getEntity());
        String existingPatient = getPatient(entity);

        if (inputPatient == null || !inputPatient.equals(existingPatient)) {
            return Streamable.of(CapDentalPatientHistoryUpdateErrorDefinition.DIFFERENT_PATIENT.builder().params(existingPatient).build());
        }
        return Streamable.empty();
    }

    private String getPatient(CapDentalPatientHistoryEntity entity) {
        return Optional.ofNullable(entity)
                .map(CapDentalPatientHistoryEntity::getPatientHistoryData)
                .map(CapDentalPatientHistoryDataEntity::getClaimData)
                .map(CapDentalClaimDataEntity::getPatient)
                .map(EntityLink::getURIString)
                .orElse(null);
    }

    private static class CapDentalPatientHistoryUpdateErrorDefinition extends BaseErrorDefinition {

        public static final CapDentalPatientHistoryUpdateErrorDefinition DIFFERENT_PATIENT =
                new CapDentalPatientHistoryUpdateErrorDefinition("dphu-001", "patient {0} URI cannot be updated after entry creation.");

        protected CapDentalPatientHistoryUpdateErrorDefinition(String code, String message) {
            super(code, message);
        }
    }

}
