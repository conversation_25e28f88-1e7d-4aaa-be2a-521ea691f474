/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.impl;

import com.eis.automation.tzappa.rest.mapping.model.filter.AbstractFilter;
import com.eis.automation.v20.capdental.entity.payment.facade.impl.modeling.interf.ICapDentalPaymentAllocationPayableItemModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.TypeModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;
import com.fasterxml.jackson.annotation.JsonFilter;

@JsonFilter(AbstractFilter.NAME)
public class CapDentalPaymentAllocationPayableItemModel extends TypeModel implements ICapDentalPaymentAllocationPayableItemModel {

    private Integer orthoMonth;
    private UriModel claimSource;

    public Integer getOrthoMonth() {
        return orthoMonth;
    }

    public void setOrthoMonth(Integer orthoMonth) {
        this.orthoMonth = orthoMonth;
    }

    public UriModel getClaimSource() {
        return claimSource;
    }

    public void setClaimSource(UriModel claimSource) {
        this.claimSource = claimSource;
    }
}
