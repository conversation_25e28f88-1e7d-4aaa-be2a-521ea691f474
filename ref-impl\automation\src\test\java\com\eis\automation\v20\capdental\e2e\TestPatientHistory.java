/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.e2e;

import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.claimsearch.service.IClaimSearchService;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalProcedureModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalPatientHistoryWrapperModel;
import com.eis.automation.v20.capdental.entity.patienthistory.facade.impl.modeling.interf.ICapDentalServiceDataModel;
import com.eis.automation.v20.capdental.entity.patienthistory.service.ICapDentalPatientHistoryService;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementInfoProcedureModel;
import com.eis.automation.v20.capdental.entity.settlement.facade.impl.modeling.interf.ICapDentalSettlementModel;
import com.eis.automation.v20.capdental.entity.settlement.service.ICapDentalSettlementService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.facade.impl.modeling.interf.ICapDentalPolicyInfoModel;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.impl.CustomerModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.Test;

import java.time.LocalDate;
import java.util.List;

import static com.eis.automation.tzappa.common.DateUtils.getCurrentDate;
import static com.eis.automation.tzappa_v20.TestGroup.E2E;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.INCOMPLETE;
import static com.eis.automation.v20.cap.constant.CapConstant.CapLossStates.PENDING;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_PATIENT_HISTORY;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalClaimTransactionType.ACTUAL_SERVICES;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalProcedureStatus.ALLOWED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalProcedureStatus.REVIEW_REQUIRED;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalSubmittedProcedureCode.*;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalToothCodes.TOOTH_CODE_5;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalToothCodes.TOOTH_CODE_6;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertSoftly;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestPatientHistory extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomerService;
    @Autowired
    private IProviderService providerService;
    @Autowired
    private IPartyRoleService partyRoleService;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicyService;
    @Autowired
    private ICapDentalLossService dentalLossService;
    @Autowired
    private ICapDentalSettlementService dentalSettlementService;
    @Autowired
    private ICapDentalPatientHistoryService patientHistoryService;
    @Autowired
    private IClaimSearchService claimSearchService;

    @Test(groups = {E2E})
    @TestInfo(testCaseId = "GENESIS-186011", component = CAP_DENTAL_PATIENT_HISTORY)
    public void testDentalPatientHistory() {
        // Preconditions
        IndividualCustomerModel individualCustomer = (IndividualCustomerModel) indCustomerService.createCustomer();
        IProviderWrapperModel providerWrapperModel = providerService.writeWrapper(individualCustomer,
                providerService.createIndividualProvider(),
                partyRoleService.createPartyRole(INDIVIDUAL_PROVIDER));
        IIndividualProviderModel individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        ICapDentalPolicyInfoModel createdDentalPolicy = (ICapDentalPolicyInfoModel) capPolicyService.writeCapUnverifiedPolicy(
                capPolicyService.createDentalUnverifiedPolicyModel(individualCustomer));

        // Dental Claim1
        ICapDentalLossModel initClaim1 = initDentalClaim(createdDentalPolicy.getCapPolicyId(),
                individualCustomer, individualProvider, D2542, false, TOOTH_CODE_6, getCurrentDate().minusDays(1));
        assertThat(initClaim1.getState()).isEqualTo(INCOMPLETE);
        ICapDentalLossModel submitClaim1 = dentalLossService.submitDentalLoss(initClaim1);
        assertThat(submitClaim1.getState()).isEqualTo(PENDING);

        // Dental Settlement1
        ICapDentalSettlementModel settlement1 = claimSearchService.searchDentalSettlement(submitClaim1, 1).get(0);
        assertThat(settlement1.getState()).isEqualTo(PENDING);

        // Step 1
        // Write Patient History
        ICapDentalPatientHistoryWrapperModel patientHistoryModel = patientHistoryService.createPatientHistoryModel(individualCustomer);
        patientHistoryModel.getEntity().getPatientHistoryData().getServiceData().setDecision(ALLOWED);
        patientHistoryModel.getEntity().getPatientHistoryData().getServiceData().setCdtCoveredCd(D8692);
        ICapDentalPatientHistoryModel initPatientHistory = patientHistoryService.initPatientHistory(patientHistoryModel);
        assertSoftly(softly -> {
            softly.assertThat(initPatientHistory.getPatientHistoryData().getServiceData().getDecision())
                    .isEqualTo(patientHistoryModel.getEntity().getPatientHistoryData().getServiceData().getDecision());
            softly.assertThat(initPatientHistory.getPatientHistoryData().getServiceData().getCdtCoveredCd())
                    .isEqualTo(patientHistoryModel.getEntity().getPatientHistoryData().getServiceData().getCdtCoveredCd());
            softly.assertThat(initPatientHistory.getPatientHistoryData().getClaimData().getPatient())
                    .isEqualTo(patientHistoryModel.getEntity().getPatientHistoryData().getClaimData().getPatient());
        });

        // Step 2
        // Readjudicate Dental Settlement1
        ICapDentalSettlementModel readjudicateModel = dentalSettlementService.createReadjudicateSettlementModel(settlement1);
        readjudicateModel.setClaimLossIdentification(submitClaim1.getGentityUri().getUriModel());
        ICapDentalSettlementModel readjudicatedSettlement1 = dentalSettlementService.readjudicateDentalSettlement(readjudicateModel);
        dentalSettlementService.loadDentalSettlement(readjudicatedSettlement1, PENDING);

        // Step 3
        // Search Settlement1
        List<ICapDentalSettlementModel> dentalSettlements = claimSearchService.searchDentalSettlement(submitClaim1, 2);
        assertThat(dentalSettlements.size()).isEqualTo(2);
        ICapDentalSettlementModel settlementVersion1 = getSettlementWithRevision(dentalSettlements, 1);
        assertThat(settlementVersion1.getState()).isEqualTo(PENDING);
        ICapDentalSettlementModel settlementVersion2 = getSettlementWithRevision(dentalSettlements, 2);
        assertThat(settlementVersion2.getState()).isEqualTo(PENDING);
        verifySettlementSubmittedProcedures(settlementVersion1, submitClaim1);
        verifySettlementSubmittedProcedures(settlementVersion2, submitClaim1);
        verifySettlementHistoryProcedures(settlementVersion2, initPatientHistory);
        verifySettlementStatusFlag(settlementVersion1, REVIEW_REQUIRED);
        verifySettlementStatusFlag(settlementVersion2, REVIEW_REQUIRED);

        // Step 4
        // Create Dental Claim2
        ICapDentalLossModel initClaim2 = initDentalClaim(createdDentalPolicy.getCapPolicyId(), individualCustomer,
                individualProvider, D0330, false, TOOTH_CODE_5, getCurrentDate().minusDays(2));
        assertThat(initClaim2.getState()).isEqualTo(INCOMPLETE);

        // Step 5
        // Submit Dental Claim2
        ICapDentalLossModel submitClaim2 = dentalLossService.submitDentalLoss(initClaim2);
        assertThat(submitClaim2.getState()).isEqualTo(PENDING);
        ICapDentalProcedureModel submitClaimProcedure = submitClaim2.getLossDetail().getSubmittedProcedures().get(0);
        ICapDentalProcedureModel initClaimProcedure = initClaim2.getLossDetail().getSubmittedProcedures().get(0);
                assertSoftly(softly -> {
            softly.assertThat(submitClaimProcedure.getProcedureCode()).isEqualTo(initClaimProcedure.getProcedureCode());
            softly.assertThat(submitClaimProcedure.getPredetInd()).isEqualTo(initClaimProcedure.getPredetInd());
            softly.assertThat(submitClaimProcedure.getToothCodes()).isEqualTo(initClaimProcedure.getToothCodes());
            softly.assertThat(submitClaimProcedure.getDateOfService()).isEqualTo(initClaimProcedure.getDateOfService());
            softly.assertThat(submitClaimProcedure.getPreauthorization().getIsProcedureAuthorized())
                    .isEqualTo(initClaimProcedure.getPreauthorization().getIsProcedureAuthorized());
            softly.assertThat(submitClaim2.getLossDetail().getClaimData().getPatientRole().getRegistryId())
                    .isEqualTo(initClaim2.getLossDetail().getClaimData().getPatientRole().getRegistryId());
        });

        // Step 6
        // Search Settlement2
        ICapDentalSettlementModel settlement2 = claimSearchService.searchDentalSettlement(submitClaim2, 1).get(0);
        verifySettlementSubmittedProcedures(settlement2, submitClaim2);
        verifySettlementHistoryProcedures(settlement2, submitClaim1);
        verifySettlementHistoryProcedures(settlement2, initPatientHistory);
        verifySettlementStatusFlag(settlement2, ALLOWED);
    }

    private void verifySettlementStatusFlag(ICapDentalSettlementModel settlementModel, String flag) {
        assertThat(settlementModel.getSettlementResult().getEntries().get(0).getStatus().getFlag()).isEqualTo(flag);
    }

    private void verifySettlementSubmittedProcedures(ICapDentalSettlementModel settlementModel,
                                                     ICapDentalLossModel claimModel) {
        ICapDentalSettlementInfoProcedureModel settlementSubmittedProcedure = settlementModel
                .getSettlementLossInfo().getSubmittedProcedures().get(0);
        ICapDentalProcedureModel claimSubmittedProcedure = claimModel.getLossDetail().getSubmittedProcedures().get(0);
        assertSoftly(softly -> {
            softly.assertThat(settlementSubmittedProcedure.getProcedureCode())
                    .isEqualTo(claimSubmittedProcedure.getProcedureCode());
            softly.assertThat(settlementSubmittedProcedure.getPredetInd())
                    .isEqualTo(claimSubmittedProcedure.getPredetInd());
            softly.assertThat(settlementSubmittedProcedure.getToothCodes())
                    .isEqualTo(claimSubmittedProcedure.getToothCodes());
            softly.assertThat(settlementSubmittedProcedure.getDateOfService())
                    .isEqualTo(claimSubmittedProcedure.getDateOfService());
            softly.assertThat(settlementSubmittedProcedure.getSurfaces())
                    .isEqualTo(claimSubmittedProcedure.getSurfaces());
            softly.assertThat(settlementSubmittedProcedure.getQuantity())
                    .isEqualTo(claimSubmittedProcedure.getQuantity());
            softly.assertThat(settlementSubmittedProcedure.getPreauthorization().getIsProcedureAuthorized())
                    .isEqualTo(claimSubmittedProcedure.getPreauthorization().getIsProcedureAuthorized());
        });
    }

    private void verifySettlementHistoryProcedures(ICapDentalSettlementModel settlementModel,
                                                   ICapDentalLossModel claimModel) {
        ICapDentalProcedureModel claimSubmittedProcedure = claimModel.getLossDetail().getSubmittedProcedures().get(0);
        ICapDentalSettlementInfoProcedureModel settlementHistoryProcedure = getHistoryProcedure(
                settlementModel, claimSubmittedProcedure.getProcedureCode());
        assertSoftly(softly -> {
            softly.assertThat(settlementHistoryProcedure.getProcedureCode())
                    .isEqualTo(claimSubmittedProcedure.getProcedureCode());
            softly.assertThat(settlementHistoryProcedure.getProcedureStatus()).isEqualTo(REVIEW_REQUIRED);
            softly.assertThat(settlementHistoryProcedure.getPredetInd())
                    .isEqualTo(claimSubmittedProcedure.getPredetInd());
            softly.assertThat(settlementHistoryProcedure.getToothCodes())
                    .isEqualTo(claimSubmittedProcedure.getToothCodes());
            softly.assertThat(settlementHistoryProcedure.getDateOfService())
                    .isEqualTo(claimSubmittedProcedure.getDateOfService());
            softly.assertThat(settlementHistoryProcedure.getSurfaces())
                    .isEqualTo(claimSubmittedProcedure.getSurfaces());
            softly.assertThat(settlementHistoryProcedure.getQuantity())
                    .isEqualTo(claimSubmittedProcedure.getQuantity());
            softly.assertThat(settlementHistoryProcedure.getPreauthorization().getIsProcedureAuthorized())
                    .isEqualTo(claimSubmittedProcedure.getPreauthorization().getIsProcedureAuthorized());
        });
    }

    private void verifySettlementHistoryProcedures(ICapDentalSettlementModel settlementModel,
                                                   ICapDentalPatientHistoryModel patientHistoryModel) {
        ICapDentalServiceDataModel patientHistoryServiceData = patientHistoryModel.getPatientHistoryData().getServiceData();
        ICapDentalSettlementInfoProcedureModel settlementHistoryProcedure = getHistoryProcedure(
                settlementModel, patientHistoryServiceData.getCdtCoveredCd());
        assertSoftly(softly -> {
            softly.assertThat(settlementHistoryProcedure.getProcedureStatus())
                    .isEqualTo(patientHistoryServiceData.getDecision());
            softly.assertThat(settlementHistoryProcedure.getProcedureCode())
                    .isEqualTo(patientHistoryServiceData.getCdtCoveredCd());
            softly.assertThat(settlementHistoryProcedure.getPredetInd())
                    .isEqualTo(patientHistoryServiceData.getIsPredet());
            softly.assertThat(settlementHistoryProcedure.getDateOfService())
                    .isEqualTo(patientHistoryServiceData.getDOSDate().toLocalDate());
            softly.assertThat(settlementHistoryProcedure.getPreauthorization().getIsProcedureAuthorized())
                    .isEqualTo(patientHistoryServiceData.getIsProcedureAuthorized());
        });
    }

    private ICapDentalSettlementInfoProcedureModel getHistoryProcedure(ICapDentalSettlementModel settlementModel,
                                                                       String procedureCode) {
        return settlementModel.getSettlementLossInfo().getPatient().getHistoryProcedures().stream()
                .filter(p -> procedureCode.equals(p.getProcedureCode()))
                .findFirst().get();
    }

    private ICapDentalSettlementModel getSettlementWithRevision(List<ICapDentalSettlementModel> dentalSettlements,
                                                                Integer revisionNumber) {
        return dentalSettlements.stream().filter(r -> revisionNumber.equals(r.revisionNumber())).findFirst().get();
    }

    private ICapDentalLossModel initDentalClaim(String policyId,
                                                CustomerModel customerModel,
                                                IProviderModel providerModel,
                                                String procedureCode,
                                                Boolean predetInd,
                                                String toothCodes,
                                                LocalDate dateOfService) {
        ICapDentalLossModel dentalClaimModel = dentalLossService.createDentalLossModel(policyId, customerModel, providerModel);
        ICapDentalProcedureModel submittedProcedureModel = dentalClaimModel.getEntity().getSubmittedProcedures().get(0);
        submittedProcedureModel.setProcedureCode(procedureCode);
        submittedProcedureModel.setToothCodes(List.of(toothCodes));
        submittedProcedureModel.setToothArea(null);
        submittedProcedureModel.setPredetInd(predetInd);
        submittedProcedureModel.setDateOfService(dateOfService);
        submittedProcedureModel.setSurfaces(List.of("F"));
        dentalClaimModel.getEntity().getClaimData().setTransactionType(ACTUAL_SERVICES);
        return dentalLossService.initDentalLoss(dentalClaimModel);
    }
}
