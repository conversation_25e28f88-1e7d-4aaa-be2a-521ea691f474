/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.transformations.executors;

import com.eisgroup.genesis.transformation.context.TransformationContext;
import com.eisgroup.genesis.transformation.executors.OperationExecutor;
import com.eisgroup.genesis.transformation.model.operations.GenericOperation;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;

/**
 * Mock operation executor that always returns <PERSON><PERSON><PERSON> null. Should only be used as a mock in tests.
 *
 * <AUTHOR>
 * @since 22.7
 */
public class MockTestGenericOperationExecutor implements OperationExecutor<GenericOperation, JsonElement> {

    private final String operatorName;

    public MockTestGenericOperationExecutor(String operatorName) {
        this.operatorName = operatorName;
    }

    @Override
    public JsonElement execute(GenericOperation genericOperation, TransformationContext<JsonElement> transformationContext) {
        return JsonNull.INSTANCE;
    }

    @Override
    public String getName() {
        return operatorName;
    }
}
