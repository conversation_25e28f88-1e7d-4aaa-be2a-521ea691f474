/*
 * Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.regression.loss;

import com.eis.automation.tzappa.rest.modeling.assertion.failure.FailureAssertion;
import com.eis.automation.tzappa.rest.modeling.common.error.IFailureResponse;
import com.eis.automation.v20.capdental.CapDentalBaseTest;
import com.eis.automation.v20.capdental.entity.loss.facade.impl.modeling.interf.ICapDentalLossModel;
import com.eis.automation.v20.capdental.entity.loss.service.ICapDentalLossService;
import com.eis.automation.v20.capdental.entity.unverifiedpolicy.service.ICapDentalUnverifiedPolicyService;
import com.eis.automation.v20.cem.entity.common.modeling.core.interf.IIndividualProviderModel;
import com.eis.automation.v20.cem.entity.customer.facade.individual.impl.modeling.IndividualCustomerModel;
import com.eis.automation.v20.cem.entity.customer.service.individual.IIndividualCustomerService;
import com.eis.automation.v20.cem.entity.partyrole.service.IPartyRoleService;
import com.eis.automation.v20.cem.entity.provider.facade.impl.action.modeling.IProviderWrapperModel;
import com.eis.automation.v20.cem.entity.provider.service.IProviderService;
import com.exigen.istf.utils.TestInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import static com.eis.automation.tzappa_v20.TestGroup.REGRESSION;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.Components.CAP_DENTAL_LOSS;
import static com.eis.automation.v20.capdental.constant.CapDentalConstant.DentalOrthoFrequencyCd.ONE_TIME;
import static com.eis.automation.v20.capdental.constant.CapDentalErrorConstant.*;
import static com.eis.automation.v20.cem.constant.CemConstant.Lookup.PartyRole.INDIVIDUAL_PROVIDER;
import static com.eis.automation.v20.platform.utils.assertion.CustomAssertionsExtended.assertThat;

public class TestDentalClaimOrthoDetails extends CapDentalBaseTest {

    @Autowired
    private IIndividualCustomerService indCustomer;
    @Autowired
    private IProviderService provider;
    @Autowired
    private IPartyRoleService partyRole;
    @Autowired
    private ICapDentalUnverifiedPolicyService capPolicy;
    @Autowired
    private ICapDentalLossService capDentalLoss;
    private IndividualCustomerModel individualCustomer;
    private IIndividualProviderModel individualProvider;
    private String capDentalPolicyId;

    @BeforeClass(groups = {REGRESSION}, alwaysRun = true)
    public void createPreconditions() {
        individualCustomer = (IndividualCustomerModel) indCustomer.createCustomer();
        IProviderWrapperModel providerWrapperModel = provider.writeWrapper(individualCustomer,
                provider.createIndividualProvider(),
                partyRole.createPartyRole(INDIVIDUAL_PROVIDER));
        individualProvider = (IIndividualProviderModel) providerWrapperModel.getProvider();
        capDentalPolicyId = capPolicy.writeCapUnverifiedPolicy(
                capPolicy.createDentalUnverifiedPolicyModel(individualCustomer)).getCapPolicyId();
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156168", component = CAP_DENTAL_LOSS)
    public void testDefaultOrthoMonthQuantity() {
        // Step 2.1
        ICapDentalLossModel initDentalClaim = initDentalClaim(null);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getLossDetail()
                .getSubmittedProcedures().get(0).getOrtho().getOrthoMonthQuantity()).isEqualTo(1);
    }

    @Test(groups = {REGRESSION})
    @TestInfo(testCaseId = "GENESIS-156168", component = CAP_DENTAL_LOSS)
    public void testDefaultOrthoFrequencyCd() {
        // Step 2.2
        ICapDentalLossModel initDentalClaim = initDentalClaim(1);
        ICapDentalLossModel submitDentalClaim = capDentalLoss.submitDentalLoss(initDentalClaim);
        assertThat(submitDentalClaim.getLossDetail()
                .getSubmittedProcedures().get(0).getOrtho().getOrthoFrequencyCd()).isEqualTo(ONE_TIME);
    }

    private ICapDentalLossModel initDentalClaim(Integer orthoMonthQuantity) {
        ICapDentalLossModel dentalClaimModel = capDentalLoss.createDentalLossModel(
                capDentalLoss.getSpecificTestData("MapOrthoDetails", "TestData"),
                capDentalPolicyId, individualCustomer, individualProvider);
        dentalClaimModel.getEntity().getSubmittedProcedures().get(0).getOrtho().setOrthoMonthQuantity(orthoMonthQuantity);
        return capDentalLoss.initDentalLoss(dentalClaimModel);
    }
}
