/* Copyright © 2022 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.*/
package com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory;

import java.util.Optional;

import com.eisgroup.genesis.Lazy;
import com.eisgroup.genesis.Streamable;
import com.eisgroup.genesis.cap.common.command.validator.CapValidatorRegistry;
import com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.input.CapDentalPatientHistoryInitInput;
import com.eisgroup.genesis.cap.ref.repository.CapDentalPatientHistoryRepository;
import com.eisgroup.genesis.commands.ProductCommandHandler;
import com.eisgroup.genesis.exception.ErrorHolder;
import com.eisgroup.genesis.factory.core.ModelInstanceFactory;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalAccessTrackInfoEntity;
import com.eisgroup.genesis.factory.model.capdentalpatienthistory.CapDentalPatientHistoryEntity;
import com.eisgroup.genesis.factory.model.domain.DomainModel;
import com.eisgroup.genesis.factory.model.lifecycle.Modifying;
import com.eisgroup.genesis.factory.repository.key.KeyTraversalUtil;
import com.eisgroup.genesis.factory.services.AccessTrackInfoService;
import com.eisgroup.genesis.model.ModelResolver;

import javax.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Autowired;

import static com.eisgroup.genesis.cap.ref.command.capdentalpatienthistory.CapDentalPatientHistoryCommands.INIT_PATIENT_HISTORY;

/**
 * Handler for dental patient history initiation.
 *
 * <AUTHOR>
 * @since 22.6
 */
@Modifying
public class CapDentalPatientHistoryInitHandler implements ProductCommandHandler<CapDentalPatientHistoryInitInput, CapDentalPatientHistoryEntity> {

    @Autowired
    private ModelResolver modelResolver;

    @Autowired
    private AccessTrackInfoService accessTrackInfoService;

    @Autowired
    private CapDentalPatientHistoryRepository capDentalPatientHistoryRepository;

    @Autowired
    private CapValidatorRegistry capValidatorRegistry;

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity load(@Nonnull CapDentalPatientHistoryInitInput entityInput) {
        return (CapDentalPatientHistoryEntity) ModelInstanceFactory.createRootInstance(this.modelResolver.getModelName(), this.modelResolver.getModelVersion());
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity execute(@Nonnull CapDentalPatientHistoryInitInput entityInput, @Nonnull CapDentalPatientHistoryEntity entity) {
        DomainModel model = modelResolver.resolveModel(DomainModel.class);
        entity.setPatientHistoryData(entityInput.getEntity().getPatientHistoryData());
        Optional.ofNullable(entity.getPatientHistoryData())
                .ifPresent(patientHistory -> patientHistory.setAccessTrackInfo(createAccessTrackInfo(entity)));
        KeyTraversalUtil.traverseRoot(entity.toJson(), model);
        return entity;
    }

    @Nonnull
    @Override
    public Streamable<ErrorHolder> validateAsync(@Nonnull CapDentalPatientHistoryInitInput request, @Nonnull CapDentalPatientHistoryEntity loadedEntity) {
        return capValidatorRegistry.validateRequest(request);
    }

    @Nonnull
    @Override
    public CapDentalPatientHistoryEntity save(@Nonnull CapDentalPatientHistoryInitInput capDentalPatientHistoryEntity, @Nonnull CapDentalPatientHistoryEntity entity) {
        return capDentalPatientHistoryRepository.save(entity).get();
    }

    @Override
    public String getName() {
        return INIT_PATIENT_HISTORY;
    }

    private CapDentalAccessTrackInfoEntity createAccessTrackInfo(CapDentalPatientHistoryEntity entity) {
        CapDentalAccessTrackInfoEntity accessTrackInfo = ModelInstanceFactory.createInstanceByBusinessType(entity.getModelName(), entity.getModelVersion(), "CapDentalAccessTrackInfoEntity");
        accessTrackInfoService.fillIn(accessTrackInfo);
        return accessTrackInfo;
    }
}
