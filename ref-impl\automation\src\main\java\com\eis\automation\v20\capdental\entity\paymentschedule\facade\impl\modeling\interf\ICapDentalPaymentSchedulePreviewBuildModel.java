/*
 * Copyright © 2023 EIS Group and/or one of its affiliates. All rights reserved. Unpublished work under U.S. copyright laws.
 * CONFIDENTIAL AND TRADE SECRET INFORMATION. No portion of this work may be copied, distributed, modified, or incorporated into any other media without EIS Group prior written consent.
 */

package com.eis.automation.v20.capdental.entity.paymentschedule.facade.impl.modeling.interf;

import com.eis.automation.tzappa.rest.modeling.IModel;
import com.eis.automation.v20.platform.common.modeling.domain.impl.UriModel;

import java.util.List;

public interface ICapDentalPaymentSchedulePreviewBuildModel extends IModel {
    List<UriModel> getSettlements();

    UriModel getOriginSource();

    void setOriginSource(UriModel originSource);

    void setSettlements(List<UriModel> settlements);
}
